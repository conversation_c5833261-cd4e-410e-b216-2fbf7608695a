import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/user_model.dart';
import '../../models/warehouse_model.dart';

import '../../services/data_service.dart';
import '../../services/image_service.dart';

class AddAgentScreen extends StatefulWidget {
  final UserModel? agent; // For editing existing agent

  const AddAgentScreen({super.key, this.agent});

  @override
  State<AddAgentScreen> createState() => _AddAgentScreenState();
}

class _AddAgentScreenState extends State<AddAgentScreen> {
  final DataService _dataService = DataService.instance;
  final ImageService _imageService = ImageService.instance;
  final _formKey = GlobalKey<FormState>();

  final _fullNameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _warehouseNameController = TextEditingController();
  final _warehouseLocationController = TextEditingController();

  List<WarehouseModel> _warehouses = [];
  WarehouseModel? _selectedWarehouse;
  bool _createNewWarehouse = true;
  bool _isLoading = false;
  bool _isSaving = false;

  // ID Card Images removed - not needed for agents

  @override
  void initState() {
    super.initState();
    _loadWarehouses();
    
    if (widget.agent != null) {
      _populateFields();
    }
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _warehouseNameController.dispose();
    _warehouseLocationController.dispose();
    super.dispose();
  }

  Future<void> _loadWarehouses() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final warehouses = await _dataService.getAllWarehouses();
      setState(() {
        _warehouses = warehouses;
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل المخازن: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _populateFields() {
    final agent = widget.agent!;
    _fullNameController.text = agent.fullName;
    _usernameController.text = agent.username;
    _phoneController.text = agent.phone;
    _emailController.text = agent.email;

    if (agent.warehouseId != null && _warehouses.isNotEmpty) {
      try {
        _selectedWarehouse = _warehouses.firstWhere(
          (w) => w.id == agent.warehouseId,
        );
        _createNewWarehouse = false;
      } catch (e) {
        // If warehouse not found, create new one
        _createNewWarehouse = true;
      }
    }
  }

  // ID Card image functions removed - not needed for agents

  Future<void> _saveAgent() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSaving = true;
    });

    try {
      // final authProvider = Provider.of<AuthProvider>(context, listen: false); // Not used currently
      String warehouseId;

      // Create or select warehouse
      if (_createNewWarehouse) {
        final warehouse = WarehouseModel(
          id: '', // Will be set by the service
          name: _warehouseNameController.text.trim(),
          type: 'agent',
          address: _warehouseLocationController.text.trim(),
          ownerId: '', // Will be set to the agent's ID after creation
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        warehouseId = await _dataService.createWarehouse(warehouse);
      } else {
        warehouseId = _selectedWarehouse!.id;
      }

      // Create or update agent
      if (widget.agent == null) {
        // Create new agent
        final agent = UserModel(
          id: '', // Will be set by the service
          username: _usernameController.text.trim(),
          email: _emailController.text.trim(),
          fullName: _fullNameController.text.trim(),
          phone: _phoneController.text.trim(),
          role: 'agent',
          warehouseId: warehouseId,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final agentId = await _dataService.createUserWithPassword(
          agent,
          _passwordController.text.trim(),
        );

        // Agent account will be created automatically by the DataService
        // when the user is created with role 'agent'
        if (kDebugMode) {
          print('Agent account will be created automatically for: $agentId');
        }

        // Update warehouse manager if new warehouse was created
        if (_createNewWarehouse) {
          await _dataService.updateWarehouseManager(warehouseId, agentId);
          if (kDebugMode) {
            print('Updated warehouse $warehouseId with owner: $agentId');
          }
        }
      } else {
        // Update existing agent
        final updatedAgent = widget.agent!.copyWith(
          fullName: _fullNameController.text.trim(),
          phone: _phoneController.text.trim(),
          email: _emailController.text.trim(),
          warehouseId: warehouseId,
          updatedAt: DateTime.now(),
        );

        await _dataService.updateUser(updatedAgent);
      }

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          widget.agent == null ? 'تم إنشاء الوكيل بنجاح' : 'تم تحديث الوكيل بنجاح',
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'خطأ في ${widget.agent == null ? 'إنشاء' : 'تحديث'} الوكيل: $e',
          isError: true,
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.agent == null ? 'إضافة وكيل جديد' : 'تعديل الوكيل'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Form(
              key: _formKey,
              child: ListView(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                children: [
                  // Agent Information
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'بيانات الوكيل',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          TextFormField(
                            controller: _fullNameController,
                            decoration: const InputDecoration(
                              labelText: 'الاسم الكامل *',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.person),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'يرجى إدخال الاسم الكامل';
                              }
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          TextFormField(
                            controller: _usernameController,
                            decoration: const InputDecoration(
                              labelText: 'اسم المستخدم *',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.account_circle),
                            ),
                            enabled: widget.agent == null, // Can't change username for existing users
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'يرجى إدخال اسم المستخدم';
                              }
                              if (value.trim().length < 3) {
                                return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                              }
                              return null;
                            },
                          ),
                          
                          if (widget.agent == null) ...[
                            const SizedBox(height: AppConstants.defaultPadding),
                            
                            TextFormField(
                              controller: _passwordController,
                              decoration: const InputDecoration(
                                labelText: 'كلمة المرور *',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.lock),
                              ),
                              obscureText: true,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'يرجى إدخال كلمة المرور';
                                }
                                if (value.trim().length < 6) {
                                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                }
                                return null;
                              },
                            ),
                          ],
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          TextFormField(
                            controller: _phoneController,
                            decoration: const InputDecoration(
                              labelText: 'رقم الهاتف *',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.phone),
                            ),
                            keyboardType: TextInputType.phone,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'يرجى إدخال رقم الهاتف';
                              }
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: AppConstants.defaultPadding),
                          
                          TextFormField(
                            controller: _emailController,
                            decoration: const InputDecoration(
                              labelText: 'البريد الإلكتروني (اختياري)',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.email),
                            ),
                            keyboardType: TextInputType.emailAddress,
                            validator: (value) {
                              if (value != null && value.trim().isNotEmpty) {
                                if (!AppUtils.isValidEmail(value.trim())) {
                                  return 'يرجى إدخال بريد إلكتروني صحيح';
                                }
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: AppConstants.defaultPadding),

                  // Warehouse Assignment
                  _buildWarehouseSection(),
                  
                  const SizedBox(height: AppConstants.largePadding),
                  
                  // Save Button
                  ElevatedButton(
                    onPressed: _isSaving ? null : _saveAgent,
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 48),
                    ),
                    child: _isSaving
                        ? const CircularProgressIndicator()
                        : Text(widget.agent == null ? 'إنشاء الوكيل' : 'حفظ التغييرات'),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildWarehouseSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تخصيص المخزن',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Warehouse selection options
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('إنشاء مخزن جديد'),
                    value: true,
                    groupValue: _createNewWarehouse,
                    onChanged: (value) {
                      setState(() {
                        _createNewWarehouse = value!;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('اختيار مخزن موجود'),
                    value: false,
                    groupValue: _createNewWarehouse,
                    onChanged: _warehouses.isNotEmpty ? (value) {
                      setState(() {
                        _createNewWarehouse = value!;
                      });
                    } : null,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            if (_createNewWarehouse) ...[
              TextFormField(
                controller: _warehouseNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المخزن *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.warehouse),
                ),
                validator: (value) {
                  if (_createNewWarehouse && (value == null || value.trim().isEmpty)) {
                    return 'يرجى إدخال اسم المخزن';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              TextFormField(
                controller: _warehouseLocationController,
                decoration: const InputDecoration(
                  labelText: 'موقع المخزن *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_on),
                ),
                validator: (value) {
                  if (_createNewWarehouse && (value == null || value.trim().isEmpty)) {
                    return 'يرجى إدخال موقع المخزن';
                  }
                  return null;
                },
              ),
            ] else if (_warehouses.isNotEmpty) ...[
              DropdownButtonFormField<WarehouseModel>(
                value: _selectedWarehouse,
                decoration: const InputDecoration(
                  labelText: 'اختيار المخزن *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.warehouse),
                ),
                items: _warehouses.map((warehouse) {
                  return DropdownMenuItem(
                    value: warehouse,
                    child: Text('${warehouse.name} - ${warehouse.address}'),
                  );
                }).toList(),
                onChanged: (warehouse) {
                  setState(() {
                    _selectedWarehouse = warehouse;
                  });
                },
                validator: (value) {
                  if (!_createNewWarehouse && value == null) {
                    return 'يرجى اختيار المخزن';
                  }
                  return null;
                },
              ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                ),
                child: const Text('لا توجد مخازن متاحة. سيتم إنشاء مخزن جديد تلقائياً.'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // ID Card section removed - not needed for agents
}
