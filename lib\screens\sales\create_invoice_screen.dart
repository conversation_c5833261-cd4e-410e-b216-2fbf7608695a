import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/item_model.dart';
import '../../models/invoice_model.dart';

import '../../models/agent_account_model.dart';
import '../../services/data_service.dart';
import '../../services/image_service.dart';
import '../../services/enhanced_notification_service.dart';
import '../../services/composite_image_service.dart';
import '../../services/notification_service.dart';
import '../../services/windows/windows_image_picker_service.dart';
import '../../providers/auth_provider.dart';

class CreateInvoiceScreen extends StatefulWidget {
  const CreateInvoiceScreen({super.key});

  @override
  State<CreateInvoiceScreen> createState() => _CreateInvoiceScreenState();
}

class _CreateInvoiceScreenState extends State<CreateInvoiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _searchController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _customerNationalIdController = TextEditingController();
  final _customerAddressController = TextEditingController();
  final _sellingPriceController = TextEditingController();

  String _paymentMethod = 'كاش'; // كاش أو قسط

  final DataService _dataService = DataService.instance;
  final ImageService _imageService = ImageService.instance;
  final EnhancedNotificationService _notificationService = EnhancedNotificationService.instance;
  final WindowsImagePickerService _windowsImagePicker = WindowsImagePickerService.instance;

  ItemModel? _selectedItem;
  List<ItemModel> _searchResults = [];
  List<ItemModel> _allAvailableItems = [];
  dynamic _customerIdFrontImage; // Can be File or Uint8List
  dynamic _customerIdBackImage; // Can be File or Uint8List
  Map<String, String> _extractedCustomerData = {};
  bool _isLoading = false;
  bool _isSearching = false;
  bool _isProcessingId = false;
  bool _showItemsList = false;

  @override
  void initState() {
    super.initState();
    _loadAllAvailableItems();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reload items when screen becomes active (in case items were transferred)
    _loadAllAvailableItems();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _customerNationalIdController.dispose();
    _customerAddressController.dispose();
    _sellingPriceController.dispose();
    super.dispose();
  }

  Future<void> _loadAllAvailableItems() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      List<ItemModel> allItems = [];

      if (currentUser.isAgent) {
        // For agents, load items from their specific warehouse
        final agentWarehouses = await _dataService.getWarehousesByOwnerId(currentUser.id);

        if (kDebugMode) {
          print('Agent ${currentUser.fullName} has ${agentWarehouses.length} warehouses');
        }

        for (final warehouse in agentWarehouses) {
          // Try both Arabic and English status values
          var warehouseItems = await _dataService.getItems(
            warehouseId: warehouse.id,
            status: 'متاح',
          );

          // If no items found with Arabic status, try English
          if (warehouseItems.isEmpty) {
            warehouseItems = await _dataService.getItems(
              warehouseId: warehouse.id,
              status: 'available',
            );
          }

          // If still no items, get all items from warehouse and filter manually
          if (warehouseItems.isEmpty) {
            final allWarehouseItems = await _dataService.getItems(
              warehouseId: warehouse.id,
            );
            warehouseItems = allWarehouseItems.where((item) =>
              item.status == 'متاح' ||
              item.status == 'available' ||
              item.status.toLowerCase() == 'available'
            ).toList();
          }

          if (kDebugMode) {
            print('Found ${warehouseItems.length} available items in agent warehouse ${warehouse.name}');
            if (warehouseItems.isNotEmpty) {
              print('Sample item statuses: ${warehouseItems.take(3).map((i) => i.status).join(', ')}');
            }
          }
          allItems.addAll(warehouseItems);
        }
      } else {
        // For admins, load items from all warehouses
        final allWarehouses = await _dataService.getAllWarehouses();

        if (kDebugMode) {
          print('Loading items from ${allWarehouses.length} warehouses for admin');
        }

        for (final warehouse in allWarehouses) {
          // Try both Arabic and English status values
          var warehouseItems = await _dataService.getItems(
            warehouseId: warehouse.id,
            status: 'متاح',
          );

          // If no items found with Arabic status, try English
          if (warehouseItems.isEmpty) {
            warehouseItems = await _dataService.getItems(
              warehouseId: warehouse.id,
              status: 'available',
            );
          }

          // If still no items, get all items from warehouse and filter manually
          if (warehouseItems.isEmpty) {
            final allWarehouseItems = await _dataService.getItems(
              warehouseId: warehouse.id,
            );
            warehouseItems = allWarehouseItems.where((item) =>
              item.status == 'متاح' ||
              item.status == 'available' ||
              item.status.toLowerCase() == 'available'
            ).toList();
          }

          if (kDebugMode) {
            print('Found ${warehouseItems.length} available items in warehouse ${warehouse.name}');
            if (warehouseItems.isNotEmpty) {
              print('Sample item statuses: ${warehouseItems.take(3).map((i) => i.status).join(', ')}');
            }
          }
          allItems.addAll(warehouseItems);
        }
      }

      // Sort by brand and model
      allItems.sort((a, b) {
        final brandCompare = a.brand.compareTo(b.brand);
        if (brandCompare != 0) return brandCompare;
        return a.model.compareTo(b.model);
      });

      if (mounted) {
        setState(() {
          _allAvailableItems = allItems;
        });
      }

      if (kDebugMode) {
        print('Loaded ${_allAvailableItems.length} total available items for ${currentUser.role}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading available items: $e');
      }
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل الأصناف: $e', isError: true);
      }
    }
  }

  Future<void> _searchItems(String query) async {
    if (query.trim().isEmpty) {
      if (mounted) {
        setState(() {
          _searchResults = [];
          _showItemsList = false;
        });
      }
      return;
    }

    setState(() {
      _isSearching = true;
      _showItemsList = false;
    });

    try {
      // If items not loaded yet, load them first
      if (_allAvailableItems.isEmpty) {
        await _loadAllAvailableItems();
      }

      // Search in already loaded items for faster response
      final searchQuery = query.trim().toLowerCase();
      final filteredResults = _allAvailableItems.where((item) {
        return item.motorFingerprintText.toLowerCase().contains(searchQuery) ||
               item.brand.toLowerCase().contains(searchQuery) ||
               item.model.toLowerCase().contains(searchQuery) ||
               item.color.toLowerCase().contains(searchQuery) ||
               item.type.toLowerCase().contains(searchQuery) ||
               item.countryOfOrigin.toLowerCase().contains(searchQuery) ||
               item.chassisNumber.toLowerCase().contains(searchQuery) ||
               item.id.toLowerCase().contains(searchQuery);
      }).toList();

      // Sort by relevance (exact matches first)
      filteredResults.sort((a, b) {
        final aExact = a.motorFingerprintText.toLowerCase() == searchQuery ||
                      a.brand.toLowerCase() == searchQuery ||
                      a.model.toLowerCase() == searchQuery;
        final bExact = b.motorFingerprintText.toLowerCase() == searchQuery ||
                      b.brand.toLowerCase() == searchQuery ||
                      b.model.toLowerCase() == searchQuery;

        if (aExact && !bExact) return -1;
        if (!aExact && bExact) return 1;

        // Then sort by brand and model
        final brandCompare = a.brand.compareTo(b.brand);
        if (brandCompare != 0) return brandCompare;
        return a.model.compareTo(b.model);
      });

      setState(() {
        _searchResults = filteredResults.take(20).toList(); // Limit to 20 results
      });

      if (kDebugMode) {
        print('Search for "$query" found ${_searchResults.length} results');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في البحث: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
      }
    }
  }

  void _showAllItems() async {
    // Load items if not loaded yet
    if (_allAvailableItems.isEmpty) {
      await _loadAllAvailableItems();
    }

    setState(() {
      _showItemsList = !_showItemsList;
      _searchResults = [];
      _searchController.clear();
    });
  }

  void _selectItem(ItemModel item) {
    setState(() {
      _selectedItem = item;
      _searchController.text = item.motorFingerprintText;
      _sellingPriceController.text = item.suggestedSellingPrice.toString();
      _searchResults = [];
      _showItemsList = false;
    });
  }

  Widget _buildItemTile(ItemModel item) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Colors.blue.shade100,
        child: Text(
          item.brand.substring(0, 1).toUpperCase(),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
      ),
      title: Text(
        '${item.brand} ${item.model}',
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('البصمة: ${item.motorFingerprintText}'),
          Text('اللون: ${item.color} | السنة: ${item.yearOfManufacture}'),
          Text(
            'السعر: ${AppUtils.formatCurrency(item.suggestedSellingPrice)}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        ],
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _selectItem(item),
    );
  }

  Future<void> _captureCustomerId() async {
    try {
      setState(() {
        _isProcessingId = true;
      });

      // Check if running on Windows/Desktop
      if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
        await _pickCustomerIdImagesWindows();
      } else {
        await _pickCustomerIdImagesMobile();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, e.toString(), isError: true);
      }
    } finally {
      setState(() {
        _isProcessingId = false;
      });
    }
  }

  Future<void> _pickCustomerIdImagesWindows() async {
    // Pick front image
    AppUtils.showSnackBar(context, 'اختر صورة الوجه الأمامي لبطاقة الهوية');
    final frontImageFile = await _windowsImagePicker.pickImage();
    if (frontImageFile == null || !mounted) return;

    AppUtils.showSnackBar(context, 'اختر الآن صورة الوجه الخلفي لبطاقة الهوية');

    // Pick back image
    final backImageFile = await _windowsImagePicker.pickImage();
    if (backImageFile == null || !mounted) return;

    // Convert to File objects for consistency
    final frontFile = File(frontImageFile.path);
    final backFile = File(backImageFile.path);

    // Extract data using OCR (mock data for Windows)
    final extractedData = await _extractMockCustomerData();

    setState(() {
      _customerIdFrontImage = frontFile;
      _customerIdBackImage = backFile;
      _extractedCustomerData = extractedData;

      // لا نملأ الحقول تلقائياً - يتم الإدخال يدوياً
    });

    if (mounted) {
      AppUtils.showSnackBar(context, 'تم اختيار صور بطاقة الهوية بنجاح. يرجى إدخال بيانات العميل يدوياً');
    }
  }

  Future<void> _pickCustomerIdImagesMobile() async {
    // Capture front image
    final frontImage = await _imageService.takeIdCardPhoto(context, isFront: true);
    if (frontImage == null || !mounted) return;

    AppUtils.showSnackBar(context, 'التقط الآن الوجه الخلفي لبطاقة الهوية');

    // Capture back image
    final backImage = await _imageService.takeIdCardPhoto(context, isFront: false);
    if (backImage == null || !mounted) return;

    // Extract data using OCR
    final extractedData = await _imageService.extractIdCardData(frontImage, backImage);

    setState(() {
      _customerIdFrontImage = frontImage;
      _customerIdBackImage = backImage;
      _extractedCustomerData = extractedData;

      // لا نملأ الحقول تلقائياً - يتم الإدخال يدوياً
    });

    if (mounted) {
      AppUtils.showSnackBar(context, 'تم تصوير بطاقة الهوية بنجاح. يرجى إدخال بيانات العميل يدوياً');
    }
  }

  Future<Map<String, String>> _extractMockCustomerData() async {
    // لا نستخرج بيانات تلقائياً - يتم الإدخال يدوياً
    return {
      'fullName': '',
      'nationalId': '',
      'address': '',
      'issueDate': '',
      'expiryDate': '',
    };
  }

  Future<String?> _uploadFileToCloudinary(File file, String folder, String fileName) async {
    try {
      if (kDebugMode) {
        print('🔄 Uploading file to Cloudinary: $fileName');
        print('📤 Uploading to folder: $folder');
      }

      // Read file as bytes
      final bytes = await file.readAsBytes();

      // Use the existing image service to upload
      return await _imageService.uploadImage(
        bytes,
        fileName,
        folder: folder,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error uploading file to Cloudinary: $e');
      }
      return null;
    }
  }

  Future<void> _createInvoice() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedItem == null) {
      AppUtils.showSnackBar(context, 'يرجى اختيار الصنف المباع', isError: true);
      return;
    }
    if (_customerIdFrontImage == null || _customerIdBackImage == null) {
      AppUtils.showSnackBar(context, 'يرجى اختيار صور بطاقة الهوية (الوجهين)', isError: true);
      return;
    }

    // التحقق من وجود الإنترنت قبل البدء
    final isOnline = await _dataService.isOnline();
    if (!isOnline) {
      AppUtils.showSnackBar(
        context,
        'لا يوجد اتصال بالإنترنت. يرجى التأكد من الاتصال قبل إنشاء الفاتورة لضمان عدم فقدان البيانات.',
        isError: true,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser!;
      
      // Upload customer ID images
      String frontImageUrl = '';
      String backImageUrl = '';

      if (_customerIdFrontImage != null) {
        if (_customerIdFrontImage is File) {
          // Windows/Desktop: File object
          frontImageUrl = await _uploadFileToCloudinary(
            _customerIdFrontImage as File,
            'customer_id_cards',
            'customer_id_front_${DateTime.now().millisecondsSinceEpoch}',
          ) ?? '';
        } else {
          // Mobile: Uint8List
          frontImageUrl = await _imageService.uploadImage(
            _customerIdFrontImage!,
            'customer_id_front_${DateTime.now().millisecondsSinceEpoch}',
          ) ?? '';
        }
      }

      if (_customerIdBackImage != null) {
        if (_customerIdBackImage is File) {
          // Windows/Desktop: File object
          backImageUrl = await _uploadFileToCloudinary(
            _customerIdBackImage as File,
            'customer_id_cards',
            'customer_id_back_${DateTime.now().millisecondsSinceEpoch}',
          ) ?? '';
        } else {
          // Mobile: Uint8List
          backImageUrl = await _imageService.uploadImage(
            _customerIdBackImage!,
            'customer_id_back_${DateTime.now().millisecondsSinceEpoch}',
          ) ?? '';
        }
      }

      // Calculate profit distribution using agent's custom profit share percentage
      final sellingPrice = double.parse(_sellingPriceController.text.trim());
      final profitAmount = sellingPrice - _selectedItem!.purchasePrice;

      double companyProfitShare = profitAmount;
      double agentProfitShare = 0;

      // If selling from agent warehouse, use agent's custom profit share percentage
      if (currentUser.isAgent) {
        final agentPercentage = currentUser.profitSharePercentage; // نسبة الوكيل المخزنة
        agentProfitShare = profitAmount * agentPercentage;
        companyProfitShare = profitAmount * (1.0 - agentPercentage);

        if (kDebugMode) {
          print('💰 Invoice profit calculation:');
          print('   Agent: ${currentUser.fullName}');
          print('   Agent profit percentage: ${(agentPercentage * 100).toStringAsFixed(1)}%');
          print('   Total profit: ${AppUtils.formatCurrency(profitAmount)}');
          print('   Agent share: ${AppUtils.formatCurrency(agentProfitShare)}');
          print('   Company share: ${AppUtils.formatCurrency(companyProfitShare)}');
        }
      }

      // Prepare customer data
      final customerData = {
        'fullName': _customerNameController.text.trim(),
        'phone': _customerPhoneController.text.trim(),
        'nationalId': _customerNationalIdController.text.trim(),
        'address': _customerAddressController.text.trim(),
        'issueDate': _extractedCustomerData['issueDate'] ?? '',
        'expiryDate': _extractedCustomerData['expiryDate'] ?? '',
      };

      // Generate invoice number
      final invoiceNumber = AppUtils.generateInvoiceId('INV-CUSTOMER');
      
      // Create invoice
      final now = DateTime.now();
      final invoice = InvoiceModel(
        id: '', // Will be set by Firestore
        invoiceNumber: invoiceNumber,
        type: 'customer',
        agentId: currentUser.isAgent ? currentUser.id : null,
        warehouseId: _selectedItem!.currentWarehouseId,
        itemId: _selectedItem!.id,
        itemCost: _selectedItem!.purchasePrice,
        sellingPrice: sellingPrice,
        profitAmount: profitAmount,
        companyProfitShare: companyProfitShare,
        agentProfitShare: agentProfitShare,
        status: 'pending',
        createdAt: now,
        updatedAt: now,
        createdBy: currentUser.id,
        customerData: customerData,
        customerIdImages: [frontImageUrl, backImageUrl],
        additionalData: {
          'paymentMethod': _paymentMethod,
          'paymentType': _paymentMethod,
        },
      );

      // Save invoice
      final invoiceId = await _dataService.createInvoice(invoice);

      // Update agent balance if selling from agent warehouse
      if (currentUser.isAgent) {
        // Only add company profit share as debt (50% of profit)
        // The purchase price debt was already added when item was transferred to agent
        if (companyProfitShare > 0) {
          final profitTransaction = AgentTransaction(
            id: AppUtils.generateId(),
            type: 'debt',
            amount: companyProfitShare,
            description: 'نصيب المؤسسة من الربح (50%) - ${_selectedItem!.type} ${_selectedItem!.model}',
            invoiceId: invoiceId,
            itemId: _selectedItem!.id,
            timestamp: DateTime.now(),
            createdBy: currentUser.id,
          );
          await _dataService.addAgentTransaction(currentUser.id, profitTransaction);
        }
      }

      // Update item status to sold
      final updatedItem = _selectedItem!.copyWith(
        status: 'sold',
        updatedAt: now,
      );
      await _dataService.updateItem(updatedItem);

      // Create composite image for managers
      try {
        await CompositeImageService.instance.createCompositeImage(
          invoice: invoice,
          item: _selectedItem!,
          motorFingerprintImagePath: _selectedItem!.motorFingerprintImageUrl,
          chassisImagePath: _selectedItem!.chassisImageUrl,
          customerIdImagePath: frontImageUrl, // Use front ID image
        );
        // Composite image created successfully
      } catch (e) {
        if (kDebugMode) {
          print('Error creating composite image: $e');
        }
        // Continue without composite image
      }

      // Send notification to admins
      await _notificationService.notifySaleCreated(
        agentName: currentUser.fullName,
        customerName: _customerNameController.text.trim(),
        amount: sellingPrice,
        invoiceId: invoice.id,
      );

      // Send notification to managers for document tracking (if agent sale)
      if (currentUser.isAgent) {
        try {
          await NotificationService.instance.notifyManagersOnAgentSale(
            invoice: invoice,
            agent: currentUser,
          );
        } catch (e) {
          if (kDebugMode) {
            print('Error sending manager notification: $e');
          }
          // Continue without notification
        }
      }

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إنشاء الفاتورة بنجاح وبدء تتبع الجواب');
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إنشاء الفاتورة: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء فاتورة بيع'),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildItemSelectionSection(),
              if (_selectedItem != null) ...[
                const SizedBox(height: AppConstants.largePadding),
                _buildSelectedItemSection(),
                const SizedBox(height: AppConstants.largePadding),
                _buildCustomerInfoSection(),
                const SizedBox(height: AppConstants.largePadding),
                _buildPaymentMethodSection(),
                const SizedBox(height: AppConstants.largePadding),
                _buildPricingSection(),
                const SizedBox(height: AppConstants.largePadding * 2),
                _buildCreateInvoiceButton(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItemSelectionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار الصنف',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: 'البحث الذكي',
                      hintText: 'ابحث بالبصمة، الماركة، الموديل، اللون، النوع، رقم الشاسيه...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (_searchController.text.isNotEmpty)
                            IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                _searchItems('');
                              },
                            ),
                          if (_isSearching)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                    ),
                    onChanged: _searchItems,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: _showAllItems,
                  icon: Icon(_showItemsList ? Icons.keyboard_arrow_up : Icons.list),
                  label: Text(_showItemsList ? 'إخفاء' : 'قائمة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            
            if (_searchResults.isNotEmpty) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Text(
                'نتائج البحث (${_searchResults.length})',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                constraints: const BoxConstraints(maxHeight: 300),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _searchResults.length,
                  itemBuilder: (context, index) {
                    final item = _searchResults[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Theme.of(context).primaryColor,
                          child: Text(
                            item.brand.substring(0, 1).toUpperCase(),
                            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                          ),
                        ),
                        title: Text(
                          '${item.brand} ${item.model}',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('البصمة: ${item.motorFingerprintText}'),
                            Text('اللون: ${item.color} | النوع: ${item.type}'),
                            if (item.chassisNumber.isNotEmpty)
                              Text('رقم الشاسيه: ${item.chassisNumber}'),
                          ],
                        ),
                        trailing: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              AppUtils.formatCurrency(item.suggestedSellingPrice),
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            const Text('سعر مقترح', style: TextStyle(fontSize: 12)),
                          ],
                        ),
                        onTap: () => _selectItem(item),
                      ),
                    );
                  },
                ),
              ),
            ] else if (_searchController.text.isNotEmpty && !_isSearching) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.search_off, color: Colors.grey.shade600),
                    const SizedBox(width: 8),
                    Text(
                      'لا توجد نتائج للبحث "${_searchController.text}"',
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                  ],
                ),
              ),
            ],

            // Show all items list
            if (_showItemsList) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.inventory_2, color: Colors.blue),
                          const SizedBox(width: 8),
                          Text(
                            'الأصناف المتاحة (${_allAvailableItems.length})',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 300,
                      child: _allAvailableItems.isEmpty
                          ? const Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.inventory_2_outlined, size: 48, color: Colors.grey),
                                  SizedBox(height: 8),
                                  Text('لا توجد أصناف متاحة', style: TextStyle(color: Colors.grey)),
                                ],
                              ),
                            )
                          : ListView.builder(
                              itemCount: _allAvailableItems.length,
                              itemBuilder: (context, index) {
                                final item = _allAvailableItems[index];
                                return _buildItemTile(item);
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedItemSection() {
    if (_selectedItem == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الصنف المحدد',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  child: SizedBox(
                    width: 80,
                    height: 80,
                    child: _selectedItem!.motorFingerprintImageUrl.isNotEmpty
                        ? Image.network(
                            _selectedItem!.motorFingerprintImageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                                child: const Icon(Icons.broken_image),
                              );
                            },
                          )
                        : Container(
                            color: Theme.of(context).colorScheme.surfaceContainerHighest,
                            child: const Icon(Icons.broken_image),
                          ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${_selectedItem!.brand} ${_selectedItem!.model}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text('${_selectedItem!.type} - ${_selectedItem!.color}'),
                      Text('البصمة: ${_selectedItem!.motorFingerprintText}'),
                      Text(
                        'سعر الشراء: ${AppUtils.formatCurrency(_selectedItem!.purchasePrice)}',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'بيانات العميل',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // ID capture button
            SizedBox(
              width: double.infinity,
              height: 120,
              child: InkWell(
                onTap: _isProcessingId ? null : _captureCustomerId,
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _customerIdFrontImage != null 
                          ? Colors.green 
                          : Theme.of(context).colorScheme.outline,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                    color: _customerIdFrontImage != null 
                        ? Colors.green.withValues(alpha: 0.1)
                        : null,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (_isProcessingId)
                        const CircularProgressIndicator()
                      else
                        Icon(
                          _customerIdFrontImage != null 
                              ? Icons.check_circle 
                              : Icons.camera_alt,
                          size: 48,
                          color: _customerIdFrontImage != null 
                              ? Colors.green 
                              : Theme.of(context).colorScheme.primary,
                        ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Text(
                        _isProcessingId
                            ? 'جاري معالجة بطاقة الهوية...'
                            : _customerIdFrontImage != null
                                ? 'تم اختيار صور بطاقة الهوية بنجاح'
                                : Platform.isWindows || Platform.isLinux || Platform.isMacOS
                                    ? 'اضغط لاختيار صور بطاقة الهوية (الوجهين)'
                                    : 'اضغط لتصوير بطاقة الهوية (الوجهين)',
                        style: Theme.of(context).textTheme.bodyLarge,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Show image previews if images are selected
            if (_customerIdFrontImage != null && _customerIdBackImage != null) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        const Text(
                          'الوجه الأمامي',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 120,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _customerIdFrontImage is File
                                ? Image.file(
                                    _customerIdFrontImage as File,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                  )
                                : Image.memory(
                                    _customerIdFrontImage as Uint8List,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      children: [
                        const Text(
                          'الوجه الخلفي',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 120,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _customerIdBackImage is File
                                ? Image.file(
                                    _customerIdBackImage as File,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                  )
                                : Image.memory(
                                    _customerIdBackImage as Uint8List,
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: AppConstants.defaultPadding),

            // Customer form fields
            TextFormField(
              controller: _customerNameController,
              decoration: const InputDecoration(
                labelText: 'اسم العميل',
                hintText: 'سيتم استخراجه من بطاقة الهوية',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اسم العميل مطلوب';
                }
                return null;
              },
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            TextFormField(
              controller: _customerNationalIdController,
              decoration: const InputDecoration(
                labelText: 'الرقم القومي',
                hintText: 'سيتم استخراجه من بطاقة الهوية',
              ),
              keyboardType: TextInputType.number,
              maxLength: 14,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'الرقم القومي مطلوب';
                }
                if (value.trim().length != 14) {
                  return 'الرقم القومي يجب أن يكون 14 رقم';
                }
                if (!RegExp(r'^\d{14}$').hasMatch(value.trim())) {
                  return 'الرقم القومي يجب أن يحتوي على أرقام فقط';
                }
                return null;
              },
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            TextFormField(
              controller: _customerPhoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                hintText: '01xxxxxxxxx',
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'رقم الهاتف مطلوب';
                }
                if (!AppUtils.isValidPhone(value.trim())) {
                  return 'رقم الهاتف غير صحيح';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            TextFormField(
              controller: _customerAddressController,
              decoration: const InputDecoration(
                labelText: 'العنوان',
                hintText: 'سيتم استخراجه من بطاقة الهوية',
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'العنوان مطلوب';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التسعير',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            TextFormField(
              controller: _sellingPriceController,
              decoration: const InputDecoration(
                labelText: 'سعر البيع النهائي (ج.م)',
                hintText: 'أدخل سعر البيع للعميل',
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'سعر البيع مطلوب';
                }
                final price = double.tryParse(value.trim());
                if (price == null || price <= 0) {
                  return 'سعر غير صحيح';
                }
                if (_selectedItem != null && price <= _selectedItem!.purchasePrice) {
                  return 'سعر البيع يجب أن يكون أكبر من سعر الشراء';
                }
                return null;
              },
              onChanged: (value) {
                setState(() {}); // Rebuild to update profit calculation
              },
            ),
            
            if (_selectedItem != null && _sellingPriceController.text.isNotEmpty) ...[
              const SizedBox(height: AppConstants.defaultPadding),
              _buildProfitCalculation(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProfitCalculation() {
    final sellingPrice = double.tryParse(_sellingPriceController.text.trim()) ?? 0;
    final profitAmount = sellingPrice - _selectedItem!.purchasePrice;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final isAgent = authProvider.isAgent;
    final currentUser = authProvider.currentUser;

    // Use agent's custom profit share percentage
    final agentPercentage = isAgent && currentUser != null ? currentUser.profitSharePercentage : 0.0;
    final companyShare = isAgent ? profitAmount * (1.0 - agentPercentage) : profitAmount;
    final agentShare = isAgent ? profitAmount * agentPercentage : 0.0;
    final totalDebt = isAgent ? _selectedItem!.purchasePrice + companyShare : 0.0;

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Column(
        children: [
          _buildProfitRow('سعر الشراء:', AppUtils.formatCurrency(_selectedItem!.purchasePrice)),
          _buildProfitRow('سعر البيع:', AppUtils.formatCurrency(sellingPrice)),
          const Divider(),
          _buildProfitRow('إجمالي الربح:', AppUtils.formatCurrency(profitAmount), isTotal: true),
          if (isAgent) ...[
            const SizedBox(height: 8),
            Text(
              'تقسيم الأرباح (${((1.0 - agentPercentage) * 100).toStringAsFixed(0)}% - ${(agentPercentage * 100).toStringAsFixed(0)}%):',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.blue[700],
              ),
            ),
            const SizedBox(height: 4),
            _buildProfitRow('نصيب المؤسسة (${((1.0 - agentPercentage) * 100).toStringAsFixed(0)}%):', AppUtils.formatCurrency(companyShare)),
            _buildProfitRow('نصيبك (${(agentPercentage * 100).toStringAsFixed(0)}%):', AppUtils.formatCurrency(agentShare)),
            const Divider(),
            Text(
              'المديونية الإجمالية:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 4),
            _buildProfitRow('تكلفة البضاعة:', AppUtils.formatCurrency(_selectedItem!.purchasePrice)),
            _buildProfitRow('نصيب المؤسسة:', AppUtils.formatCurrency(companyShare)),
            _buildProfitRow('إجمالي المديونية:', AppUtils.formatCurrency(totalDebt), isTotal: true),
          ],
        ],
      ),
    );
  }

  Widget _buildProfitRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isTotal ? Theme.of(context).colorScheme.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payment,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'طريقة الدفع',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('كاش'),
                    subtitle: const Text('دفع نقدي فوري'),
                    value: 'كاش',
                    groupValue: _paymentMethod,
                    onChanged: (value) {
                      setState(() {
                        _paymentMethod = value!;
                      });
                    },
                    activeColor: Colors.green,
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('قسط'),
                    subtitle: const Text('دفع بالتقسيط'),
                    value: 'قسط',
                    groupValue: _paymentMethod,
                    onChanged: (value) {
                      setState(() {
                        _paymentMethod = value!;
                      });
                    },
                    activeColor: Colors.orange,
                  ),
                ),
              ],
            ),
            if (_paymentMethod == 'قسط') ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.orange, size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'سيتم إنشاء الفاتورة بحالة "قسط" ويمكن تحديث حالة الدفع لاحقاً',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCreateInvoiceButton() {
    return SizedBox(
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _createInvoice,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                'إنشاء الفاتورة (${_paymentMethod})',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }
}
