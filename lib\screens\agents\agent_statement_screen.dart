import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_account_model.dart';
import '../../models/user_model.dart';
import '../../services/data_service.dart';

class AgentStatementScreen extends StatefulWidget {
  final UserModel agent;

  const AgentStatementScreen({
    super.key,
    required this.agent,
  });

  @override
  State<AgentStatementScreen> createState() => _AgentStatementScreenState();
}

class _AgentStatementScreenState extends State<AgentStatementScreen> {
  bool _isLoading = true;
  AgentAccountModel? _agentAccount;
  List<AgentTransaction> _filteredTransactions = [];
  String _selectedFilter = 'all';
  DateTimeRange? _selectedDateRange;

  // إحصائيات مفصلة
  double _totalSales = 0.0;
  double _totalAgentProfits = 0.0;
  double _totalCompanyProfits = 0.0;
  double _averageCompanyProfitPercentage = 0.0;
  int _totalTransactions = 0;

  @override
  void initState() {
    super.initState();
    _loadAgentAccount();
  }

  Future<void> _loadAgentAccount() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final dataService = Provider.of<DataService>(context, listen: false);

      if (kDebugMode) {
        print('🔍 Loading agent account for: ${widget.agent.id} (${widget.agent.fullName})');
      }

      final account = await dataService.getAgentAccount(widget.agent.id);

      if (mounted) {
        setState(() {
          _agentAccount = account;
          _filteredTransactions = account?.transactions ?? [];
          _isLoading = false;
        });

        if (kDebugMode) {
          print('📊 Agent account loaded:');
          print('   - Balance: ${account?.currentBalance ?? 0} ج.م');
          print('   - Transactions: ${account?.transactions.length ?? 0}');
          print('   - Total Debt: ${account?.totalDebt ?? 0} ج.م');
          print('   - Total Paid: ${account?.totalPaid ?? 0} ج.م');
        }

        _calculateDetailedStatistics();
        _applyFilters();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading agent account: $e');
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        AppUtils.showSnackBar(context, 'خطأ في تحميل بيانات الحساب: $e', isError: true);
      }
    }
  }

  void _calculateDetailedStatistics() {
    if (_agentAccount == null) return;

    double totalSales = 0.0;
    double totalAgentProfits = 0.0;
    double totalCompanyProfits = 0.0;
    double totalCompanyProfitPercentages = 0.0;
    int salesTransactions = 0;

    for (final transaction in _agentAccount!.transactions) {
      if (transaction.type == 'debt') {
        totalSales += transaction.amount;
        totalAgentProfits += transaction.amount * 0.7; // Assume 70% for agent
        totalCompanyProfits += transaction.amount * 0.3; // Assume 30% for company
        salesTransactions++;
      }
    }

    if (mounted) {
      setState(() {
        _totalSales = totalSales;
        _totalAgentProfits = totalAgentProfits;
        _totalCompanyProfits = totalCompanyProfits;
        _averageCompanyProfitPercentage = salesTransactions > 0
            ? 0.3 // 30% for company
            : 0.0;
        _totalTransactions = _agentAccount!.transactions.length;
      });
    }
  }

  void _applyFilters() {
    if (_agentAccount == null) return;

    List<AgentTransaction> transactions = List.from(_agentAccount!.transactions);

    // Filter by type
    if (_selectedFilter != 'all') {
      transactions = transactions.where((t) => t.type == _selectedFilter).toList();
    }

    // Filter by date range
    if (_selectedDateRange != null) {
      transactions = transactions.where((t) {
        return t.timestamp.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
               t.timestamp.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    // Sort by date (newest first)
    transactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    if (mounted) {
      setState(() {
        _filteredTransactions = transactions;
      });
    }

    // إعادة حساب الإحصائيات بناءً على المعاملات المفلترة
    _calculateFilteredStatistics();
  }

  void _calculateFilteredStatistics() {
    double totalSales = 0.0;
    double totalAgentProfits = 0.0;
    double totalCompanyProfits = 0.0;
    double totalCompanyProfitPercentages = 0.0;
    int salesTransactions = 0;

    for (final transaction in _filteredTransactions) {
      if (transaction.type == 'sale') {
        totalSales += transaction.amount;
        totalAgentProfits += transaction.agentProfitShare ?? 0.0;
        totalCompanyProfits += transaction.companyProfitShare ?? 0.0;

        // حساب نسبة ربح المؤسسة لهذه المعاملة
        final totalProfit = (transaction.agentProfitShare ?? 0.0) + (transaction.companyProfitShare ?? 0.0);
        if (totalProfit > 0) {
          final companyPercentage = (transaction.companyProfitShare ?? 0.0) / totalProfit;
          totalCompanyProfitPercentages += companyPercentage;
          salesTransactions++;
        }
      }
    }

    setState(() {
      _totalSales = totalSales;
      _totalAgentProfits = totalAgentProfits;
      _totalCompanyProfits = totalCompanyProfits;
      _averageCompanyProfitPercentage = salesTransactions > 0
          ? totalCompanyProfitPercentages / salesTransactions
          : 0.0;
      _totalTransactions = _filteredTransactions.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('كشف حساب ${widget.agent.fullName}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: _agentAccount != null ? _exportToPDF : null,
            tooltip: 'تصدير PDF',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAgentAccount,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _agentAccount == null
              ? _buildNoDataWidget()
              : Column(
                  children: [
                    _buildAccountSummary(),
                    _buildFilters(),
                    Expanded(child: _buildTransactionsList()),
                  ],
                ),
    );
  }

  Widget _buildNoDataWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.account_balance_wallet, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'لا توجد بيانات حساب لهذا الوكيل',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSummary() {
    if (_agentAccount == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.account_balance_wallet, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  'ملخص حساب ${widget.agent.fullName}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // الصف الأول - المعلومات الأساسية
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المديونية',
                    _agentAccount!.totalDebt,
                    Colors.red,
                    Icons.trending_up,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المدفوع',
                    _agentAccount!.totalPaid,
                    Colors.green,
                    Icons.payment,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // الصف الثاني - إحصائيات المبيعات والأرباح
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المبيعات',
                    _totalSales,
                    Colors.blue,
                    Icons.point_of_sale,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildSummaryCard(
                    'عدد المعاملات',
                    _totalTransactions.toDouble(),
                    Colors.purple,
                    Icons.receipt_long,
                    isCount: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // الصف الثالث - تقسيم الأرباح
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'أرباح الوكيل',
                    _totalAgentProfits,
                    Colors.green,
                    Icons.person,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildSummaryCard(
                    'أرباح المؤسسة',
                    _totalCompanyProfits,
                    Colors.orange,
                    Icons.business,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // الصف الرابع - النسب والرصيد
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'متوسط نسبة المؤسسة',
                    _averageCompanyProfitPercentage * 100,
                    Colors.indigo,
                    Icons.percent,
                    isPercentage: true,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildSummaryCard(
                    'نسبة ربح الوكيل',
                    (widget.agent.profitSharePercentage ?? 0.0) * 100,
                    Colors.teal,
                    Icons.trending_up,
                    isPercentage: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // الرصيد الحالي - عرض كامل
            _buildSummaryCard(
              'الرصيد الحالي',
              _agentAccount!.currentBalance,
              _agentAccount!.currentBalance >= 0 ? Colors.green : Colors.red,
              _agentAccount!.currentBalance >= 0 ? Icons.account_balance : Icons.warning,
              isFullWidth: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    double amount,
    Color color,
    IconData icon, {
    bool isFullWidth = false,
    bool isPercentage = false,
    bool isCount = false,
  }) {
    String displayText;
    if (isPercentage) {
      displayText = '${amount.toStringAsFixed(1)}%';
    } else if (isCount) {
      displayText = amount.toInt().toString();
    } else {
      displayText = AppUtils.formatCurrency(amount.abs());
    }

    return Container(
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.4), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 22),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 13,
                    color: color.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.2,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            displayText,
            style: TextStyle(
              fontSize: isFullWidth ? 22 : 18,
              fontWeight: FontWeight.w900,
              color: color,
              letterSpacing: 0.5,
              shadows: [
                Shadow(
                  offset: const Offset(0.5, 0.5),
                  blurRadius: 1.0,
                  color: color.withValues(alpha: 0.3),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilters() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فلترة المعاملات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedFilter,
                    decoration: const InputDecoration(
                      labelText: 'نوع المعاملة',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('جميع المعاملات')),
                      DropdownMenuItem(value: 'sale', child: Text('مبيعات')),
                      DropdownMenuItem(value: 'payment', child: Text('دفعات')),
                      DropdownMenuItem(value: 'transfer', child: Text('تحويلات')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedFilter = value ?? 'all';
                      });
                      _applyFilters();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _selectDateRange,
                    icon: const Icon(Icons.date_range),
                    label: Text(
                      _selectedDateRange == null
                          ? 'اختيار فترة'
                          : '${AppUtils.formatDate(_selectedDateRange!.start)} - ${AppUtils.formatDate(_selectedDateRange!.end)}',
                    ),
                  ),
                ),
              ],
            ),
            if (_selectedDateRange != null || _selectedFilter != 'all') ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Text(
                    'عدد المعاملات: ${_filteredTransactions.length}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  TextButton.icon(
                    onPressed: _clearFilters,
                    icon: const Icon(Icons.clear),
                    label: const Text('مسح الفلاتر'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList() {
    if (_filteredTransactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد معاملات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _selectedFilter != 'all' || _selectedDateRange != null
                  ? 'جرب تغيير الفلاتر'
                  : 'لم يتم تسجيل أي معاملات بعد',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: _filteredTransactions.length,
      itemBuilder: (context, index) {
        final transaction = _filteredTransactions[index];
        return _buildTransactionCard(transaction, index);
      },
    );
  }

  Widget _buildTransactionCard(AgentTransaction transaction, int index) {
    final isDebit = transaction.type == 'sale' || transaction.type == 'transfer';
    final color = isDebit ? Colors.red : Colors.green;
    final icon = _getTransactionIcon(transaction.type);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(
          transaction.description,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppUtils.formatDateTime(transaction.timestamp),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            if (transaction.metadata != null && transaction.metadata!.isNotEmpty)
              Text(
                _getTransactionDetails(transaction),
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[500],
                ),
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${isDebit ? '-' : '+'}${AppUtils.formatCurrency(transaction.amount)}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              _getTransactionTypeText(transaction.type),
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        onTap: () => _showTransactionDetails(transaction),
      ),
    );
  }

  IconData _getTransactionIcon(String type) {
    switch (type) {
      case 'sale':
        return Icons.shopping_cart;
      case 'payment':
        return Icons.payment;
      case 'transfer':
        return Icons.swap_horiz;
      default:
        return Icons.receipt;
    }
  }

  String _getTransactionTypeText(String type) {
    switch (type) {
      case 'sale':
        return 'مبيعة';
      case 'payment':
        return 'دفعة';
      case 'transfer':
        return 'تحويل';
      default:
        return 'معاملة';
    }
  }

  String _getTransactionDetails(AgentTransaction transaction) {
    final metadata = transaction.metadata ?? {};
    List<String> details = [];

    if (metadata['invoiceNumber'] != null) {
      details.add('فاتورة: ${metadata['invoiceNumber']}');
    }
    if (metadata['paymentMethod'] != null) {
      details.add('طريقة الدفع: ${metadata['paymentMethod']}');
    }
    if (metadata['notes'] != null && metadata['notes'].toString().isNotEmpty) {
      details.add('ملاحظات: ${metadata['notes']}');
    }

    return details.join(' • ');
  }

  void _showTransactionDetails(AgentTransaction transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل ${_getTransactionTypeText(transaction.type)}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('المبلغ', AppUtils.formatCurrency(transaction.amount)),
              _buildDetailRow('التاريخ', AppUtils.formatDateTime(transaction.timestamp)),
              _buildDetailRow('الوصف', transaction.description),
              _buildDetailRow('النوع', _getTransactionTypeText(transaction.type)),
              if (transaction.metadata != null) ...[
                const Divider(),
                const Text(
                  'تفاصيل إضافية:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...transaction.metadata!.entries.map((entry) =>
                    _buildDetailRow(entry.key, entry.value.toString())),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
      locale: const Locale('ar'),
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
      _applyFilters();
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedFilter = 'all';
      _selectedDateRange = null;
    });
    _applyFilters();
  }

  Future<void> _exportToPDF() async {
    try {
      if (_agentAccount == null) return;

      // عرض خيارات التصدير للويندوز
      final result = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تصدير كشف الحساب'),
          content: const Text('اختر تنسيق التصدير المناسب:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, 'html'),
              child: const Text('HTML (قابل للطباعة)'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, 'csv'),
              child: const Text('CSV (Excel)'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );

      if (result != null) {
        await _exportToFormat(result);
      }
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في التصدير: $e', isError: true);
    }
  }

  Future<void> _exportToFormat(String format) async {
    try {
      AppUtils.showSnackBar(context, 'جاري إنشاء الملف...');

      final fileName = 'كشف_حساب_${widget.agent.fullName}_${DateTime.now().millisecondsSinceEpoch}';

      if (format == 'html') {
        await _exportToHTML(fileName);
      } else if (format == 'csv') {
        await _exportToCSV(fileName);
      }
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في إنشاء الملف: $e', isError: true);
    }
  }

  Future<void> _exportToHTML(String fileName) async {
    try {
      final html = _generateHTMLReport();

      // حفظ الملف في مجلد التحميلات
      final downloadsPath = await _getDownloadsPath();
      if (downloadsPath != null) {
        final file = File('$downloadsPath/$fileName.html');
        await file.writeAsString(html, encoding: utf8);

        AppUtils.showSnackBar(context, 'تم حفظ الملف في: ${file.path}');

        // فتح الملف في المتصفح
        await _openFile(file.path);
      }
    } catch (e) {
      throw 'خطأ في إنشاء ملف HTML: $e';
    }
  }

  Future<void> _exportToCSV(String fileName) async {
    try {
      final csv = _generateCSVReport();

      // حفظ الملف في مجلد التحميلات
      final downloadsPath = await _getDownloadsPath();
      if (downloadsPath != null) {
        final file = File('$downloadsPath/$fileName.csv');
        await file.writeAsString(csv, encoding: utf8);

        AppUtils.showSnackBar(context, 'تم حفظ الملف في: ${file.path}');

        // فتح الملف في Excel
        await _openFile(file.path);
      }
    } catch (e) {
      throw 'خطأ في إنشاء ملف CSV: $e';
    }
  }

  String _generateHTMLReport() {
    final agent = widget.agent;
    final account = _agentAccount!;
    final now = DateTime.now();

    return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كشف حساب ${agent.fullName}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .header { text-align: center; margin-bottom: 30px; }
        .company-name { font-size: 24px; font-weight: bold; color: #1565C0; }
        .report-title { font-size: 20px; margin: 10px 0; }
        .agent-info { background: #f5f5f5; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .summary-card { background: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 8px; text-align: center; }
        .summary-title { font-size: 14px; color: #666; margin-bottom: 5px; }
        .summary-value { font-size: 18px; font-weight: bold; }
        .positive { color: #4CAF50; }
        .negative { color: #f44336; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background: #1565C0; color: white; }
        .sale { background: #e8f5e8; }
        .payment { background: #ffe8e8; }
        .transfer { background: #e8f0ff; }
        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">مؤسسة آل فرحان للنقل</div>
        <div class="report-title">كشف حساب تفصيلي</div>
        <div>التاريخ: ${_formatDate(now)}</div>
    </div>

    <div class="agent-info">
        <h3>بيانات الوكيل</h3>
        <p><strong>الاسم:</strong> ${agent.fullName}</p>
        <p><strong>الهاتف:</strong> ${agent.phone ?? 'غير محدد'}</p>
        <p><strong>نسبة الربح:</strong> ${((agent.profitSharePercentage ?? 0.0) * 100).toStringAsFixed(1)}%</p>
    </div>

    <div class="summary">
        <div class="summary-card">
            <div class="summary-title">إجمالي المديونية</div>
            <div class="summary-value negative">${AppUtils.formatCurrency(account.totalDebt)}</div>
        </div>
        <div class="summary-card">
            <div class="summary-title">إجمالي المدفوع</div>
            <div class="summary-value positive">${AppUtils.formatCurrency(account.totalPaid)}</div>
        </div>
        <div class="summary-card">
            <div class="summary-title">الرصيد الحالي</div>
            <div class="summary-value ${account.currentBalance >= 0 ? 'positive' : 'negative'}">${AppUtils.formatCurrency(account.currentBalance)}</div>
        </div>
        <div class="summary-card">
            <div class="summary-title">إجمالي المبيعات</div>
            <div class="summary-value">${AppUtils.formatCurrency(_totalSales)}</div>
        </div>
        <div class="summary-card">
            <div class="summary-title">أرباح الوكيل</div>
            <div class="summary-value positive">${AppUtils.formatCurrency(_totalAgentProfits)}</div>
        </div>
        <div class="summary-card">
            <div class="summary-title">أرباح المؤسسة</div>
            <div class="summary-value negative">${AppUtils.formatCurrency(_totalCompanyProfits)}</div>
        </div>
    </div>

    <h3>تفاصيل المعاملات</h3>
    <table>
        <thead>
            <tr>
                <th>التاريخ</th>
                <th>النوع</th>
                <th>الوصف</th>
                <th>المبلغ</th>
                <th>نصيب الوكيل</th>
                <th>نصيب المؤسسة</th>
                <th>تغيير الرصيد</th>
                <th>المرجع</th>
            </tr>
        </thead>
        <tbody>
            ${_filteredTransactions.map((transaction) => '''
            <tr class="${transaction.type}">
                <td>${_formatDate(transaction.timestamp)}</td>
                <td>${_getTransactionTypeArabic(transaction.type)}</td>
                <td>${transaction.description}</td>
                <td>${AppUtils.formatCurrency(transaction.amount)}</td>
                <td>${AppUtils.formatCurrency(transaction.agentProfitShare ?? 0.0)}</td>
                <td>${AppUtils.formatCurrency(transaction.companyProfitShare ?? 0.0)}</td>
                <td class="${transaction.balanceChange >= 0 ? 'positive' : 'negative'}">${AppUtils.formatCurrency(transaction.balanceChange)}</td>
                <td>${transaction.referenceNumber}</td>
            </tr>
            ''').join('')}
        </tbody>
    </table>

    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة تطبيق آل فرحان للنقل</p>
        <p>التاريخ والوقت: ${now.toString()}</p>
    </div>
</body>
</html>
    ''';
  }

  String _generateCSVReport() {
    final agent = widget.agent;
    final account = _agentAccount!;

    final buffer = StringBuffer();

    // Header
    buffer.writeln('كشف حساب ${agent.fullName}');
    buffer.writeln('التاريخ,${_formatDate(DateTime.now())}');
    buffer.writeln('');

    // Summary
    buffer.writeln('ملخص الحساب');
    buffer.writeln('البيان,المبلغ');
    buffer.writeln('إجمالي المديونية,${account.totalDebt}');
    buffer.writeln('إجمالي المدفوع,${account.totalPaid}');
    buffer.writeln('الرصيد الحالي,${account.currentBalance}');
    buffer.writeln('إجمالي المبيعات,${_totalSales}');
    buffer.writeln('أرباح الوكيل,${_totalAgentProfits}');
    buffer.writeln('أرباح المؤسسة,${_totalCompanyProfits}');
    buffer.writeln('');

    // Transactions
    buffer.writeln('تفاصيل المعاملات');
    buffer.writeln('التاريخ,النوع,الوصف,المبلغ,نصيب الوكيل,نصيب المؤسسة,تغيير الرصيد,المرجع');

    for (final transaction in _filteredTransactions) {
      buffer.writeln([
        _formatDate(transaction.timestamp),
        _getTransactionTypeArabic(transaction.type),
        transaction.description,
        transaction.amount,
        transaction.agentProfitShare ?? 0.0,
        transaction.companyProfitShare ?? 0.0,
        transaction.balanceChange,
        transaction.referenceNumber,
      ].join(','));
    }

    return buffer.toString();
  }

  String _getTransactionTypeArabic(String type) {
    switch (type) {
      case 'sale':
        return 'بيع';
      case 'payment':
        return 'دفعة';
      case 'transfer':
        return 'تحويل';
      default:
        return type;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<String?> _getDownloadsPath() async {
    try {
      if (Platform.isWindows) {
        final userProfile = Platform.environment['USERPROFILE'];
        if (userProfile != null) {
          return '$userProfile\\Downloads';
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> _openFile(String filePath) async {
    try {
      if (Platform.isWindows) {
        await Process.run('start', ['', filePath], runInShell: true);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error opening file: $e');
      }
    }
  }
}
