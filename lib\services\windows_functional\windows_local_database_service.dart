import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

/// خدمة قاعدة البيانات المحلية للويندوز - متوافقة وظيفياً مع Android
class WindowsLocalDatabaseService {
  static WindowsLocalDatabaseService? _instance;
  static WindowsLocalDatabaseService get instance => _instance ??= WindowsLocalDatabaseService._();
  
  WindowsLocalDatabaseService._();

  Database? _database;
  bool _isInitialized = false;

  /// الحصول على قاعدة البيانات
  Database get database {
    if (_database == null) {
      throw Exception('Database not initialized. Call initialize() first.');
    }
    return _database!;
  }

  bool get isInitialized => _isInitialized;

  /// تهيئة قاعدة البيانات المحلية
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      final databasePath = await getDatabasesPath();
      final path = join(databasePath, 'el_farhan_windows.db');

      _database = await openDatabase(
        path,
        version: 1,
        onCreate: _createTables,
        onUpgrade: _upgradeDatabase,
      );

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Windows Local Database initialized: $path');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing Windows Local Database: $e');
      }
      rethrow;
    }
  }

  /// إنشاء الجداول - نفس هيكل Android
  Future<void> _createTables(Database db, int version) async {
    try {
      // Users table - same as Android
      await db.execute('''
        CREATE TABLE users (
          id TEXT PRIMARY KEY,
          username TEXT NOT NULL,
          email TEXT NOT NULL,
          fullName TEXT NOT NULL,
          phone TEXT NOT NULL,
          role TEXT NOT NULL,
          warehouseId TEXT,
          passwordHash TEXT,
          isActive INTEGER NOT NULL DEFAULT 1,
          profitSharePercentage REAL NOT NULL DEFAULT 0.5,
          frontIdImageUrl TEXT,
          backIdImageUrl TEXT,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Items table - same as Android
      await db.execute('''
        CREATE TABLE items (
          id TEXT PRIMARY KEY,
          type TEXT NOT NULL,
          model TEXT NOT NULL,
          color TEXT NOT NULL,
          brand TEXT NOT NULL,
          countryOfOrigin TEXT NOT NULL,
          yearOfManufacture INTEGER NOT NULL,
          purchasePrice REAL NOT NULL,
          suggestedSellingPrice REAL NOT NULL,
          motorFingerprintImageUrl TEXT NOT NULL,
          motorFingerprintText TEXT NOT NULL,
          chassisImageUrl TEXT NOT NULL,
          chassisNumber TEXT NOT NULL,
          currentWarehouseId TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'متاح',
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Invoices table - same as Android
      await db.execute('''
        CREATE TABLE invoices (
          id TEXT PRIMARY KEY,
          invoiceNumber TEXT NOT NULL,
          type TEXT NOT NULL,
          customerId TEXT,
          agentId TEXT,
          warehouseId TEXT NOT NULL,
          itemId TEXT NOT NULL,
          itemCost REAL NOT NULL,
          sellingPrice REAL NOT NULL,
          profitAmount REAL NOT NULL,
          companyProfitShare REAL NOT NULL,
          agentProfitShare REAL NOT NULL DEFAULT 0,
          status TEXT NOT NULL DEFAULT 'pending',
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          customerData TEXT,
          customerIdImages TEXT,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Agent accounts table - same as Android
      await db.execute('''
        CREATE TABLE agent_accounts (
          id TEXT PRIMARY KEY,
          agentId TEXT NOT NULL,
          agentName TEXT NOT NULL,
          agentPhone TEXT NOT NULL,
          totalDebt REAL NOT NULL,
          totalPaid REAL NOT NULL,
          currentBalance REAL NOT NULL,
          transactions TEXT NOT NULL,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Warehouses table - same as Android
      await db.execute('''
        CREATE TABLE warehouses (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          type TEXT NOT NULL,
          ownerId TEXT,
          address TEXT NOT NULL,
          phone TEXT,
          email TEXT,
          isActive INTEGER NOT NULL DEFAULT 1,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Notifications table - same as Android
      await db.execute('''
        CREATE TABLE notifications (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          type TEXT NOT NULL,
          targetUserId TEXT,
          targetRole TEXT,
          relatedId TEXT,
          data TEXT,
          isRead INTEGER NOT NULL DEFAULT 0,
          createdAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          readAt TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Document tracking table - same as Android
      await db.execute('''
        CREATE TABLE document_tracking (
          id TEXT PRIMARY KEY,
          itemId TEXT NOT NULL,
          invoiceId TEXT NOT NULL,
          currentStatus TEXT NOT NULL,
          statusHistory TEXT NOT NULL,
          createdAt TEXT NOT NULL,
          updatedAt TEXT NOT NULL,
          createdBy TEXT NOT NULL,
          compositeImagePath TEXT,
          additionalData TEXT,
          syncStatus INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // Sync queue table for offline operations
      await db.execute('''
        CREATE TABLE sync_queue (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          tableName TEXT NOT NULL,
          recordId TEXT NOT NULL,
          operation TEXT NOT NULL,
          data TEXT NOT NULL,
          createdAt TEXT NOT NULL,
          attempts INTEGER NOT NULL DEFAULT 0
        )
      ''');

      if (kDebugMode) {
        print('✅ All database tables created successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating database tables: $e');
      }
      rethrow;
    }
  }

  /// ترقية قاعدة البيانات
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    if (kDebugMode) {
      print('🔄 Upgrading database from version $oldVersion to $newVersion');
    }
    // Add migration logic here when needed
  }

  /// إدراج سجل جديد
  Future<int> insert(String table, Map<String, dynamic> data) async {
    try {
      data['syncStatus'] = 0; // Mark as needing sync
      final result = await database.insert(
        table,
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      if (kDebugMode) {
        print('✅ Inserted record in $table: ${data['id']}');
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error inserting record in $table: $e');
      }
      rethrow;
    }
  }

  /// تحديث سجل موجود
  Future<int> update(String table, Map<String, dynamic> data, {String? where, List<dynamic>? whereArgs}) async {
    try {
      data['syncStatus'] = 0; // Mark as needing sync
      final result = await database.update(
        table,
        data,
        where: where,
        whereArgs: whereArgs,
      );
      
      if (kDebugMode) {
        print('✅ Updated $result record(s) in $table');
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating record in $table: $e');
      }
      rethrow;
    }
  }

  /// حذف سجل
  Future<int> delete(String table, {String? where, List<dynamic>? whereArgs}) async {
    try {
      final result = await database.delete(
        table,
        where: where,
        whereArgs: whereArgs,
      );
      
      if (kDebugMode) {
        print('✅ Deleted $result record(s) from $table');
      }
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting record from $table: $e');
      }
      rethrow;
    }
  }

  /// استعلام السجلات
  Future<List<Map<String, dynamic>>> query(
    String table, {
    bool? distinct,
    List<String>? columns,
    String? where,
    List<dynamic>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    try {
      final result = await database.query(
        table,
        distinct: distinct,
        columns: columns,
        where: where,
        whereArgs: whereArgs,
        groupBy: groupBy,
        having: having,
        orderBy: orderBy,
        limit: limit,
        offset: offset,
      );
      
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error querying $table: $e');
      }
      rethrow;
    }
  }

  /// تنفيذ استعلام SQL مخصص
  Future<List<Map<String, dynamic>>> rawQuery(String sql, [List<dynamic>? arguments]) async {
    try {
      final result = await database.rawQuery(sql, arguments);
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error executing raw query: $e');
      }
      rethrow;
    }
  }

  /// إضافة عملية إلى قائمة انتظار المزامنة
  Future<void> addToSyncQueue(String tableName, String recordId, String operation, Map<String, dynamic> data) async {
    try {
      await insert('sync_queue', {
        'tableName': tableName,
        'recordId': recordId,
        'operation': operation,
        'data': data.toString(),
        'createdAt': DateTime.now().toIso8601String(),
        'attempts': 0,
      });
      
      if (kDebugMode) {
        print('✅ Added to sync queue: $tableName/$recordId ($operation)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error adding to sync queue: $e');
      }
    }
  }

  /// الحصول على العمليات المعلقة للمزامنة
  Future<List<Map<String, dynamic>>> getPendingSyncOperations() async {
    try {
      return await query('sync_queue', orderBy: 'createdAt ASC');
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting pending sync operations: $e');
      }
      return [];
    }
  }

  /// إزالة عملية من قائمة انتظار المزامنة
  Future<void> removeSyncOperation(int id) async {
    try {
      await delete('sync_queue', where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error removing sync operation: $e');
      }
    }
  }

  /// تنظيف قاعدة البيانات
  Future<void> clearAllData() async {
    try {
      final tables = ['users', 'items', 'invoices', 'agent_accounts', 'warehouses', 'notifications', 'document_tracking', 'sync_queue'];
      
      for (final table in tables) {
        await delete(table);
      }
      
      if (kDebugMode) {
        print('✅ All data cleared from local database');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing database: $e');
      }
      rethrow;
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    try {
      if (_database != null) {
        await _database!.close();
        _database = null;
        _isInitialized = false;
        
        if (kDebugMode) {
          print('✅ Windows Local Database closed');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error closing database: $e');
      }
    }
  }

  /// تنظيف الموارد
  void dispose() {
    close();
    if (kDebugMode) {
      print('🧹 Windows Local Database Service disposed');
    }
  }
}
