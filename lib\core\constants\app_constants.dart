import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'آل فرحان للنقل الخفيف';
  static const String appSubtitle = 'نظام إدارة المخزون والمبيعات';
  static const String companyName = 'مؤسسة آل فرحان للنقل الخفيف';
  
  // Colors
  static const int primaryColorValue = 0xFF1976D2;
  static const int secondaryColorValue = 0xFF2196F3;
  static const int accentColorValue = 0xFF4CAF50;
  static const int errorColorValue = 0xFFE53935;
  static const int warningColorValue = 0xFFFF9800;

  // Color objects
  static const primaryColor = Color(primaryColorValue);
  static const secondaryColor = Color(secondaryColorValue);
  static const accentColor = Color(accentColorValue);
  static const errorColor = Color(errorColorValue);
  static const warningColor = Color(warningColorValue);
  
  // Dimensions
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Animation Durations
  static const int splashDuration = 3000; // milliseconds
  static const int animationDuration = 300; // milliseconds
  
  // Database Collections
  static const String usersCollection = 'users';
  static const String itemsCollection = 'items';
  static const String warehousesCollection = 'warehouses';
  static const String invoicesCollection = 'invoices';
  static const String paymentsCollection = 'payments';
  static const String documentsCollection = 'documents';
  static const String documentTrackingCollection = 'document_tracking';
  static const String agentAccountsCollection = 'agent_accounts';
  static const String agentTransferInvoicesCollection = 'agent_transfer_invoices';
  static const String notificationsCollection = 'notifications';
  
  // User Roles
  static const String superAdminRole = 'super_admin';
  static const String adminRole = 'admin';
  static const String agentRole = 'agent';
  static const String showroomRole = 'showroom';
  
  // Warehouse Types
  static const String mainWarehouse = 'main';
  static const String branchWarehouse = 'branch';
  static const String showroomWarehouse = 'showroom';
  static const String agentWarehouse = 'agent';
  
  // Invoice Types
  static const String customerInvoice = 'customer';
  static const String agentInvoice = 'agent';
  
  // Document Status
  static const String documentSentToManager = 'sent_to_manager';
  static const String documentSentToManufacturer = 'sent_to_manufacturer';
  static const String documentReceivedFromManufacturer = 'received_from_manufacturer';
  static const String documentDeliveredToAgent = 'delivered_to_agent';
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxNameLength = 50;
  static const int maxDescriptionLength = 500;
  
  // File Upload
  static const int maxImageSizeBytes = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageExtensions = ['jpg', 'jpeg', 'png'];
}
