import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../auth/login_screen.dart';

class SimpleDesktopApp extends StatefulWidget {
  const SimpleDesktopApp({super.key});

  @override
  State<SimpleDesktopApp> createState() => _SimpleDesktopAppState();
}

class _SimpleDesktopAppState extends State<SimpleDesktopApp> {
  int _selectedIndex = 0;
  bool _isCollapsed = false;

  final List<DesktopMenuItem> _menuItems = [
    DesktopMenuItem(
      icon: Icons.dashboard,
      title: 'لوحة التحكم',
      screen: const DashboardScreen(),
    ),
    DesktopMenuItem(
      icon: Icons.inventory,
      title: 'المخزون',
      screen: const InventoryScreen(),
    ),
    DesktopMenuItem(
      icon: Icons.point_of_sale,
      title: 'المبيعات',
      screen: const SalesScreen(),
    ),
    DesktopMenuItem(
      icon: Icons.people,
      title: 'الوكلاء',
      screen: const AgentsScreen(),
      requiredRoles: ['admin', 'super_admin'],
    ),
    DesktopMenuItem(
      icon: Icons.bar_chart,
      title: 'التقارير',
      screen: const ReportsScreen(),
    ),
    DesktopMenuItem(
      icon: Icons.settings,
      title: 'الإعدادات',
      screen: const SettingsScreen(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final currentUser = authProvider.currentUser;
        
        if (currentUser == null) {
          return const LoginScreen();
        }

        // Filter menu items based on user role
        final filteredMenuItems = _menuItems.where((item) {
          if (item.requiredRoles == null) return true;
          return item.requiredRoles!.contains(currentUser.role);
        }).toList();

        return Scaffold(
          body: Row(
            children: [
              // Sidebar Navigation
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: _isCollapsed ? 70 : 280,
                child: _buildSidebar(currentUser, filteredMenuItems),
              ),
              
              // Main Content Area
              Expanded(
                child: Column(
                  children: [
                    // Top App Bar
                    _buildTopAppBar(currentUser),
                    
                    // Content
                    Expanded(
                      child: Container(
                        color: Colors.grey[50],
                        child: filteredMenuItems[_selectedIndex].screen,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSidebar(UserModel currentUser, List<DesktopMenuItem> menuItems) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.blue.shade800,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Logo and Company Name
          Container(
            height: 80,
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.local_shipping,
                    color: Colors.blue.shade800,
                    size: 24,
                  ),
                ),
                if (!_isCollapsed) ...[
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'الفرحان للنقل الخفيف',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          const Divider(color: Colors.white24, height: 1),
          
          // Menu Items
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: menuItems.length,
              itemBuilder: (context, index) {
                final item = menuItems[index];
                final isSelected = _selectedIndex == index;
                
                return _buildMenuItem(item, index, isSelected);
              },
            ),
          ),
          
          // Collapse Button
          Container(
            padding: const EdgeInsets.all(8),
            child: IconButton(
              onPressed: () {
                setState(() {
                  _isCollapsed = !_isCollapsed;
                });
              },
              icon: Icon(
                _isCollapsed ? Icons.menu : Icons.menu_open,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(DesktopMenuItem item, int index, bool isSelected) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Material(
        color: isSelected ? Colors.white.withOpacity(0.2) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            setState(() {
              _selectedIndex = index;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Icon(
                  item.icon,
                  color: Colors.white,
                  size: 20,
                ),
                if (!_isCollapsed) ...[
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      item.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopAppBar(UserModel currentUser) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const SizedBox(width: 20),
          
          // Page Title
          Text(
            _menuItems[_selectedIndex].title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade800,
            ),
          ),
          
          const Spacer(),
          
          // User Info and Actions
          Row(
            children: [
              // User Profile
              PopupMenuButton<String>(
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.blue.shade800,
                      child: Text(
                        currentUser.fullName.isNotEmpty 
                          ? currentUser.fullName[0].toUpperCase()
                          : 'U',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          currentUser.fullName,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          _getRoleDisplayName(currentUser.role),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 4),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, color: Colors.red),
                        SizedBox(width: 8),
                        Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
                onSelected: (value) {
                  if (value == 'logout') {
                    _handleLogout();
                  }
                },
              ),
              
              const SizedBox(width: 20),
            ],
          ),
        ],
      ),
    );
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<AuthProvider>(context, listen: false).signOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'super_admin':
        return 'المدير الأعلى';
      case 'admin':
        return 'مدير';
      case 'agent':
        return 'وكيل';
      case 'showroom':
        return 'صالة عرض';
      default:
        return 'مستخدم';
    }
  }
}

class DesktopMenuItem {
  final IconData icon;
  final String title;
  final Widget screen;
  final List<String>? requiredRoles;

  DesktopMenuItem({
    required this.icon,
    required this.title,
    required this.screen,
    this.requiredRoles,
  });
}

// Simple Desktop Screens
class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مرحباً بك في نظام الفرحان للنقل الخفيف',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Text(
            'نسخة سطح المكتب - نظام إدارة شامل للمخزون والمبيعات والوكلاء',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 32),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.desktop_windows,
                    size: 64,
                    color: Colors.blue,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'نسخة سطح المكتب جاهزة للاستخدام!',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'استخدم القائمة الجانبية للتنقل بين الأقسام المختلفة',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class InventoryScreen extends StatelessWidget {
  const InventoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildPlaceholderScreen(
      'المخزون',
      'إدارة المخزون والأصناف',
      Icons.inventory,
      Colors.green,
    );
  }
}

class SalesScreen extends StatelessWidget {
  const SalesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildPlaceholderScreen(
      'المبيعات',
      'إدارة المبيعات والفواتير',
      Icons.point_of_sale,
      Colors.orange,
    );
  }
}

class AgentsScreen extends StatelessWidget {
  const AgentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildPlaceholderScreen(
      'الوكلاء',
      'إدارة الوكلاء وكشوف الحسابات',
      Icons.people,
      Colors.purple,
    );
  }
}

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildPlaceholderScreen(
      'التقارير',
      'التقارير والإحصائيات',
      Icons.bar_chart,
      Colors.blue,
    );
  }
}

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildPlaceholderScreen(
      'الإعدادات',
      'إعدادات النظام والتطبيق',
      Icons.settings,
      Colors.grey,
    );
  }
}

Widget _buildPlaceholderScreen(String title, String subtitle, IconData icon, Color color) {
  return Padding(
    padding: const EdgeInsets.all(24),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 32),
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    icon,
                    size: 40,
                    color: color,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'قسم $title',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'هذا القسم متاح في النسخة الكاملة للتطبيق',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Navigate to full screen
                  },
                  icon: const Icon(Icons.open_in_new),
                  label: const Text('فتح النسخة الكاملة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: color,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    ),
  );
}
