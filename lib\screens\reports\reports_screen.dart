import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../services/data_service.dart';

import '../../services/report_service.dart';
import '../../providers/auth_provider.dart';

import 'detailed_inventory_report.dart';
import 'detailed_sales_report.dart';
import 'warehouse_movement_reports_screen.dart';
import 'advanced_warehouse_reports_screen.dart';
import 'detailed_agent_statement_screen.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  final DataService _dataService = DataService.instance;
  
  Map<String, dynamic> _inventoryStats = {};
  Map<String, dynamic> _salesStats = {};
  Map<String, dynamic> _profitStats = {};
  Map<String, dynamic> _agentStats = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadReports();
  }

  Future<void> _loadReports() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load various statistics
      await Future.wait([
        _loadInventoryStats(),
        _loadSalesStats(),
        _loadProfitStats(),
        _loadAgentStats(),
      ]);
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل التقارير: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadInventoryStats() async {
    try {
      // Get real data from DataService
      final allItems = await _dataService.getItems();
      // Use Arabic status values that match the database
      final availableItems = allItems.where((item) =>
        item.status.trim() == 'متاح' || item.status.trim() == 'available').toList();
      final soldItems = allItems.where((item) =>
        item.status.trim() == 'مباع' || item.status.trim() == 'sold').toList();
      final transferredItems = allItems.where((item) =>
        item.status.trim() == 'محول' || item.status.trim() == 'transferred').toList();

      final availableValue = availableItems.fold<double>(0, (sum, item) => sum + item.purchasePrice);

      // حساب إحصائيات إضافية
      final soldValue = soldItems.fold<double>(0, (sum, item) => sum + item.suggestedSellingPrice);
      final transferredValue = transferredItems.fold<double>(0, (sum, item) => sum + item.purchasePrice);
      final totalValue = allItems.fold<double>(0, (sum, item) => sum + item.suggestedSellingPrice);
      final averageItemValue = allItems.isNotEmpty ? totalValue / allItems.length : 0.0;
      final inventoryTurnover = allItems.isNotEmpty ? (soldItems.length / allItems.length) * 100 : 0.0;

      _inventoryStats = {
        'totalItems': availableItems.length, // Show only available items count
        'availableItems': availableItems.length,
        'soldItems': soldItems.length,
        'transferredItems': transferredItems.length,
        'totalValue': availableValue, // Show only available items value
        'availableValue': availableValue,
        'soldValue': soldValue,
        'transferredValue': transferredValue,
        'actualTotalItems': allItems.length, // Keep track of actual total for reference
        'actualTotalValue': totalValue,
        'averageItemValue': averageItemValue,
        'inventoryTurnover': inventoryTurnover,
      };

      if (kDebugMode) {
        print('📊 Inventory stats calculated:');
        print('   Total items: ${allItems.length}');
        print('   Available items (متاح): ${availableItems.length}');
        print('   Sold items (مباع): ${soldItems.length}');
        print('   Transferred items (محول): ${transferredItems.length}');
        print('   Available value: ${availableValue.toStringAsFixed(0)} ج.م');
        print('   Sold value: ${soldValue.toStringAsFixed(0)} ج.م');

        // Debug: Show first few item statuses
        if (allItems.isNotEmpty) {
          print('   Sample statuses:');
          for (int i = 0; i < allItems.length && i < 5; i++) {
            print('     - ${allItems[i].brand} ${allItems[i].model}: "${allItems[i].status}"');
          }
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل إحصائيات المخزون: $e', isError: true);
      }
      // Fallback to empty stats
      _inventoryStats = {
        'totalItems': 0,
        'availableItems': 0,
        'soldItems': 0,
        'transferredItems': 0,
        'totalValue': 0.0,
        'availableValue': 0.0,
      };
    }
  }

  Future<void> _loadSalesStats() async {
    try {
      // Get real sales data
      final allItems = await _dataService.getItems();
      final soldItems = allItems.where((item) =>
        item.status.trim() == 'مباع' || item.status.trim() == 'sold').toList();

      final totalSales = soldItems.fold<double>(0, (sum, item) => sum + item.suggestedSellingPrice);
      final salesCount = soldItems.length;
      final averageSaleValue = salesCount > 0 ? totalSales / salesCount : 0.0;

      // Calculate this month's sales
      final now = DateTime.now();
      final thisMonthStart = DateTime(now.year, now.month, 1);
      final thisMonthSoldItems = soldItems.where((item) =>
        item.updatedAt.isAfter(thisMonthStart)).toList();
      final thisMonthSales = thisMonthSoldItems.fold<double>(0, (sum, item) => sum + item.suggestedSellingPrice);

      // Calculate last month's sales
      final lastMonthStart = DateTime(now.year, now.month - 1, 1);
      final lastMonthEnd = DateTime(now.year, now.month, 0);
      final lastMonthSoldItems = soldItems.where((item) =>
        item.updatedAt.isAfter(lastMonthStart) && item.updatedAt.isBefore(lastMonthEnd)).toList();
      final lastMonthSales = lastMonthSoldItems.fold<double>(0, (sum, item) => sum + item.suggestedSellingPrice);

      _salesStats = {
        'totalSales': totalSales,
        'salesCount': salesCount,
        'averageSaleValue': averageSaleValue,
        'thisMonthSales': thisMonthSales,
        'lastMonthSales': lastMonthSales,
      };
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل إحصائيات المبيعات: $e', isError: true);
      }
      // Fallback to empty stats
      _salesStats = {
        'totalSales': 0.0,
        'salesCount': 0,
        'averageSaleValue': 0.0,
        'thisMonthSales': 0.0,
        'lastMonthSales': 0.0,
      };
    }
  }

  Future<void> _loadProfitStats() async {
    try {
      // Check if current user can view profit data
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser == null || currentUser.isAgent) {
        // Agents cannot see profit data
        _profitStats = {};
        return;
      }

      // Get all invoices to calculate real profit statistics
      final allInvoices = await _dataService.getInvoices();

      double totalProfit = 0.0;
      double companyProfit = 0.0;
      double agentProfit = 0.0;
      double totalSales = 0.0;
      double thisMonthProfit = 0.0;

      final now = DateTime.now();
      final thisMonth = DateTime(now.year, now.month, 1);

      for (final invoice in allInvoices) {
        totalProfit += invoice.profitAmount;
        companyProfit += invoice.companyProfitShare;
        agentProfit += invoice.agentProfitShare;
        totalSales += invoice.sellingPrice;

        // Calculate this month's profit
        if (invoice.createdAt.isAfter(thisMonth)) {
          thisMonthProfit += invoice.profitAmount;
        }
      }

      // Calculate profit margin
      final profitMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0.0;

      _profitStats = {
        'totalProfit': totalProfit,
        'companyProfit': companyProfit,
        'agentProfit': agentProfit,
        'profitMargin': profitMargin,
        'thisMonthProfit': thisMonthProfit,
        'totalSales': totalSales,
        'totalInvoices': allInvoices.length,
      };
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل إحصائيات الأرباح: $e', isError: true);
      }
      _profitStats = {};
    }
  }

  Future<void> _loadAgentStats() async {
    try {
      // Get real agent statistics
      final agents = await _dataService.getUsersByRole(AppConstants.agentRole);
      final agentAccounts = await _dataService.getAllAgentAccounts();
      final allInvoices = await _dataService.getInvoices();

      int totalAgents = agents.length;
      int activeAgents = agents.where((agent) => agent.isActive).length;

      double totalAgentSales = 0.0;
      double totalAgentProfits = 0.0;
      double totalAgentBalance = 0.0;

      // Calculate from invoices - only customer sales by agents
      for (final invoice in allInvoices) {
        // Only count customer sales (not transfers)
        if (invoice.type == 'customer' || invoice.type == AppConstants.customerInvoice) {
          totalAgentSales += invoice.sellingPrice;
          totalAgentProfits += invoice.agentProfitShare;
        }
      }

      // Calculate from agent accounts
      for (final account in agentAccounts) {
        totalAgentBalance += account.currentBalance;
      }

      final averageAgentBalance = agentAccounts.isNotEmpty
          ? totalAgentBalance / agentAccounts.length
          : 0.0;

      _agentStats = {
        'totalAgents': totalAgents,
        'activeAgents': activeAgents,
        'totalAgentSales': totalAgentSales,
        'totalAgentProfits': totalAgentProfits,
        'averageAgentBalance': averageAgentBalance,
        'totalAgentBalance': totalAgentBalance,
        'agentsWithDebt': agentAccounts.where((acc) => acc.currentBalance > 0).length,
        'agentsWithCredit': agentAccounts.where((acc) => acc.currentBalance < 0).length,
      };
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل إحصائيات الوكلاء: $e', isError: true);
      }
      _agentStats = {};
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    if (!authProvider.canViewReports) {
      return Scaffold(
        appBar: AppBar(title: const Text('التقارير')),
        body: const Center(
          child: Text(
            'ليس لديك صلاحية لعرض التقارير',
            style: TextStyle(fontSize: 18),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReports,
            tooltip: 'تحديث التقارير',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInventorySection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildSalesSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  _buildWarehouseMovementSection(),
                  const SizedBox(height: AppConstants.largePadding),
                  // Only show profit section for super admin
                  if (Provider.of<AuthProvider>(context, listen: false).currentUser?.role == 'super_admin') ...[
                    _buildProfitSection(),
                    const SizedBox(height: AppConstants.largePadding),
                  ],
                  _buildAgentSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildInventorySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.inventory_2,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    'تقرير المخزون',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const DetailedInventoryReport()),
                    );
                  },
                  icon: const Icon(Icons.table_chart, size: 16),
                  label: const Text('تفصيلي'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الأصناف',
                    _inventoryStats['totalItems']?.toString() ?? '0',
                    Icons.widgets,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الأصناف المتاحة',
                    _inventoryStats['availableItems']?.toString() ?? '0',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'الأصناف المباعة',
                    _inventoryStats['soldItems']?.toString() ?? '0',
                    Icons.shopping_cart,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الأصناف المحولة',
                    _inventoryStats['transferredItems']?.toString() ?? '0',
                    Icons.transfer_within_a_station,
                    Colors.purple,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'قيمة المخزون المتاح',
                    AppUtils.formatCurrency(_inventoryStats['availableValue'] ?? 0),
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'معدل دوران المخزون',
                    '${(_inventoryStats['inventoryTurnover'] ?? 0.0).toStringAsFixed(1)}%',
                    Icons.trending_up,
                    Colors.indigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _exportReportToCSV('inventory'),
                  icon: const Icon(Icons.table_chart),
                  label: const Text('تصدير CSV'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _exportReportToPDF('inventory'),
                  icon: const Icon(Icons.picture_as_pdf),
                  label: const Text('تصدير PDF'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.point_of_sale,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: Text(
                    'تقرير المبيعات',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const DetailedSalesReport()),
                    );
                  },
                  icon: const Icon(Icons.table_chart, size: 16),
                  label: const Text('تفصيلي'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي المبيعات',
                    AppUtils.formatCurrency(_salesStats['totalSales'] ?? 0),
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'عدد المبيعات',
                    _salesStats['salesCount']?.toString() ?? '0',
                    Icons.receipt,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'مبيعات هذا الشهر',
                    AppUtils.formatCurrency(_salesStats['thisMonthSales'] ?? 0),
                    Icons.calendar_today,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'متوسط قيمة البيع',
                    AppUtils.formatCurrency(_salesStats['averageSaleValue'] ?? 0),
                    Icons.analytics,
                    Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _exportReportToCSV('sales'),
                  icon: const Icon(Icons.table_chart),
                  label: const Text('تصدير CSV'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _exportReportToPDF('sales'),
                  icon: const Icon(Icons.picture_as_pdf),
                  label: const Text('تصدير PDF'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfitSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.monetization_on,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'تقرير الأرباح',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الأرباح',
                    AppUtils.formatCurrency(_profitStats['totalProfit'] ?? 0),
                    Icons.account_balance_wallet,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'هامش الربح',
                    '${((_profitStats['profitMargin'] ?? 0) as num).toStringAsFixed(1)}%',
                    Icons.percent,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'ربح المؤسسة',
                    AppUtils.formatCurrency(_profitStats['companyProfit'] ?? 0),
                    Icons.business,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'ربح الوكلاء',
                    AppUtils.formatCurrency(_profitStats['agentProfit'] ?? 0),
                    Icons.people,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarehouseMovementSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.swap_horiz,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'تقارير حركة المخازن',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            Text(
              'تقارير شاملة ومتطورة لحركة المخازن وتحليل المنتجات وفواتير التحويل',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const WarehouseMovementReportsScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.table_chart),
                    label: const Text('التقارير الأساسية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const AdvancedWarehouseReportsScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.analytics),
                    label: const Text('التقارير المتطورة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1565C0),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgentSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.people,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Text(
                  'تقرير الوكلاء',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الوكلاء',
                    _agentStats['totalAgents']?.toString() ?? '0',
                    Icons.group,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الوكلاء النشطون',
                    _agentStats['activeAgents']?.toString() ?? '0',
                    Icons.group_add,
                    Colors.green,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'مبيعات الوكلاء',
                    AppUtils.formatCurrency(_agentStats['totalAgentSales'] ?? 0),
                    Icons.point_of_sale,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'أرباح الوكلاء',
                    AppUtils.formatCurrency(_agentStats['totalAgentProfits'] ?? 0),
                    Icons.account_balance,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Export report to PDF
  Future<void> _exportReportToPDF(String reportType) async {
    try {
      // Use static methods from ReportService

      String title;
      Map<String, dynamic> reportData;
      List<Map<String, dynamic>> tableData = [];
      List<String> tableHeaders = [];

      switch (reportType) {
        case 'inventory':
          title = 'تقرير المخزون';
          reportData = _inventoryStats;
          // Add table data for inventory items
          tableHeaders = ['الصنف', 'النوع', 'المخزن', 'الحالة'];
          break;
        case 'sales':
          title = 'تقرير المبيعات';
          reportData = _salesStats;
          tableHeaders = ['رقم الفاتورة', 'التاريخ', 'الوكيل', 'المبلغ'];
          break;
        case 'profit':
          title = 'تقرير الأرباح';
          reportData = _profitStats;
          tableHeaders = ['الفترة', 'المبيعات', 'التكلفة', 'الربح'];
          break;
        case 'agent':
          title = 'تقرير الوكلاء';
          reportData = _agentStats;
          tableHeaders = ['الوكيل', 'المبيعات', 'الفواتير', 'الأرباح'];
          break;
        default:
          throw Exception('نوع التقرير غير مدعوم');
      }

      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إنشاء التقرير...'),
            ],
          ),
        ),
      );

      // Generate PDF - TODO: Implement PDF export
      // final pdfFile = await ReportService.exportToPDF(
      //   reportTitle: title,
      //   reportData: reportData,
      //   tableData: tableData,
      //   tableHeaders: tableHeaders,
      // );

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Save to history - TODO: Implement save to history
      // await ReportService.saveReportToHistory(
      //   title: title,
      //   type: reportType,
      //   filePath: pdfFile.path,
      //   metadata: {'generatedAt': DateTime.now().toIso8601String()},
      // );

      // Show success dialog with options
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تم إنشاء التقرير'),
            content: Text('تم إنشاء $title بنجاح'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // TODO: Implement share report
                  // ReportService.shareReport(pdfFile, title);
                },
                child: const Text('مشاركة'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // Close loading dialog if open
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إنشاء التقرير: $e', isError: true);
      }
    }
  }

  Future<void> _exportReportToCSV(String reportType) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إنشاء التقرير...'),
            ],
          ),
        ),
      );

      // Use static methods from ReportService
      File? csvFile;
      String title;

      if (reportType == 'inventory') {
        // Generate inventory report
        final inventoryReport = await ReportService.generateInventoryReport();
        // csvFile = await ReportService.exportInventoryReportToCSV(inventoryReport);
        title = 'تقرير المخزون';
      } else {
        // Generate sales report (default to last 30 days)
        final endDate = DateTime.now();
        final startDate = endDate.subtract(const Duration(days: 30));
        final salesReport = await ReportService.generateSalesReport(
          startDate: startDate,
          endDate: endDate,
        );
        // csvFile = await ReportService.exportSalesReportToCSV(salesReport);
        title = 'تقرير المبيعات';
      }

      // Close loading dialog
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        // Show success dialog with options
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تم إنشاء التقرير'),
            content: Text('تم حفظ التقرير في:\n${csvFile?.path ?? 'مسار غير محدد'}'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // TODO: Implement share report
                  // ReportService.shareReport(csvFile, title);
                },
                child: const Text('مشاركة'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      // Close loading dialog if open
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إنشاء التقرير: $e', isError: true);
      }
    }
  }
}
