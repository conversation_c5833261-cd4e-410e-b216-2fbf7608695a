import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_financial_account_model.dart';
import '../../models/user_model.dart';
import '../../models/item_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/agent_financial_service.dart';
import '../../services/data_service.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';

/// شاشة سحب البضاعة من الوكيل
/// تسمح للمديرين بسحب بضاعة من الوكيل وتحديث حساباته تلقائياً
class WithdrawGoodsScreen extends StatefulWidget {
  final UserModel agent;
  final AgentFinancialAccount account;

  const WithdrawGoodsScreen({
    super.key,
    required this.agent,
    required this.account,
  });

  @override
  State<WithdrawGoodsScreen> createState() => _WithdrawGoodsScreenState();
}

class _WithdrawGoodsScreenState extends State<WithdrawGoodsScreen> {
  final AgentFinancialService _financialService = AgentFinancialService();
  final DataService _dataService = DataService.instance;
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();

  List<ItemModel> _agentItems = [];
  List<ItemModel> _selectedItems = [];
  bool _isLoading = true;
  bool _isProcessing = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadAgentItems();
  }

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  /// تحميل أصناف الوكيل المتاحة للسحب
  Future<void> _loadAgentItems() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      if (kDebugMode) {
        print('🔄 Loading items for agent: ${widget.agent.fullName}');
      }

      // البحث عن جميع الأصناف في مخزن الوكيل
      final allItems = await _dataService.getItems();
      
      // فلترة الأصناف التي تخص الوكيل والمتاحة للسحب
      _agentItems = allItems.where((item) {
        return item.currentWarehouseId == 'agent_warehouse_${widget.agent.id}' &&
               (item.status == 'متاح' || item.status == 'available');
      }).toList();

      if (kDebugMode) {
        print('✅ Found ${_agentItems.length} items available for withdrawal');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading agent items: $e');
      }
      setState(() {
        _error = 'خطأ في تحميل أصناف الوكيل: $e';
        _isLoading = false;
      });
    }
  }

  /// سحب الأصناف المحددة
  Future<void> _withdrawSelectedItems() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedItems.isEmpty) {
      AppUtils.showSnackBar(context, 'يرجى اختيار صنف واحد على الأقل للسحب', isError: true);
      return;
    }

    try {
      setState(() {
        _isProcessing = true;
      });

      final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
      if (currentUser == null) {
        throw 'يجب تسجيل الدخول أولاً';
      }

      if (kDebugMode) {
        print('🔄 Withdrawing ${_selectedItems.length} items from agent: ${widget.agent.fullName}');
      }

      // سحب كل صنف محدد
      for (final item in _selectedItems) {
        await _financialService.withdrawGoods(
          agentId: widget.agent.id,
          itemId: item.id,
          originalValue: item.purchasePrice,
          userId: currentUser.id,
          reason: _reasonController.text.trim(),
        );

        if (kDebugMode) {
          print('✅ Withdrawn item: ${item.motorFingerprintText}');
        }
      }

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'تم سحب ${_selectedItems.length} صنف بنجاح من الوكيل ${widget.agent.fullName}',
        );
        Navigator.pop(context, true); // إرجاع true للإشارة إلى نجاح العملية
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error withdrawing items: $e');
      }
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في سحب الأصناف: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text('سحب بضاعة من ${widget.agent.fullName}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAgentItems,
            tooltip: 'تحديث القائمة',
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحميل أصناف الوكيل...');
    }

    if (_error != null) {
      return CustomErrorWidget(
        message: _error!,
        onRetry: _loadAgentItems,
      );
    }

    if (_agentItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'لا توجد أصناف متاحة للسحب',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'الوكيل ${widget.agent.fullName} لا يملك أي أصناف متاحة للسحب حالياً',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAgentInfo(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildReasonInput(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildItemsSection(),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات الوكيل
  Widget _buildAgentInfo() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الوكيل',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Theme.of(context).primaryColor,
                  child: Text(
                    widget.agent.fullName.isNotEmpty ? widget.agent.fullName[0] : 'و',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.agent.fullName,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (widget.agent.phone.isNotEmpty)
                        Text(
                          widget.agent.phone,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'الرصيد الحالي',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      AppUtils.formatCurrency(widget.account.currentBalance.abs()),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: widget.account.currentBalance > 0 ? Colors.red : Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حقل سبب السحب
  Widget _buildReasonInput() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'سبب السحب',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _reasonController,
              decoration: const InputDecoration(
                hintText: 'اكتب سبب سحب البضاعة من الوكيل...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى كتابة سبب السحب';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }
