import 'package:flutter/material.dart';

/// مجموعة الألوان المحسنة لتطبيق آل فرحان للنقل الخفيف
class AppColors {
  AppColors._();

  // الألوان الأساسية - تدرج أخضر احترافي
  static const Color primary = Color(0xFF2E7D32);
  static const Color primaryLight = Color(0xFF4CAF50);
  static const Color primaryDark = Color(0xFF1B5E20);
  static const Color primaryAccent = Color(0xFF43A047);

  // الألوان الثانوية - تدرج أزرق مكمل
  static const Color secondary = Color(0xFF1976D2);
  static const Color secondaryLight = Color(0xFF42A5F5);
  static const Color secondaryDark = Color(0xFF0D47A1);

  // ألوان الحالة
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // ألوان الخلفية
  static const Color background = Color(0xFFF8F9FA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);

  // ألوان النص
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  // ألوان الحدود والفواصل
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFEEEEEE);

  // ألوان الظلال
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);

  // ألوان خاصة بالتطبيق
  static const Color inventory = Color(0xFF3F51B5);
  static const Color sales = Color(0xFF009688);
  static const Color reports = Color(0xFF795548);
  static const Color agents = Color(0xFF607D8B);
  static const Color accounting = Color(0xFF9C27B0);

  // تدرجات لونية
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight, primaryAccent],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient warningGradient = LinearGradient(
    colors: [Color(0xFFFF9800), Color(0xFFFFB74D)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient errorGradient = LinearGradient(
    colors: [Color(0xFFF44336), Color(0xFFEF5350)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // ألوان مع شفافية
  static Color primaryWithOpacity(double opacity) => primary.withOpacity(opacity);
  static Color secondaryWithOpacity(double opacity) => secondary.withOpacity(opacity);
  static Color successWithOpacity(double opacity) => success.withOpacity(opacity);
  static Color warningWithOpacity(double opacity) => warning.withOpacity(opacity);
  static Color errorWithOpacity(double opacity) => error.withOpacity(opacity);
  static Color whiteWithOpacity(double opacity) => Colors.white.withOpacity(opacity);
  static Color blackWithOpacity(double opacity) => Colors.black.withOpacity(opacity);

  // ألوان للبطاقات والمكونات
  static const Color cardBackground = surface;
  static const Color cardBorder = border;
  static Color cardShadow = shadow;

  // ألوان للأزرار
  static const Color buttonPrimary = primary;
  static const Color buttonSecondary = secondary;
  static const Color buttonSuccess = success;
  static const Color buttonWarning = warning;
  static const Color buttonError = error;

  // ألوان للحقول
  static const Color inputBackground = surface;
  static const Color inputBorder = border;
  static const Color inputFocusedBorder = primary;
  static const Color inputErrorBorder = error;

  // ألوان للشاشات المختلفة
  static const Map<String, Color> screenColors = {
    'dashboard': primary,
    'inventory': inventory,
    'sales': sales,
    'reports': reports,
    'agents': agents,
    'accounting': accounting,
    'settings': Color(0xFF9E9E9E),
    'profile': Color(0xFF673AB7),
  };

  // ألوان للحالات المختلفة
  static const Map<String, Color> statusColors = {
    'available': success,
    'sold': info,
    'transferred': warning,
    'pending': Color(0xFFFF9800),
    'completed': success,
    'cancelled': error,
    'draft': Color(0xFF9E9E9E),
  };

  // دالة للحصول على لون حسب النوع
  static Color getColorByType(String type) {
    switch (type.toLowerCase()) {
      case 'primary':
        return primary;
      case 'secondary':
        return secondary;
      case 'success':
        return success;
      case 'warning':
        return warning;
      case 'error':
        return error;
      case 'info':
        return info;
      default:
        return primary;
    }
  }

  // دالة للحصول على لون الشاشة
  static Color getScreenColor(String screenName) {
    return screenColors[screenName.toLowerCase()] ?? primary;
  }

  // دالة للحصول على لون الحالة
  static Color getStatusColor(String status) {
    return statusColors[status.toLowerCase()] ?? primary;
  }

  // ألوان للوضع المظلم (للمستقبل)
  static const Color darkPrimary = Color(0xFF4CAF50);
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFB3B3B3);

  // دالة لإنشاء BoxShadow محسن
  static List<BoxShadow> createShadow({
    Color? color,
    double blurRadius = 10,
    double spreadRadius = 0,
    Offset offset = const Offset(0, 4),
    double opacity = 0.1,
  }) {
    return [
      BoxShadow(
        color: (color ?? Colors.black).withOpacity(opacity),
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
        offset: offset,
      ),
    ];
  }

  // دالة لإنشاء تدرج لوني مخصص
  static LinearGradient createGradient({
    required Color startColor,
    required Color endColor,
    Alignment begin = Alignment.topLeft,
    Alignment end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      colors: [startColor, endColor],
      begin: begin,
      end: end,
    );
  }

  // ألوان للرسوم البيانية
  static const List<Color> chartColors = [
    Color(0xFF2E7D32),
    Color(0xFF1976D2),
    Color(0xFFFF9800),
    Color(0xFFF44336),
    Color(0xFF9C27B0),
    Color(0xFF009688),
    Color(0xFF795548),
    Color(0xFF607D8B),
    Color(0xFF4CAF50),
    Color(0xFF3F51B5),
  ];

  // دالة للحصول على لون الرسم البياني
  static Color getChartColor(int index) {
    return chartColors[index % chartColors.length];
  }
}
