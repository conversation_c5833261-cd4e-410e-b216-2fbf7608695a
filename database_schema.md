# مخطط قاعدة البيانات - تطبيق آل فرحان للنقل الخفيف

## نظرة عامة
يستخدم التطبيق نظام قاعدة بيانات مزدوج:
- **Firebase Cloud Firestore**: للتخزين السحابي والمزامنة
- **SQLite (محلي)**: للعمل بدون إنترنت والأداء السريع

## مجموعات البيانات الرئيسية

### 1. Users (المستخدمون)
```
Collection: users
Document ID: Firebase Auth UID

Fields:
- username: string (فريد)
- email: string (فريد)
- fullName: string
- phone: string
- role: string (super_admin, admin, agent, showroom)
- warehouseId: string (اختياري - للوكلاء ومستخدمي المعرض)
- isActive: boolean
- createdAt: timestamp
- updatedAt: timestamp
- fcmToken: string (اختياري - للإشعارات)
- additionalData: map (بيانات إضافية)

Indexes:
- role
- username
- email
- warehouseId
```

### 2. Warehouses (المخازن)
```
Collection: warehouses
Document ID: Auto-generated

Fields:
- name: string
- type: string (main, showroom, agent)
- ownerId: string (اختياري - معرف المالك للوكلاء)
- address: string
- phone: string (اختياري)
- email: string (اختياري)
- isActive: boolean
- createdAt: timestamp
- updatedAt: timestamp
- additionalData: map

Indexes:
- type
- ownerId
- isActive
```

### 3. Items (الأصناف)
```
Collection: items
Document ID: Motor fingerprint text (النص المستخرج من بصمة الموتور)

Fields:
- type: string (موتوسيكل، تروسيكل، سكوتر، توكتوك)
- model: string
- color: string
- brand: string
- countryOfOrigin: string
- yearOfManufacture: number
- purchasePrice: number
- suggestedSellingPrice: number
- motorFingerprintImageUrl: string
- motorFingerprintText: string (نفس الـ Document ID)
- currentWarehouseId: string
- status: string (available, sold, transferred, returned)
- createdAt: timestamp
- updatedAt: timestamp
- createdBy: string (User ID)
- additionalData: map

Indexes:
- currentWarehouseId
- status
- type
- brand
- createdBy
- createdAt
```

### 4. Invoices (الفواتير)
```
Collection: invoices
Document ID: Auto-generated

Fields:
- invoiceNumber: string (فريد)
- type: string (customer, agent)
- customerId: string (اختياري - للفواتير للعملاء)
- agentId: string (اختياري - للفواتير للوكلاء)
- warehouseId: string
- itemId: string (معرف الصنف - بصمة الموتور)
- itemCost: number (التكلفة الأساسية)
- sellingPrice: number (سعر البيع النهائي)
- profitAmount: number (مبلغ الربح)
- companyProfitShare: number (نصيب المؤسسة)
- agentProfitShare: number (نصيب الوكيل)
- status: string (pending, paid, cancelled)
- createdAt: timestamp
- updatedAt: timestamp
- createdBy: string (User ID)
- customerData: map (بيانات العميل المستخرجة من بطاقة الهوية)
  - fullName: string
  - nationalId: string
  - phone: string
  - address: string
  - issueDate: string
  - expiryDate: string
- customerIdImages: array of strings (روابط صور بطاقة الهوية)
- additionalData: map

Indexes:
- invoiceNumber
- type
- agentId
- warehouseId
- itemId
- status
- createdBy
- createdAt
```

### 5. Payments (المدفوعات)
```
Collection: payments
Document ID: Auto-generated

Fields:
- receiptNumber: string (فريد)
- agentId: string
- amount: number
- paymentMethod: string (نقدي، تحويل بنكي، شيك، إلخ)
- paymentDate: timestamp
- status: string (pending, confirmed, cancelled)
- notes: string (اختياري)
- createdAt: timestamp
- updatedAt: timestamp
- createdBy: string (User ID)
- confirmedBy: string (اختياري - User ID)
- additionalData: map

Indexes:
- receiptNumber
- agentId
- status
- paymentDate
- createdBy
```

### 6. Document Tracking (تتبع الجوابات)
```
Collection: document_tracking
Document ID: Auto-generated

Fields:
- itemId: string (معرف الصنف - بصمة الموتور)
- invoiceId: string (معرف فاتورة البيع للعميل النهائي)
- currentStatus: string (sent_to_manufacturer, received_from_manufacturer, sent_to_sale_point, ready_for_pickup)
- statusHistory: array of objects
  - status: string
  - timestamp: timestamp
  - updatedBy: string (User ID)
  - notes: string (اختياري)
- createdAt: timestamp
- updatedAt: timestamp
- createdBy: string (User ID)
- additionalData: map

Indexes:
- itemId
- invoiceId
- currentStatus
- createdAt
```

### 7. Settings (إعدادات المؤسسة)
```
Collection: settings
Document ID: setting_name

Fields:
- value: any (القيمة)
- description: string (وصف الإعداد)
- updatedAt: timestamp
- updatedBy: string (User ID)

Common Settings:
- company_logo_url
- splash_screen_image_url
- company_info
- profit_sharing_percentage
- notification_settings
```

### 8. Reports (التقارير)
```
Collection: reports
Document ID: Auto-generated

Fields:
- reportType: string (inventory, sales, profits, agent_accounts)
- title: string
- description: string
- data: map (بيانات التقرير)
- filters: map (المرشحات المستخدمة)
- generatedAt: timestamp
- generatedBy: string (User ID)
- expiresAt: timestamp (اختياري - للتقارير المؤقتة)

Indexes:
- reportType
- generatedBy
- generatedAt
```

### 9. Audit Logs (سجلات المراجعة)
```
Collection: audit_logs
Document ID: Auto-generated

Fields:
- action: string (create, update, delete, login, logout)
- collection: string (اسم المجموعة المتأثرة)
- documentId: string (معرف الوثيقة المتأثرة)
- userId: string (معرف المستخدم الذي قام بالعملية)
- userRole: string (دور المستخدم)
- timestamp: timestamp
- ipAddress: string (اختياري)
- userAgent: string (اختياري)
- changes: map (التغييرات التي تمت)
- additionalData: map

Indexes:
- action
- collection
- userId
- timestamp
```

### 10. Sync Metadata (بيانات المزامنة)
```
Collection: sync_metadata
Document ID: user_id

Fields:
- lastSyncTimestamp: timestamp
- pendingOperations: array of objects
  - operation: string (create, update, delete)
  - collection: string
  - documentId: string
  - data: map
  - timestamp: timestamp
- syncStatus: string (synced, pending, error)
- errorLog: array of objects (اختياري)
  - error: string
  - timestamp: timestamp
  - retryCount: number

Indexes:
- syncStatus
- lastSyncTimestamp
```

## العلاقات بين المجموعات

### علاقات المستخدمين والمخازن
- كل وكيل مرتبط بمخزن واحد (users.warehouseId → warehouses.id)
- مستخدمو المعرض مرتبطون بمخزن المعرض
- المديرون يمكنهم الوصول لجميع المخازن

### علاقات الأصناف والمخازن
- كل صنف موجود في مخزن واحد في أي وقت (items.currentWarehouseId → warehouses.id)
- تتبع حركة الأصناف بين المخازن عبر الفواتير

### علاقات الفواتير والأصناف
- كل فاتورة مرتبطة بصنف واحد (invoices.itemId → items.id)
- فواتير العملاء النهائيين (type: customer)
- فواتير تحويل البضاعة للوكلاء (type: agent)

### علاقات المدفوعات والوكلاء
- كل دفعة مرتبطة بوكيل (payments.agentId → users.id where role = agent)
- تتبع رصيد كل وكيل من خلال الفواتير والمدفوعات

### علاقات تتبع الجوابات
- كل تتبع مرتبط بصنف وفاتورة (document_tracking.itemId → items.id, document_tracking.invoiceId → invoices.id)

## استراتيجية المزامنة

### المزامنة التلقائية
- مزامنة كل 5 دقائق عند الاتصال بالإنترنت
- مزامنة فورية عند استعادة الاتصال
- مزامنة عند فتح التطبيق

### أولوية المزامنة
1. المستخدمون والمخازن
2. الأصناف
3. الفواتير
4. المدفوعات
5. تتبع الجوابات
6. التقارير والإعدادات

### معالجة التعارضات
- Last-Write-Wins للبيانات العامة
- Firebase Transactions للعمليات المالية الحرجة
- طابع زمني للتحديثات (updatedAt)

## الأمان والصلاحيات

### مستويات الوصول
- **Super Admin**: وصول كامل لجميع البيانات والوظائف
- **Admin**: إدارة المخزون والفواتير والتقارير (بدون إدارة المستخدمين)
- **Agent**: الوصول لمخزنه وحساباته وفواتيره فقط
- **Showroom**: إدارة مخزن المعرض وإنشاء فواتير البيع

### قواعد الأمان
- مصادقة مطلوبة لجميع العمليات
- تشفير البيانات الحساسة
- تسجيل جميع العمليات في Audit Logs
- Firebase Security Rules للتحكم في الوصول
