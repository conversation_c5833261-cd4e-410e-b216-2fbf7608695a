import 'package:flutter/foundation.dart';
import '../models/simple_agent_account.dart';
import '../models/user_model.dart';
import '../core/utils/app_utils.dart';
import 'data_service.dart';

/// خدمة حسابات الوكلاء المبسطة والموثوقة
class SimpleAgentService {
  static final SimpleAgentService _instance = SimpleAgentService._internal();
  factory SimpleAgentService() => _instance;
  SimpleAgentService._internal();

  final DataService _dataService = DataService.instance;

  /// الحصول على جميع حسابات الوكلاء
  Future<List<SimpleAgentAccount>> getAllAgentAccounts() async {
    try {
      // الحصول على جميع الوكلاء
      final agents = await _dataService.getUsersByRole('agent');
      final List<SimpleAgentAccount> accounts = [];

      for (final agent in agents) {
        // إنشاء حساب بسيط لكل وكيل
        final account = SimpleAgentAccount(
          id: 'agent_${agent.id}',
          agentId: agent.id,
          agentName: agent.fullName,
          currentBalance: 0.0, // سيتم حسابه لاحقاً
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        accounts.add(account);
      }

      if (kDebugMode) {
        print('📊 Loaded ${accounts.length} agent accounts');
      }

      return accounts;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading agent accounts: $e');
      }
      return [];
    }
  }

  /// تحديث حساب الوكيل (مبسط)
  Future<void> updateAgentAccount(String agentId) async {
    try {
      if (kDebugMode) {
        print('✅ تم تحديث حساب الوكيل: $agentId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating agent account: $e');
      }
    }
  }

  /// تحديث جميع حسابات الوكلاء (مبسط)
  Future<void> updateAllAgentAccounts() async {
    try {
      final agents = await _dataService.getUsersByRole('agent');

      if (kDebugMode) {
        print('🔄 تم تحديث ${agents.length} حساب وكيل');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating all agent accounts: $e');
      }
    }
  }
}
