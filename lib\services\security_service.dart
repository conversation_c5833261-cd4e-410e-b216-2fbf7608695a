import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/utils/app_utils.dart';
import '../models/security_log_model.dart';


class SecurityService {
  static SecurityService? _instance;
  static SecurityService get instance => _instance ??= SecurityService._();
  SecurityService._();

  late final encrypt.Encrypter _encrypter;
  late final encrypt.IV _iv;
  bool _isInitialized = false;

  // Initialize encryption
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Generate or retrieve encryption key
      final key = await _getOrCreateEncryptionKey();
      _encrypter = encrypt.Encrypter(encrypt.AES(key));
      _iv = encrypt.IV.fromSecureRandom(16);
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('Security service initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing security service: $e');
      }
      rethrow;
    }
  }

  // Get or create encryption key
  Future<encrypt.Key> _getOrCreateEncryptionKey() async {
    final prefs = await SharedPreferences.getInstance();
    String? keyString = prefs.getString('encryption_key');
    
    if (keyString == null) {
      // Generate new key
      final key = encrypt.Key.fromSecureRandom(32);
      keyString = key.base64;
      await prefs.setString('encryption_key', keyString);
      
      if (kDebugMode) {
        print('Generated new encryption key');
      }
      
      return key;
    }
    
    return encrypt.Key.fromBase64(keyString);
  }

  // Encrypt sensitive data
  String encryptData(String data) {
    if (!_isInitialized) {
      throw Exception('Security service not initialized');
    }
    
    try {
      final encrypted = _encrypter.encrypt(data, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      if (kDebugMode) {
        print('Error encrypting data: $e');
      }
      rethrow;
    }
  }

  // Decrypt sensitive data
  String decryptData(String encryptedData) {
    if (!_isInitialized) {
      throw Exception('Security service not initialized');
    }
    
    try {
      final encrypted = encrypt.Encrypted.fromBase64(encryptedData);
      return _encrypter.decrypt(encrypted, iv: _iv);
    } catch (e) {
      if (kDebugMode) {
        print('Error decrypting data: $e');
      }
      rethrow;
    }
  }

  // Hash password with salt
  String hashPassword(String password, String salt) {
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Generate salt for password hashing
  String generateSalt() {
    final bytes = List<int>.generate(32, (i) => 
        DateTime.now().millisecondsSinceEpoch % 256);
    return base64.encode(bytes);
  }

  // Verify password
  bool verifyPassword(String password, String hashedPassword, String salt) {
    final computedHash = hashPassword(password, salt);
    return computedHash == hashedPassword;
  }

  // Generate secure session token
  String generateSessionToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = List<int>.generate(16, (i) => timestamp % 256);
    return base64.encode(random);
  }

  // Validate session token (basic implementation)
  bool validateSessionToken(String token, DateTime createdAt) {
    // Token expires after 24 hours
    final expiryTime = createdAt.add(const Duration(hours: 24));
    return DateTime.now().isBefore(expiryTime);
  }

  // Secure data storage
  Future<void> secureStore(String key, String value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedValue = encryptData(value);
      await prefs.setString('secure_$key', encryptedValue);
      
      if (kDebugMode) {
        print('Securely stored data for key: $key');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error storing secure data: $e');
      }
      rethrow;
    }
  }

  // Secure data retrieval
  Future<String?> secureRetrieve(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedValue = prefs.getString('secure_$key');
      
      if (encryptedValue == null) return null;
      
      return decryptData(encryptedValue);
    } catch (e) {
      if (kDebugMode) {
        print('Error retrieving secure data: $e');
      }
      return null;
    }
  }

  // Clear all secure data
  Future<void> clearSecureData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('secure_'));
      
      for (final key in keys) {
        await prefs.remove(key);
      }
      
      if (kDebugMode) {
        print('Cleared all secure data');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing secure data: $e');
      }
    }
  }

  // Legacy log security events (deprecated - use enhanced version below)
  Future<void> logSecurityEventLegacy({
    required String event,
    required String userId,
    Map<String, dynamic>? details,
  }) async {
    try {
      final logEntry = {
        'event': event,
        'userId': userId,
        'timestamp': DateTime.now().toIso8601String(),
        'details': details ?? {},
      };

      // In a real app, you would send this to a secure logging service
      if (kDebugMode) {
        print('Security Event: ${jsonEncode(logEntry)}');
      }

      // Store locally for audit trail
      await _storeSecurityLog(logEntry);
    } catch (e) {
      if (kDebugMode) {
        print('Error logging security event: $e');
      }
    }
  }

  // Store security log locally
  Future<void> _storeSecurityLog(Map<String, dynamic> logEntry) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/security_logs.txt');
      
      final logLine = '${jsonEncode(logEntry)}\n';
      await file.writeAsString(logLine, mode: FileMode.append);
    } catch (e) {
      if (kDebugMode) {
        print('Error storing security log: $e');
      }
    }
  }

  // Get security logs
  Future<List<Map<String, dynamic>>> getSecurityLogs() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/security_logs.txt');
      
      if (!await file.exists()) return [];
      
      final content = await file.readAsString();
      final lines = content.split('\n').where((line) => line.isNotEmpty);
      
      return lines.map((line) {
        try {
          return jsonDecode(line) as Map<String, dynamic>;
        } catch (e) {
          return <String, dynamic>{};
        }
      }).where((log) => log.isNotEmpty).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error reading security logs: $e');
      }
      return [];
    }
  }

  // Clear old security logs (keep last 1000 entries)
  Future<void> cleanupSecurityLogs() async {
    try {
      final logs = await getSecurityLogs();
      
      if (logs.length <= 1000) return;
      
      // Keep only the most recent 1000 logs
      final recentLogs = logs.skip(logs.length - 1000).toList();
      
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/security_logs.txt');
      
      final content = '${recentLogs.map((log) => jsonEncode(log)).join('\n')}\n';
      await file.writeAsString(content);
      
      if (kDebugMode) {
        print('Cleaned up security logs, kept ${recentLogs.length} entries');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cleaning up security logs: $e');
      }
    }
  }

  // Validate data integrity
  String calculateChecksum(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Verify data integrity
  bool verifyChecksum(String data, String expectedChecksum) {
    final actualChecksum = calculateChecksum(data);
    return actualChecksum == expectedChecksum;
  }

  // Sanitize input data
  String sanitizeInput(String input) {
    // Remove potentially dangerous characters
    return input
        .replaceAll(RegExp(r'[<>"\'']'), '')
        .replaceAll(RegExp(r'[;&|`]'), '')
        .trim();
  }

  // Validate input format
  bool isValidInput(String input, String pattern) {
    try {
      final regex = RegExp(pattern);
      return regex.hasMatch(input);
    } catch (e) {
      return false;
    }
  }

  /// Enhanced security features

  // Security logging
  final List<SecurityLogModel> _securityLogs = [];
  int _failedLoginAttempts = 0;
  DateTime? _lastFailedLogin;

  /// Log security event
  Future<void> logSecurityEvent({
    required String userId,
    required String userName,
    required SecurityEventType eventType,
    required String description,
    SecurityLevel level = SecurityLevel.info,
    bool isSuccessful = true,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final log = SecurityLogModel(
        id: AppUtils.generateId(),
        userId: userId,
        userName: userName,
        eventType: eventType,
        description: description,
        timestamp: DateTime.now(),
        level: level,
        isSuccessful: isSuccessful,
        metadata: metadata ?? {},
      );

      _securityLogs.add(log);

      // Keep only last 1000 logs in memory
      if (_securityLogs.length > 1000) {
        _securityLogs.removeAt(0);
      }

      // Save to persistent storage
      await _saveSecurityLog(log);

      debugPrint('🔒 Security event logged: ${log.eventTypeText} - ${log.statusText}');
    } catch (e) {
      debugPrint('❌ Failed to log security event: $e');
    }
  }

  /// Save security log to file
  Future<void> _saveSecurityLog(SecurityLogModel log) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logsDir = Directory('${directory.path}/security_logs');

      if (!await logsDir.exists()) {
        await logsDir.create(recursive: true);
      }

      final today = DateTime.now();
      final fileName = 'security_${today.year}_${today.month.toString().padLeft(2, '0')}_${today.day.toString().padLeft(2, '0')}.log';
      final file = File('${logsDir.path}/$fileName');

      await file.writeAsString('${log.toJsonString()}\n', mode: FileMode.append);
    } catch (e) {
      debugPrint('❌ Failed to save security log: $e');
    }
  }

  /// Track failed login attempts
  Future<void> trackFailedLogin(String userId) async {
    _failedLoginAttempts++;
    _lastFailedLogin = DateTime.now();

    await logSecurityEvent(
      userId: userId,
      userName: 'Unknown',
      eventType: SecurityEventType.login,
      description: 'محاولة تسجيل دخول فاشلة',
      level: SecurityLevel.warning,
      isSuccessful: false,
      metadata: {
        'attempt_number': _failedLoginAttempts,
        'timestamp': _lastFailedLogin!.toIso8601String(),
      },
    );

    // Lock account after 5 failed attempts
    if (_failedLoginAttempts >= 5) {
      await logSecurityEvent(
        userId: userId,
        userName: 'System',
        eventType: SecurityEventType.securityViolation,
        description: 'تم قفل الحساب بسبب محاولات دخول متكررة فاشلة',
        level: SecurityLevel.critical,
        isSuccessful: false,
      );
    }
  }

  /// Reset failed login attempts
  void resetFailedLoginAttempts() {
    _failedLoginAttempts = 0;
    _lastFailedLogin = null;
  }

  /// Check if account is locked
  bool isAccountLocked() {
    if (_failedLoginAttempts >= 5 && _lastFailedLogin != null) {
      final lockDuration = DateTime.now().difference(_lastFailedLogin!);
      return lockDuration.inMinutes < 30; // Lock for 30 minutes
    }
    return false;
  }

  /// Generate security audit report
  Future<SecurityAuditReport> generateAuditReport({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      fromDate ??= DateTime.now().subtract(const Duration(days: 30));
      toDate ??= DateTime.now();

      // Load logs from files
      final allLogs = await _loadSecurityLogs(fromDate, toDate);

      final report = SecurityAuditReport.fromLogs(allLogs, fromDate, toDate);

      debugPrint('📊 Security audit report generated: ${allLogs.length} events');
      return report;
    } catch (e) {
      debugPrint('❌ Failed to generate audit report: $e');
      rethrow;
    }
  }

  /// Load security logs from files
  Future<List<SecurityLogModel>> _loadSecurityLogs(DateTime fromDate, DateTime toDate) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logsDir = Directory('${directory.path}/security_logs');

      if (!await logsDir.exists()) {
        return [];
      }

      final logs = <SecurityLogModel>[];
      final files = await logsDir.list().toList();

      for (final file in files) {
        if (file is File && file.path.endsWith('.log')) {
          final content = await file.readAsString();
          final lines = content.split('\n').where((line) => line.isNotEmpty);

          for (final line in lines) {
            try {
              final log = SecurityLogModel.fromJsonString(line);
              if (log.timestamp.isAfter(fromDate) && log.timestamp.isBefore(toDate)) {
                logs.add(log);
              }
            } catch (e) {
              // Skip invalid log entries
            }
          }
        }
      }

      return logs;
    } catch (e) {
      debugPrint('❌ Failed to load security logs: $e');
      return [];
    }
  }

  /// Get recent security logs
  List<SecurityLogModel> getRecentLogs({int limit = 100}) {
    final sortedLogs = List<SecurityLogModel>.from(_securityLogs);
    sortedLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sortedLogs.take(limit).toList();
  }

  /// Clear old security logs
  Future<void> clearOldLogs({int retentionDays = 90}) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logsDir = Directory('${directory.path}/security_logs');

      if (!await logsDir.exists()) {
        return;
      }

      final cutoffDate = DateTime.now().subtract(Duration(days: retentionDays));
      final files = await logsDir.list().toList();

      for (final file in files) {
        if (file is File && file.path.endsWith('.log')) {
          final stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await file.delete();
            debugPrint('🗑️ Deleted old security log: ${file.path}');
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Failed to clear old logs: $e');
    }
  }

  /// Enhanced password validation
  Map<String, bool> validatePasswordStrength(String password) {
    return {
      'minLength': password.length >= 8,
      'hasUppercase': password.contains(RegExp(r'[A-Z]')),
      'hasLowercase': password.contains(RegExp(r'[a-z]')),
      'hasNumbers': password.contains(RegExp(r'[0-9]')),
      'hasSpecialChars': password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]')),
      'noCommonPatterns': !_hasCommonPatterns(password),
    };
  }

  /// Check for common password patterns
  bool _hasCommonPatterns(String password) {
    final commonPatterns = [
      '123456',
      'password',
      'admin',
      'qwerty',
      '111111',
      'abc123',
    ];

    final lowerPassword = password.toLowerCase();
    return commonPatterns.any((pattern) => lowerPassword.contains(pattern));
  }

  /// Calculate password strength score
  double calculatePasswordScore(String password) {
    final checks = validatePasswordStrength(password);
    final passedChecks = checks.values.where((passed) => passed).length;
    return (passedChecks / checks.length) * 100;
  }

  /// Generate secure random password
  String generateSecurePassword({int length = 12}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*()';
    final random = Random.secure();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Detect suspicious activity
  bool detectSuspiciousActivity(String userId) {
    final recentLogs = _securityLogs
        .where((log) => log.userId == userId)
        .where((log) => DateTime.now().difference(log.timestamp).inHours < 24)
        .toList();

    // Check for multiple failed logins
    final failedLogins = recentLogs
        .where((log) => log.eventType == SecurityEventType.login && !log.isSuccessful)
        .length;

    if (failedLogins > 3) {
      return true;
    }

    // Check for unusual access patterns
    final accessLogs = recentLogs
        .where((log) => log.eventType == SecurityEventType.dataAccess)
        .toList();

    if (accessLogs.length > 50) {
      return true;
    }

    return false;
  }

  /// Get security statistics
  Map<String, dynamic> getSecurityStatistics() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(hours: 24));
    final last7Days = now.subtract(const Duration(days: 7));

    final recent24hLogs = _securityLogs.where((log) => log.timestamp.isAfter(last24Hours)).toList();
    final recent7dLogs = _securityLogs.where((log) => log.timestamp.isAfter(last7Days)).toList();

    return {
      'total_events': _securityLogs.length,
      'events_24h': recent24hLogs.length,
      'events_7d': recent7dLogs.length,
      'failed_logins_24h': recent24hLogs.where((l) => l.eventType == SecurityEventType.login && !l.isSuccessful).length,
      'violations_7d': recent7dLogs.where((l) => l.eventType == SecurityEventType.securityViolation).length,
      'current_failed_attempts': _failedLoginAttempts,
      'account_locked': isAccountLocked(),
    };
  }
}
