import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج محكم لحساب الوكيل المالي مع المؤسسة
/// يتبع مبادئ المحاسبة المزدوجة ويضمن دقة الحسابات
class AgentFinancialAccount {
  final String id;
  final String agentId;
  final String agentName;
  final String agentPhone;
  
  // الحسابات الأساسية
  final double totalGoodsReceived;      // إجمالي قيمة البضاعة المستلمة
  final double totalGoodsReturned;      // إجمالي قيمة البضاعة المرتجعة
  final double totalCustomerSales;      // إجمالي مبيعات العملاء
  final double totalAgentProfits;       // إجمالي أرباح الوكيل
  final double totalCompanyProfits;     // إجمالي أرباح المؤسسة
  final double totalPaymentsReceived;   // إجمالي المدفوعات المستلمة
  
  // الأرصدة المحسوبة
  final double currentDebt;             // المديونية الحالية
  final double currentBalance;          // الرصيد الحالي (موجب = مدين، سالب = دائن)
  
  // بيانات التتبع
  final List<AgentTransaction> transactions;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final String lastUpdatedBy;

  AgentFinancialAccount({
    required this.id,
    required this.agentId,
    required this.agentName,
    required this.agentPhone,
    required this.totalGoodsReceived,
    required this.totalGoodsReturned,
    required this.totalCustomerSales,
    required this.totalAgentProfits,
    required this.totalCompanyProfits,
    required this.totalPaymentsReceived,
    required this.currentDebt,
    required this.currentBalance,
    required this.transactions,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    required this.lastUpdatedBy,
  });

  /// حساب الرصيد الحالي بناءً على المعادلة المحاسبية
  /// الرصيد = (البضاعة المستلمة - البضاعة المرتجعة) + أرباح المؤسسة - المدفوعات
  double calculateCurrentBalance() {
    final netGoodsValue = totalGoodsReceived - totalGoodsReturned;
    return netGoodsValue + totalCompanyProfits - totalPaymentsReceived;
  }

  /// حساب المديونية الحالية (الجزء الموجب من الرصيد)
  double calculateCurrentDebt() {
    final balance = calculateCurrentBalance();
    return balance > 0 ? balance : 0.0;
  }

  /// حساب الرصيد الدائن (الجزء السالب من الرصيد)
  double calculateCreditBalance() {
    final balance = calculateCurrentBalance();
    return balance < 0 ? balance.abs() : 0.0;
  }

  /// التحقق من صحة الحسابات
  bool validateAccounts() {
    final calculatedBalance = calculateCurrentBalance();
    final calculatedDebt = calculateCurrentDebt();
    
    // التحقق من تطابق الحسابات المخزنة مع المحسوبة
    const tolerance = 0.01; // هامش خطأ مقبول
    
    return (currentBalance - calculatedBalance).abs() < tolerance &&
           (currentDebt - calculatedDebt).abs() < tolerance;
  }

  /// إنشاء نسخة محدثة من الحساب
  AgentFinancialAccount copyWith({
    String? id,
    String? agentId,
    String? agentName,
    String? agentPhone,
    double? totalGoodsReceived,
    double? totalGoodsReturned,
    double? totalCustomerSales,
    double? totalAgentProfits,
    double? totalCompanyProfits,
    double? totalPaymentsReceived,
    double? currentDebt,
    double? currentBalance,
    List<AgentTransaction>? transactions,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? lastUpdatedBy,
  }) {
    return AgentFinancialAccount(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      agentPhone: agentPhone ?? this.agentPhone,
      totalGoodsReceived: totalGoodsReceived ?? this.totalGoodsReceived,
      totalGoodsReturned: totalGoodsReturned ?? this.totalGoodsReturned,
      totalCustomerSales: totalCustomerSales ?? this.totalCustomerSales,
      totalAgentProfits: totalAgentProfits ?? this.totalAgentProfits,
      totalCompanyProfits: totalCompanyProfits ?? this.totalCompanyProfits,
      totalPaymentsReceived: totalPaymentsReceived ?? this.totalPaymentsReceived,
      currentDebt: currentDebt ?? this.currentDebt,
      currentBalance: currentBalance ?? this.currentBalance,
      transactions: transactions ?? this.transactions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      lastUpdatedBy: lastUpdatedBy ?? this.lastUpdatedBy,
    );
  }

  /// تحويل إلى Map للحفظ في Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'agentId': agentId,
      'agentName': agentName,
      'agentPhone': agentPhone,
      'totalGoodsReceived': totalGoodsReceived,
      'totalGoodsReturned': totalGoodsReturned,
      'totalCustomerSales': totalCustomerSales,
      'totalAgentProfits': totalAgentProfits,
      'totalCompanyProfits': totalCompanyProfits,
      'totalPaymentsReceived': totalPaymentsReceived,
      'currentDebt': currentDebt,
      'currentBalance': currentBalance,
      'transactions': transactions.map((t) => t.toMap()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'lastUpdatedBy': lastUpdatedBy,
    };
  }

  /// إنشاء من Map من Firestore
  factory AgentFinancialAccount.fromMap(Map<String, dynamic> map) {
    return AgentFinancialAccount(
      id: map['id'] ?? '',
      agentId: map['agentId'] ?? '',
      agentName: map['agentName'] ?? '',
      agentPhone: map['agentPhone'] ?? '',
      totalGoodsReceived: (map['totalGoodsReceived'] ?? 0.0).toDouble(),
      totalGoodsReturned: (map['totalGoodsReturned'] ?? 0.0).toDouble(),
      totalCustomerSales: (map['totalCustomerSales'] ?? 0.0).toDouble(),
      totalAgentProfits: (map['totalAgentProfits'] ?? 0.0).toDouble(),
      totalCompanyProfits: (map['totalCompanyProfits'] ?? 0.0).toDouble(),
      totalPaymentsReceived: (map['totalPaymentsReceived'] ?? 0.0).toDouble(),
      currentDebt: (map['currentDebt'] ?? 0.0).toDouble(),
      currentBalance: (map['currentBalance'] ?? 0.0).toDouble(),
      transactions: (map['transactions'] as List<dynamic>?)
          ?.map((t) => AgentTransaction.fromMap(t as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdBy: map['createdBy'] ?? '',
      lastUpdatedBy: map['lastUpdatedBy'] ?? '',
    );
  }
}

/// نموذج معاملة الوكيل
class AgentTransaction {
  final String id;
  final String type;                    // goods_received, goods_returned, customer_sale, payment_received
  final String description;
  final double amount;
  final double balanceAfter;
  final String? relatedInvoiceId;
  final String? relatedPaymentId;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final String createdBy;

  AgentTransaction({
    required this.id,
    required this.type,
    required this.description,
    required this.amount,
    required this.balanceAfter,
    this.relatedInvoiceId,
    this.relatedPaymentId,
    this.metadata,
    required this.createdAt,
    required this.createdBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'description': description,
      'amount': amount,
      'balanceAfter': balanceAfter,
      'relatedInvoiceId': relatedInvoiceId,
      'relatedPaymentId': relatedPaymentId,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
    };
  }

  factory AgentTransaction.fromMap(Map<String, dynamic> map) {
    return AgentTransaction(
      id: map['id'] ?? '',
      type: map['type'] ?? '',
      description: map['description'] ?? '',
      amount: (map['amount'] ?? 0.0).toDouble(),
      balanceAfter: (map['balanceAfter'] ?? 0.0).toDouble(),
      relatedInvoiceId: map['relatedInvoiceId'],
      relatedPaymentId: map['relatedPaymentId'],
      metadata: map['metadata'] as Map<String, dynamic>?,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdBy: map['createdBy'] ?? '',
    );
  }
}
