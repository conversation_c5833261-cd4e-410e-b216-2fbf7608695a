import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/desktop_theme.dart';
import '../../widgets/desktop/desktop_dashboard_cards.dart';
import '../../services/data_service.dart';
import '../../providers/auth_provider.dart';
import '../../core/utils/app_utils.dart';
import '../../models/user_model.dart';
import '../../models/warehouse_model.dart';
import 'agents_management_screen.dart';
import 'warehouse_management_screen.dart';
import '../users/add_user_screen.dart';

class DesktopAdminScreen extends StatefulWidget {
  const DesktopAdminScreen({super.key});

  @override
  State<DesktopAdminScreen> createState() => _DesktopAdminScreenState();
}

class _DesktopAdminScreenState extends State<DesktopAdminScreen> {
  bool _isLoading = true;
  List<UserModel> _users = [];
  List<WarehouseModel> _warehouses = [];
  Map<String, dynamic> _systemStats = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      final users = await dataService.getUsers();
      final warehouses = await dataService.getWarehouses();
      final items = await dataService.getItems();
      final invoices = await dataService.getInvoices();

      setState(() {
        _users = users;
        _warehouses = warehouses;
        _systemStats = {
          'totalUsers': users.length,
          'totalAgents': users.where((u) => u.role == 'agent').length,
          'totalCustomers': users.where((u) => u.role == 'customer').length,
          'totalWarehouses': warehouses.length,
          'totalItems': items.length,
          'totalInvoices': invoices.length,
          'systemUptime': '99.9%',
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          _buildHeaderSection(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // System Statistics
          _buildSystemStatistics(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // Admin Tools
          _buildAdminTools(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // User Management
          _buildUserManagement(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // System Settings
          _buildSystemSettings(),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    final authProvider = Provider.of<AuthProvider>(context);

    return Container(
      padding: const EdgeInsets.all(DesktopTheme.spacingXLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DesktopTheme.accentPurple,
            DesktopTheme.accentPurple.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(DesktopTheme.radiusXLarge),
        boxShadow: DesktopTheme.elevatedShadow,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'لوحة الإدارة',
                  style: DesktopTheme.headingLarge.copyWith(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: DesktopTheme.spacingSmall),
                Text(
                  'إدارة شاملة للنظام والمستخدمين والإعدادات',
                  style: DesktopTheme.titleMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: DesktopTheme.spacingMedium),
                Row(
                  children: [
                    _buildQuickStat('المستخدمين', _systemStats['totalUsers'].toString(), Icons.people),
                    const SizedBox(width: DesktopTheme.spacingXLarge),
                    _buildQuickStat('وقت التشغيل', _systemStats['systemUptime'], Icons.timer),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesktopTheme.radiusLarge),
            ),
            child: const Icon(
              Icons.admin_panel_settings_outlined,
              size: 64,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 20),
        ),
        const SizedBox(width: DesktopTheme.spacingSmall),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: DesktopTheme.titleLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: DesktopTheme.bodySmall.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSystemStatistics() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات النظام',
          style: DesktopTheme.headingMedium,
        ),
        const SizedBox(height: DesktopTheme.spacingLarge),
        DesktopDashboardGrid(
          crossAxisCount: 4,
          childAspectRatio: 1.3,
          children: [
            DesktopDashboardCard(
              title: 'إجمالي المستخدمين',
              value: _systemStats['totalUsers'].toString(),
              subtitle: 'مستخدم مسجل',
              icon: Icons.people_outlined,
              iconColor: DesktopTheme.primaryBlue,
              onTap: () => _manageUsers(),
            ),
            DesktopDashboardCard(
              title: 'الوكلاء النشطون',
              value: _systemStats['totalAgents'].toString(),
              subtitle: 'وكيل',
              icon: Icons.business_outlined,
              iconColor: DesktopTheme.accentGreen,
              onTap: () => _manageAgents(),
            ),
            DesktopDashboardCard(
              title: 'العملاء',
              value: _systemStats['totalCustomers'].toString(),
              subtitle: 'عميل',
              icon: Icons.person_outline,
              iconColor: DesktopTheme.accentOrange,
              onTap: () => _manageCustomers(),
            ),
            DesktopDashboardCard(
              title: 'المخازن',
              value: _systemStats['totalWarehouses'].toString(),
              subtitle: 'مخزن',
              icon: Icons.warehouse_outlined,
              iconColor: DesktopTheme.accentPurple,
              onTap: () => _manageWarehouses(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdminTools() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أدوات الإدارة',
          style: DesktopTheme.headingMedium,
        ),
        const SizedBox(height: DesktopTheme.spacingLarge),
        DesktopDashboardGrid(
          crossAxisCount: 4,
          childAspectRatio: 1.2,
          children: [
            DesktopQuickActionCard(
              title: 'إدارة المستخدمين',
              description: 'إضافة وتعديل وحذف المستخدمين',
              icon: Icons.manage_accounts_outlined,
              iconColor: DesktopTheme.primaryBlue,
              onTap: _manageUsers,
            ),
            DesktopQuickActionCard(
              title: 'إدارة المخازن',
              description: 'إعداد وإدارة المخازن',
              icon: Icons.warehouse_outlined,
              iconColor: DesktopTheme.accentGreen,
              onTap: _manageWarehouses,
            ),
            DesktopQuickActionCard(
              title: 'النسخ الاحتياطي',
              description: 'إنشاء واستعادة النسخ الاحتياطية',
              icon: Icons.backup_outlined,
              iconColor: DesktopTheme.accentOrange,
              onTap: _manageBackups,
            ),
            DesktopQuickActionCard(
              title: 'سجل النظام',
              description: 'عرض سجلات النشاط والأخطاء',
              icon: Icons.history_outlined,
              iconColor: DesktopTheme.accentPurple,
              onTap: _viewSystemLogs,
            ),
            DesktopQuickActionCard(
              title: 'إعدادات النظام',
              description: 'تكوين إعدادات النظام العامة',
              icon: Icons.settings_outlined,
              iconColor: DesktopTheme.textSecondary,
              onTap: _systemSettings,
            ),
            DesktopQuickActionCard(
              title: 'الأمان والصلاحيات',
              description: 'إدارة صلاحيات المستخدمين',
              icon: Icons.security_outlined,
              iconColor: DesktopTheme.accentRed,
              onTap: _manageSecurity,
            ),
            DesktopQuickActionCard(
              title: 'تحديث النظام',
              description: 'فحص وتطبيق التحديثات',
              icon: Icons.system_update_outlined,
              iconColor: DesktopTheme.statusInfo,
              onTap: _checkUpdates,
            ),
            DesktopQuickActionCard(
              title: 'إحصائيات الأداء',
              description: 'مراقبة أداء النظام',
              icon: Icons.analytics_outlined,
              iconColor: DesktopTheme.primaryBlueDark,
              onTap: _viewPerformanceStats,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUserManagement() {
    return Container(
      decoration: DesktopTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: const BoxDecoration(
              color: DesktopTheme.backgroundTertiary,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(DesktopTheme.radiusLarge),
                topRight: Radius.circular(DesktopTheme.radiusLarge),
              ),
              border: Border(
                bottom: BorderSide(
                  color: DesktopTheme.borderLight,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.people, color: DesktopTheme.primaryBlue, size: 24),
                const SizedBox(width: DesktopTheme.spacingSmall),
                const Text(
                  'إدارة المستخدمين',
                  style: DesktopTheme.titleLarge,
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _addNewUser,
                  icon: const Icon(Icons.person_add, size: 20),
                  label: const Text('إضافة مستخدم'),
                  style: DesktopTheme.primaryButtonStyle,
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildUserTypeCard(
                        'المديرون',
                        _users.where((u) => u.role == 'admin').length.toString(),
                        Icons.admin_panel_settings,
                        DesktopTheme.accentRed,
                      ),
                    ),
                    const SizedBox(width: DesktopTheme.spacingMedium),
                    Expanded(
                      child: _buildUserTypeCard(
                        'الوكلاء',
                        _users.where((u) => u.role == 'agent').length.toString(),
                        Icons.business,
                        DesktopTheme.accentGreen,
                      ),
                    ),
                    const SizedBox(width: DesktopTheme.spacingMedium),
                    Expanded(
                      child: _buildUserTypeCard(
                        'العملاء',
                        _users.where((u) => u.role == 'customer').length.toString(),
                        Icons.person,
                        DesktopTheme.primaryBlue,
                      ),
                    ),
                    const SizedBox(width: DesktopTheme.spacingMedium),
                    Expanded(
                      child: _buildUserTypeCard(
                        'الموظفون',
                        _users.where((u) => u.role == 'employee').length.toString(),
                        Icons.work,
                        DesktopTheme.accentOrange,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: DesktopTheme.spacingLarge),
                Row(
                  children: [
                    ElevatedButton.icon(
                      onPressed: _viewAllUsers,
                      icon: const Icon(Icons.list),
                      label: const Text('عرض جميع المستخدمين'),
                      style: DesktopTheme.secondaryButtonStyle,
                    ),
                    const SizedBox(width: DesktopTheme.spacingMedium),
                    ElevatedButton.icon(
                      onPressed: _exportUsersList,
                      icon: const Icon(Icons.file_download),
                      label: const Text('تصدير قائمة المستخدمين'),
                      style: DesktopTheme.tertiaryButtonStyle,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserTypeCard(String title, String count, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: DesktopTheme.spacingSmall),
          Text(
            count,
            style: DesktopTheme.headingSmall.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: DesktopTheme.bodyMedium.copyWith(
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemSettings() {
    return Container(
      decoration: DesktopTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: const BoxDecoration(
              color: DesktopTheme.backgroundTertiary,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(DesktopTheme.radiusLarge),
                topRight: Radius.circular(DesktopTheme.radiusLarge),
              ),
              border: Border(
                bottom: BorderSide(
                  color: DesktopTheme.borderLight,
                  width: 1,
                ),
              ),
            ),
            child: const Row(
              children: [
                Icon(Icons.settings, color: DesktopTheme.primaryBlue, size: 24),
                SizedBox(width: DesktopTheme.spacingSmall),
                Text(
                  'إعدادات النظام',
                  style: DesktopTheme.titleLarge,
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            child: Column(
              children: [
                _buildSettingItem(
                  'إعدادات قاعدة البيانات',
                  'تكوين اتصال قاعدة البيانات',
                  Icons.storage,
                  () => _databaseSettings(),
                ),
                _buildSettingItem(
                  'إعدادات الأمان',
                  'تكوين إعدادات الأمان والتشفير',
                  Icons.security,
                  () => _securitySettings(),
                ),
                _buildSettingItem(
                  'إعدادات الإشعارات',
                  'تكوين نظام الإشعارات',
                  Icons.notifications,
                  () => _notificationSettings(),
                ),
                _buildSettingItem(
                  'إعدادات النسخ الاحتياطي',
                  'تكوين النسخ الاحتياطي التلقائي',
                  Icons.backup,
                  () => _backupSettings(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(String title, String description, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: DesktopTheme.primaryBlue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: DesktopTheme.primaryBlue),
      ),
      title: Text(title, style: DesktopTheme.titleMedium),
      subtitle: Text(description, style: DesktopTheme.bodyMedium),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: onTap,
    );
  }

  // Action Methods
  void _manageUsers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إدارة المستخدمين قيد التطوير')),
    );
  }

  void _manageAgents() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AgentsManagementScreen(),
      ),
    );
  }

  void _manageCustomers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إدارة العملاء قيد التطوير')),
    );
  }

  void _manageWarehouses() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const WarehouseManagementScreen(),
      ),
    );
  }

  void _manageBackups() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إدارة النسخ الاحتياطية قيد التطوير')),
    );
  }

  void _viewSystemLogs() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سجل النظام قيد التطوير')),
    );
  }

  void _systemSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات النظام'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'إعدادات عامة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('تفعيل الإشعارات'),
                subtitle: const Text('إرسال إشعارات للعمليات المهمة'),
                value: true,
                onChanged: (value) {},
              ),
              SwitchListTile(
                title: const Text('المزامنة التلقائية'),
                subtitle: const Text('مزامنة البيانات مع Firebase تلقائياً'),
                value: true,
                onChanged: (value) {},
              ),
              SwitchListTile(
                title: const Text('وضع التطوير'),
                subtitle: const Text('إظهار معلومات إضافية للمطورين'),
                value: false,
                onChanged: (value) {},
              ),
              const SizedBox(height: 16),
              const Text(
                'إعدادات الأمان:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ListTile(
                leading: const Icon(Icons.lock),
                title: const Text('تغيير كلمة المرور'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {},
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم حفظ الإعدادات')),
              );
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _manageSecurity() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدارة الأمان'),
        content: SizedBox(
          width: 450,
          height: 400,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'إعدادات الأمان والحماية:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 20),

              // Security Cards
              Card(
                child: ListTile(
                  leading: const Icon(Icons.security, color: Colors.green),
                  title: const Text('حالة الأمان'),
                  subtitle: const Text('النظام محمي بالكامل'),
                  trailing: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'آمن',
                      style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              Card(
                child: ListTile(
                  leading: const Icon(Icons.vpn_key, color: Colors.blue),
                  title: const Text('إدارة كلمات المرور'),
                  subtitle: const Text('تغيير وإعادة تعيين كلمات المرور'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.pop(context);
                    _showPasswordManagement();
                  },
                ),
              ),

              const SizedBox(height: 12),

              Card(
                child: ListTile(
                  leading: const Icon(Icons.history, color: Colors.orange),
                  title: const Text('سجل النشاطات'),
                  subtitle: const Text('عرض سجل العمليات والتسجيلات'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.pop(context);
                    _showActivityLog();
                  },
                ),
              ),

              const SizedBox(height: 12),

              Card(
                child: ListTile(
                  leading: const Icon(Icons.backup, color: Colors.purple),
                  title: const Text('النسخ الاحتياطي'),
                  subtitle: const Text('إنشاء وإدارة النسخ الاحتياطية'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.pop(context);
                    _showBackupOptions();
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _checkUpdates() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        title: Text('فحص التحديثات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري فحص التحديثات...'),
          ],
        ),
      ),
    );

    // Simulate update check
    Future.delayed(const Duration(seconds: 3), () {
      Navigator.pop(context);
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('نتيجة فحص التحديثات'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green),
                  SizedBox(width: 8),
                  Text('النسخة الحالية: 1.0.0'),
                ],
              ),
              const SizedBox(height: 8),
              const Row(
                children: [
                  Icon(Icons.info, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('أحدث نسخة متاحة: 1.0.0'),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.check, color: Colors.green),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'التطبيق محدث إلى أحدث نسخة',
                        style: TextStyle(color: Colors.green),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
    });
  }

  void _viewPerformanceStats() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات الأداء'),
        content: SizedBox(
          width: 500,
          height: 400,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'أداء النظام:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 16),

              // Performance Cards
              Card(
                color: Colors.blue[50],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(Icons.speed, color: Colors.blue, size: 32),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'سرعة الاستجابة',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '< 200ms',
                              style: TextStyle(color: Colors.blue[700], fontSize: 18),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.green[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'ممتاز',
                          style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 12),

              Card(
                color: Colors.green[50],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(Icons.memory, color: Colors.green, size: 32),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'استخدام الذاكرة',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '45% (180MB)',
                              style: TextStyle(color: Colors.green[700], fontSize: 18),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.green[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'جيد',
                          style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 12),

              Card(
                color: Colors.orange[50],
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(Icons.cloud_sync, color: Colors.orange, size: 32),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'حالة المزامنة',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'متصل - آخر مزامنة: الآن',
                              style: TextStyle(color: Colors.orange[700], fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.green[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'متصل',
                          style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم تحديث إحصائيات الأداء')),
              );
            },
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  void _addNewUser() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddUserScreen(),
      ),
    );
  }

  void _viewAllUsers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض جميع المستخدمين قيد التطوير')),
    );
  }

  void _exportUsersList() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تصدير قائمة المستخدمين قيد التطوير')),
    );
  }

  void _databaseSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات قاعدة البيانات قيد التطوير')),
    );
  }

  void _securitySettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات الأمان قيد التطوير')),
    );
  }

  void _notificationSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات الإشعارات قيد التطوير')),
    );
  }

  void _backupSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات النسخ الاحتياطي قيد التطوير')),
    );
  }

  void _showPasswordManagement() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدارة كلمات المرور'),
        content: const SizedBox(
          width: 300,
          height: 200,
          child: Column(
            children: [
              ListTile(
                leading: Icon(Icons.person),
                title: Text('تغيير كلمة مرور المدير'),
                trailing: Icon(Icons.arrow_forward_ios),
              ),
              ListTile(
                leading: Icon(Icons.group),
                title: Text('إعادة تعيين كلمات مرور الوكلاء'),
                trailing: Icon(Icons.arrow_forward_ios),
              ),
              ListTile(
                leading: Icon(Icons.security),
                title: Text('سياسة كلمات المرور'),
                trailing: Icon(Icons.arrow_forward_ios),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showActivityLog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('سجل النشاطات'),
        content: const SizedBox(
          width: 400,
          height: 300,
          child: Column(
            children: [
              Text('آخر النشاطات:'),
              SizedBox(height: 16),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      ListTile(
                        leading: Icon(Icons.login, color: Colors.green),
                        title: Text('تسجيل دخول - أحمد عابدين'),
                        subtitle: Text('منذ 5 دقائق'),
                      ),
                      ListTile(
                        leading: Icon(Icons.add, color: Colors.blue),
                        title: Text('إضافة صنف جديد'),
                        subtitle: Text('منذ 15 دقيقة'),
                      ),
                      ListTile(
                        leading: Icon(Icons.edit, color: Colors.orange),
                        title: Text('تعديل بيانات وكيل'),
                        subtitle: Text('منذ 30 دقيقة'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showBackupOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('النسخ الاحتياطي'),
        content: const SizedBox(
          width: 350,
          height: 250,
          child: Column(
            children: [
              ListTile(
                leading: Icon(Icons.backup, color: Colors.blue),
                title: Text('إنشاء نسخة احتياطية'),
                subtitle: Text('حفظ جميع البيانات'),
                trailing: Icon(Icons.arrow_forward_ios),
              ),
              ListTile(
                leading: Icon(Icons.restore, color: Colors.green),
                title: Text('استعادة من نسخة احتياطية'),
                subtitle: Text('استعادة البيانات المحفوظة'),
                trailing: Icon(Icons.arrow_forward_ios),
              ),
              ListTile(
                leading: Icon(Icons.schedule, color: Colors.orange),
                title: Text('النسخ التلقائي'),
                subtitle: Text('جدولة النسخ الاحتياطية'),
                trailing: Icon(Icons.arrow_forward_ios),
              ),
              ListTile(
                leading: Icon(Icons.cloud, color: Colors.purple),
                title: Text('النسخ السحابي'),
                subtitle: Text('حفظ في التخزين السحابي'),
                trailing: Icon(Icons.arrow_forward_ios),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
