@echo off
echo ========================================
echo     بناء APK لتطبيق آل فرحان
echo ========================================
echo.

echo [1/4] التحقق من Flutter...
flutter --version
if %errorlevel% neq 0 (
    echo خطأ: Flutter غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

echo.
echo [2/4] تنظيف المشروع...
flutter clean

echo.
echo [3/4] تحميل التبعيات...
flutter pub get

echo.
echo [4/4] بناء APK...
echo ملاحظة: قد يستغرق هذا عدة دقائق
echo.

flutter build apk --release

echo.
echo ========================================
echo تم بناء APK بنجاح!
echo ========================================
echo.
echo مكان الملف:
echo build\app\outputs\flutter-apk\app-release.apk
echo.
echo يمكنك الآن:
echo 1. نسخ الملف إلى هاتفك وتثبيته
echo 2. أو استخدام: adb install build\app\outputs\flutter-apk\app-release.apk
echo.

pause
