rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }
    
    function isSuperAdmin() {
      return isAuthenticated() && getUserRole() == 'super_admin';
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'admin';
    }
    
    function isAgent() {
      return isAuthenticated() && getUserRole() == 'agent';
    }
    
    function isShowroom() {
      return isAuthenticated() && getUserRole() == 'showroom';
    }
    
    function isAdminOrSuperAdmin() {
      return isSuperAdmin() || isAdmin();
    }
    
    function canManageUsers() {
      return isSuperAdmin();
    }
    
    function canManageInventory() {
      return isAdminOrSuperAdmin();
    }
    
    function canCreateInvoices() {
      return isAuthenticated();
    }
    
    function canViewReports() {
      return isAdminOrSuperAdmin();
    }
    
    function getUserWarehouseId() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.warehouseId;
    }
    
    function isOwnerOfWarehouse(warehouseId) {
      return getUserWarehouseId() == warehouseId;
    }
    
    // Users collection rules
    match /users/{userId} {
      allow read: if isAuthenticated() && (
        isSuperAdmin() || 
        isAdmin() || 
        request.auth.uid == userId
      );
      
      allow create: if canManageUsers();
      
      allow update: if canManageUsers() || (
        request.auth.uid == userId && 
        !('role' in request.resource.data.diff(resource.data).affectedKeys())
      );
      
      allow delete: if canManageUsers();
    }
    
    // Warehouses collection rules
    match /warehouses/{warehouseId} {
      allow read: if isAuthenticated() && (
        isAdminOrSuperAdmin() ||
        isOwnerOfWarehouse(warehouseId) ||
        resource.data.type == 'showroom'
      );
      
      allow create, update, delete: if canManageInventory();
    }
    
    // Items collection rules
    match /items/{itemId} {
      allow read: if isAuthenticated() && (
        isAdminOrSuperAdmin() ||
        isOwnerOfWarehouse(resource.data.currentWarehouseId) ||
        resource.data.currentWarehouseId == 'showroom'
      );
      
      allow create: if canManageInventory();
      
      allow update: if canManageInventory() || (
        isAuthenticated() && 
        isOwnerOfWarehouse(resource.data.currentWarehouseId) &&
        // Agents can only update status to 'sold' and add sale info
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'updatedAt'])
      );
      
      allow delete: if canManageInventory();
    }
    
    // Invoices collection rules
    match /invoices/{invoiceId} {
      allow read: if isAuthenticated() && (
        isAdminOrSuperAdmin() ||
        resource.data.createdBy == request.auth.uid ||
        (isAgent() && resource.data.agentId == request.auth.uid)
      );
      
      allow create: if canCreateInvoices() && (
        request.resource.data.createdBy == request.auth.uid
      );
      
      allow update: if isAdminOrSuperAdmin() || (
        resource.data.createdBy == request.auth.uid &&
        resource.data.status == 'pending'
      );
      
      allow delete: if isAdminOrSuperAdmin();
    }
    
    // Payments collection rules
    match /payments/{paymentId} {
      allow read: if isAuthenticated() && (
        isAdminOrSuperAdmin() ||
        resource.data.agentId == request.auth.uid ||
        resource.data.createdBy == request.auth.uid
      );
      
      allow create: if isAuthenticated() && (
        request.resource.data.createdBy == request.auth.uid
      );
      
      allow update: if isAdminOrSuperAdmin() || (
        resource.data.createdBy == request.auth.uid &&
        resource.data.status == 'pending'
      );
      
      allow delete: if isAdminOrSuperAdmin();
    }
    
    // Document tracking collection rules
    match /document_tracking/{trackingId} {
      allow read: if isAuthenticated() && (
        isAdminOrSuperAdmin() ||
        // Agents can see tracking for items sold from their warehouse
        exists(/databases/$(database)/documents/invoices/$(resource.data.invoiceId)) &&
        get(/databases/$(database)/documents/invoices/$(resource.data.invoiceId)).data.agentId == request.auth.uid
      );
      
      allow create: if canCreateInvoices();
      
      allow update: if isAdminOrSuperAdmin() || (
        // Agents can update status to 'ready_for_pickup' only
        isAgent() &&
        request.resource.data.currentStatus == 'ready_for_pickup' &&
        resource.data.currentStatus == 'sent_to_sale_point'
      );
      
      allow delete: if isAdminOrSuperAdmin();
    }
    
    // Company settings (only super admin can modify)
    match /settings/{settingId} {
      allow read: if isAuthenticated();
      allow write: if isSuperAdmin();
    }
    
    // Reports collection (read-only, generated by cloud functions)
    match /reports/{reportId} {
      allow read: if canViewReports();
      allow write: if false; // Only cloud functions can write reports
    }
    
    // Audit logs (read-only for admins, write by cloud functions)
    match /audit_logs/{logId} {
      allow read: if isAdminOrSuperAdmin();
      allow write: if false; // Only cloud functions can write logs
    }
    
    // Sync metadata (for offline sync management)
    match /sync_metadata/{metadataId} {
      allow read, write: if isAuthenticated();
    }
  }
}
