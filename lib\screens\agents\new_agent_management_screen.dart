import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_financial_account_model.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/agent_financial_service.dart';
import '../../services/data_service.dart';
import 'new_agent_account_screen.dart';
import 'record_payment_screen_new.dart';

/// شاشة إدارة الوكلاء الجديدة والمحسنة
/// تعرض حسابات الوكلاء بدقة وإحصائيات صحيحة
class NewAgentManagementScreen extends StatefulWidget {
  const NewAgentManagementScreen({super.key});

  @override
  State<NewAgentManagementScreen> createState() => _NewAgentManagementScreenState();
}

class _NewAgentManagementScreenState extends State<NewAgentManagementScreen> {
  final AgentFinancialService _financialService = AgentFinancialService();
  final DataService _dataService = DataService.instance;
  
  List<UserModel> _agents = [];
  List<AgentFinancialAccount> _agentAccounts = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات الأساسية
  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      if (kDebugMode) {
        print('🔄 Loading agents and financial accounts...');
      }

      // تحميل جميع الوكلاء
      final allUsers = await _dataService.getUsers();
      _agents = allUsers.where((user) => user.role == 'agent').toList();
      
      // تحميل جميع الحسابات المالية
      _agentAccounts = await _financialService.getAllAgentAccounts();
      
      // إنشاء حسابات للوكلاء الذين لا يملكون حسابات
      await _ensureAllAgentsHaveAccounts();
      
      // تحميل الحسابات مرة أخرى بعد الإنشاء
      _agentAccounts = await _financialService.getAllAgentAccounts();
      
      // حساب الإحصائيات
      _statistics = await _financialService.getAgentsStatistics();

      if (kDebugMode) {
        print('✅ Loaded ${_agents.length} agents and ${_agentAccounts.length} accounts');
        print('📊 Statistics: $_statistics');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading data: $e');
      }
      setState(() {
        _error = 'خطأ في تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  /// التأكد من وجود حسابات لجميع الوكلاء
  Future<void> _ensureAllAgentsHaveAccounts() async {
    final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
    if (currentUser == null) return;

    for (final agent in _agents) {
      final existingAccount = _agentAccounts.firstWhere(
        (account) => account.agentId == agent.id,
        orElse: () => AgentFinancialAccount(
          id: '',
          agentId: '',
          agentName: '',
          agentPhone: '',
          totalGoodsReceived: 0,
          totalGoodsReturned: 0,
          totalCustomerSales: 0,
          totalAgentProfits: 0,
          totalCompanyProfits: 0,
          totalPaymentsReceived: 0,
          currentDebt: 0,
          currentBalance: 0,
          transactions: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: '',
          lastUpdatedBy: '',
        ),
      );

      if (existingAccount.id.isEmpty) {
        try {
          await _financialService.createOrUpdateAgentAccount(
            agentId: agent.id,
            agentName: agent.fullName,
            agentPhone: agent.phone,
            userId: currentUser.id,
          );
          
          if (kDebugMode) {
            print('✅ Created account for agent: ${agent.fullName}');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error creating account for ${agent.fullName}: $e');
          }
        }
      }
    }
  }

  /// إعادة حساب جميع الحسابات
  Future<void> _recalculateAllAccounts() async {
    try {
      final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
      if (currentUser == null) return;

      setState(() {
        _isLoading = true;
      });

      await _financialService.recalculateAllAgentAccounts(currentUser.id);
      await _loadData();

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إعادة حساب جميع الحسابات بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إعادة الحساب: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('إدارة حسابات الوكلاء'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.calculate),
            onPressed: _recalculateAllAccounts,
            tooltip: 'إعادة حساب جميع الحسابات',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل بيانات الوكلاء...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(_error!, textAlign: TextAlign.center),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatisticsCards(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildAgentsSection(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات عامة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: AppConstants.smallPadding),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppConstants.smallPadding,
          mainAxisSpacing: AppConstants.smallPadding,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'إجمالي الوكلاء',
              '${_statistics['totalAgents'] ?? 0}',
              Icons.people,
              Colors.blue,
            ),
            _buildStatCard(
              'الوكلاء النشطون',
              '${_statistics['activeAgents'] ?? 0}',
              Icons.person,
              Colors.green,
            ),
            _buildStatCard(
              'إجمالي المديونية',
              AppUtils.formatCurrency(_statistics['totalDebt'] ?? 0.0),
              Icons.trending_up,
              Colors.red,
            ),
            _buildStatCard(
              'إجمالي الأرصدة الدائنة',
              AppUtils.formatCurrency(_statistics['totalCredit'] ?? 0.0),
              Icons.trending_down,
              Colors.orange,
            ),
            _buildStatCard(
              'إجمالي المبيعات',
              AppUtils.formatCurrency(_statistics['totalSales'] ?? 0.0),
              Icons.shopping_cart,
              Colors.purple,
            ),
            _buildStatCard(
              'إجمالي الأرباح',
              AppUtils.formatCurrency(_statistics['totalProfits'] ?? 0.0),
              Icons.monetization_on,
              Colors.teal,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.smallPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الوكلاء
  Widget _buildAgentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'قائمة الوكلاء',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            Text(
              '${_agentAccounts.length} وكيل',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.smallPadding),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _agentAccounts.length,
          itemBuilder: (context, index) {
            final account = _agentAccounts[index];
            final agent = _agents.firstWhere(
              (a) => a.id == account.agentId,
              orElse: () => UserModel(
                id: account.agentId,
                fullName: account.agentName,
                username: '',
                email: '',
                phone: account.agentPhone,
                role: 'agent',
                isActive: false,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            );
            
            return _buildAgentCard(agent, account);
          },
        ),
      ],
    );
  }

  /// بناء بطاقة الوكيل
  Widget _buildAgentCard(UserModel agent, AgentFinancialAccount account) {
    final isDebt = account.currentBalance > 0;
    final isCredit = account.currentBalance < 0;
    final balanceColor = isDebt ? Colors.red : isCredit ? Colors.green : Colors.grey;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      elevation: 2,
      child: InkWell(
        onTap: () => _openAgentAccount(agent, account),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات الوكيل الأساسية
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Theme.of(context).primaryColor,
                    child: Text(
                      agent.fullName.isNotEmpty ? agent.fullName[0] : 'و',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          agent.fullName,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (agent.phone.isNotEmpty)
                          Text(
                            agent.phone,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),
                  // حالة النشاط
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: agent.isActive ? Colors.green : Colors.grey,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      agent.isActive ? 'نشط' : 'غير نشط',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.smallPadding),
              const Divider(),

              // الملخص المالي
              Row(
                children: [
                  Expanded(
                    child: _buildFinancialSummaryItem(
                      'الرصيد الحالي',
                      AppUtils.formatCurrency(account.currentBalance.abs()),
                      balanceColor,
                      isDebt ? 'مدين' : isCredit ? 'دائن' : 'متوازن',
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: _buildFinancialSummaryItem(
                      'إجمالي المبيعات',
                      AppUtils.formatCurrency(account.totalCustomerSales),
                      Colors.blue,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.smallPadding),

              Row(
                children: [
                  Expanded(
                    child: _buildFinancialSummaryItem(
                      'البضاعة المستلمة',
                      AppUtils.formatCurrency(account.totalGoodsReceived),
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: _buildFinancialSummaryItem(
                      'المدفوعات',
                      AppUtils.formatCurrency(account.totalPaymentsReceived),
                      Colors.green,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.smallPadding),

              // أزرار العمليات
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildActionButton(
                    'كشف الحساب',
                    Icons.account_balance_wallet,
                    () => _openAgentAccount(agent, account),
                    Colors.blue,
                  ),
                  _buildActionButton(
                    'تسجيل دفعة',
                    Icons.payment,
                    () => _recordPayment(agent, account),
                    Colors.green,
                  ),
                  _buildActionButton(
                    'إعادة حساب',
                    Icons.calculate,
                    () => _recalculateAgentAccount(agent, account),
                    Colors.orange,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر ملخص مالي
  Widget _buildFinancialSummaryItem(
    String label,
    String value,
    Color color, [
    String? subtitle,
  ]) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        if (subtitle != null)
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontSize: 10,
            ),
          ),
      ],
    );
  }

  /// بناء زر عملية
  Widget _buildActionButton(
    String label,
    IconData icon,
    VoidCallback onPressed,
    Color color,
  ) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton.icon(
          onPressed: onPressed,
          icon: Icon(icon, size: 16),
          label: Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }

  /// فتح كشف حساب الوكيل
  void _openAgentAccount(UserModel agent, AgentFinancialAccount account) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NewAgentAccountScreen(
          agent: agent,
          account: account,
        ),
      ),
    ).then((_) => _loadData()); // تحديث البيانات عند العودة
  }

  /// تسجيل دفعة جديدة
  void _recordPayment(UserModel agent, AgentFinancialAccount account) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RecordPaymentScreen(
          agent: agent,
          currentBalance: account.currentBalance,
        ),
      ),
    ).then((_) => _loadData()); // تحديث البيانات عند العودة
  }

  /// إعادة حساب حساب وكيل محدد
  Future<void> _recalculateAgentAccount(UserModel agent, AgentFinancialAccount account) async {
    try {
      final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
      if (currentUser == null) return;

      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إعادة حساب الحساب...'),
            ],
          ),
        ),
      );

      await _financialService.recalculateAgentAccount(agent.id, currentUser.id);

      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
        AppUtils.showSnackBar(context, 'تم إعادة حساب حساب ${agent.fullName} بنجاح');
        await _loadData(); // تحديث البيانات
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
        AppUtils.showSnackBar(context, 'خطأ في إعادة الحساب: $e', isError: true);
      }
    }
  }
}
