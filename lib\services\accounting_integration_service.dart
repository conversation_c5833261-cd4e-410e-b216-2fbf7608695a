import 'package:flutter/foundation.dart';

// import '../models/invoice_model.dart'; // Disabled for Windows
// import '../models/payment_model.dart'; // Disabled for Windows
import 'accounting_journal_service.dart';

/// خدمة ربط العمليات التجارية بالنظام المحاسبي (مبسطة)
class AccountingIntegrationService {
  static AccountingIntegrationService? _instance;
  static AccountingIntegrationService get instance => _instance ??= AccountingIntegrationService._();
  
  AccountingIntegrationService._();

  final AccountingJournalService _journalService = AccountingJournalService.instance;

  /// إنشاء قيد محاسبي من فاتورة بيع (معطل للWindows)
  Future<void> createJournalEntryFromInvoice(dynamic invoice) async {
    try {
      if (kDebugMode) print('🧾 Creating journal entry from invoice: ${invoice.invoiceNumber}');

      // إنشاء قيد بسيط للبيع
      await _journalService.createJournalEntry(
        date: invoice.createdAt,
        description: 'بيع قطعة غيار - فاتورة ${invoice.invoiceNumber}',
        reference: 'INV-${invoice.invoiceNumber}',
        lines: [], // سيتم إضافة البنود لاحقاً
      );

      if (kDebugMode) print('✅ Journal entry created for invoice: ${invoice.invoiceNumber}');
    } catch (e) {
      if (kDebugMode) print('❌ Error creating journal entry from invoice: $e');
    }
  }

  /// إنشاء قيد محاسبي من دفعة وكيل (معطل للWindows)
  Future<void> createJournalEntryFromPayment(dynamic payment) async {
    try {
      if (kDebugMode) print('💰 Creating journal entry from payment: ${payment.receiptNumber}');

      // إنشاء قيد بسيط للدفعة
      await _journalService.createJournalEntry(
        date: payment.createdAt,
        description: 'دفعة من وكيل - سند ${payment.receiptNumber}',
        reference: 'PAY-${payment.receiptNumber}',
        lines: [], // سيتم إضافة البنود لاحقاً
      );

      if (kDebugMode) print('✅ Journal entry created for payment: ${payment.receiptNumber}');
    } catch (e) {
      if (kDebugMode) print('❌ Error creating journal entry from payment: $e');
    }
  }

  /// إنشاء قيد محاسبي من مصروف عام (مبسط)
  Future<void> createGeneralExpenseEntry({
    required String description,
    required double amount,
    required String expenseType,
    required DateTime expenseDate,
    String? reference,
  }) async {
    try {
      if (kDebugMode) print('💸 Creating general expense entry: $description');

      // إنشاء قيد بسيط للمصروف
      await _journalService.createJournalEntry(
        date: expenseDate,
        description: description,
        reference: reference ?? 'EXP-${DateTime.now().millisecondsSinceEpoch}',
        lines: [], // سيتم إضافة البنود لاحقاً
      );

      if (kDebugMode) print('✅ General expense entry created: $description');
    } catch (e) {
      if (kDebugMode) print('❌ Error creating general expense entry: $e');
    }
  }

  /// الحصول على إحصائيات مالية سريعة
  Map<String, dynamic> getFinancialSummary() {
    try {
      final statistics = _journalService.generateStatistics();
      
      return {
        'totalEntries': statistics.totalEntries,
        'totalDebits': statistics.totalDebits,
        'totalCredits': statistics.totalCredits,
        'isBalanced': statistics.isBalanced,
        'lastEntryDate': statistics.lastEntryDate,
        'lastEntryNumber': statistics.lastEntryNumber,
        'draftEntries': statistics.draftEntries,
        'postedEntries': statistics.postedEntries,
      };
    } catch (e) {
      if (kDebugMode) print('❌ Error getting financial summary: $e');
      return {};
    }
  }

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      if (!_journalService.isInitialized) {
        await _journalService.initialize();
      }
      if (kDebugMode) print('✅ Accounting Integration Service initialized');
    } catch (e) {
      if (kDebugMode) print('❌ Error initializing Accounting Integration Service: $e');
    }
  }
}
