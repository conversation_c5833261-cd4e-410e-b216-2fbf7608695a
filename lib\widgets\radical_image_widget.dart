import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../core/network/radical_ssl_bypass.dart';
import '../core/theme/desktop_theme.dart';
import '../utils/image_url_fixer.dart';

/// Radical image widget that bypasses ALL SSL issues
class RadicalImageWidget extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;
  final String? tooltip;

  const RadicalImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.onTap,
    this.tooltip,
  });

  @override
  State<RadicalImageWidget> createState() => _RadicalImageWidgetState();
}

class _RadicalImageWidgetState extends State<RadicalImageWidget> {
  Uint8List? _imageBytes;
  bool _isLoading = true;
  String? _error;
  final RadicalSSLBypass _radicalBypass = RadicalSSLBypass();

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(RadicalImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    // Fix and validate URL
    final fixedUrl = ImageUrlFixer.getSafeUrl(widget.imageUrl);

    if (fixedUrl.isEmpty) {
      if (kDebugMode) {
        print('❌ RADICAL IMAGE: Invalid or empty URL: ${widget.imageUrl}');
      }
      setState(() {
        _isLoading = false;
        _error = 'Invalid URL';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
      _imageBytes = null;
    });

    try {
      if (kDebugMode) {
        print('🔓 RADICAL IMAGE: Loading fixed URL: $fixedUrl');
        if (fixedUrl != widget.imageUrl) {
          print('🔧 RADICAL IMAGE: Original URL: ${widget.imageUrl}');
        }
      }

      final bytes = await _radicalBypass.downloadImage(fixedUrl);
      
      if (mounted) {
        setState(() {
          _imageBytes = bytes;
          _isLoading = false;
          _error = bytes == null ? 'Failed to download' : null;
        });
      }

      if (kDebugMode) {
        if (bytes != null) {
          print('✅ RADICAL IMAGE: Loaded successfully (${bytes.length} bytes)');
        } else {
          print('❌ RADICAL IMAGE: Failed to load');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ RADICAL IMAGE: Error loading ${widget.imageUrl} - $e');
      }
      
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = e.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child;

    if (_isLoading) {
      child = _buildLoadingWidget();
    } else if (_error != null || _imageBytes == null) {
      child = _buildErrorWidget();
    } else {
      child = Image.memory(
        _imageBytes!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        errorBuilder: (context, error, stackTrace) {
          if (kDebugMode) {
            print('❌ RADICAL IMAGE: Memory image error - $error');
          }
          return _buildErrorWidget();
        },
      );
    }

    // Apply border radius
    if (widget.borderRadius != null) {
      child = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: child,
      );
    }

    // Apply tap handler
    if (widget.onTap != null) {
      child = InkWell(
        onTap: widget.onTap,
        child: child,
      );
    }

    // Apply tooltip
    if (widget.tooltip != null) {
      child = Tooltip(
        message: widget.tooltip!,
        child: child,
      );
    }

    return child;
  }

  Widget _buildLoadingWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: DesktopTheme.backgroundTertiary,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(strokeWidth: 2),
            SizedBox(height: 8),
            Text(
              'جاري التحميل...',
              style: TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: DesktopTheme.surfaceMedium,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image_outlined,
            size: (widget.width ?? 80) * 0.3,
            color: DesktopTheme.textTertiary,
          ),
          const SizedBox(height: 4),
          Text(
            'فشل التحميل',
            style: DesktopTheme.bodySmall.copyWith(
              color: DesktopTheme.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          TextButton(
            onPressed: _loadImage,
            child: const Text(
              'إعادة المحاولة',
              style: TextStyle(fontSize: 10),
            ),
          ),
          if (kDebugMode && _error != null) ...[
            const SizedBox(height: 2),
            Text(
              _error!,
              style: DesktopTheme.bodySmall.copyWith(
                color: DesktopTheme.statusError,
                fontSize: 8,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}

/// Specialized radical image widget for item thumbnails
class RadicalItemImageWidget extends StatelessWidget {
  final String imageUrl;
  final double size;
  final VoidCallback? onTap;

  const RadicalItemImageWidget({
    super.key,
    required this.imageUrl,
    this.size = 80,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return RadicalImageWidget(
      imageUrl: imageUrl,
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
      onTap: onTap,
      tooltip: 'انقر لعرض الصورة بحجم أكبر',
    );
  }
}

/// Radical image gallery widget
class RadicalImageGallery extends StatelessWidget {
  final List<String> imageUrls;
  final double itemSize;
  final int maxItems;

  const RadicalImageGallery({
    super.key,
    required this.imageUrls,
    this.itemSize = 100,
    this.maxItems = 4,
  });

  @override
  Widget build(BuildContext context) {
    final displayUrls = imageUrls.take(maxItems).toList();
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: displayUrls.map((url) => RadicalItemImageWidget(
        imageUrl: url,
        size: itemSize,
        onTap: () => _showFullImage(context, url),
      )).toList(),
    );
  }

  void _showFullImage(BuildContext context, String imageUrl) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800, maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppBar(
                title: const Text('عرض الصورة'),
                automaticallyImplyLeading: false,
                actions: [
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              Expanded(
                child: RadicalImageWidget(
                  imageUrl: imageUrl,
                  fit: BoxFit.contain,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
