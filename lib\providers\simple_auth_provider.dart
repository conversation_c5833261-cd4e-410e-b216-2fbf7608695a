import 'package:flutter/foundation.dart';
import '../models/user_model.dart';

class SimpleAuthProvider extends ChangeNotifier {
  UserModel? _currentUser;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _currentUser != null;

  // Role-based getters
  bool get isSuperAdmin => _currentUser?.role == 'super_admin';
  bool get isAdmin => _currentUser?.role == 'admin';
  bool get isAgent => _currentUser?.role == 'agent';
  bool get isShowroom => _currentUser?.role == 'showroom';

  // Permission getters
  bool get canManageUsers => isSuperAdmin;
  bool get canManageInventory => isSuperAdmin || isAdmin;
  bool get canCreateInvoices => isSuperAdmin || isAdmin || isAgent || isShowroom;
  bool get canViewReports => isSuperAdmin || isAdmin;
  bool get canManageAgents => isSuperAdmin || isAdmin;

  // Simple login method (no Firebase)
  Future<bool> signIn(String username, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Simple hardcoded authentication for demo
      if (username == 'ahmed' && password == 'admin123') {
        _currentUser = UserModel(
          id: '1',
          username: 'ahmed',
          fullName: 'أحمد محمد',
          email: '<EMAIL>',
          phone: '01234567890',
          role: 'super_admin',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        if (kDebugMode) {
          print('✅ Login successful: ${_currentUser!.fullName}');
        }
        
        notifyListeners();
        return true;
      } else if (username == 'agent1' && password == '123456') {
        _currentUser = UserModel(
          id: '2',
          username: 'agent1',
          fullName: 'محمد أحمد',
          email: '<EMAIL>',
          phone: '01234567891',
          role: 'agent',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        if (kDebugMode) {
          print('✅ Login successful: ${_currentUser!.fullName}');
        }
        
        notifyListeners();
        return true;
      } else {
        _setError('اسم المستخدم أو كلمة المرور غير صحيحة');
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تسجيل الدخول: $e');
      if (kDebugMode) {
        print('❌ Login error: $e');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Sign out method
  Future<void> signOut() async {
    _setLoading(true);
    
    try {
      _currentUser = null;
      _clearError();
      
      if (kDebugMode) {
        print('✅ User signed out successfully');
      }
      
      notifyListeners();
    } catch (e) {
      _setError('حدث خطأ أثناء تسجيل الخروج: $e');
      if (kDebugMode) {
        print('❌ Sign out error: $e');
      }
    } finally {
      _setLoading(false);
    }
  }

  // Auto login for demo
  Future<void> autoLogin() async {
    if (kDebugMode) {
      print('🔄 Attempting auto login...');
    }
    
    // For demo purposes, auto login as super admin
    await signIn('ahmed', 'admin123');
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Check if user has specific permission
  bool hasPermission(String permission) {
    if (_currentUser == null) return false;
    
    switch (permission) {
      case 'manage_users':
        return canManageUsers;
      case 'manage_inventory':
        return canManageInventory;
      case 'create_invoices':
        return canCreateInvoices;
      case 'view_reports':
        return canViewReports;
      case 'manage_agents':
        return canManageAgents;
      default:
        return false;
    }
  }

  // Get user display name
  String get userDisplayName {
    return _currentUser?.fullName ?? 'مستخدم';
  }

  // Get user role display name
  String get userRoleDisplayName {
    switch (_currentUser?.role) {
      case 'super_admin':
        return 'المدير الأعلى';
      case 'admin':
        return 'مدير';
      case 'agent':
        return 'وكيل';
      case 'showroom':
        return 'صالة عرض';
      default:
        return 'مستخدم';
    }
  }
}
