import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

/// Custom HTTP client that bypasses SSL certificate verification for Cloudinary
class CustomHttpClient {
  static final CustomHttpClient _instance = CustomHttpClient._internal();
  factory CustomHttpClient() => _instance;
  CustomHttpClient._internal();

  late final http.Client _client;
  bool _initialized = false;

  /// Initialize the custom HTTP client
  void initialize() {
    if (_initialized) return;

    if (kDebugMode) {
      print('🔧 Initializing custom HTTP client for SSL bypass...');
    }

    // Create HttpClient with SSL bypass
    final httpClient = HttpClient();
    
    // Bypass SSL certificate verification
    httpClient.badCertificateCallback = (X509Certificate cert, String host, int port) {
      if (kDebugMode) {
        print('🔓 Custom HTTP Client: Bypassing SSL for $host:$port');
      }
      
      // Allow all Cloudinary domains
      final cloudinaryDomains = [
        'cloudinary.com',
        'res.cloudinary.com',
        'api.cloudinary.com',
      ];
      
      final isCloudinary = cloudinaryDomains.any((domain) => host.contains(domain));
      
      if (isCloudinary) {
        if (kDebugMode) {
          print('✅ Allowing Cloudinary SSL certificate for $host');
        }
        return true;
      }
      
      // For other domains, use default behavior
      return false;
    };

    // Set timeouts
    httpClient.connectionTimeout = const Duration(seconds: 30);
    httpClient.idleTimeout = const Duration(seconds: 30);

    // Create IOClient wrapper
    _client = IOClient(httpClient);
    _initialized = true;

    if (kDebugMode) {
      print('✅ Custom HTTP client initialized successfully');
    }
  }

  /// Get the HTTP client instance
  http.Client get client {
    if (!_initialized) {
      initialize();
    }
    return _client;
  }

  /// Test connection to Cloudinary
  Future<bool> testCloudinaryConnection() async {
    try {
      if (kDebugMode) {
        print('🧪 Testing Cloudinary connection...');
      }

      final response = await client.get(
        Uri.parse('https://res.cloudinary.com/demo/image/upload/sample.jpg'),
        headers: {
          'User-Agent': 'El-Farhan-Desktop/1.0',
          'Accept': 'image/*',
        },
      ).timeout(const Duration(seconds: 10));

      final success = response.statusCode == 200;
      
      if (kDebugMode) {
        print('🧪 Cloudinary connection test: ${success ? 'SUCCESS' : 'FAILED'}');
        print('🧪 Status code: ${response.statusCode}');
        print('🧪 Content length: ${response.contentLength}');
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Cloudinary connection test failed: $e');
      }
      return false;
    }
  }

  /// Download image with custom client
  Future<List<int>?> downloadImage(String imageUrl) async {
    try {
      if (kDebugMode) {
        print('📥 Downloading image with custom client: $imageUrl');
      }

      final response = await client.get(
        Uri.parse(imageUrl),
        headers: {
          'User-Agent': 'El-Farhan-Desktop/1.0',
          'Accept': 'image/*',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        if (kDebugMode) {
          print('✅ Image downloaded successfully: ${response.bodyBytes.length} bytes');
        }
        return response.bodyBytes;
      } else {
        if (kDebugMode) {
          print('❌ Failed to download image: ${response.statusCode}');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error downloading image: $e');
      }
      return null;
    }
  }

  /// Dispose the client
  void dispose() {
    if (_initialized) {
      _client.close();
      _initialized = false;
      if (kDebugMode) {
        print('🗑️ Custom HTTP client disposed');
      }
    }
  }
}

/// HTTP overrides specifically for image loading
class ImageHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final client = super.createHttpClient(context);
    
    // Completely bypass SSL verification for images
    client.badCertificateCallback = (cert, host, port) {
      if (kDebugMode) {
        print('🖼️ Image HTTP Override: Bypassing SSL for $host:$port');
      }
      return true; // Allow all certificates for image loading
    };
    
    // Set reasonable timeouts
    client.connectionTimeout = const Duration(seconds: 30);
    client.idleTimeout = const Duration(seconds: 30);
    
    return client;
  }
}

/// Initialize global HTTP overrides for images
void initializeImageHttpOverrides() {
  if (kDebugMode) {
    print('🔧 Setting up global HTTP overrides for image loading...');
  }
  
  // Set global HTTP overrides for image loading
  HttpOverrides.global = ImageHttpOverrides();
  
  // Initialize custom HTTP client
  CustomHttpClient().initialize();
  
  if (kDebugMode) {
    print('✅ Image HTTP overrides initialized');
  }
}
