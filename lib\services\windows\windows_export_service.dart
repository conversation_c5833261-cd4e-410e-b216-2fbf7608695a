import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:convert';

/// خدمة التصدير المحسنة للويندوز - بديل لـ PDF
class WindowsExportService {
  static final WindowsExportService _instance = WindowsExportService._internal();
  factory WindowsExportService() => _instance;
  WindowsExportService._internal();

  static WindowsExportService get instance => _instance;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (kDebugMode) {
      print('🔄 Initializing WindowsExportService...');
    }
    
    try {
      if (kDebugMode) {
        print('✅ WindowsExportService initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize WindowsExportService: $e');
      }
    }
  }

  /// تصدير البيانات إلى Excel
  Future<File?> exportToExcel({
    required String title,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    String? fileName,
  }) async {
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1'];

      // إضافة العنوان
      sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue(title);

      // دمج خلايا العنوان
      if (headers.isNotEmpty) {
        final endColumn = String.fromCharCode(65 + headers.length - 1);
        sheet.merge(CellIndex.indexByString('A1'), CellIndex.indexByString('${endColumn}1'));
      }

      // إضافة الرؤوس
      for (int i = 0; i < headers.length; i++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 2));
        cell.value = TextCellValue(headers[i]);

        // تنسيق الرؤوس
        cell.cellStyle = CellStyle(
          bold: true,
          backgroundColorHex: ExcelColor.blue50,
          fontColorHex: ExcelColor.black,
        );
      }

      // إضافة البيانات
      for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
        final row = data[rowIndex];
        for (int colIndex = 0; colIndex < headers.length; colIndex++) {
          final key = headers[colIndex];
          final value = row[key]?.toString() ?? '';
          final cell = sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: rowIndex + 3,
          ));
          cell.value = TextCellValue(value);

          // تنسيق البيانات
          if (rowIndex % 2 == 0) {
            cell.cellStyle = CellStyle(
              backgroundColorHex: ExcelColor.gray25,
            );
          }
        }
      }

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/${fileName ?? 'export_${DateTime.now().millisecondsSinceEpoch}.xlsx'}';
      final file = File(filePath);
      
      final bytes = excel.encode();
      if (bytes != null) {
        await file.writeAsBytes(bytes);
        return file;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error exporting to Excel: $e');
      }
      return null;
    }
  }

  /// تصدير البيانات إلى CSV
  Future<File?> exportToCSV({
    required String title,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    String? fileName,
  }) async {
    try {
      final buffer = StringBuffer();
      
      // إضافة العنوان
      buffer.writeln(title);
      buffer.writeln();
      
      // إضافة الرؤوس
      buffer.writeln(headers.join(','));
      
      // إضافة البيانات
      for (final row in data) {
        final values = headers.map((header) => row[header]?.toString() ?? '').toList();
        buffer.writeln(values.join(','));
      }

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/${fileName ?? 'export_${DateTime.now().millisecondsSinceEpoch}.csv'}';
      final file = File(filePath);
      
      await file.writeAsString(buffer.toString(), encoding: utf8);
      return file;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error exporting to CSV: $e');
      }
      return null;
    }
  }

  /// تصدير البيانات إلى HTML (يمكن طباعته)
  Future<File?> exportToHTML({
    required String title,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    String? fileName,
  }) async {
    try {
      final buffer = StringBuffer();
      
      // HTML structure
      buffer.writeln('<!DOCTYPE html>');
      buffer.writeln('<html dir="rtl" lang="ar">');
      buffer.writeln('<head>');
      buffer.writeln('<meta charset="UTF-8">');
      buffer.writeln('<title>$title</title>');
      buffer.writeln('<style>');
      buffer.writeln('body { font-family: Arial, sans-serif; margin: 20px; }');
      buffer.writeln('h1 { text-align: center; color: #2E7D32; }');
      buffer.writeln('table { width: 100%; border-collapse: collapse; margin-top: 20px; }');
      buffer.writeln('th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }');
      buffer.writeln('th { background-color: #f2f2f2; font-weight: bold; }');
      buffer.writeln('tr:nth-child(even) { background-color: #f9f9f9; }');
      buffer.writeln('@media print { body { margin: 0; } }');
      buffer.writeln('</style>');
      buffer.writeln('</head>');
      buffer.writeln('<body>');
      
      // Title
      buffer.writeln('<h1>$title</h1>');
      
      // Table
      buffer.writeln('<table>');
      
      // Headers
      buffer.writeln('<thead><tr>');
      for (final header in headers) {
        buffer.writeln('<th>$header</th>');
      }
      buffer.writeln('</tr></thead>');
      
      // Data
      buffer.writeln('<tbody>');
      for (final row in data) {
        buffer.writeln('<tr>');
        for (final header in headers) {
          final value = row[header]?.toString() ?? '';
          buffer.writeln('<td>$value</td>');
        }
        buffer.writeln('</tr>');
      }
      buffer.writeln('</tbody>');
      
      buffer.writeln('</table>');
      buffer.writeln('</body>');
      buffer.writeln('</html>');

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final filePath = '${directory.path}/${fileName ?? 'export_${DateTime.now().millisecondsSinceEpoch}.html'}';
      final file = File(filePath);
      
      await file.writeAsString(buffer.toString(), encoding: utf8);
      return file;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error exporting to HTML: $e');
      }
      return null;
    }
  }

  /// تصدير تقرير المبيعات
  Future<File?> exportSalesReport({
    required List<Map<String, dynamic>> salesData,
    required DateTime startDate,
    required DateTime endDate,
    String format = 'excel', // excel, csv, html
  }) async {
    final title = 'تقرير المبيعات من ${_formatDate(startDate)} إلى ${_formatDate(endDate)}';
    final headers = ['رقم الفاتورة', 'التاريخ', 'العميل', 'المبلغ', 'الحالة'];
    final fileName = 'sales_report_${DateTime.now().millisecondsSinceEpoch}';

    switch (format.toLowerCase()) {
      case 'csv':
        return await exportToCSV(
          title: title,
          data: salesData,
          headers: headers,
          fileName: '$fileName.csv',
        );
      case 'html':
        return await exportToHTML(
          title: title,
          data: salesData,
          headers: headers,
          fileName: '$fileName.html',
        );
      default:
        return await exportToExcel(
          title: title,
          data: salesData,
          headers: headers,
          fileName: '$fileName.xlsx',
        );
    }
  }

  /// تصدير تقرير المخزون
  Future<File?> exportInventoryReport({
    required List<Map<String, dynamic>> inventoryData,
    String format = 'excel',
  }) async {
    final title = 'تقرير المخزون - ${_formatDate(DateTime.now())}';
    final headers = ['كود الصنف', 'اسم الصنف', 'الكمية', 'السعر', 'القيمة الإجمالية'];
    final fileName = 'inventory_report_${DateTime.now().millisecondsSinceEpoch}';

    switch (format.toLowerCase()) {
      case 'csv':
        return await exportToCSV(
          title: title,
          data: inventoryData,
          headers: headers,
          fileName: '$fileName.csv',
        );
      case 'html':
        return await exportToHTML(
          title: title,
          data: inventoryData,
          headers: headers,
          fileName: '$fileName.html',
        );
      default:
        return await exportToExcel(
          title: title,
          data: inventoryData,
          headers: headers,
          fileName: '$fileName.xlsx',
        );
    }
  }

  /// تصدير تقرير الوكلاء
  Future<File?> exportAgentsReport({
    required List<Map<String, dynamic>> agentsData,
    String format = 'excel',
  }) async {
    final title = 'تقرير الوكلاء - ${_formatDate(DateTime.now())}';
    final headers = ['كود الوكيل', 'اسم الوكيل', 'الرصيد', 'المبيعات', 'العمولة'];
    final fileName = 'agents_report_${DateTime.now().millisecondsSinceEpoch}';

    switch (format.toLowerCase()) {
      case 'csv':
        return await exportToCSV(
          title: title,
          data: agentsData,
          headers: headers,
          fileName: '$fileName.csv',
        );
      case 'html':
        return await exportToHTML(
          title: title,
          data: agentsData,
          headers: headers,
          fileName: '$fileName.html',
        );
      default:
        return await exportToExcel(
          title: title,
          data: agentsData,
          headers: headers,
          fileName: '$fileName.xlsx',
        );
    }
  }

  /// فتح مجلد الملفات المصدرة
  Future<void> openExportsFolder() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      await Process.run('explorer', [directory.path]);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error opening exports folder: $e');
      }
    }
  }

  /// اختيار مجلد للحفظ
  Future<String?> selectSaveLocation() async {
    try {
      final result = await FilePicker.platform.getDirectoryPath();
      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error selecting save location: $e');
      }
      return null;
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// الحصول على معلومات الملف المصدر
  Map<String, dynamic> getFileInfo(File file) {
    final stat = file.statSync();
    return {
      'name': file.path.split('\\').last,
      'path': file.path,
      'size': stat.size,
      'sizeInMB': (stat.size / (1024 * 1024)).toStringAsFixed(2),
      'created': stat.changed,
      'extension': file.path.split('.').last.toLowerCase(),
    };
  }
}
