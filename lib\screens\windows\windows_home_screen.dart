import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../core/constants/app_constants.dart';

// Import available screens
import '../home/<USER>';
import '../inventory/inventory_screen.dart';
import '../agents/agents_screen.dart';
import '../sales/sales_screen.dart';
import '../reports/reports_screen.dart';
import '../settings/settings_screen.dart';
// Note: Additional screens will be imported as they are created

/// الشاشة الرئيسية المحسنة للويندوز مع جميع الميزات
class WindowsHomeScreen extends StatefulWidget {
  const WindowsHomeScreen({Key? key}) : super(key: key);

  @override
  State<WindowsHomeScreen> createState() => _WindowsHomeScreenState();
}

class _WindowsHomeScreenState extends State<WindowsHomeScreen>
    with TickerProviderStateMixin {
  int _selectedIndex = 0;
  bool _isNavigationExpanded = true;
  late AnimationController _animationController;

  // Available screens
  final List<Widget> _screens = [
    const HomeScreen(),           // لوحة التحكم
    const InventoryScreen(),      // المخزون
    const AgentsScreen(),         // الوكلاء
    const SalesScreen(),          // المبيعات
    const _PlaceholderScreen(title: 'تتبع الجوابات'),  // تتبع الجوابات
    const _PlaceholderScreen(title: 'المحاسبة'),       // المحاسبة
    const ReportsScreen(),        // التقارير
    const _PlaceholderScreen(title: 'الإدارة'),        // الإدارة
    const SettingsScreen(),       // الإعدادات
  ];

  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.dashboard,
      label: 'لوحة التحكم',
      shortcut: 'Ctrl+1',
    ),
    NavigationItem(
      icon: Icons.inventory,
      label: 'المخزون',
      shortcut: 'Ctrl+2',
    ),
    NavigationItem(
      icon: Icons.people,
      label: 'الوكلاء',
      shortcut: 'Ctrl+3',
    ),
    NavigationItem(
      icon: Icons.shopping_cart,
      label: 'المبيعات',
      shortcut: 'Ctrl+4',
    ),
    NavigationItem(
      icon: Icons.track_changes,
      label: 'تتبع الجوابات',
      shortcut: 'Ctrl+5',
    ),
    NavigationItem(
      icon: Icons.account_balance,
      label: 'المحاسبة',
      shortcut: 'Ctrl+6',
    ),
    NavigationItem(
      icon: Icons.analytics,
      label: 'التقارير',
      shortcut: 'Ctrl+7',
    ),
    NavigationItem(
      icon: Icons.admin_panel_settings,
      label: 'الإدارة',
      shortcut: 'Ctrl+8',
    ),
    NavigationItem(
      icon: Icons.settings,
      label: 'الإعدادات',
      shortcut: 'Ctrl+9',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _setupKeyboardShortcuts();
  }

  void _setupKeyboardShortcuts() {
    // Setup keyboard shortcuts for Windows
    RawKeyboard.instance.addListener(_handleKeyEvent);
  }

  void _handleKeyEvent(RawKeyEvent event) {
    if (event is RawKeyDownEvent) {
      final isCtrlPressed = event.isControlPressed;
      
      if (isCtrlPressed) {
        switch (event.logicalKey.keyLabel) {
          case '1':
            _navigateToIndex(0);
            break;
          case '2':
            _navigateToIndex(1);
            break;
          case '3':
            _navigateToIndex(2);
            break;
          case '4':
            _navigateToIndex(3);
            break;
          case '5':
            _navigateToIndex(4);
            break;
          case '6':
            _navigateToIndex(5);
            break;
          case '7':
            _navigateToIndex(6);
            break;
          case '8':
            _navigateToIndex(7);
            break;
          case '9':
            _navigateToIndex(8);
            break;
        }
      }
    }
  }

  void _navigateToIndex(int index) {
    if (index >= 0 && index < _screens.length) {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  @override
  void dispose() {
    RawKeyboard.instance.removeListener(_handleKeyEvent);
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Navigation Rail - Enhanced for Windows
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: _isNavigationExpanded ? 280 : 80,
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                border: Border(
                  right: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(
                          Icons.local_shipping,
                          size: 32,
                          color: Theme.of(context).primaryColor,
                        ),
                        if (_isNavigationExpanded) ...[
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'آل فرحان',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                ),
                                Text(
                                  'نسخة Windows',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                        IconButton(
                          icon: Icon(
                            _isNavigationExpanded
                                ? Icons.menu_open
                                : Icons.menu,
                          ),
                          onPressed: () {
                            setState(() {
                              _isNavigationExpanded = !_isNavigationExpanded;
                            });
                          },
                          tooltip: _isNavigationExpanded ? 'طي القائمة' : 'توسيع القائمة',
                        ),
                      ],
                    ),
                  ),
                  
                  const Divider(),
                  
                  // Navigation Items
                  Expanded(
                    child: ListView.builder(
                      itemCount: _navigationItems.length,
                      itemBuilder: (context, index) {
                        final item = _navigationItems[index];
                        final isSelected = index == _selectedIndex;
                        
                        return Container(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          child: ListTile(
                            leading: Icon(
                              item.icon,
                              color: isSelected
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey[600],
                            ),
                            title: _isNavigationExpanded
                                ? Text(
                                    item.label,
                                    style: TextStyle(
                                      color: isSelected
                                          ? Theme.of(context).primaryColor
                                          : Colors.grey[700],
                                      fontWeight: isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                    ),
                                  )
                                : null,
                            subtitle: _isNavigationExpanded
                                ? Text(
                                    item.shortcut,
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: Colors.grey[500],
                                    ),
                                  )
                                : null,
                            selected: isSelected,
                            selectedTileColor: Theme.of(context)
                                .primaryColor
                                .withOpacity(0.1),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            onTap: () => _navigateToIndex(index),
                            tooltip: _isNavigationExpanded
                                ? null
                                : '${item.label} (${item.shortcut})',
                          ),
                        );
                      },
                    ),
                  ),
                  
                  const Divider(),
                  
                  // User Info and Logout
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Consumer<AuthProvider>(
                      builder: (context, authProvider, child) {
                        return Column(
                          children: [
                            if (_isNavigationExpanded) ...[
                              Row(
                                children: [
                                  CircleAvatar(
                                    backgroundColor: Theme.of(context).primaryColor,
                                    child: Text(
                                      authProvider.currentUser?.displayName
                                              ?.substring(0, 1)
                                              .toUpperCase() ??
                                          'U',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          authProvider.currentUser?.displayName ??
                                              'مستخدم',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        Text(
                                          authProvider.currentUser?.email ?? '',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                            ],
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: () async {
                                  await authProvider.signOut();
                                  if (mounted) {
                                    Navigator.of(context).pushReplacementNamed('/login');
                                  }
                                },
                                icon: const Icon(Icons.logout, size: 18),
                                label: _isNavigationExpanded
                                    ? const Text('تسجيل الخروج')
                                    : const SizedBox(),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red[600],
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(
                                    vertical: 12,
                                    horizontal: _isNavigationExpanded ? 16 : 8,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Main Content Area
          Expanded(
            child: Column(
              children: [
                // Top App Bar
                Container(
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 16),
                      Text(
                        _navigationItems[_selectedIndex].label,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: () {
                          // Refresh current screen
                        },
                        tooltip: 'تحديث (F5)',
                      ),
                      IconButton(
                        icon: const Icon(Icons.notifications),
                        onPressed: () {
                          // Show notifications
                        },
                        tooltip: 'الإشعارات',
                      ),
                      IconButton(
                        icon: const Icon(Icons.help_outline),
                        onPressed: () {
                          // Show help
                        },
                        tooltip: 'المساعدة (F1)',
                      ),
                      const SizedBox(width: 16),
                    ],
                  ),
                ),
                
                // Screen Content
                Expanded(
                  child: _screens[_selectedIndex],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// نموذج عنصر التنقل
class NavigationItem {
  final IconData icon;
  final String label;
  final String shortcut;

  NavigationItem({
    required this.icon,
    required this.label,
    required this.shortcut,
  });
}

/// شاشة مؤقتة للميزات قيد التطوير
class _PlaceholderScreen extends StatelessWidget {
  final String title;

  const _PlaceholderScreen({required this.title});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.construction,
            size: 100,
            color: Colors.orange,
          ),
          const SizedBox(height: 20),
          Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'هذه الميزة قيد التطوير',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            '🚧 سيتم إضافتها قريباً 🚧',
            style: TextStyle(
              fontSize: 18,
              color: Colors.orange,
            ),
          ),
        ],
      ),
    );
  }
}
