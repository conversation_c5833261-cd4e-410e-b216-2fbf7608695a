rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isSuperAdmin() {
      return isAuthenticated() && getUserData().role == 'super_admin';
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserData().role in ['admin', 'super_admin'];
    }
    
    function isAgent() {
      return isAuthenticated() && getUserData().role == 'agent';
    }
    
    function isActiveUser() {
      return isAuthenticated() && getUserData().isActive == true;
    }
    
    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated() && (isOwner(userId) || isAdmin());
      allow create: if isSuperAdmin();
      allow update: if isOwner(userId) || isSuperAdmin();
      allow delete: if isSuperAdmin();
    }
    
    // Warehouses collection
    match /warehouses/{warehouseId} {
      allow read: if isAuthenticated() && isActiveUser();
      allow write: if isAdmin();
    }
    
    // Items collection
    match /items/{itemId} {
      allow read: if isAuthenticated() && isActiveUser();
      allow create: if isAdmin();
      allow update: if isAdmin() || (isAgent() && resource.data.agentId == request.auth.uid);
      allow delete: if isSuperAdmin();
    }
    
    // Invoices collection
    match /invoices/{invoiceId} {
      allow read: if isAuthenticated() && isActiveUser() && 
        (isAdmin() || resource.data.agentId == request.auth.uid);
      allow create: if isAuthenticated() && isActiveUser() && 
        (isAdmin() || request.resource.data.agentId == request.auth.uid);
      allow update: if isAdmin() || 
        (isAgent() && resource.data.agentId == request.auth.uid);
      allow delete: if isSuperAdmin();
    }
    
    // Document tracking collection
    match /document_tracking/{docId} {
      allow read: if isAuthenticated() && isActiveUser() && 
        (isAdmin() || resource.data.createdBy == request.auth.uid);
      allow create: if isAuthenticated() && isActiveUser();
      allow update: if isAdmin();
      allow delete: if isSuperAdmin();
    }
    
    // Agent accounts collection
    match /agent_accounts/{accountId} {
      allow read: if isAuthenticated() && isActiveUser() && 
        (isAdmin() || resource.data.agentId == request.auth.uid);
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isSuperAdmin();
    }
    
    // Payments collection
    match /payments/{paymentId} {
      allow read: if isAuthenticated() && isActiveUser() && 
        (isAdmin() || resource.data.agentId == request.auth.uid);
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isSuperAdmin();
    }
    
    // Reports collection (if needed)
    match /reports/{reportId} {
      allow read: if isAuthenticated() && isActiveUser() && 
        (isAdmin() || resource.data.createdBy == request.auth.uid);
      allow create: if isAuthenticated() && isActiveUser();
      allow update: if isAdmin() || resource.data.createdBy == request.auth.uid;
      allow delete: if isSuperAdmin();
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      allow read: if isAuthenticated() && isActiveUser() && 
        (isAdmin() || resource.data.userId == request.auth.uid);
      allow create: if isAdmin();
      allow update: if isAdmin() || 
        (resource.data.userId == request.auth.uid && 
         request.resource.data.diff(resource.data).affectedKeys().hasOnly(['isRead', 'readAt']));
      allow delete: if isAdmin() || resource.data.userId == request.auth.uid;
    }
    
    // Settings collection (app-wide settings)
    match /settings/{settingId} {
      allow read: if isAuthenticated() && isActiveUser();
      allow write: if isSuperAdmin();
    }
    
    // Audit logs collection
    match /audit_logs/{logId} {
      allow read: if isSuperAdmin();
      allow create: if isAuthenticated() && isActiveUser();
      // No update or delete - logs are immutable
    }
    
    // Backup metadata collection
    match /backup_metadata/{backupId} {
      allow read: if isSuperAdmin();
      allow create: if isSuperAdmin();
      allow update: if isSuperAdmin();
      allow delete: if isSuperAdmin();
    }
    
    // System status collection (for monitoring)
    match /system_status/{statusId} {
      allow read: if isAdmin();
      allow write: if isSuperAdmin();
    }
    
    // Connectivity test collection (for checking Firebase connection)
    match /_connectivity_test/{testId} {
      allow read, write: if isAuthenticated();
    }
    
    // Default deny rule for any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

// Storage rules for file uploads
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*');
    }
    
    function isValidFileSize() {
      return request.resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    // Profile pictures
    match /profile_pictures/{userId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        request.auth.uid == userId && 
        isValidImageFile() && 
        isValidFileSize();
    }
    
    // Item images
    match /item_images/{itemId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        isValidImageFile() && 
        isValidFileSize();
    }
    
    // Invoice attachments
    match /invoice_attachments/{invoiceId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        isValidFileSize();
    }
    
    // Document images (motor fingerprints, ID cards, etc.)
    match /document_images/{docType}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        isValidImageFile() && 
        isValidFileSize();
    }
    
    // Backup files (super admin only)
    match /backups/{backupId}/{fileName} {
      allow read, write: if isAuthenticated() && 
        get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'super_admin';
    }
    
    // Reports and exports
    match /exports/{userId}/{fileName} {
      allow read: if isAuthenticated() && request.auth.uid == userId;
      allow write: if isAuthenticated() && request.auth.uid == userId;
    }
    
    // Default deny rule
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
