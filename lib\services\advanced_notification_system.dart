import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:io';

import '../core/constants/app_constants.dart';
import '../core/utils/app_utils.dart';
import '../models/notification_model.dart';
import '../models/notification_settings_model.dart';
import '../models/user_model.dart';
import '../models/invoice_model.dart';
import '../models/payment_model.dart';
import '../models/item_model.dart';

import 'data_service.dart';
import 'auth_service.dart';

/// نظام الإشعارات المتطور والموحد
/// يدعم جميع أنواع الإشعارات مع تخصيص متقدم وذكاء اصطناعي
class AdvancedNotificationSystem {
  static AdvancedNotificationSystem? _instance;
  static AdvancedNotificationSystem get instance => _instance ??= AdvancedNotificationSystem._();
  
  AdvancedNotificationSystem._();

  // Core services
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final DataService _dataService = DataService.instance;
  final AuthService _authService = AuthService.instance;

  // State management
  bool _isInitialized = false;
  String? _fcmToken;
  
  // Navigation
  static GlobalKey<NavigatorState>? navigatorKey;
  
  // In-app notifications
  final List<NotificationModel> _inAppNotifications = [];
  final List<NotificationModel> _priorityNotifications = [];
  int _unreadCount = 0;
  
  // Advanced settings
  NotificationSettings _settings = NotificationSettings();
  
  // Analytics
  final Map<String, int> _notificationStats = {};
  final List<NotificationAnalytics> _analytics = [];
  
  // AI suggestions
  final List<String> _smartSuggestions = [];
  
  // Callbacks
  Function(NotificationModel)? onNotificationReceived;
  Function(String)? onNotificationTapped;
  Function(int)? onUnreadCountChanged;
  
  // Notification channels with priorities
  static const Map<String, NotificationChannelConfig> _channels = {
    'critical': NotificationChannelConfig(
      id: 'critical_notifications',
      name: 'إشعارات حرجة',
      description: 'إشعارات عاجلة تتطلب اهتمام فوري',
      importance: Importance.max,
      priority: Priority.high,
      color: Colors.red,
      sound: 'critical_sound.mp3',
    ),
    'invoice': NotificationChannelConfig(
      id: 'invoice_notifications', 
      name: 'إشعارات الفواتير',
      description: 'إشعارات إنشاء وتحديث الفواتير',
      importance: Importance.high,
      priority: Priority.high,
      color: Colors.blue,
    ),
    'payment': NotificationChannelConfig(
      id: 'payment_notifications',
      name: 'إشعارات الدفعات', 
      description: 'إشعارات الدفعات والمعاملات المالية',
      importance: Importance.high,
      priority: Priority.high,
      color: Colors.green,
    ),
    'inventory': NotificationChannelConfig(
      id: 'inventory_notifications',
      name: 'إشعارات المخزون',
      description: 'إشعارات إضافة وتحديث المخزون',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
      color: Colors.orange,
    ),
    'document': NotificationChannelConfig(
      id: 'document_notifications',
      name: 'إشعارات الوثائق',
      description: 'إشعارات تتبع الوثائق والمستندات',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
      color: Colors.purple,
    ),
    'general': NotificationChannelConfig(
      id: 'general_notifications',
      name: 'إشعارات عامة',
      description: 'إشعارات عامة ومعلومات النظام',
      importance: Importance.low,
      priority: Priority.low,
      color: Colors.grey,
    ),
  };

  /// تهيئة النظام المتطور
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🚀 Initializing Advanced Notification System...');
      }

      // Load settings
      await _loadSettings();
      
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Initialize Firebase messaging
      await _initializeFirebaseMessaging();
      
      // Setup message handlers
      _setupMessageHandlers();
      
      // Subscribe to topics
      await _subscribeToTopics();
      
      // Load existing notifications
      await _loadNotifications();
      
      // Initialize analytics
      await _initializeAnalytics();
      
      // Setup AI suggestions
      await _initializeAISuggestions();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Advanced Notification System initialized successfully');
        print('📊 Loaded ${_inAppNotifications.length} notifications');
        print('🎯 Priority notifications: ${_priorityNotifications.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing Advanced Notification System: $e');
      }
    }
  }

  /// تحميل الإعدادات
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('notification_settings');
      
      if (settingsJson != null) {
        _settings = NotificationSettings.fromJson(jsonDecode(settingsJson));
      }
      
      if (kDebugMode) {
        print('📱 Notification settings loaded: ${_settings.toString()}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error loading notification settings: $e');
      }
    }
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('notification_settings', jsonEncode(_settings.toJson()));
      
      if (kDebugMode) {
        print('💾 Notification settings saved');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving notification settings: $e');
      }
    }
  }

  /// تهيئة الإشعارات المحلية
  Future<void> _initializeLocalNotifications() async {
    // Android initialization
    const androidInitialization = AndroidInitializationSettings('@mipmap/ic_launcher');
    
    // iOS initialization  
    const iosInitialization = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initializationSettings = InitializationSettings(
      android: androidInitialization,
      iOS: iosInitialization,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }

  /// إنشاء قنوات الإشعارات
  Future<void> _createNotificationChannels() async {
    final androidPlugin = _localNotifications.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
    
    if (androidPlugin != null) {
      for (final channel in _channels.values) {
        await androidPlugin.createNotificationChannel(
          AndroidNotificationChannel(
            channel.id,
            channel.name,
            description: channel.description,
            importance: channel.importance,
            playSound: _settings.soundEnabled,
            enableVibration: _settings.vibrationEnabled,
            ledColor: channel.color,
          ),
        );
      }
      
      if (kDebugMode) {
        print('📢 Created ${_channels.length} notification channels');
      }
    }
  }

  /// تهيئة Firebase Messaging
  Future<void> _initializeFirebaseMessaging() async {
    try {
      // Request permissions
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
        criticalAlert: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // Get FCM token
        try {
          _fcmToken = await _firebaseMessaging.getToken();

          if (kDebugMode && _fcmToken != null) {
            print('📱 FCM Token: $_fcmToken');
          }

          // Save token to user profile
          await _saveFCMTokenToProfile();

          if (kDebugMode) {
            print('✅ Firebase messaging initialized successfully');
          }
        } catch (tokenError) {
          if (kDebugMode) {
            print('⚠️ FCM Token not available on this platform: $tokenError');
          }
        }
      } else {
        if (kDebugMode) {
          print('⚠️ Notification permissions denied');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing Firebase messaging: $e');
      }
    }
  }

  /// حفظ FCM Token في ملف المستخدم
  Future<void> _saveFCMTokenToProfile() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser != null && _fcmToken != null) {
        // Update user profile with FCM token
        await _dataService.updateUser(currentUser.copyWith(
          fcmToken: _fcmToken,
        ));

        if (kDebugMode) {
          print('📱 FCM Token saved to user profile: ${currentUser.id}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving FCM token: $e');
      }
    }
  }

  /// إعداد معالجات الرسائل
  void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    _firebaseMessaging.getInitialMessage().then((message) {
      if (message != null) {
        _handleNotificationTap(message);
      }
    });
  }

  /// معالجة الرسائل في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      print('📨 Received foreground message: ${message.notification?.title}');
    }

    // Create notification model
    final notification = _createNotificationFromRemoteMessage(message);

    // Add to in-app notifications
    _addInAppNotification(notification);

    // Show local notification if enabled
    if (_settings.showInApp) {
      await _showLocalNotification(notification);
    }

    // Save to database
    await _saveNotificationToDatabase(notification);

    // Trigger callback
    onNotificationReceived?.call(notification);

    // Update analytics
    _updateAnalytics(notification);
  }

  /// معالجة الرسائل في الخلفية
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      print('📨 Received background message: ${message.notification?.title}');
    }

    // Save to database for later processing
    // This will be handled when app comes to foreground
  }

  /// معالجة النقر على الإشعار
  Future<void> _handleNotificationTap(RemoteMessage message) async {
    if (kDebugMode) {
      print('👆 Notification tapped: ${message.notification?.title}');
    }

    // Navigate to appropriate screen
    await _navigateFromNotification(message.data);

    // Mark as read
    final notificationId = message.messageId ?? message.data['id'];
    if (notificationId != null) {
      await markAsRead(notificationId);
    }
  }

  /// الاشتراك في المواضيع
  Future<void> _subscribeToTopics() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return;

      // Subscribe to role-based topics
      await _firebaseMessaging.subscribeToTopic('role_${currentUser.role}');

      // Subscribe to user-specific topic
      await _firebaseMessaging.subscribeToTopic('user_${currentUser.id}');

      // Subscribe to general topics based on settings
      if (_settings.enabledTypes.contains('general')) {
        await _firebaseMessaging.subscribeToTopic('general_notifications');
      }

      if (_settings.enabledTypes.contains('invoice')) {
        await _firebaseMessaging.subscribeToTopic('invoice_notifications');
      }

      if (_settings.enabledTypes.contains('payment')) {
        await _firebaseMessaging.subscribeToTopic('payment_notifications');
      }

      if (kDebugMode) {
        print('📢 Subscribed to notification topics for: ${currentUser.role}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error subscribing to topics: $e');
      }
    }
  }

  /// تحميل الإشعارات الموجودة
  Future<void> _loadNotifications() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return;

      // Load from database
      final notifications = await _dataService.getNotificationsForUser(currentUser.id);

      _inAppNotifications.clear();
      _priorityNotifications.clear();

      for (final notification in notifications) {
        _inAppNotifications.add(notification);

        // Add to priority list if high priority
        if (_isHighPriority(notification)) {
          _priorityNotifications.add(notification);
        }
      }

      // Update unread count
      _updateUnreadCount();

      if (kDebugMode) {
        print('📥 Loaded ${notifications.length} notifications');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading notifications: $e');
      }
    }
  }

  /// تهيئة التحليلات
  Future<void> _initializeAnalytics() async {
    try {
      // Load analytics data
      final prefs = await SharedPreferences.getInstance();
      final analyticsJson = prefs.getString('notification_analytics');

      if (analyticsJson != null) {
        final List<dynamic> analyticsData = jsonDecode(analyticsJson);
        _analytics.clear();
        _analytics.addAll(analyticsData.map((data) => NotificationAnalytics.fromJson(data)));
      }

      if (kDebugMode) {
        print('📊 Analytics initialized with ${_analytics.length} records');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing analytics: $e');
      }
    }
  }

  /// تهيئة اقتراحات الذكاء الاصطناعي
  Future<void> _initializeAISuggestions() async {
    try {
      // Analyze user behavior and generate suggestions
      await _generateSmartSuggestions();

      if (kDebugMode) {
        print('🤖 AI suggestions initialized: ${_smartSuggestions.length} suggestions');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing AI suggestions: $e');
      }
    }
  }

  // ==================== PUBLIC API ====================

  /// إرسال إشعار فاتورة جديدة
  Future<void> sendInvoiceNotification({
    required InvoiceModel invoice,
    required ItemModel item,
    required UserModel agent,
    String? compositeImageUrl,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'فاتورة جديدة - ${invoice.invoiceNumber}',
        message: 'تم إنشاء فاتورة جديدة بواسطة ${agent.fullName}\n'
               'المركبة: ${item.brand} ${item.model}\n'
               'المبلغ: ${AppUtils.formatCurrency(invoice.sellingPrice)}',
        type: 'invoice_created',
        targetRole: 'manager',
        relatedId: invoice.id,
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'agentId': agent.id,
          'agentName': agent.fullName,
          'itemBrand': item.brand,
          'itemModel': item.model,
          'sellingPrice': invoice.sellingPrice,
          'compositeImageUrl': compositeImageUrl,
          'targetScreen': 'invoice_details',
          'priority': 'high',
        },
        createdAt: DateTime.now(),
        createdBy: agent.id,
      );

      await _sendNotificationToManagers(notification);

      if (kDebugMode) {
        print('✅ Invoice notification sent for: ${invoice.invoiceNumber}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending invoice notification: $e');
      }
    }
  }

  /// إرسال إشعار دفعة جديدة
  Future<void> sendPaymentNotification({
    required PaymentModel payment,
    required UserModel agent,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'دفعة جديدة - ${AppUtils.formatCurrency(payment.amount)}',
        message: 'تم استلام دفعة جديدة من ${agent.fullName}\n'
               'المبلغ: ${AppUtils.formatCurrency(payment.amount)}\n'
               'النوع: ${payment.type}',
        type: 'payment_received',
        targetRole: 'manager',
        relatedId: payment.id,
        data: {
          'paymentId': payment.id,
          'agentId': agent.id,
          'agentName': agent.fullName,
          'amount': payment.amount,
          'type': payment.type,
          'targetScreen': 'payment_details',
          'priority': 'high',
        },
        createdAt: DateTime.now(),
        createdBy: agent.id,
      );

      await _sendNotificationToManagers(notification);

      if (kDebugMode) {
        print('✅ Payment notification sent for: ${AppUtils.formatCurrency(payment.amount)}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending payment notification: $e');
      }
    }
  }

  /// إرسال إشعار تذكير بالدفع
  Future<void> sendPaymentReminderNotification({
    required String agentId,
    required double amount,
    required int daysOverdue,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'تذكير بالدفع',
        message: 'لديك مبلغ مستحق ${AppUtils.formatCurrency(amount)}\n'
               'متأخر منذ $daysOverdue أيام',
        type: 'payment_reminder',
        targetUserId: agentId,
        data: {
          'amount': amount,
          'daysOverdue': daysOverdue,
          'targetScreen': 'agent_statement',
          'priority': daysOverdue > 7 ? 'critical' : 'high',
        },
        createdAt: DateTime.now(),
        createdBy: 'system',
      );

      await _sendNotificationToUser(agentId, notification);

      if (kDebugMode) {
        print('✅ Payment reminder sent to agent: $agentId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending payment reminder: $e');
      }
    }
  }

  /// إرسال إشعار إضافة مخزون
  Future<void> sendInventoryAddedNotification({
    required ItemModel item,
    required UserModel addedBy,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'إضافة مخزون جديد',
        message: 'تم إضافة مركبة جديدة للمخزون\n'
               'المركبة: ${item.brand} ${item.model}\n'
               'بواسطة: ${addedBy.fullName}',
        type: 'inventory_added',
        targetRole: 'manager',
        relatedId: item.id,
        data: {
          'itemId': item.id,
          'itemBrand': item.brand,
          'itemModel': item.model,
          'addedBy': addedBy.fullName,
          'targetScreen': 'inventory_details',
          'priority': 'medium',
        },
        createdAt: DateTime.now(),
        createdBy: addedBy.id,
      );

      await _sendNotificationToManagers(notification);

      if (kDebugMode) {
        print('✅ Inventory notification sent for: ${item.brand} ${item.model}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending inventory notification: $e');
      }
    }
  }

  /// إرسال إشعار عام
  Future<void> sendGeneralNotification({
    required String title,
    required String message,
    String? targetUserId,
    String? targetRole,
    Map<String, dynamic>? data,
    String priority = 'medium',
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: title,
        message: message,
        type: 'general',
        targetUserId: targetUserId,
        targetRole: targetRole,
        data: {
          ...?data,
          'priority': priority,
        },
        createdAt: DateTime.now(),
        createdBy: _authService.currentUser?.id ?? 'system',
      );

      if (targetUserId != null) {
        await _sendNotificationToUser(targetUserId, notification);
      } else if (targetRole != null) {
        await _sendNotificationToRole(targetRole, notification);
      } else {
        await _sendNotificationToAll(notification);
      }

      if (kDebugMode) {
        print('✅ General notification sent: $title');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending general notification: $e');
      }
    }
  }

  /// تحديد إشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      // Update in local list
      final index = _inAppNotifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _inAppNotifications[index] = _inAppNotifications[index].copyWith(
          isRead: true,
          readAt: DateTime.now(),
        );
      }

      // Update in database
      await _dataService.updateNotification(notificationId, {'isRead': true, 'readAt': DateTime.now().toIso8601String()});

      // Update unread count
      _updateUnreadCount();

      if (kDebugMode) {
        print('✅ Notification marked as read: $notificationId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as read: $e');
      }
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return;

      // Update local list
      for (int i = 0; i < _inAppNotifications.length; i++) {
        if (!_inAppNotifications[i].isRead) {
          _inAppNotifications[i] = _inAppNotifications[i].copyWith(
            isRead: true,
            readAt: DateTime.now(),
          );
        }
      }

      // Update in database
      await _dataService.markAllNotificationsAsRead(currentUser.id);

      // Update unread count
      _updateUnreadCount();

      if (kDebugMode) {
        print('✅ All notifications marked as read');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking all notifications as read: $e');
      }
    }
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      // Remove from local list
      _inAppNotifications.removeWhere((n) => n.id == notificationId);
      _priorityNotifications.removeWhere((n) => n.id == notificationId);

      // Delete from database
      await _dataService.deleteNotification(notificationId);

      // Update unread count
      _updateUnreadCount();

      if (kDebugMode) {
        print('✅ Notification deleted: $notificationId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting notification: $e');
      }
    }
  }

  /// حذف جميع الإشعارات
  Future<void> clearAllNotifications() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return;

      // Clear local lists
      _inAppNotifications.clear();
      _priorityNotifications.clear();

      // Delete from database
      await _dataService.deleteAllNotificationsForUser(currentUser.id);

      // Update unread count
      _updateUnreadCount();

      if (kDebugMode) {
        print('✅ All notifications cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing notifications: $e');
      }
    }
  }

  /// تحديث إعدادات الإشعارات
  Future<void> updateSettings(NotificationSettings newSettings) async {
    try {
      _settings = newSettings;
      await _saveSettings();

      // Re-subscribe to topics based on new settings
      await _subscribeToTopics();

      if (kDebugMode) {
        print('✅ Notification settings updated');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating notification settings: $e');
      }
    }
  }

  // ==================== GETTERS ====================

  /// الحصول على جميع الإشعارات
  List<NotificationModel> get allNotifications => List.unmodifiable(_inAppNotifications);

  /// الحصول على الإشعارات غير المقروءة
  List<NotificationModel> get unreadNotifications =>
      _inAppNotifications.where((n) => !n.isRead).toList();

  /// الحصول على الإشعارات عالية الأولوية
  List<NotificationModel> get priorityNotifications => List.unmodifiable(_priorityNotifications);

  /// عدد الإشعارات غير المقروءة
  int get unreadCount => _unreadCount;

  /// الإعدادات الحالية
  NotificationSettings get settings => _settings;

  /// FCM Token
  String? get fcmToken => _fcmToken;

  /// حالة التهيئة
  bool get isInitialized => _isInitialized;

  /// الاقتراحات الذكية
  List<String> get smartSuggestions => List.unmodifiable(_smartSuggestions);

  /// إحصائيات الإشعارات
  Map<String, int> get notificationStats => Map.unmodifiable(_notificationStats);

  // ==================== PRIVATE HELPERS ====================

  /// إضافة إشعار للقائمة المحلية
  void _addInAppNotification(NotificationModel notification) {
    _inAppNotifications.insert(0, notification);

    // Add to priority list if high priority
    if (_isHighPriority(notification)) {
      _priorityNotifications.insert(0, notification);
    }

    // Limit notifications count
    if (_inAppNotifications.length > 100) {
      _inAppNotifications.removeLast();
    }

    if (_priorityNotifications.length > 20) {
      _priorityNotifications.removeLast();
    }

    _updateUnreadCount();
  }

  /// تحديث عدد الإشعارات غير المقروءة
  void _updateUnreadCount() {
    final newCount = _inAppNotifications.where((n) => !n.isRead).length;
    if (newCount != _unreadCount) {
      _unreadCount = newCount;
      onUnreadCountChanged?.call(_unreadCount);
    }
  }

  /// التحقق من أولوية الإشعار
  bool _isHighPriority(NotificationModel notification) {
    final priority = notification.data?['priority'] as String?;
    return priority == 'high' || priority == 'critical';
  }

  /// إنشاء إشعار من رسالة Firebase
  NotificationModel _createNotificationFromRemoteMessage(RemoteMessage message) {
    return NotificationModel(
      id: message.messageId ?? AppUtils.generateId(),
      title: message.notification?.title ?? 'إشعار جديد',
      message: message.notification?.body ?? '',
      type: message.data['type'] ?? 'general',
      targetUserId: message.data['targetUserId'],
      targetRole: message.data['targetRole'],
      relatedId: message.data['relatedId'],
      data: message.data.isNotEmpty ? message.data : null,
      createdAt: DateTime.now(),
      createdBy: message.data['createdBy'] ?? 'system',
      isRead: false,
    );
  }

  /// عرض إشعار محلي
  Future<void> _showLocalNotification(NotificationModel notification) async {
    try {
      // Check if notifications are enabled
      if (!_settings.enabled) return;

      // Check quiet hours
      if (_settings.quietHours?.isQuietTime(DateTime.now()) == true) {
        final priority = notification.data?['priority'] as String?;
        if (priority != 'critical' || !_settings.quietHours!.allowCritical) {
          return;
        }
      }

      // Check rate limiting
      if (!_checkRateLimit()) return;

      // Get channel config
      final channelConfig = _getChannelConfig(notification.type);

      // Create notification details
      final androidDetails = AndroidNotificationDetails(
        channelConfig.id,
        channelConfig.name,
        channelDescription: channelConfig.description,
        importance: channelConfig.importance,
        priority: channelConfig.priority,
        color: channelConfig.color,
        playSound: _settings.soundEnabled,
        enableVibration: _settings.vibrationEnabled,
        groupKey: _settings.groupSimilar ? notification.type : null,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show notification
      await _localNotifications.show(
        notification.id.hashCode,
        notification.title,
        notification.message,
        details,
        payload: jsonEncode({
          'id': notification.id,
          'type': notification.type,
          'data': notification.data,
        }),
      );

      if (kDebugMode) {
        print('📱 Local notification shown: ${notification.title}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error showing local notification: $e');
      }
    }
  }
}
