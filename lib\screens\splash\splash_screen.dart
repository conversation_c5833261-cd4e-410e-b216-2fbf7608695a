import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/local_database_service.dart';
import '../auth/login_screen.dart';
import '../responsive/responsive_wrapper.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final LocalDatabaseService _localDb = LocalDatabaseService.instance;
  String? _customSplashImagePath;

  @override
  void initState() {
    super.initState();
    _loadCustomSplashImage();
    _checkAuthStatus();
  }

  Future<void> _loadCustomSplashImage() async {
    try {
      final settings = await _localDb.query('settings', where: 'key = ?', whereArgs: ['splash_image']);
      if (settings.isNotEmpty) {
        final imagePath = settings.first['value'] as String?;
        if (imagePath != null && File(imagePath).existsSync()) {
          setState(() {
            _customSplashImagePath = imagePath;
          });
        }
      }
    } catch (e) {
      // Ignore error and use default splash
    }
  }

  Future<void> _checkAuthStatus() async {
    try {
      // Show splash for minimum 1 second for better UX
      final splashFuture = Future.delayed(const Duration(seconds: 1));

      // Wait a frame to avoid setState during build
      await Future.delayed(Duration.zero);

      if (mounted) {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);

        // Initialize auth provider and check for saved session
        await authProvider.initialize();

        // Wait for minimum splash time
        await splashFuture;

        if (mounted) {
          if (authProvider.isAuthenticated) {
            // User is already logged in, go to home
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const ResponsiveWrapper()),
            );
          } else {
            // No saved session, go to login
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const LoginScreen()),
            );
          }
        }
      }
    } catch (e) {
      // On error, go to login screen
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: _customSplashImagePath == null
              ? LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                  ],
                )
              : null,
          image: DecorationImage(
            image: const AssetImage('assets/images/splash_screen.jpeg'),
            fit: BoxFit.cover,
            onError: (exception, stackTrace) {
              // Fallback handled by gradient background
            },
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.4),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: Image.asset(
                      'assets/images/logo.png',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.local_shipping,
                          size: 60,
                          color: Color(0xFF1976D2),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 30),
                const Text(
                  'الفرحان للنقل الخفيف',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        offset: Offset(2, 2),
                        blurRadius: 4,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                const Text(
                  'نظام إدارة شامل',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 50),
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 3,
                ),
                const SizedBox(height: 20),
                const Text(
                  'جاري التحميل...',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
