import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image/image.dart' as img;
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

// Import Windows OCR service
import 'windows_ocr_service.dart';

/// خدمة الصور المحسنة للويندوز - بديل للكاميرا مع OCR
class WindowsImageService {
  static final WindowsImageService _instance = WindowsImageService._internal();
  factory WindowsImageService() => _instance;
  WindowsImageService._internal();

  static WindowsImageService get instance => _instance;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (kDebugMode) {
      print('🔄 Initializing WindowsImageService...');
    }
    
    try {
      if (kDebugMode) {
        print('✅ WindowsImageService initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize WindowsImageService: $e');
      }
    }
  }

  /// اختيار صورة من الملفات (بديل للكاميرا)
  Future<File?> pickImageFromFiles() async {
    try {
      if (kDebugMode) {
        print('📁 Opening file picker for image selection...');
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        
        if (kDebugMode) {
          print('✅ Image selected: ${file.path}');
        }
        
        return file;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking image: $e');
      }
      return null;
    }
  }

  /// اختيار صور متعددة من الملفات
  Future<List<File>> pickMultipleImagesFromFiles() async {
    try {
      if (kDebugMode) {
        print('📁 Opening file picker for multiple image selection...');
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
      );

      if (result != null) {
        final files = result.paths
            .where((path) => path != null)
            .map((path) => File(path!))
            .toList();
        
        if (kDebugMode) {
          print('✅ ${files.length} images selected');
        }
        
        return files;
      }
      
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking multiple images: $e');
      }
      return [];
    }
  }

  /// معالجة الصورة واستخراج النص
  Future<Map<String, dynamic>> processImageWithOCR(File imageFile) async {
    try {
      if (kDebugMode) {
        print('🔍 Processing image with OCR: ${imageFile.path}');
      }

      // قراءة الصورة
      final imageBytes = await imageFile.readAsBytes();
      
      // استخراج النص باستخدام Windows OCR Service
      final ocrResult = await WindowsOCRService.instance.extractTextFromImage(imageBytes);
      
      // معلومات الصورة
      final imageInfo = await _getImageInfo(imageBytes);
      
      final result = {
        'text': ocrResult.text,
        'confidence': ocrResult.confidence,
        'isMotorFingerprint': ocrResult.isMotorFingerprint,
        'isChassisNumber': ocrResult.isChassisNumber,
        'extractedData': ocrResult.extractedData,
        'imageInfo': imageInfo,
        'filePath': imageFile.path,
        'fileName': imageFile.path.split('/').last,
      };

      if (kDebugMode) {
        print('✅ Image processing completed');
        print('📝 Extracted text: ${ocrResult.text}');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error processing image with OCR: $e');
      }
      return {
        'text': '',
        'confidence': 0.0,
        'isMotorFingerprint': false,
        'isChassisNumber': false,
        'extractedData': {},
        'error': e.toString(),
      };
    }
  }

  /// ضغط الصورة
  Future<File?> compressImage(File imageFile, {int quality = 85}) async {
    try {
      if (kDebugMode) {
        print('🗜️ Compressing image...');
      }

      final imageBytes = await imageFile.readAsBytes();
      final image = img.decodeImage(imageBytes);
      
      if (image == null) {
        throw 'Failed to decode image';
      }

      // ضغط الصورة
      final compressedBytes = img.encodeJpg(image, quality: quality);
      
      // حفظ الصورة المضغوطة
      final compressedFile = File('${imageFile.path}_compressed.jpg');
      await compressedFile.writeAsBytes(compressedBytes);

      if (kDebugMode) {
        final originalSize = imageBytes.length;
        final compressedSize = compressedBytes.length;
        final compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toStringAsFixed(1);
        print('✅ Image compressed: ${compressionRatio}% size reduction');
      }

      return compressedFile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error compressing image: $e');
      }
      return null;
    }
  }

  /// تغيير حجم الصورة
  Future<File?> resizeImage(File imageFile, {int? width, int? height}) async {
    try {
      if (kDebugMode) {
        print('📏 Resizing image...');
      }

      final imageBytes = await imageFile.readAsBytes();
      final image = img.decodeImage(imageBytes);
      
      if (image == null) {
        throw 'Failed to decode image';
      }

      // تغيير حجم الصورة
      final resizedImage = img.copyResize(
        image,
        width: width,
        height: height,
      );
      
      // حفظ الصورة المعدلة
      final resizedBytes = img.encodeJpg(resizedImage);
      final resizedFile = File('${imageFile.path}_resized.jpg');
      await resizedFile.writeAsBytes(resizedBytes);

      if (kDebugMode) {
        print('✅ Image resized to ${resizedImage.width}x${resizedImage.height}');
      }

      return resizedFile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error resizing image: $e');
      }
      return null;
    }
  }

  /// الحصول على معلومات الصورة
  Future<Map<String, dynamic>> _getImageInfo(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      
      if (image == null) {
        return {'error': 'Failed to decode image'};
      }

      return {
        'width': image.width,
        'height': image.height,
        'format': 'JPEG',
        'size': imageBytes.length,
        'aspectRatio': (image.width / image.height).toStringAsFixed(2),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting image info: $e');
      }
      return {'error': e.toString()};
    }
  }

  /// إنشاء صورة مصغرة
  Future<File?> createThumbnail(File imageFile, {int size = 150}) async {
    try {
      if (kDebugMode) {
        print('🖼️ Creating thumbnail...');
      }

      final imageBytes = await imageFile.readAsBytes();
      final image = img.decodeImage(imageBytes);
      
      if (image == null) {
        throw 'Failed to decode image';
      }

      // إنشاء صورة مصغرة مربعة
      final thumbnail = img.copyResizeCropSquare(image, size: size);
      
      // حفظ الصورة المصغرة
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: 80);
      final thumbnailFile = File('${imageFile.path}_thumbnail.jpg');
      await thumbnailFile.writeAsBytes(thumbnailBytes);

      if (kDebugMode) {
        print('✅ Thumbnail created: ${size}x${size}');
      }

      return thumbnailFile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating thumbnail: $e');
      }
      return null;
    }
  }

  /// التحقق من صحة الصورة
  bool validateImage(File imageFile) {
    try {
      // التحقق من وجود الملف
      if (!imageFile.existsSync()) {
        return false;
      }

      // التحقق من امتداد الملف
      final extension = imageFile.path.toLowerCase().split('.').last;
      final validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
      
      if (!validExtensions.contains(extension)) {
        return false;
      }

      // التحقق من حجم الملف (أقل من 10 ميجابايت)
      final fileSize = imageFile.lengthSync();
      if (fileSize > 10 * 1024 * 1024) {
        return false;
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating image: $e');
      }
      return false;
    }
  }

  /// حذف الملفات المؤقتة
  Future<void> cleanupTempFiles(String originalPath) async {
    try {
      final tempFiles = [
        '${originalPath}_compressed.jpg',
        '${originalPath}_resized.jpg',
        '${originalPath}_thumbnail.jpg',
      ];

      for (final filePath in tempFiles) {
        final file = File(filePath);
        if (file.existsSync()) {
          await file.delete();
        }
      }

      if (kDebugMode) {
        print('🧹 Temporary files cleaned up');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error cleaning up temp files: $e');
      }
    }
  }

  /// تحميل صورة من URL وحفظها في مجلد التحميلات
  Future<File?> downloadImageFromUrl(String imageUrl, String fileName) async {
    try {
      if (kDebugMode) {
        print('📥 Downloading image from URL: $imageUrl');
      }

      // إنشاء HTTP request
      final response = await http.get(Uri.parse(imageUrl));

      if (response.statusCode != 200) {
        throw Exception('Failed to download image: ${response.statusCode}');
      }

      // الحصول على مجلد التحميلات
      final downloadsDirectory = await getDownloadsDirectory();
      if (downloadsDirectory == null) {
        throw Exception('Could not access downloads directory');
      }

      // إنشاء مجلد فرعي للتطبيق
      final appDownloadsDir = Directory(path.join(downloadsDirectory.path, 'ElFarhan'));
      if (!await appDownloadsDir.exists()) {
        await appDownloadsDir.create(recursive: true);
      }

      // تحديد امتداد الملف
      String fileExtension = '.jpg';
      if (imageUrl.toLowerCase().contains('.png')) {
        fileExtension = '.png';
      } else if (imageUrl.toLowerCase().contains('.jpeg')) {
        fileExtension = '.jpeg';
      }

      // إنشاء اسم الملف مع الطابع الزمني
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final finalFileName = '${fileName}_$timestamp$fileExtension';

      // حفظ الملف
      final file = File(path.join(appDownloadsDir.path, finalFileName));
      await file.writeAsBytes(response.bodyBytes);

      if (kDebugMode) {
        print('✅ Image downloaded successfully: ${file.path}');
      }

      // فتح مجلد التحميلات
      await _openDownloadsFolder(appDownloadsDir.path);

      return file;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error downloading image: $e');
      }
      rethrow;
    }
  }

  /// فتح مجلد التحميلات في مستكشف الملفات
  Future<void> _openDownloadsFolder(String folderPath) async {
    try {
      if (Platform.isWindows) {
        await Process.run('explorer', [folderPath]);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error opening downloads folder: $e');
      }
    }
  }
}
