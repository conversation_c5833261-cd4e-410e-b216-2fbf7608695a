import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// Android-specific session service with enhanced persistent login
class AndroidSessionService {
  static AndroidSessionService? _instance;
  static AndroidSessionService get instance => _instance ??= AndroidSessionService._();
  
  AndroidSessionService._();

  SharedPreferences? _prefs;
  bool _isInitialized = false;

  // Session keys
  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyUserId = 'user_id';
  static const String _keyUserData = 'user_data';
  static const String _keySessionToken = 'session_token';
  static const String _keyLoginTimestamp = 'login_timestamp';
  static const String _keyRememberLogin = 'remember_login';
  static const String _keyLastActivity = 'last_activity';
  static const String _keyDeviceId = 'device_id';

  /// Initialize the session service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🔐 Initializing Android Session Service...');
      }

      _prefs = await SharedPreferences.getInstance();
      
      // Update last activity
      await _updateLastActivity();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Android Session Service initialized successfully');
        await _logSessionStatus();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing Android Session Service: $e');
      }
    }
  }

  /// Save user session with enhanced security
  Future<bool> saveUserSession({
    required String userId,
    required Map<String, dynamic> userData,
    required bool rememberLogin,
  }) async {
    try {
      if (!_isInitialized || _prefs == null) {
        await initialize();
      }

      if (kDebugMode) {
        print('💾 Saving user session for: ${userData['fullName']}');
      }

      // Generate secure session token
      final sessionToken = _generateSessionToken(userId);
      final timestamp = DateTime.now().toIso8601String();
      
      // Encrypt sensitive data
      final encryptedUserData = _encryptData(jsonEncode(userData));

      // Save session data
      await _prefs!.setBool(_keyIsLoggedIn, true);
      await _prefs!.setString(_keyUserId, userId);
      await _prefs!.setString(_keyUserData, encryptedUserData);
      await _prefs!.setString(_keySessionToken, sessionToken);
      await _prefs!.setString(_keyLoginTimestamp, timestamp);
      await _prefs!.setBool(_keyRememberLogin, rememberLogin);
      await _updateLastActivity();

      if (kDebugMode) {
        print('✅ User session saved successfully');
        print('   - User ID: $userId');
        print('   - Remember login: $rememberLogin');
        print('   - Session token: ${sessionToken.substring(0, 10)}...');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving user session: $e');
      }
      return false;
    }
  }

  /// Load user session with validation
  Future<Map<String, dynamic>?> loadUserSession() async {
    try {
      if (!_isInitialized || _prefs == null) {
        await initialize();
      }

      final isLoggedIn = _prefs!.getBool(_keyIsLoggedIn) ?? false;
      final rememberLogin = _prefs!.getBool(_keyRememberLogin) ?? false;

      if (kDebugMode) {
        print('🔍 Loading user session...');
        print('   - Is logged in: $isLoggedIn');
        print('   - Remember login: $rememberLogin');
      }

      if (!isLoggedIn) {
        if (kDebugMode) {
          print('❌ No active session found');
        }
        return null;
      }

      // Check if session should be remembered
      if (!rememberLogin) {
        if (kDebugMode) {
          print('❌ Session not set to be remembered');
        }
        await clearSession();
        return null;
      }

      // Validate session
      if (!await _validateSession()) {
        if (kDebugMode) {
          print('❌ Session validation failed');
        }
        await clearSession();
        return null;
      }

      // Load session data
      final userId = _prefs!.getString(_keyUserId);
      final encryptedUserData = _prefs!.getString(_keyUserData);
      final sessionToken = _prefs!.getString(_keySessionToken);
      final loginTimestamp = _prefs!.getString(_keyLoginTimestamp);

      if (userId == null || encryptedUserData == null || sessionToken == null) {
        if (kDebugMode) {
          print('❌ Incomplete session data');
        }
        await clearSession();
        return null;
      }

      // Decrypt user data
      final decryptedUserData = _decryptData(encryptedUserData);
      final userData = jsonDecode(decryptedUserData) as Map<String, dynamic>;

      // Update last activity
      await _updateLastActivity();

      if (kDebugMode) {
        print('✅ User session loaded successfully');
        print('   - User: ${userData['fullName']}');
        print('   - Role: ${userData['role']}');
        print('   - Login time: $loginTimestamp');
      }

      return {
        'userId': userId,
        'userData': userData,
        'sessionToken': sessionToken,
        'loginTimestamp': loginTimestamp,
        'isValid': true,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading user session: $e');
      }
      await clearSession();
      return null;
    }
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    try {
      if (!_isInitialized || _prefs == null) {
        await initialize();
      }

      final isLoggedIn = _prefs!.getBool(_keyIsLoggedIn) ?? false;
      final rememberLogin = _prefs!.getBool(_keyRememberLogin) ?? false;

      if (!isLoggedIn || !rememberLogin) {
        return false;
      }

      return await _validateSession();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking login status: $e');
      }
      return false;
    }
  }

  /// Clear user session
  Future<void> clearSession() async {
    try {
      if (!_isInitialized || _prefs == null) {
        await initialize();
      }

      if (kDebugMode) {
        print('🗑️ Clearing user session...');
      }

      await _prefs!.remove(_keyIsLoggedIn);
      await _prefs!.remove(_keyUserId);
      await _prefs!.remove(_keyUserData);
      await _prefs!.remove(_keySessionToken);
      await _prefs!.remove(_keyLoginTimestamp);
      await _prefs!.remove(_keyRememberLogin);
      await _prefs!.remove(_keyLastActivity);

      if (kDebugMode) {
        print('✅ User session cleared successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing session: $e');
      }
    }
  }

  /// Generate secure session token
  String _generateSessionToken(String userId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final data = '$userId:$timestamp:${DateTime.now().toIso8601String()}';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Validate session (check expiry, token validity, etc.)
  Future<bool> _validateSession() async {
    try {
      final loginTimestamp = _prefs!.getString(_keyLoginTimestamp);
      final sessionToken = _prefs!.getString(_keySessionToken);
      final lastActivity = _prefs!.getString(_keyLastActivity);

      if (loginTimestamp == null || sessionToken == null) {
        return false;
      }

      // Check session age (30 days max)
      final loginTime = DateTime.parse(loginTimestamp);
      final now = DateTime.now();
      final sessionAge = now.difference(loginTime).inDays;

      if (sessionAge > 30) {
        if (kDebugMode) {
          print('❌ Session expired (age: $sessionAge days)');
        }
        return false;
      }

      // Check last activity (7 days max inactivity)
      if (lastActivity != null) {
        final lastActivityTime = DateTime.parse(lastActivity);
        final inactivityDays = now.difference(lastActivityTime).inDays;

        if (inactivityDays > 7) {
          if (kDebugMode) {
            print('❌ Session expired due to inactivity ($inactivityDays days)');
          }
          return false;
        }
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating session: $e');
      }
      return false;
    }
  }

  /// Update last activity timestamp
  Future<void> _updateLastActivity() async {
    try {
      if (_prefs != null) {
        await _prefs!.setString(_keyLastActivity, DateTime.now().toIso8601String());
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating last activity: $e');
      }
    }
  }

  /// Simple encryption for user data
  String _encryptData(String data) {
    // Simple base64 encoding for now - can be enhanced with proper encryption
    final bytes = utf8.encode(data);
    return base64.encode(bytes);
  }

  /// Simple decryption for user data
  String _decryptData(String encryptedData) {
    // Simple base64 decoding for now - can be enhanced with proper decryption
    final bytes = base64.decode(encryptedData);
    return utf8.decode(bytes);
  }

  /// Log current session status for debugging
  Future<void> _logSessionStatus() async {
    try {
      if (_prefs == null) return;

      final isLoggedIn = _prefs!.getBool(_keyIsLoggedIn) ?? false;
      final rememberLogin = _prefs!.getBool(_keyRememberLogin) ?? false;
      final userId = _prefs!.getString(_keyUserId);
      final loginTimestamp = _prefs!.getString(_keyLoginTimestamp);
      final lastActivity = _prefs!.getString(_keyLastActivity);

      if (kDebugMode) {
        print('📊 Session Status:');
        print('   - Is logged in: $isLoggedIn');
        print('   - Remember login: $rememberLogin');
        print('   - User ID: ${userId ?? "None"}');
        print('   - Login time: ${loginTimestamp ?? "None"}');
        print('   - Last activity: ${lastActivity ?? "None"}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error logging session status: $e');
      }
    }
  }
}
