import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

/// Radical SSL bypass solution for Cloudinary images
class RadicalSSLBypass {
  static final RadicalSSLBypass _instance = RadicalSSLBypass._internal();
  factory RadicalSSLBypass() => _instance;
  RadicalSSLBypass._internal();

  late final http.Client _httpClient;
  bool _initialized = false;

  /// Initialize with complete SSL bypass
  void initialize() {
    if (_initialized) return;

    if (kDebugMode) {
      print('🔓 RADICAL SSL BYPASS: Initializing complete SSL bypass...');
    }

    // Create HttpClient with COMPLETE SSL bypass
    final httpClient = HttpClient();
    
    // COMPLETELY DISABLE ALL SSL VERIFICATION
    httpClient.badCertificateCallback = (cert, host, port) {
      if (kDebugMode) {
        print('🔓 RADICAL BYPASS: Allowing ALL certificates for $host:$port');
      }
      return true; // ALLOW EVERYTHING
    };

    // Disable all security checks
    httpClient.connectionTimeout = const Duration(seconds: 60);
    httpClient.idleTimeout = const Duration(seconds: 60);
    
    // Create IOClient wrapper
    _httpClient = IOClient(httpClient);
    _initialized = true;

    if (kDebugMode) {
      print('✅ RADICAL SSL BYPASS: Complete bypass initialized');
    }
  }

  /// Download image with radical bypass
  Future<Uint8List?> downloadImage(String imageUrl) async {
    if (!_initialized) initialize();

    if (imageUrl.isEmpty) {
      if (kDebugMode) {
        print('❌ RADICAL BYPASS: Empty URL provided');
      }
      return null;
    }

    try {
      if (kDebugMode) {
        print('🔓 RADICAL BYPASS: Downloading $imageUrl');
      }

      // Always use HTTP for Cloudinary to completely bypass SSL
      String finalUrl = imageUrl;
      if (imageUrl.contains('cloudinary.com') && imageUrl.startsWith('https://')) {
        finalUrl = imageUrl.replaceFirst('https://', 'http://');
        if (kDebugMode) {
          print('🔓 RADICAL BYPASS: Converting Cloudinary HTTPS to HTTP: $finalUrl');
        }
      } else if (kDebugMode) {
        print('🔓 RADICAL BYPASS: Using URL as-is: $finalUrl');
      }

      final response = await _httpClient.get(
        Uri.parse(finalUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
          'Accept-Encoding': 'gzip, deflate',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        if (kDebugMode) {
          print('✅ RADICAL BYPASS: Image downloaded successfully (${response.bodyBytes.length} bytes)');
        }
        return response.bodyBytes;
      } else {
        if (kDebugMode) {
          print('❌ RADICAL BYPASS: HTTP ${response.statusCode} for $finalUrl');
        }
        
        // Try original HTTPS URL if HTTP failed
        if (finalUrl != imageUrl) {
          if (kDebugMode) {
            print('🔄 RADICAL BYPASS: Retrying with original HTTPS URL');
          }
          
          final httpsResponse = await _httpClient.get(
            Uri.parse(imageUrl),
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              'Accept': 'image/*',
            },
          ).timeout(const Duration(seconds: 30));
          
          if (httpsResponse.statusCode == 200) {
            if (kDebugMode) {
              print('✅ RADICAL BYPASS: HTTPS retry successful');
            }
            return httpsResponse.bodyBytes;
          }
        }
        
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ RADICAL BYPASS: Error downloading $imageUrl - $e');
      }
      return null;
    }
  }

  /// Test connectivity with radical bypass
  Future<bool> testConnectivity() async {
    try {
      if (kDebugMode) {
        print('🧪 RADICAL BYPASS: Testing connectivity...');
      }

      // Test multiple URLs
      final testUrls = [
        'http://httpbin.org/get',
        'https://httpbin.org/get',
        'http://res.cloudinary.com/demo/image/upload/sample.jpg',
        'https://res.cloudinary.com/demo/image/upload/sample.jpg',
      ];

      for (final url in testUrls) {
        try {
          final response = await _httpClient.get(Uri.parse(url)).timeout(const Duration(seconds: 10));
          if (response.statusCode == 200) {
            if (kDebugMode) {
              print('✅ RADICAL BYPASS: Connectivity test passed for $url');
            }
            return true;
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ RADICAL BYPASS: Test failed for $url - $e');
          }
        }
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ RADICAL BYPASS: Connectivity test failed - $e');
      }
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    if (_initialized) {
      _httpClient.close();
      _initialized = false;
      if (kDebugMode) {
        print('🗑️ RADICAL SSL BYPASS: Disposed');
      }
    }
  }
}

/// Global HTTP overrides with radical SSL bypass
class RadicalHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final client = super.createHttpClient(context);
    
    if (kDebugMode) {
      print('🔓 RADICAL HTTP OVERRIDE: Creating client with complete SSL bypass');
    }
    
    // COMPLETELY DISABLE SSL VERIFICATION
    client.badCertificateCallback = (cert, host, port) {
      if (kDebugMode) {
        print('🔓 RADICAL OVERRIDE: Bypassing SSL for $host:$port');
      }
      return true; // ALLOW ALL CERTIFICATES
    };
    
    // Set aggressive timeouts
    client.connectionTimeout = const Duration(seconds: 60);
    client.idleTimeout = const Duration(seconds: 60);
    
    return client;
  }
}

/// Initialize radical SSL bypass globally
void initializeRadicalSSLBypass() {
  if (kDebugMode) {
    print('🔓 INITIALIZING RADICAL SSL BYPASS...');
  }
  
  // Set global HTTP overrides
  HttpOverrides.global = RadicalHttpOverrides();
  
  // Initialize radical bypass
  RadicalSSLBypass().initialize();
  
  if (kDebugMode) {
    print('✅ RADICAL SSL BYPASS INITIALIZED GLOBALLY');
  }
}
