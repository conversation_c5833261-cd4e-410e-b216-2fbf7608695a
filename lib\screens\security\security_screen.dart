import 'package:flutter/material.dart';

import '../../core/utils/app_utils.dart';
import '../../services/security_service.dart';
import '../../services/backup_service.dart';
import '../../models/backup_model.dart';

class SecurityScreen extends StatefulWidget {
  const SecurityScreen({super.key});

  @override
  State<SecurityScreen> createState() => _SecurityScreenState();
}

class _SecurityScreenState extends State<SecurityScreen> {
  final SecurityService _securityService = SecurityService.instance;
  final BackupService _backupService = BackupService.instance;
  
  bool _isLoading = false;
  List<BackupModel> _availableBackups = [];
  List<Map<String, dynamic>> _securityLogs = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final backups = await _backupService.getAvailableBackups();
      final logs = await _securityService.getSecurityLogs();
      
      setState(() {
        _availableBackups = backups;
        _securityLogs = logs.take(50).toList(); // Show last 50 logs
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الأمان والنسخ الاحتياطي'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBackupSection(),
                  const SizedBox(height: 24),
                  _buildSecuritySection(),
                  const SizedBox(height: 24),
                  _buildSecurityLogsSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildBackupSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.backup, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'النسخ الاحتياطي',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Backup actions
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createFullBackup,
                    icon: const Icon(Icons.backup_table),
                    label: const Text('نسخة كاملة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createIncrementalBackup,
                    icon: const Icon(Icons.update),
                    label: const Text('نسخة تدريجية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Available backups
            Text(
              'النسخ المتاحة (${_availableBackups.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            if (_availableBackups.isEmpty)
              const Text('لا توجد نسخ احتياطية متاحة')
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _availableBackups.length,
                itemBuilder: (context, index) {
                  final backup = _availableBackups[index];
                  return _buildBackupItem(backup);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupItem(BackupModel backup) {
    return ListTile(
      leading: Icon(
        backup.type == BackupType.full ? Icons.backup_table : Icons.update,
        color: backup.type == BackupType.full ? Colors.blue : Colors.green,
      ),
      title: Text(backup.name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(AppUtils.formatDateTime(backup.createdAt)),
          Text('الحجم: ${backup.formattedSize}'),
          Text('النوع: ${backup.typeText}'),
        ],
      ),
      trailing: PopupMenuButton<String>(
        onSelected: (action) => _handleBackupAction(action, backup),
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'restore',
            child: Text('استعادة'),
          ),
          const PopupMenuItem(
            value: 'delete',
            child: Text('حذف'),
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.security, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'الأمان',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.lock_reset),
              title: const Text('تغيير كلمة المرور'),
              subtitle: const Text('تغيير كلمة مرور الحساب الحالي'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _changePassword,
            ),
            
            const Divider(),
            
            ListTile(
              leading: const Icon(Icons.vpn_key),
              title: const Text('إعادة تعيين مفاتيح التشفير'),
              subtitle: const Text('إنشاء مفاتيح تشفير جديدة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _resetEncryptionKeys,
            ),
            
            const Divider(),
            
            ListTile(
              leading: const Icon(Icons.cleaning_services),
              title: const Text('تنظيف البيانات الآمنة'),
              subtitle: const Text('مسح جميع البيانات المشفرة المحفوظة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _clearSecureData,
            ),
            
            const Divider(),
            
            ListTile(
              leading: const Icon(Icons.auto_delete),
              title: const Text('تنظيف السجلات القديمة'),
              subtitle: const Text('حذف سجلات الأمان القديمة'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _cleanupLogs,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityLogsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.history, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'سجل الأمان (آخر ${_securityLogs.length} حدث)',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (_securityLogs.isEmpty)
              const Text('لا توجد أحداث أمان مسجلة')
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _securityLogs.length,
                itemBuilder: (context, index) {
                  final log = _securityLogs[index];
                  return _buildSecurityLogItem(log);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityLogItem(Map<String, dynamic> log) {
    final event = log['event'] as String? ?? 'Unknown';
    final timestamp = log['timestamp'] as String?;
    final userId = log['userId'] as String? ?? 'Unknown';
    
    IconData icon;
    Color color;
    
    switch (event) {
      case 'login':
        icon = Icons.login;
        color = Colors.green;
        break;
      case 'logout':
        icon = Icons.logout;
        color = Colors.orange;
        break;
      case 'failed_login':
        icon = Icons.error;
        color = Colors.red;
        break;
      case 'password_change':
        icon = Icons.lock_reset;
        color = Colors.blue;
        break;
      default:
        icon = Icons.info;
        color = Colors.grey;
    }
    
    return ListTile(
      leading: Icon(icon, color: color, size: 20),
      title: Text(
        _getEventDisplayName(event),
        style: const TextStyle(fontSize: 14),
      ),
      subtitle: Text(
        'المستخدم: $userId',
        style: const TextStyle(fontSize: 12),
      ),
      trailing: timestamp != null
          ? Text(
              AppUtils.formatDateTime(DateTime.parse(timestamp)),
              style: const TextStyle(fontSize: 10),
            )
          : null,
    );
  }

  String _getEventDisplayName(String event) {
    switch (event) {
      case 'login':
        return 'تسجيل دخول';
      case 'logout':
        return 'تسجيل خروج';
      case 'failed_login':
        return 'فشل تسجيل دخول';
      case 'password_change':
        return 'تغيير كلمة المرور';
      case 'backup_created':
        return 'إنشاء نسخة احتياطية';
      case 'backup_restored':
        return 'استعادة نسخة احتياطية';
      default:
        return event;
    }
  }

  // Action handlers
  Future<void> _createFullBackup() async {
    try {
      AppUtils.showSnackBar(context, 'جاري إنشاء النسخة الاحتياطية الكاملة...');
      
      await _backupService.createFullBackup();
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إنشاء النسخة الاحتياطية بنجاح');
        _loadData(); // Refresh the list
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إنشاء النسخة الاحتياطية: $e', isError: true);
      }
    }
  }

  Future<void> _createIncrementalBackup() async {
    try {
      AppUtils.showSnackBar(context, 'جاري إنشاء النسخة الاحتياطية التدريجية...');
      
      await _backupService.createIncrementalBackup();

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إنشاء النسخة الاحتياطية بنجاح');
        _loadData(); // Refresh the list
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إنشاء النسخة الاحتياطية: $e', isError: true);
      }
    }
  }

  void _handleBackupAction(String action, BackupModel backup) {
    switch (action) {
      case 'restore':
        _showRestoreConfirmation(backup);
        break;
      case 'delete':
        _showDeleteConfirmation(backup);
        break;
    }
  }

  void _showRestoreConfirmation(BackupModel backup) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعادة النسخة الاحتياطية'),
        content: Text('هل أنت متأكد من استعادة النسخة الاحتياطية "${backup.name}"؟\n\nسيتم استبدال جميع البيانات الحالية.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _restoreBackup(backup);
            },
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(BackupModel backup) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف النسخة الاحتياطية'),
        content: Text('هل أنت متأكد من حذف النسخة الاحتياطية "${backup.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteBackup(backup);
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _restoreBackup(BackupModel backup) async {
    try {
      AppUtils.showSnackBar(context, 'جاري استعادة النسخة الاحتياطية...');
      
      await _backupService.restoreFromBackupModel(backup);
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم استعادة النسخة الاحتياطية بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في استعادة النسخة الاحتياطية: $e', isError: true);
      }
    }
  }

  Future<void> _deleteBackup(BackupModel backup) async {
    try {
      // Delete the backup file
      // In a real implementation, you would delete from both local and cloud
      AppUtils.showSnackBar(context, 'تم حذف النسخة الاحتياطية');
      _loadData(); // Refresh the list
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في حذف النسخة الاحتياطية: $e', isError: true);
      }
    }
  }

  void _changePassword() {
    // Navigate to change password screen
    AppUtils.showSnackBar(context, 'ميزة تغيير كلمة المرور قيد التطوير');
  }

  Future<void> _resetEncryptionKeys() async {
    try {
      // This would regenerate encryption keys
      AppUtils.showSnackBar(context, 'تم إعادة تعيين مفاتيح التشفير');
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في إعادة تعيين المفاتيح: $e', isError: true);
    }
  }

  Future<void> _clearSecureData() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تنظيف البيانات الآمنة'),
        content: const Text('هل أنت متأكد من مسح جميع البيانات المشفرة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _securityService.clearSecureData();
                if (mounted) {
                  AppUtils.showSnackBar(context, 'تم مسح البيانات الآمنة');
                }
              } catch (e) {
                if (mounted) {
                  AppUtils.showSnackBar(context, 'خطأ في مسح البيانات: $e', isError: true);
                }
              }
            },
            child: const Text('مسح'),
          ),
        ],
      ),
    );
  }

  Future<void> _cleanupLogs() async {
    try {
      await _securityService.cleanupSecurityLogs();
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تنظيف السجلات القديمة');
        _loadData(); // Refresh logs
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تنظيف السجلات: $e', isError: true);
      }
    }
  }
}
