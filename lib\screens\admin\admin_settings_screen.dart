import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/data_service.dart';
import '../../core/utils/app_colors.dart';

class AdminSettingsScreen extends StatefulWidget {
  const AdminSettingsScreen({super.key});

  @override
  State<AdminSettingsScreen> createState() => _AdminSettingsScreenState();
}

class _AdminSettingsScreenState extends State<AdminSettingsScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات المدير'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إدارة البيانات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: ListTile(
                leading: const Icon(
                  Icons.delete_sweep,
                  color: Colors.orange,
                ),
                title: const Text('مسح البيانات الوهمية'),
                subtitle: const Text('مسح جميع البيانات التجريبية والاحتفاظ بحساب المدير فقط'),
                trailing: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.arrow_forward_ios),
                onTap: _isLoading ? null : _showClearDemoDataDialog,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: ListTile(
                leading: const Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                ),
                title: const Text('معلومات التطبيق'),
                subtitle: const Text('الإصدار 1.0.0'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: _showAppInfoDialog,
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: ListTile(
                leading: const Icon(
                  Icons.contact_support,
                  color: Colors.green,
                ),
                title: const Text('تواصل مع المطور'),
                subtitle: const Text('مطاسم سالم - 01062606098'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: _showDeveloperContactDialog,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearDemoDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد مسح البيانات'),
        content: const Text(
          'هل أنت متأكد من مسح جميع البيانات الوهمية؟\n\n'
          'سيتم مسح:\n'
          '• جميع المخازن\n'
          '• جميع الأصناف\n'
          '• جميع الفواتير\n'
          '• جميع المدفوعات\n'
          '• جميع حسابات الوكلاء\n\n'
          'سيتم الاحتفاظ بحساب المدير الأعلى فقط.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _clearDemoData();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('مسح البيانات'),
          ),
        ],
      ),
    );
  }

  void _showAppInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات التطبيق'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تطبيق آل فرحان للنقل الخفيف'),
            SizedBox(height: 8),
            Text('الإصدار: 1.0.0'),
            SizedBox(height: 8),
            Text('تاريخ الإصدار: ديسمبر 2024'),
            SizedBox(height: 8),
            Text('المطور: مطاسم سالم'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showDeveloperContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تواصل مع المطور'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المطور: مطاسم سالم'),
            SizedBox(height: 8),
            Text('واتساب: 01062606098'),
            SizedBox(height: 8),
            Text('للدعم الفني والاستفسارات'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Future<void> _clearDemoData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final dataService = Provider.of<DataService>(context, listen: false);
      await dataService.clearDemoData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم مسح البيانات الوهمية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في مسح البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
