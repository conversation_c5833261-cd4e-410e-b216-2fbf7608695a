import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

/// Ultimate SSL bypass solution - converts all HTTPS to HTTP
class UltimateSSLBypass {
  static final UltimateSSLBypass _instance = UltimateSSLBypass._internal();
  factory UltimateSSLBypass() => _instance;
  UltimateSSLBypass._internal();

  late final http.Client _httpClient;
  bool _initialized = false;

  /// Initialize with complete SSL bypass
  void initialize() {
    if (_initialized) return;

    if (kDebugMode) {
      print('🔓 ULTIMATE SSL BYPASS: Initializing...');
    }

    // Create HttpClient with COMPLETE SSL bypass
    final httpClient = HttpClient();
    
    // COMPLETELY DISABLE ALL SSL VERIFICATION
    httpClient.badCertificateCallback = (cert, host, port) {
      if (kDebugMode) {
        print('🔓 ULTIMATE BYPASS: Allowing ALL certificates for $host:$port');
      }
      return true; // ALLOW EVERYTHING
    };

    // Disable all security checks
    httpClient.connectionTimeout = const Duration(seconds: 60);
    httpClient.idleTimeout = const Duration(seconds: 60);
    
    // Create IOClient wrapper
    _httpClient = IOClient(httpClient);
    _initialized = true;

    if (kDebugMode) {
      print('✅ ULTIMATE SSL BYPASS: Complete bypass initialized');
    }
  }

  /// Convert HTTPS URL to HTTP for complete SSL bypass
  String convertToHttp(String originalUrl) {
    if (originalUrl.isEmpty || originalUrl.trim().isEmpty) {
      return '';
    }

    // Convert ALL HTTPS URLs to HTTP
    if (originalUrl.startsWith('https://')) {
      final httpUrl = originalUrl.replaceFirst('https://', 'http://');
      if (kDebugMode) {
        print('🔓 ULTIMATE BYPASS: Converting HTTPS to HTTP');
        print('   Original: $originalUrl');
        print('   HTTP: $httpUrl');
      }
      return httpUrl;
    }

    return originalUrl;
  }

  /// Download image with ultimate bypass
  Future<Uint8List?> downloadImage(String imageUrl) async {
    if (!_initialized) initialize();

    if (imageUrl.isEmpty) {
      if (kDebugMode) {
        print('❌ ULTIMATE BYPASS: Empty URL provided');
      }
      return null;
    }

    try {
      // Convert to HTTP first
      final httpUrl = convertToHttp(imageUrl);
      
      if (kDebugMode) {
        print('🔓 ULTIMATE BYPASS: Downloading $httpUrl');
      }

      final response = await _httpClient.get(
        Uri.parse(httpUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
          'Accept-Encoding': 'gzip, deflate',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        if (kDebugMode) {
          print('✅ ULTIMATE BYPASS: Image downloaded successfully (${response.bodyBytes.length} bytes)');
        }
        return response.bodyBytes;
      } else {
        if (kDebugMode) {
          print('❌ ULTIMATE BYPASS: HTTP ${response.statusCode} for $httpUrl');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ ULTIMATE BYPASS: Error downloading $imageUrl - $e');
      }
      return null;
    }
  }

  /// Test connectivity with ultimate bypass
  Future<bool> testConnectivity() async {
    try {
      if (kDebugMode) {
        print('🧪 ULTIMATE BYPASS: Testing connectivity...');
      }

      // Test HTTP URLs only
      final testUrls = [
        'http://httpbin.org/get',
        'http://res.cloudinary.com/demo/image/upload/sample.jpg',
      ];

      for (final url in testUrls) {
        try {
          final response = await _httpClient.get(Uri.parse(url)).timeout(const Duration(seconds: 10));
          if (response.statusCode == 200) {
            if (kDebugMode) {
              print('✅ ULTIMATE BYPASS: Connectivity test passed for $url');
            }
            return true;
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ ULTIMATE BYPASS: Test failed for $url - $e');
          }
        }
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ ULTIMATE BYPASS: Connectivity test failed - $e');
      }
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    if (_initialized) {
      _httpClient.close();
      _initialized = false;
      if (kDebugMode) {
        print('🗑️ ULTIMATE SSL BYPASS: Disposed');
      }
    }
  }
}

/// Global HTTP overrides with ultimate SSL bypass
class UltimateHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final client = super.createHttpClient(context);
    
    if (kDebugMode) {
      print('🔓 ULTIMATE HTTP OVERRIDE: Creating client with complete SSL bypass');
    }
    
    // COMPLETELY DISABLE SSL VERIFICATION
    client.badCertificateCallback = (cert, host, port) {
      if (kDebugMode) {
        print('🔓 ULTIMATE OVERRIDE: Bypassing SSL for $host:$port');
      }
      return true; // ALLOW ALL CERTIFICATES
    };
    
    // Set aggressive timeouts
    client.connectionTimeout = const Duration(seconds: 60);
    client.idleTimeout = const Duration(seconds: 60);
    
    return client;
  }
}

/// Initialize ultimate SSL bypass globally
void initializeUltimateSSLBypass() {
  if (kDebugMode) {
    print('🔓 INITIALIZING ULTIMATE SSL BYPASS...');
  }
  
  // Set global HTTP overrides
  HttpOverrides.global = UltimateHttpOverrides();
  
  // Initialize ultimate bypass
  UltimateSSLBypass().initialize();
  
  if (kDebugMode) {
    print('✅ ULTIMATE SSL BYPASS INITIALIZED GLOBALLY');
  }
}
