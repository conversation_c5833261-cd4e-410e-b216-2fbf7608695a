import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/theme/app_colors.dart';
import '../../core/utils/app_utils.dart';
import '../../models/professional_agent_account.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/professional_agent_service.dart';
import '../../services/data_service.dart';

/// شاشة إدارة الوكلاء الاحترافية
/// تدعم جميع العمليات المحاسبية بدقة ووضوح
class ProfessionalAgentManagementScreen extends StatefulWidget {
  const ProfessionalAgentManagementScreen({super.key});

  @override
  State<ProfessionalAgentManagementScreen> createState() => _ProfessionalAgentManagementScreenState();
}

class _ProfessionalAgentManagementScreenState extends State<ProfessionalAgentManagementScreen> {
  final ProfessionalAgentService _agentService = ProfessionalAgentService();
  final DataService _dataService = DataService.instance;
  
  List<ProfessionalAgentAccount> _agentAccounts = [];
  List<UserModel> _agents = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _sortBy = 'name'; // 'name', 'balance', 'sales', 'transactions'
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل البيانات
      final accounts = await _agentService.getAllAgentAccounts();
      final agents = await _dataService.getUsersByRole('agent');

      setState(() {
        _agentAccounts = accounts;
        _agents = agents;
        _isLoading = false;
      });

      _sortAccounts();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    }
  }

  void _sortAccounts() {
    _agentAccounts.sort((a, b) {
      int comparison = 0;
      
      switch (_sortBy) {
        case 'name':
          comparison = a.agentName.compareTo(b.agentName);
          break;
        case 'balance':
          comparison = a.currentBalance.compareTo(b.currentBalance);
          break;
        case 'sales':
          comparison = a.totalCustomerSales.compareTo(b.totalCustomerSales);
          break;
        case 'transactions':
          comparison = a.transactionCount.compareTo(b.transactionCount);
          break;
      }
      
      return _sortAscending ? comparison : -comparison;
    });
  }

  List<ProfessionalAgentAccount> get _filteredAccounts {
    if (_searchQuery.isEmpty) return _agentAccounts;
    
    return _agentAccounts.where((account) {
      return account.agentName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             account.agentPhone?.toLowerCase().contains(_searchQuery.toLowerCase()) == true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الوكلاء الاحترافية'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() {
                if (_sortBy == value) {
                  _sortAscending = !_sortAscending;
                } else {
                  _sortBy = value;
                  _sortAscending = true;
                }
                _sortAccounts();
              });
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'name', child: Text('ترتيب بالاسم')),
              const PopupMenuItem(value: 'balance', child: Text('ترتيب بالرصيد')),
              const PopupMenuItem(value: 'sales', child: Text('ترتيب بالمبيعات')),
              const PopupMenuItem(value: 'transactions', child: Text('ترتيب بالمعاملات')),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والإحصائيات
          _buildHeaderSection(),
          
          // قائمة الوكلاء
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildAgentsList(),
          ),
        ],
      ),
      floatingActionButton: authProvider.canManageUsers
          ? FloatingActionButton(
              onPressed: () {
                // الانتقال لشاشة إضافة وكيل جديد
                Navigator.pushNamed(context, '/admin/add_agent');
              },
              backgroundColor: AppColors.primary,
              child: const Icon(Icons.person_add, color: Colors.white),
            )
          : null,
    );
  }

  Widget _buildHeaderSection() {
    final totalBalance = _agentAccounts.fold(0.0, (sum, account) => sum + account.currentBalance);
    final totalSales = _agentAccounts.fold(0.0, (sum, account) => sum + account.totalCustomerSales);
    final totalCommissions = _agentAccounts.fold(0.0, (sum, account) => sum + account.totalAgentCommission);
    final activeAgents = _agentAccounts.where((account) => account.isActive).length;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            decoration: InputDecoration(
              hintText: 'البحث عن وكيل (الاسم أو الهاتف)...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          // الإحصائيات
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 2.5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard(
                title: 'إجمالي الوكلاء',
                value: '$activeAgents',
                icon: Icons.people,
                color: AppColors.primary,
              ),
              _buildStatCard(
                title: 'إجمالي الأرصدة',
                value: '${totalBalance.toStringAsFixed(0)} ج.م',
                icon: Icons.account_balance_wallet,
                color: totalBalance >= 0 ? Colors.green : Colors.red,
              ),
              _buildStatCard(
                title: 'إجمالي المبيعات',
                value: '${totalSales.toStringAsFixed(0)} ج.م',
                icon: Icons.trending_up,
                color: Colors.blue,
              ),
              _buildStatCard(
                title: 'إجمالي العمولات',
                value: '${totalCommissions.toStringAsFixed(0)} ج.م',
                icon: Icons.monetization_on,
                color: Colors.orange,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildAgentsList() {
    final filteredAccounts = _filteredAccounts;
    
    if (filteredAccounts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد حسابات وكلاء',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredAccounts.length,
      itemBuilder: (context, index) {
        final account = filteredAccounts[index];
        return _buildAgentCard(account);
      },
    );
  }

  Widget _buildAgentCard(ProfessionalAgentAccount account) {
    final balanceColor = account.currentBalance >= 0 ? Colors.green : Colors.red;
    final isValid = account.validateAccount();
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isValid ? Colors.transparent : Colors.red.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: isValid ? AppColors.primary : Colors.red,
          child: Text(
            account.agentName.isNotEmpty ? account.agentName[0] : 'و',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                account.agentName,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            if (!isValid)
              const Icon(Icons.warning, color: Colors.red, size: 16),
            if (account.isVerified)
              const Icon(Icons.verified, color: Colors.green, size: 16),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('الرصيد: ${account.currentBalance.toStringAsFixed(2)} ج.م'),
            Text('المبيعات: ${account.totalCustomerSales.toStringAsFixed(0)} ج.م'),
            Text('المعاملات: ${account.transactionCount}'),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: balanceColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            account.getAccountStatus(),
            style: TextStyle(
              color: balanceColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildDetailRow('الهاتف', account.agentPhone ?? 'غير محدد'),
                _buildDetailRow('البريد الإلكتروني', account.agentEmail ?? 'غير محدد'),
                _buildDetailRow('إجمالي العمولات', '${account.totalAgentCommission.toStringAsFixed(2)} ج.م'),
                _buildDetailRow('أرباح الشركة', '${account.totalCompanyProfits.toStringAsFixed(2)} ج.م'),
                _buildDetailRow('المدفوعات المستلمة', '${account.totalPaymentsReceived.toStringAsFixed(2)} ج.م'),
                _buildDetailRow('آخر معاملة', account.lastTransactionDate != null 
                    ? AppUtils.formatDate(account.lastTransactionDate!) 
                    : 'لا توجد معاملات'),
                _buildDetailRow('تاريخ الإنشاء', AppUtils.formatDate(account.createdAt)),
                _buildDetailRow('آخر تحديث', AppUtils.formatDate(account.updatedAt)),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _showAccountDetails(account),
                        icon: const Icon(Icons.visibility, size: 16),
                        label: const Text('التفاصيل'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _recalculateAccount(account),
                        icon: const Icon(Icons.refresh, size: 16),
                        label: const Text('إعادة حساب'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  void _showAccountDetails(ProfessionalAgentAccount account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل حساب ${account.agentName}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('ملخص الحساب:', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: 8),
              ...account.getAccountSummary().entries.map((entry) {
                return _buildDetailRow(entry.key, entry.value.toString());
              }),
              const SizedBox(height: 16),
              Text('حالة التحقق:', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    account.validateAccount() ? Icons.check_circle : Icons.error,
                    color: account.validateAccount() ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  Text(account.validateAccount() ? 'الحساب صحيح' : 'يحتاج إعادة حساب'),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<void> _recalculateAccount(ProfessionalAgentAccount account) async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إعادة حساب الحساب...'),
            ],
          ),
        ),
      );

      await _agentService.getOrCreateAgentAccount(account.agentId);
      await _loadData();

      if (mounted) {
        Navigator.pop(context); // إغلاق dialog التحميل
        AppUtils.showSnackBar(context, 'تم إعادة حساب الحساب بنجاح');
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // إغلاق dialog التحميل
        AppUtils.showSnackBar(context, 'خطأ في إعادة حساب الحساب: $e', isError: true);
      }
    }
  }
}
