import 'dart:typed_data';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// خدمة استخراج بيانات الموتورات من الصور باستخدام OCR
class MotorDataExtractionService {
  static final MotorDataExtractionService _instance = MotorDataExtractionService._internal();
  factory MotorDataExtractionService() => _instance;
  MotorDataExtractionService._internal();

  static MotorDataExtractionService get instance => _instance;

  // OCR text recognizer
  final TextRecognizer _textRecognizer = TextRecognizer();

  // نماذج البيانات المتوقعة من الصور
  final Map<String, List<String>> _motorBrandPatterns = {
    'هوندا': ['honda', 'هوندا', 'HONDA'],
    'ياماها': ['yamaha', 'ياماها', 'YAMA<PERSON>'],
    'سوزوكي': ['suzuki', 'سوزوكي', 'SUZUKI'],
    'كاواساكي': ['kawasaki', 'كاواساكي', 'KAWASAKI'],
    'بينيلي': ['benelli', 'بينيلي', 'BENELLI'],
    'كيم كو': ['kymco', 'كيم كو', 'KYMCO'],
    'سيم': ['sym', 'سيم', 'SYM'],
    'TVS': ['tvs', 'تي في اس', 'TVS'],
    'باجاج': ['bajaj', 'باجاج', 'BAJAJ'],
  };

  final Map<String, List<String>> _vehicleTypePatterns = {
    'موتوسيكل': ['motorcycle', 'bike', 'موتوسيكل', 'دراجة نارية'],
    'تروسيكل': ['tricycle', 'تروسيكل', 'ثلاث عجلات'],
    'سكوتر كهرباء': ['electric scooter', 'e-scooter', 'سكوتر كهرباء', 'كهربائي'],
    'توكتوك': ['tuk tuk', 'توك توك', 'توكتوك', 'ريكشا'],
  };

  /// استخراج بيانات الموتور من الصورة باستخدام OCR
  Future<Map<String, dynamic>> extractMotorData(Uint8List imageBytes) async {
    try {
      if (kDebugMode) {
        print('🔍 بدء استخراج بيانات الموتور من الصورة باستخدام OCR...');
      }

      // استخراج النص من الصورة باستخدام OCR
      final extractedText = await _extractTextFromImage(imageBytes);

      if (kDebugMode) {
        print('📝 النص المستخرج: $extractedText');
      }

      // تحليل النص المستخرج لاستخراج البيانات
      final extractedData = await _parseExtractedText(extractedText);

      if (kDebugMode) {
        print('✅ تم استخراج البيانات بنجاح: $extractedData');
      }

      return extractedData;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في استخراج البيانات: $e');
      }
      // في حالة فشل OCR، استخدم البيانات المحاكاة كبديل
      return await _simulateDataExtraction(imageBytes);
    }
  }

  /// استخراج النص من الصورة باستخدام OCR
  Future<String> _extractTextFromImage(Uint8List imageBytes) async {
    try {
      // حفظ الصورة مؤقتاً
      final tempDir = await getTemporaryDirectory();
      final tempFile = File(path.join(tempDir.path, 'temp_image_${DateTime.now().millisecondsSinceEpoch}.jpg'));
      await tempFile.writeAsBytes(imageBytes);

      // إنشاء InputImage من الملف
      final inputImage = InputImage.fromFile(tempFile);

      // استخراج النص باستخدام OCR
      final recognizedText = await _textRecognizer.processImage(inputImage);

      // حذف الملف المؤقت
      await tempFile.delete();

      return recognizedText.text;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في OCR: $e');
      }
      return '';
    }
  }

  /// تحليل النص المستخرج لاستخراج البيانات المطلوبة
  Future<Map<String, dynamic>> _parseExtractedText(String extractedText) async {
    if (extractedText.isEmpty) {
      return _getDefaultData();
    }

    final lines = extractedText.split('\n').map((line) => line.trim()).where((line) => line.isNotEmpty).toList();

    // البحث عن البيانات في النص المستخرج
    String? brand = _extractBrand(lines);
    String? model = _extractModel(lines, brand);
    String? motorFingerprint = _extractMotorFingerprint(lines);
    String? chassisNumber = _extractChassisNumber(lines);
    String? vehicleType = _extractVehicleType(lines);
    String? color = _extractColor(lines);
    int? year = _extractYear(lines);
    String? engineCapacity = _extractEngineCapacity(lines);

    // حساب مستوى الثقة بناءً على عدد البيانات المستخرجة
    double confidence = _calculateConfidenceFromExtraction(
      brand, model, motorFingerprint, chassisNumber, vehicleType, color, year, engineCapacity
    );

    return {
      'brand': brand ?? 'غير محدد',
      'model': model ?? 'غير محدد',
      'vehicleType': vehicleType ?? 'موتوسيكل',
      'motorFingerprint': motorFingerprint ?? '',
      'chassisNumber': chassisNumber ?? '',
      'year': year ?? DateTime.now().year,
      'color': color ?? 'غير محدد',
      'engineCapacity': engineCapacity ?? '150cc',
      'confidence': confidence,
      'extractedText': extractedText, // للمراجعة
    };
  }

  /// استخراج بصمة الموتور من الصورة (أرقام فقط)
  Future<String?> extractMotorFingerprint(Uint8List imageBytes) async {
    try {
      if (kDebugMode) {
        print('🔍 استخراج بصمة الموتور (أرقام فقط)...');
      }

      // استخراج النص باستخدام OCR
      final extractedText = await _extractTextFromImage(imageBytes);

      if (kDebugMode) {
        print('📝 النص المستخرج: $extractedText');
      }

      // استخراج الأرقام فقط من النص
      final numbersOnly = _extractNumbersOnly(extractedText);

      if (kDebugMode) {
        print('✅ الأرقام المستخرجة: $numbersOnly');
      }

      return numbersOnly;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في استخراج بصمة الموتور: $e');
      }
      return null;
    }
  }

  /// استخراج رقم الشاسيه من الصورة (أرقام فقط)
  Future<String?> extractChassisNumber(Uint8List imageBytes) async {
    try {
      if (kDebugMode) {
        print('🔍 استخراج رقم الشاسيه (أرقام فقط)...');
      }

      // استخراج النص باستخدام OCR
      final extractedText = await _extractTextFromImage(imageBytes);

      if (kDebugMode) {
        print('📝 النص المستخرج: $extractedText');
      }

      // استخراج الأرقام فقط من النص
      final numbersOnly = _extractNumbersOnly(extractedText);

      if (kDebugMode) {
        print('✅ الأرقام المستخرجة: $numbersOnly');
      }

      return numbersOnly;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في استخراج رقم الشاسيه: $e');
      }
      return null;
    }
  }

  /// استخراج الأرقام فقط من النص
  String? _extractNumbersOnly(String text) {
    if (text.isEmpty) return null;

    // إزالة جميع المسافات والأحرف والرموز، الاحتفاظ بالأرقام فقط
    final numbersOnly = text.replaceAll(RegExp(r'[^0-9]'), '');

    // إذا كان النص يحتوي على أرقام، إرجاعها
    if (numbersOnly.isNotEmpty && numbersOnly.length >= 3) {
      return numbersOnly;
    }

    return null;
  }

  /// محاكاة استخراج البيانات الكاملة
  Future<Map<String, dynamic>> _simulateDataExtraction(Uint8List imageBytes) async {
    // محاكاة تأخير المعالجة
    await Future.delayed(const Duration(seconds: 2));

    // تحليل حجم الصورة لتحديد نوع البيانات
    final imageSize = imageBytes.length;
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    // توليد بيانات واقعية بناءً على النماذج
    final brands = _motorBrandPatterns.keys.toList();
    final vehicleTypes = _vehicleTypePatterns.keys.toList();

    final selectedBrand = brands[timestamp % brands.length];
    final selectedType = vehicleTypes[timestamp % vehicleTypes.length];

    return {
      'brand': selectedBrand,
      'model': _generateModel(selectedBrand, timestamp),
      'vehicleType': selectedType,
      'motorFingerprint': _generateMotorFingerprint(timestamp),
      'chassisNumber': _generateChassisNumber(timestamp),
      'year': _generateYear(),
      'color': _generateColor(timestamp),
      'engineCapacity': _generateEngineCapacity(selectedType),
      'confidence': _calculateConfidence(imageSize),
    };
  }

  /// محاكاة استخراج بصمة الموتور
  Future<String> _simulateMotorFingerprintExtraction(Uint8List imageBytes) async {
    await Future.delayed(const Duration(seconds: 1));
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return _generateMotorFingerprint(timestamp);
  }

  /// محاكاة استخراج رقم الشاسيه
  Future<String> _simulateChassisNumberExtraction(Uint8List imageBytes) async {
    await Future.delayed(const Duration(seconds: 1));
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return _generateChassisNumber(timestamp);
  }

  /// توليد موديل واقعي
  String _generateModel(String brand, int timestamp) {
    final models = {
      'هوندا': ['CB150', 'CBR150', 'CG125', 'Wave110'],
      'ياماها': ['YBR125', 'FZ150', 'R15', 'Vino125'],
      'سوزوكي': ['GS150', 'Hayate110', 'GSX150'],
      'كاواساكي': ['Ninja150', 'KLX150', 'Versys150'],
      'بينيلي': ['TNT150', 'Leoncino150', 'TRK150'],
      'كيم كو': ['Agility125', 'People150', 'Downtown300'],
      'سيم': ['Wolf150', 'Jet4', 'Citycom300'],
      'TVS': ['Apache150', 'Star110', 'Jupiter110'],
      'باجاج': ['Pulsar150', 'Discover125', 'CT100'],
    };

    final brandModels = models[brand] ?? ['Model150'];
    return brandModels[timestamp % brandModels.length];
  }

  /// توليد بصمة موتور واقعية
  String _generateMotorFingerprint(int timestamp) {
    final prefixes = ['ENG', 'MOT', 'FP'];
    final prefix = prefixes[timestamp % prefixes.length];
    final numbers = (timestamp % 1000000).toString().padLeft(6, '0');
    return '$prefix$numbers';
  }

  /// توليد رقم شاسيه واقعي
  String _generateChassisNumber(int timestamp) {
    final prefixes = ['JH2', 'LC6', 'MD2', 'RF4'];
    final prefix = prefixes[timestamp % prefixes.length];
    final numbers = (timestamp % 10000000).toString().padLeft(7, '0');
    return '$prefix$numbers';
  }

  /// توليد سنة الصنع
  int _generateYear() {
    final currentYear = DateTime.now().year;
    final years = List.generate(10, (index) => currentYear - index);
    return years[DateTime.now().millisecondsSinceEpoch % years.length];
  }

  /// توليد لون واقعي
  String _generateColor(int timestamp) {
    final colors = ['أسود', 'أبيض', 'أحمر', 'أزرق', 'فضي', 'رمادي', 'أخضر'];
    return colors[timestamp % colors.length];
  }

  /// توليد سعة المحرك
  String _generateEngineCapacity(String vehicleType) {
    switch (vehicleType) {
      case 'موتوسيكل':
        final capacities = ['125cc', '150cc', '200cc', '250cc'];
        return capacities[DateTime.now().millisecondsSinceEpoch % capacities.length];
      case 'تروسيكل':
        return '200cc';
      case 'سكوتر كهرباء':
        return 'كهربائي';
      case 'توكتوك':
        return '200cc';
      default:
        return '150cc';
    }
  }

  /// حساب مستوى الثقة
  double _calculateConfidence(int imageSize) {
    // حساب الثقة بناءً على حجم الصورة
    if (imageSize > 1000000) return 0.95; // صورة عالية الجودة
    if (imageSize > 500000) return 0.85;  // صورة جودة متوسطة
    if (imageSize > 100000) return 0.75;  // صورة جودة منخفضة
    return 0.60; // صورة جودة ضعيفة
  }

  /// بيانات افتراضية في حالة الفشل
  Map<String, dynamic> _getDefaultData() {
    return {
      'brand': 'غير محدد',
      'model': 'غير محدد',
      'vehicleType': 'موتوسيكل',
      'motorFingerprint': '',
      'chassisNumber': '',
      'year': DateTime.now().year,
      'color': 'غير محدد',
      'engineCapacity': '150cc',
      'confidence': 0.0,
    };
  }

  /// التحقق من صحة البيانات المستخرجة
  bool validateExtractedData(Map<String, dynamic> data) {
    final requiredFields = ['brand', 'model', 'vehicleType'];
    
    for (final field in requiredFields) {
      if (!data.containsKey(field) || 
          data[field] == null || 
          data[field].toString().isEmpty ||
          data[field] == 'غير محدد') {
        return false;
      }
    }

    // التحقق من مستوى الثقة
    final confidence = data['confidence'] as double? ?? 0.0;
    return confidence >= 0.6; // مستوى ثقة مقبول
  }

  /// تحسين البيانات المستخرجة
  Map<String, dynamic> enhanceExtractedData(Map<String, dynamic> data) {
    final enhanced = Map<String, dynamic>.from(data);

    // تنظيف وتحسين النصوص
    if (enhanced['brand'] != null) {
      enhanced['brand'] = _cleanText(enhanced['brand'].toString());
    }
    
    if (enhanced['model'] != null) {
      enhanced['model'] = _cleanText(enhanced['model'].toString());
    }

    // إضافة معلومات إضافية
    enhanced['extractionTimestamp'] = DateTime.now().toIso8601String();
    enhanced['extractionMethod'] = 'AI_SIMULATION';

    return enhanced;
  }

  /// استخراج الماركة من النص
  String? _extractBrand(List<String> lines) {
    for (final line in lines) {
      final cleanLine = line.toLowerCase();
      for (final brand in _motorBrandPatterns.keys) {
        for (final pattern in _motorBrandPatterns[brand]!) {
          if (cleanLine.contains(pattern.toLowerCase())) {
            return brand;
          }
        }
      }
    }
    return null;
  }

  /// استخراج الموديل من النص
  String? _extractModel(List<String> lines, String? brand) {
    // البحث عن أرقام وحروف تشبه أسماء الموديلات
    final modelPattern = RegExp(r'[A-Z]{1,3}[0-9]{2,4}|[0-9]{2,4}[A-Z]{1,3}');

    for (final line in lines) {
      final matches = modelPattern.allMatches(line);
      for (final match in matches) {
        final model = match.group(0);
        if (model != null && model.length >= 3) {
          return model;
        }
      }
    }

    // إذا لم نجد موديل، استخدم موديل افتراضي بناءً على الماركة
    if (brand != null) {
      return _generateModel(brand, DateTime.now().millisecondsSinceEpoch);
    }

    return null;
  }

  /// استخراج بصمة الموتور من النص
  String? _extractMotorFingerprint(List<String> lines) {
    // البحث عن أنماط بصمة الموتور (أرقام وحروف)
    final fingerprintPatterns = [
      RegExp(r'[A-Z]{2,3}[0-9]{6,8}'), // مثل ENG123456
      RegExp(r'[0-9]{6,8}[A-Z]{1,2}'), // مثل 123456AB
      RegExp(r'[A-Z]{1,2}[0-9]{4,6}[A-Z]{1,2}'), // مثل AB1234CD
      RegExp(r'[0-9]{8,12}'), // أرقام طويلة
    ];

    for (final line in lines) {
      for (final pattern in fingerprintPatterns) {
        final matches = pattern.allMatches(line);
        for (final match in matches) {
          final fingerprint = match.group(0);
          if (fingerprint != null && fingerprint.length >= 6) {
            return fingerprint;
          }
        }
      }
    }

    return null;
  }

  /// استخراج رقم الشاسيه من النص
  String? _extractChassisNumber(List<String> lines) {
    // البحث عن أنماط رقم الشاسيه (عادة 17 رقم/حرف)
    final chassisPatterns = [
      RegExp(r'[A-Z0-9]{17}'), // رقم شاسيه قياسي 17 رقم/حرف
      RegExp(r'[A-Z]{2,3}[0-9]{7,10}'), // أنماط أخرى
      RegExp(r'[0-9]{10,15}'), // أرقام طويلة
    ];

    for (final line in lines) {
      for (final pattern in chassisPatterns) {
        final matches = pattern.allMatches(line);
        for (final match in matches) {
          final chassis = match.group(0);
          if (chassis != null && chassis.length >= 10) {
            return chassis;
          }
        }
      }
    }

    return null;
  }

  /// استخراج نوع المركبة من النص
  String? _extractVehicleType(List<String> lines) {
    for (final line in lines) {
      final cleanLine = line.toLowerCase();
      for (final type in _vehicleTypePatterns.keys) {
        for (final pattern in _vehicleTypePatterns[type]!) {
          if (cleanLine.contains(pattern.toLowerCase())) {
            return type;
          }
        }
      }
    }
    return null;
  }

  /// استخراج اللون من النص
  String? _extractColor(List<String> lines) {
    final colors = ['أسود', 'أبيض', 'أحمر', 'أزرق', 'فضي', 'رمادي', 'أخضر', 'أصفر', 'بني', 'بنفسجي'];
    final englishColors = ['black', 'white', 'red', 'blue', 'silver', 'gray', 'green', 'yellow', 'brown', 'purple'];

    for (final line in lines) {
      final cleanLine = line.toLowerCase();

      // البحث عن الألوان العربية
      for (final color in colors) {
        if (cleanLine.contains(color)) {
          return color;
        }
      }

      // البحث عن الألوان الإنجليزية
      for (int i = 0; i < englishColors.length; i++) {
        if (cleanLine.contains(englishColors[i])) {
          return colors[i];
        }
      }
    }

    return null;
  }

  /// استخراج سنة الصنع من النص
  int? _extractYear(List<String> lines) {
    final currentYear = DateTime.now().year;
    final yearPattern = RegExp(r'(19|20)[0-9]{2}');

    for (final line in lines) {
      final matches = yearPattern.allMatches(line);
      for (final match in matches) {
        final yearStr = match.group(0);
        if (yearStr != null) {
          final year = int.tryParse(yearStr);
          if (year != null && year >= 1990 && year <= currentYear + 1) {
            return year;
          }
        }
      }
    }

    return null;
  }

  /// استخراج سعة المحرك من النص
  String? _extractEngineCapacity(List<String> lines) {
    final capacityPatterns = [
      RegExp(r'[0-9]{2,4}cc', caseSensitive: false),
      RegExp(r'[0-9]{2,4}\s*سي\s*سي'),
      RegExp(r'[0-9]{1,2}\.[0-9]L', caseSensitive: false),
    ];

    for (final line in lines) {
      for (final pattern in capacityPatterns) {
        final matches = pattern.allMatches(line);
        for (final match in matches) {
          final capacity = match.group(0);
          if (capacity != null) {
            return capacity;
          }
        }
      }
    }

    return null;
  }

  /// حساب مستوى الثقة بناءً على البيانات المستخرجة
  double _calculateConfidenceFromExtraction(
    String? brand, String? model, String? motorFingerprint,
    String? chassisNumber, String? vehicleType, String? color,
    int? year, String? engineCapacity
  ) {
    int foundFields = 0;
    int totalFields = 8;

    if (brand != null && brand != 'غير محدد') foundFields++;
    if (model != null && model != 'غير محدد') foundFields++;
    if (motorFingerprint != null && motorFingerprint.isNotEmpty) foundFields++;
    if (chassisNumber != null && chassisNumber.isNotEmpty) foundFields++;
    if (vehicleType != null && vehicleType != 'غير محدد') foundFields++;
    if (color != null && color != 'غير محدد') foundFields++;
    if (year != null) foundFields++;
    if (engineCapacity != null && engineCapacity != 'غير محدد') foundFields++;

    return foundFields / totalFields;
  }

  /// تنظيف النصوص
  String _cleanText(String text) {
    return text.trim()
        .replaceAll(RegExp(r'\s+'), ' ')
        .replaceAll(RegExp(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z0-9\s]'), '');
  }

  /// تنظيف موارد OCR
  void dispose() {
    _textRecognizer.close();
  }
}
