import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../core/utils/app_utils.dart';
import '../../widgets/accounting_journal_dashboard_card.dart';

/// لوحة التحكم الرئيسية لنظام Windows
/// مصممة خصيصاً للشاشات الكبيرة مع تخطيط متقدم
class WindowsDashboard extends StatefulWidget {
  const WindowsDashboard({Key? key}) : super(key: key);

  @override
  State<WindowsDashboard> createState() => _WindowsDashboardState();
}

class _WindowsDashboardState extends State<WindowsDashboard> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس لوحة التحكم
            _buildDashboardHeader(),
            
            const SizedBox(height: 24),
            
            // الإحصائيات السريعة
            _buildQuickStats(),
            
            const SizedBox(height: 24),
            
            // الصف الأول من البطاقات
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // العمود الأيسر
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      _buildInventoryOverview(),
                      const SizedBox(height: 16),
                      _buildAgentsOverview(),
                    ],
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // العمود الأيمن
                Expanded(
                  flex: 1,
                  child: Column(
                    children: [
                      _buildRecentActivities(),
                      const SizedBox(height: 16),
                      _buildQuickActions(),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // الصف الثاني من البطاقات
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // النظام المحاسبي
                Expanded(
                  child: AccountingJournalDashboardCard(),
                ),
                
                const SizedBox(width: 16),
                
                // التقارير والإحصائيات
                Expanded(
                  child: _buildReportsOverview(),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // الصف الثالث - الرسوم البيانية
            _buildChartsSection(),
          ],
        ),
      ),
    );
  }

  /// رأس لوحة التحكم
  Widget _buildDashboardHeader() {
    final now = DateTime.now();
    final greeting = _getGreeting();
    
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$greeting، مرحباً بك في El Farhan',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'اليوم ${DateFormat('EEEE، d MMMM yyyy', 'ar').format(now)}',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        
        // أزرار سريعة
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: _handleQuickSync,
              icon: const Icon(Icons.sync, size: 18),
              label: const Text('مزامنة سريعة'),
            ),
            const SizedBox(width: 12),
            OutlinedButton.icon(
              onPressed: _handleExportData,
              icon: const Icon(Icons.download, size: 18),
              label: const Text('تصدير البيانات'),
            ),
          ],
        ),
      ],
    );
  }

  /// الإحصائيات السريعة
  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي المخزون',
            '1,234',
            'صنف',
            Icons.inventory,
            Colors.blue,
            '+5.2%',
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'الوكلاء النشطون',
            '45',
            'وكيل',
            Icons.people,
            Colors.green,
            '+2.1%',
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'المبيعات اليوم',
            '12,500',
            'ج.م',
            Icons.trending_up,
            Colors.orange,
            '+8.7%',
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'الأرباح الشهرية',
            '85,000',
            'ج.م',
            Icons.monetization_on,
            Colors.purple,
            '+12.3%',
          ),
        ),
      ],
    );
  }

  /// بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    String unit,
    IconData icon,
    Color color,
    String change,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    change,
                    style: const TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 4),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  unit,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// نظرة عامة على المخزون
  Widget _buildInventoryOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.inventory, size: 24),
                const SizedBox(width: 12),
                Text(
                  'نظرة عامة على المخزون',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _navigateToInventory(),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInventoryItem('فلاتر الزيت', '150 قطعة', 'متوفر', Colors.green),
            _buildInventoryItem('فرامل أمامية', '75 قطعة', 'متوفر', Colors.green),
            _buildInventoryItem('إطارات', '25 قطعة', 'مخزون منخفض', Colors.orange),
            _buildInventoryItem('بطاريات', '5 قطع', 'نفد المخزون', Colors.red),
          ],
        ),
      ),
    );
  }

  /// عنصر المخزون
  Widget _buildInventoryItem(String name, String quantity, String status, Color statusColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  quantity,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: statusColor,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// نظرة عامة على الوكلاء
  Widget _buildAgentsOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.people, size: 24),
                const SizedBox(width: 12),
                Text(
                  'الوكلاء النشطون',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _navigateToAgents(),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildAgentItem('وكيل القاهرة', '15,000 ج.م', 'نشط'),
            _buildAgentItem('وكيل الإسكندرية', '8,500 ج.م', 'نشط'),
            _buildAgentItem('وكيل أسوان', '12,200 ج.م', 'نشط'),
          ],
        ),
      ),
    );
  }

  /// عنصر الوكيل
  Widget _buildAgentItem(String name, String balance, String status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            child: Icon(
              Icons.person,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  'الرصيد: $balance',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  /// الأنشطة الحديثة
  Widget _buildRecentActivities() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأنشطة الحديثة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              'تم إضافة فاتورة جديدة',
              'منذ 5 دقائق',
              Icons.receipt,
              Colors.blue,
            ),
            _buildActivityItem(
              'دفعة من وكيل القاهرة',
              'منذ 15 دقيقة',
              Icons.payment,
              Colors.green,
            ),
            _buildActivityItem(
              'تحديث مخزون الفلاتر',
              'منذ 30 دقيقة',
              Icons.inventory,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  /// عنصر النشاط
  Widget _buildActivityItem(String title, String time, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                Text(
                  time,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// الإجراءات السريعة
  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildQuickActionButton(
              'إضافة فاتورة',
              Icons.add_box,
              Colors.blue,
              () => _handleQuickAction('add_invoice'),
            ),
            const SizedBox(height: 8),
            _buildQuickActionButton(
              'تسجيل دفعة',
              Icons.payment,
              Colors.green,
              () => _handleQuickAction('add_payment'),
            ),
            const SizedBox(height: 8),
            _buildQuickActionButton(
              'إضافة صنف',
              Icons.inventory,
              Colors.orange,
              () => _handleQuickAction('add_item'),
            ),
          ],
        ),
      ),
    );
  }

  /// زر إجراء سريع
  Widget _buildQuickActionButton(String title, IconData icon, Color color, VoidCallback onPressed) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, color: color, size: 18),
        label: Text(title),
        style: OutlinedButton.styleFrom(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.all(12),
        ),
      ),
    );
  }

  /// نظرة عامة على التقارير
  Widget _buildReportsOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, size: 24),
                const SizedBox(width: 12),
                Text(
                  'التقارير والإحصائيات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'تقرير المبيعات الشهرية',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'إجمالي المبيعات: 250,000 ج.م',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'نمو: +15.2% عن الشهر الماضي',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _navigateToReports(),
                child: const Text('عرض جميع التقارير'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// قسم الرسوم البيانية
  Widget _buildChartsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الرسوم البيانية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).dividerColor,
                ),
              ),
              child: const Center(
                child: Text('الرسوم البيانية ستُضاف هنا'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return 'صباح الخير';
    if (hour < 17) return 'مساء الخير';
    return 'مساء الخير';
  }

  void _handleQuickSync() {
    AppUtils.showInfoSnackBar(context, 'جاري المزامنة...');
  }

  void _handleExportData() {
    AppUtils.showInfoSnackBar(context, 'جاري تصدير البيانات...');
  }

  void _navigateToInventory() {
    AppUtils.showInfoSnackBar(context, 'الانتقال لإدارة المخزون');
  }

  void _navigateToAgents() {
    AppUtils.showInfoSnackBar(context, 'الانتقال لإدارة الوكلاء');
  }

  void _navigateToReports() {
    AppUtils.showInfoSnackBar(context, 'الانتقال للتقارير');
  }

  void _handleQuickAction(String action) {
    AppUtils.showInfoSnackBar(context, 'تنفيذ إجراء: $action');
  }
}
