import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'dart:convert';

// Import Android services
import 'android_ocr_service.dart';
import 'android_permission_service.dart';

/// Android-specific image service with full camera and OCR support
class AndroidImageService {
  static AndroidImageService? _instance;
  static AndroidImageService get instance => _instance ??= AndroidImageService._();
  
  AndroidImageService._();

  final ImagePicker _picker = ImagePicker();
  bool _isInitialized = false;
  
  // Cloudinary configuration
  static const String _cloudName = 'dzh4fpnnw';
  static const String _uploadPreset = 'farhan';
  static const String _baseUrl = 'https://api.cloudinary.com/v1_1/$_cloudName';

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🔧 Initializing Android Image Service...');
      }

      // Initialize OCR service
      await AndroidOCRService.instance.initialize();

      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Android Image Service initialized successfully');
        print('☁️ Cloudinary configured: $_cloudName');
        print('📤 Upload preset: $_uploadPreset');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing Android Image Service: $e');
      }
    }
  }

  /// Request camera permission
  Future<bool> requestCameraPermission() async {
    return await AndroidPermissionService.instance.requestCameraPermission();
  }

  /// Request storage permission
  Future<bool> requestStoragePermission() async {
    return await AndroidPermissionService.instance.requestStoragePermission();
  }

  /// Request both camera and storage permissions
  Future<bool> requestCameraAndStoragePermissions() async {
    return await AndroidPermissionService.instance.requestCameraAndStoragePermissions();
  }

  /// Take photo from camera
  Future<Uint8List?> takePhoto() async {
    try {
      // Request camera permission
      if (kDebugMode) {
        print('📸 Requesting camera permission...');
      }

      if (!await requestCameraPermission()) {
        if (kDebugMode) {
          print('❌ Camera permission denied');
        }
        return null;
      }

      if (kDebugMode) {
        print('✅ Camera permission granted, opening camera...');
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
        preferredCameraDevice: CameraDevice.rear,
      );

      if (image != null) {
        final bytes = await image.readAsBytes();
        if (kDebugMode) {
          print('✅ Image captured successfully: ${bytes.length} bytes');
        }
        return bytes;
      } else {
        if (kDebugMode) {
          print('⚠️ No image captured (user cancelled)');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error taking photo: $e');
      }
    }
    return null;
  }

  /// Pick image from gallery
  Future<Uint8List?> pickFromGallery() async {
    try {
      if (kDebugMode) {
        print('🖼️ Opening gallery...');
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        final bytes = await image.readAsBytes();
        if (kDebugMode) {
          print('✅ Image selected from gallery: ${bytes.length} bytes');
        }
        return bytes;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking from gallery: $e');
      }
    }
    return null;
  }

  /// Show image source dialog
  Future<Uint8List?> showImageSourceDialog(BuildContext context, String title) async {
    return await showDialog<Uint8List?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('التقاط صورة'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final image = await takePhoto();
                  if (context.mounted) {
                    Navigator.of(context).pop(image);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('اختيار من المعرض'),
                onTap: () async {
                  Navigator.of(context).pop();
                  final image = await pickFromGallery();
                  if (context.mounted) {
                    Navigator.of(context).pop(image);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  /// Take motor fingerprint photo
  Future<Uint8List?> takeMotorFingerprintPhoto(BuildContext context) async {
    return await showImageSourceDialog(context, 'تصوير بصمة الموتور');
  }

  /// Take chassis photo
  Future<Uint8List?> takeChassisPhoto(BuildContext context) async {
    return await showImageSourceDialog(context, 'تصوير رقم الشاسيه');
  }

  /// Take ID card photo
  Future<Uint8List?> takeIdCardPhoto(BuildContext context, {required bool isFront}) async {
    final title = isFront ? 'تصوير الوجه الأمامي للبطاقة' : 'تصوير الوجه الخلفي للبطاقة';
    return await showImageSourceDialog(context, title);
  }

  /// Extract motor fingerprint text using OCR
  Future<Map<String, dynamic>> extractMotorFingerprintText(Uint8List imageBytes) async {
    try {
      if (kDebugMode) {
        print('🔍 Starting OCR extraction for motor fingerprint...');
      }

      final ocrResult = await AndroidOCRService.instance.extractTextFromImage(imageBytes);

      if (kDebugMode) {
        print('📝 OCR Result: ${ocrResult.text}');
        print('📊 Confidence: ${ocrResult.confidence}');
        print('🏍️ Is Motor Fingerprint: ${ocrResult.isMotorFingerprint}');
      }

      return {
        'motorFingerprint': ocrResult.text,
        'confidence': ocrResult.confidence.toString(),
        'method': 'android_ocr',
        'extractedData': ocrResult.extractedData,
        'isMotorFingerprint': ocrResult.isMotorFingerprint,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error extracting motor fingerprint: $e');
      }
      return {};
    }
  }

  /// Extract ID card data using OCR
  Future<Map<String, dynamic>> extractIdCardData(Uint8List frontImage, Uint8List backImage) async {
    try {
      if (kDebugMode) {
        print('🔍 Starting OCR extraction for ID card...');
      }

      // Extract text from front image
      final frontResult = await AndroidOCRService.instance.extractTextFromImage(frontImage);
      
      // Extract text from back image
      final backResult = await AndroidOCRService.instance.extractTextFromImage(backImage);

      // Combine and parse the results
      final combinedText = '${frontResult.text}\n${backResult.text}';
      final extractedData = _parseIdCardData(combinedText);

      if (kDebugMode) {
        print('📝 Front OCR: ${frontResult.text}');
        print('📝 Back OCR: ${backResult.text}');
        print('📊 Extracted Data: $extractedData');
      }

      return {
        'frontText': frontResult.text,
        'backText': backResult.text,
        'combinedText': combinedText,
        'extractedData': extractedData,
        'frontConfidence': frontResult.confidence,
        'backConfidence': backResult.confidence,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error extracting ID card data: $e');
      }
      return {};
    }
  }

  /// Parse ID card data from OCR text
  Map<String, dynamic> _parseIdCardData(String text) {
    final data = <String, dynamic>{};
    
    // Extract national ID number (14 digits)
    final idMatch = RegExp(r'\b\d{14}\b').firstMatch(text);
    if (idMatch != null) {
      data['nationalId'] = idMatch.group(0);
    }
    
    // Extract names (Arabic text patterns)
    final arabicNamePattern = RegExp(r'[\u0600-\u06FF\s]+');
    final names = arabicNamePattern.allMatches(text)
        .map((m) => m.group(0)?.trim())
        .where((name) => name != null && name.length > 2)
        .toList();
    
    if (names.isNotEmpty) {
      data['fullName'] = names.first;
    }
    
    // Extract dates (birth date, issue date, expiry date)
    final datePattern = RegExp(r'\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}\b');
    final dates = datePattern.allMatches(text).map((m) => m.group(0)).toList();
    
    if (dates.isNotEmpty) {
      data['birthDate'] = dates.first;
    }
    
    return data;
  }

  /// Upload image to Cloudinary
  Future<String?> uploadToCloudinary(Uint8List imageBytes, String folder, String fileName) async {
    try {
      if (kDebugMode) {
        print('☁️ Uploading image to Cloudinary...');
        print('📁 Folder: $folder');
        print('📄 File: $fileName');
      }

      final uri = Uri.parse('$_baseUrl/image/upload');
      final request = http.MultipartRequest('POST', uri);

      // Add upload parameters
      request.fields['upload_preset'] = _uploadPreset;
      request.fields['folder'] = folder;
      request.fields['public_id'] = fileName;

      // Add image file
      request.files.add(
        http.MultipartFile.fromBytes(
          'file',
          imageBytes,
          filename: '$fileName.jpg',
        ),
      );

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(responseBody);
        final imageUrl = jsonResponse['secure_url'] as String;

        if (kDebugMode) {
          print('✅ Image uploaded successfully');
          print('🔗 URL: $imageUrl');
        }

        return imageUrl;
      } else {
        if (kDebugMode) {
          print('❌ Upload failed with status: ${response.statusCode}');
          print('📄 Response: $responseBody');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error uploading image: $e');
      }
      return null;
    }
  }
}
