import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Performance optimizations for Al Farhan Transport App
class PerformanceOptimizations {
  static PerformanceOptimizations? _instance;
  static PerformanceOptimizations get instance => _instance ??= PerformanceOptimizations._();
  PerformanceOptimizations._();

  // Cache for frequently accessed data
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// Initialize performance optimizations
  void initialize() {
    if (kDebugMode) {
      print('🚀 Performance optimizations initialized');
    }

    // Set up memory pressure handling
    _setupMemoryPressureHandling();
    
    // Configure system UI for better performance
    _configureSystemUI();
    
    // Start cache cleanup timer
    _startCacheCleanup();
  }

  /// Cache data with expiry
  void cacheData(String key, dynamic data) {
    _cache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
    
    if (kDebugMode) {
      print('📦 Cached data for key: $key');
    }
  }

  /// Get cached data if not expired
  T? getCachedData<T>(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return null;
    
    final isExpired = DateTime.now().difference(timestamp) > _cacheExpiry;
    if (isExpired) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
      return null;
    }
    
    return _cache[key] as T?;
  }

  /// Clear cache
  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
    
    if (kDebugMode) {
      print('🧹 Cache cleared');
    }
  }

  /// Optimize image loading
  Widget optimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width?.round(),
      cacheHeight: height?.round(),
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: width,
          height: height,
          color: Colors.grey[300],
          child: const Icon(Icons.error),
        );
      },
    );
  }

  /// Optimized list view builder
  Widget optimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    bool shrinkWrap = false,
  }) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      controller: controller,
      shrinkWrap: shrinkWrap,
      // Performance optimizations
      cacheExtent: 500, // Cache 500 pixels ahead
      addAutomaticKeepAlives: false, // Don't keep alive off-screen items
      addRepaintBoundaries: true, // Isolate repaints
    );
  }

  /// Debounced function execution
  static Timer? _debounceTimer;
  static void debounce(Duration delay, VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  /// Throttled function execution
  static DateTime? _lastThrottleTime;
  static void throttle(Duration interval, VoidCallback callback) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || 
        now.difference(_lastThrottleTime!) >= interval) {
      _lastThrottleTime = now;
      callback();
    }
  }

  /// Lazy loading widget
  Widget lazyLoadWidget({
    required Widget Function() builder,
    Widget? placeholder,
  }) {
    return FutureBuilder<Widget>(
      future: Future.microtask(builder),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return snapshot.data!;
        }
        return placeholder ?? const CircularProgressIndicator();
      },
    );
  }

  /// Memory-efficient grid view
  Widget optimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required int crossAxisCount,
    double childAspectRatio = 1.0,
    ScrollController? controller,
  }) {
    return GridView.builder(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      controller: controller,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
      ),
      // Performance optimizations
      cacheExtent: 500,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
    );
  }

  /// Optimized text widget for large texts
  Widget optimizedText(
    String text, {
    TextStyle? style,
    int? maxLines,
    TextOverflow? overflow,
  }) {
    return Text(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
      // Performance optimization
      textWidthBasis: TextWidthBasis.longestLine,
    );
  }

  /// Batch database operations
  Future<void> batchDatabaseOperations(
    List<Future<void> Function()> operations,
  ) async {
    // Execute operations in batches to avoid overwhelming the database
    const batchSize = 10;
    
    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = operations.skip(i).take(batchSize);
      await Future.wait(batch.map((op) => op()));
      
      // Small delay between batches to prevent blocking
      if (i + batchSize < operations.length) {
        await Future.delayed(const Duration(milliseconds: 10));
      }
    }
  }

  /// Preload critical data
  Future<void> preloadCriticalData() async {
    try {
      if (kDebugMode) {
        print('🔄 Preloading critical data...');
      }

      // Preload commonly used data here
      // This would be implemented based on app usage patterns
      
      if (kDebugMode) {
        print('✅ Critical data preloaded');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error preloading data: $e');
      }
    }
  }

  /// Setup memory pressure handling
  void _setupMemoryPressureHandling() {
    SystemChannels.system.setMessageHandler((message) async {
      if (message is Map && message['type'] == 'memoryPressure') {
        _handleMemoryPressure();
      }
      return null;
    });
  }

  /// Handle memory pressure
  void _handleMemoryPressure() {
    if (kDebugMode) {
      print('⚠️ Memory pressure detected, clearing caches...');
    }
    
    // Clear image cache
    PaintingBinding.instance.imageCache.clear();
    
    // Clear our custom cache
    clearCache();
    
    // Force garbage collection
    if (kDebugMode) {
      print('🗑️ Caches cleared due to memory pressure');
    }
  }

  /// Configure system UI for better performance
  void _configureSystemUI() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
      ),
    );
  }

  /// Start cache cleanup timer
  void _startCacheCleanup() {
    Timer.periodic(const Duration(minutes: 10), (timer) {
      _cleanupExpiredCache();
    });
  }

  /// Cleanup expired cache entries
  void _cleanupExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _cache.remove(key);
      _cacheTimestamps.remove(key);
    }
    
    if (expiredKeys.isNotEmpty && kDebugMode) {
      debugPrint('🧹 Cleaned up ${expiredKeys.length} expired cache entries');
    }
  }

  /// Get memory usage statistics
  Map<String, dynamic> getMemoryStats() {
    return {
      'cacheSize': _cache.length,
      'imageCacheSize': PaintingBinding.instance.imageCache.currentSize,
      'imageCacheMaxSize': PaintingBinding.instance.imageCache.maximumSize,
    };
  }

  /// Optimize for low-end devices
  void optimizeForLowEndDevices() {
    // Reduce image cache size
    PaintingBinding.instance.imageCache.maximumSize = 50;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50MB
    
    // Reduce our cache expiry time
    // This would require modifying the const _cacheExpiry
    
    if (kDebugMode) {
      print('📱 Optimized for low-end devices');
    }
  }

  /// Check if device is low-end (simplified heuristic)
  bool isLowEndDevice() {
    // This is a simplified check - in a real app you might use
    // device_info_plus package to get more detailed hardware info
    return PaintingBinding.instance.imageCache.maximumSizeBytes < 100 << 20;
  }
}


