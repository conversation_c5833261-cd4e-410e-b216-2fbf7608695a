name: el_<PERSON>han_transport_android
description: "نظام إدارة النقل الشامل - النسخة الأصلية للأندرويد"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # Core dependencies
  cupertino_icons: ^1.0.8
  provider: ^6.1.2
  shared_preferences: ^2.3.2
  intl: ^0.20.2

  # Firebase - Native Android SDK
  firebase_core: ^3.6.0
  cloud_firestore: ^5.4.4
  firebase_auth: ^5.3.1
  firebase_messaging: ^15.1.3
  firebase_storage: ^12.3.2

  # Network & HTTP
  http: ^1.2.2
  connectivity_plus: ^6.1.0

  # UI & Charts
  google_fonts: ^6.2.1
  fl_chart: ^0.69.0
  cached_network_image: ^3.4.1

  # File handling - Android native
  path_provider: ^2.1.4
  path: ^1.9.0

  # Image handling
  image_picker: ^1.1.2
  image: ^4.2.0

  # OCR and ML - Android native
  google_mlkit_text_recognition: ^0.13.0
  google_mlkit_commons: ^0.7.1

  # Notifications
  flutter_local_notifications: ^18.0.1

  # Permissions
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2

  # Database - Android native SQLite
  sqflite: ^2.4.2

  # Utilities
  uuid: ^4.5.1
  crypto: ^3.0.5

  # Localization
  flutter_localizations:
    sdk: flutter

  # Security
  encrypt: ^5.0.3

  # PDF generation with Arabic support
  pdf: ^3.11.1
  printing: ^5.13.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/NotoSansArabic-Regular.ttf
        - asset: assets/fonts/NotoSansArabic-Bold.ttf
          weight: 700
