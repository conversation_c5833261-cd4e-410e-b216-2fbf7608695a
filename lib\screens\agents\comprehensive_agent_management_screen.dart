import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_accounting_system.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/agent_accounting_service.dart';
import '../../services/data_service.dart';
import 'agent_account_details_screen.dart';
import 'record_agent_payment_screen.dart';

/// شاشة إدارة الوكلاء الشاملة الجديدة
/// تعرض المديونيات والإحصائيات والحسابات بدقة محاسبية عالية
class ComprehensiveAgentManagementScreen extends StatefulWidget {
  const ComprehensiveAgentManagementScreen({super.key});

  @override
  State<ComprehensiveAgentManagementScreen> createState() => _ComprehensiveAgentManagementScreenState();
}

class _ComprehensiveAgentManagementScreenState extends State<ComprehensiveAgentManagementScreen>
    with TickerProviderStateMixin {
  
  final AgentAccountingService _accountingService = AgentAccountingService();
  final DataService _dataService = DataService.instance;
  
  late TabController _tabController;
  
  List<AgentAccount> _agentAccounts = [];
  Map<String, dynamic> _statistics = {};
  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل جميع البيانات
  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      if (kDebugMode) {
        print('📊 Loading comprehensive agent data...');
      }

      // تحميل حسابات الوكلاء
      _agentAccounts = await _accountingService.getAllAgentAccounts();
      
      // تحميل الإحصائيات
      _statistics = await _accountingService.getAgentsStatistics();

      if (kDebugMode) {
        print('✅ Loaded ${_agentAccounts.length} agent accounts');
        print('📈 Statistics: $_statistics');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading agent data: $e');
      }
      setState(() {
        _isLoading = false;
        _error = 'خطأ في تحميل بيانات الوكلاء: $e';
      });
    }
  }

  /// إعادة حساب جميع الحسابات
  Future<void> _recalculateAllAccounts() async {
    try {
      setState(() {
        _isLoading = true;
      });

      await _accountingService.recalculateAllAgentAccounts();
      await _loadData();

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إعادة حساب جميع حسابات الوكلاء بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إعادة الحساب: $e', isError: true);
      }
    }
  }

  /// البحث في الوكلاء
  Future<void> _searchAgents(String query) async {
    setState(() {
      _searchQuery = query;
    });

    if (query.isEmpty) {
      _agentAccounts = await _accountingService.getAllAgentAccounts();
    } else {
      _agentAccounts = await _accountingService.searchAgentAccounts(query);
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('إدارة الوكلاء الشاملة'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.calculate),
            onPressed: _recalculateAllAccounts,
            tooltip: 'إعادة حساب جميع الحسابات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
            Tab(icon: Icon(Icons.people), text: 'قائمة الوكلاء'),
            Tab(icon: Icon(Icons.analytics), text: 'التقارير'),
          ],
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل بيانات الوكلاء...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(_error!, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildAgentsListTab(),
        _buildReportsTab(),
      ],
    );
  }

  /// تبويب النظرة العامة
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(),
          const SizedBox(height: AppConstants.largePadding),
          _buildQuickActions(),
          const SizedBox(height: AppConstants.largePadding),
          _buildTopAgents(),
        ],
      ),
    );
  }

  /// بطاقات الإحصائيات
  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات العامة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppConstants.defaultPadding,
          mainAxisSpacing: AppConstants.defaultPadding,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'إجمالي الوكلاء',
              '${_statistics['totalAgents'] ?? 0}',
              Icons.people,
              Colors.blue,
            ),
            _buildStatCard(
              'الوكلاء النشطون',
              '${_statistics['activeAgents'] ?? 0}',
              Icons.person,
              Colors.green,
            ),
            _buildStatCard(
              'إجمالي المديونيات',
              AppUtils.formatCurrency(_statistics['totalDebt'] ?? 0.0),
              Icons.trending_up,
              Colors.red,
            ),
            _buildStatCard(
              'إجمالي الأرصدة الدائنة',
              AppUtils.formatCurrency(_statistics['totalCredit'] ?? 0.0),
              Icons.trending_down,
              Colors.orange,
            ),
            _buildStatCard(
              'إجمالي المبيعات',
              AppUtils.formatCurrency(_statistics['totalSales'] ?? 0.0),
              Icons.shopping_cart,
              Colors.purple,
            ),
            _buildStatCard(
              'إجمالي العمولات',
              AppUtils.formatCurrency(_statistics['totalCommissions'] ?? 0.0),
              Icons.monetization_on,
              Colors.teal,
            ),
          ],
        ),
      ],
    );
  }

  /// بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// الإجراءات السريعة
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                'إعادة حساب الحسابات',
                'إعادة حساب جميع حسابات الوكلاء',
                Icons.calculate,
                Colors.blue,
                _recalculateAllAccounts,
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: _buildQuickActionCard(
                'تصدير التقارير',
                'تصدير تقارير الوكلاء',
                Icons.file_download,
                Colors.green,
                () {
                  // TODO: تنفيذ تصدير التقارير
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بطاقة إجراء سريع
  Widget _buildQuickActionCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// أفضل الوكلاء
  Widget _buildTopAgents() {
    final sortedAgents = List<AgentAccount>.from(_agentAccounts);
    sortedAgents.sort((a, b) => b.totalCustomerSales.compareTo(a.totalCustomerSales));
    final topAgents = sortedAgents.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أفضل الوكلاء (حسب المبيعات)',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: topAgents.length,
          itemBuilder: (context, index) {
            final agent = topAgents[index];
            return _buildTopAgentCard(agent, index + 1);
          },
        ),
      ],
    );
  }

  /// بطاقة أفضل وكيل
  Widget _buildTopAgentCard(AgentAccount agent, int rank) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getRankColor(rank),
          child: Text(
            '$rank',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(
          agent.agentName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text('مبيعات: ${AppUtils.formatCurrency(agent.totalCustomerSales)}'),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              agent.getCurrentDebt() > 0 ? 'مدين' : 'دائن',
              style: TextStyle(
                color: agent.getCurrentDebt() > 0 ? Colors.red : Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              AppUtils.formatCurrency(
                agent.getCurrentDebt() > 0 ? agent.getCurrentDebt() : agent.getCreditBalance(),
              ),
              style: TextStyle(
                color: agent.getCurrentDebt() > 0 ? Colors.red : Colors.green,
              ),
            ),
          ],
        ),
        onTap: () => _viewAgentDetails(agent),
      ),
    );
  }

  /// لون الترتيب
  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return Colors.amber;
      case 2:
        return Colors.grey;
      case 3:
        return Colors.brown;
      default:
        return Colors.blue;
    }
  }

  /// عرض تفاصيل الوكيل
  void _viewAgentDetails(AgentAccount agent) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AgentAccountDetailsScreen(agentAccount: agent),
      ),
    );
  }

  /// تبويب قائمة الوكلاء
  Widget _buildAgentsListTab() {
    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: TextField(
            decoration: const InputDecoration(
              hintText: 'البحث في الوكلاء...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: _searchAgents,
          ),
        ),
        // قائمة الوكلاء
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
            itemCount: _agentAccounts.length,
            itemBuilder: (context, index) {
              final agent = _agentAccounts[index];
              return _buildAgentCard(agent);
            },
          ),
        ),
      ],
    );
  }

  /// بطاقة الوكيل
  Widget _buildAgentCard(AgentAccount agent) {
    final isDebt = agent.getCurrentDebt() > 0;
    final balanceColor = isDebt ? Colors.red : Colors.green;
    final balanceAmount = isDebt ? agent.getCurrentDebt() : agent.getCreditBalance();

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).primaryColor,
          child: Text(
            agent.agentName.isNotEmpty ? agent.agentName[0] : 'و',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(
          agent.agentName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (agent.agentPhone.isNotEmpty)
              Text('📞 ${agent.agentPhone}'),
            Text(
              '${isDebt ? 'مدين' : 'دائن'}: ${AppUtils.formatCurrency(balanceAmount)}',
              style: TextStyle(color: balanceColor, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                _buildAgentSummaryRow('إجمالي البضاعة المستلمة', agent.totalGoodsReceived),
                _buildAgentSummaryRow('إجمالي البضاعة المسحوبة', agent.totalGoodsWithdrawn),
                _buildAgentSummaryRow('إجمالي المبيعات', agent.totalCustomerSales),
                _buildAgentSummaryRow('إجمالي العمولات', agent.totalAgentCommission),
                _buildAgentSummaryRow('إجمالي المدفوعات', agent.totalPaymentsReceived),
                const Divider(),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _viewAgentDetails(agent),
                        icon: const Icon(Icons.visibility),
                        label: const Text('عرض التفاصيل'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _recordPayment(agent),
                        icon: const Icon(Icons.payment),
                        label: const Text('تسجيل دفعة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// صف ملخص الوكيل
  Widget _buildAgentSummaryRow(String label, double value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            AppUtils.formatCurrency(value),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// تسجيل دفعة
  void _recordPayment(AgentAccount agent) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RecordAgentPaymentScreen(
          agentAccount: agent,
          onPaymentRecorded: _loadData,
        ),
      ),
    );
  }

  /// تبويب التقارير
  Widget _buildReportsTab() {
    return const Center(
      child: Text('تبويب التقارير - قيد التطوير'),
    );
  }
}
