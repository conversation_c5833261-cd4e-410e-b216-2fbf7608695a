import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../services/image_picker_service.dart';
// // import '../core/utils/app_utils.dart'; // Unused import // Unused import

/// ويدجت بسيط لاختيار الصور من الكاميرا أو المعرض
class SimpleImagePicker extends StatelessWidget {
  final String title;
  final dynamic currentImageData; // Can be String (path), File, or Uint8List
  final Function(dynamic) onImageSelected; // Can return File or Uint8List
  final bool allowCamera;
  final bool allowGallery;
  final IconData icon;
  final String emptyText;

  const SimpleImagePicker({
    Key? key,
    required this.title,
    this.currentImageData,
    required this.onImageSelected,
    this.allowCamera = true,
    this.allowGallery = true,
    this.icon = Icons.add_a_photo,
    this.emptyText = 'اضغط لإضافة صورة',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _showImageSourceDialog(context),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          height: 200,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: _hasImage()
              ? _buildImagePreview()
              : _buildEmptyState(),
        ),
      ),
    );
  }

  bool _hasImage() {
    if (currentImageData == null) return false;
    if (currentImageData is String) return currentImageData.isNotEmpty;
    if (currentImageData is Uint8List) return currentImageData.isNotEmpty;
    if (currentImageData is File) return true;
    return false;
  }

  Widget _buildImagePreview() {
    return Stack(
      children: [
        // الصورة
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: _buildImageWidget(),
        ),
        // العنوان
        Positioned(
          top: 8,
          left: 8,
          right: 8,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        // زر الحذف
        Positioned(
          top: 8,
          right: 8,
          child: GestureDetector(
            onTap: () => onImageSelected(null),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 48,
          color: Colors.grey.shade400,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          emptyText,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Future<void> _showImageSourceDialog(BuildContext context) async {
    if (!allowCamera && !allowGallery) {
      return;
    }

    // إذا كان خيار واحد فقط متاح
    if (allowCamera && !allowGallery) {
      final image = await ImagePickerService.instance.pickImageFromCamera();
      onImageSelected(image);
      return;
    }

    if (allowGallery && !allowCamera) {
      final image = await ImagePickerService.instance.pickImageFromGallery();
      onImageSelected(image);
      return;
    }

    // إذا كان كلا الخيارين متاحين، اعرض حوار الاختيار
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختر مصدر الصورة',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                if (allowCamera)
                  Expanded(
                    child: _buildSourceOption(
                      context: context,
                      icon: Icons.camera_alt,
                      title: 'الكاميرا',
                      onTap: () async {
                        Navigator.pop(context);
                        final image = await ImagePickerService.instance.pickImageFromCamera();
                        if (image != null) {
                          onImageSelected(image);
                        }
                      },
                    ),
                  ),
                if (allowCamera && allowGallery) const SizedBox(width: 16),
                if (allowGallery)
                  Expanded(
                    child: _buildSourceOption(
                      context: context,
                      icon: Icons.photo_library,
                      title: 'المعرض',
                      onTap: () async {
                        Navigator.pop(context);
                        final image = await ImagePickerService.instance.pickImageFromGallery();
                        if (image != null) {
                          onImageSelected(image);
                        }
                      },
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 20),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSourceOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 48,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageWidget() {
    if (currentImageData is String) {
      return Image.file(
        File(currentImageData),
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildEmptyState();
        },
      );
    } else if (currentImageData is Uint8List) {
      return Image.memory(
        currentImageData,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildEmptyState();
        },
      );
    } else if (currentImageData is File) {
      return Image.file(
        currentImageData,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildEmptyState();
        },
      );
    } else {
      return _buildEmptyState();
    }
  }
}

/// ويدجت لاختيار عدة صور
class MultipleImagePicker extends StatelessWidget {
  final String title;
  final List<String> currentImagePaths;
  final Function(List<File>) onImagesSelected;
  final int maxImages;

  const MultipleImagePicker({
    Key? key,
    required this.title,
    required this.currentImagePaths,
    required this.onImagesSelected,
    this.maxImages = 5,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: currentImagePaths.length + 1,
            itemBuilder: (context, index) {
              if (index == currentImagePaths.length) {
                // زر إضافة صورة جديدة
                return _buildAddImageButton(context);
              }
              
              // عرض الصورة الموجودة
              return _buildImageItem(context, currentImagePaths[index], index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAddImageButton(BuildContext context) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: 8),
      child: InkWell(
        onTap: () => _pickImages(context),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_a_photo,
                size: 32,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 4),
              Text(
                'إضافة صورة',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageItem(BuildContext context, String imagePath, int index) {
    return Container(
      width: 120,
      margin: const EdgeInsets.only(right: 8),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.file(
              File(imagePath),
              width: 120,
              height: 120,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.error),
                );
              },
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () => _removeImage(index),
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImages(BuildContext context) async {
    final images = await ImagePickerService.instance.pickMultipleImagesFromGallery(
      maxImages: maxImages - currentImagePaths.length,
    );
    
    if (images.isNotEmpty) {
      onImagesSelected(images);
    }
  }

  void _removeImage(int index) {
    final updatedPaths = List<String>.from(currentImagePaths);
    updatedPaths.removeAt(index);
    // تحويل المسارات إلى ملفات
    final files = updatedPaths.map((path) => File(path)).toList();
    onImagesSelected(files);
  }
}
