import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'windows_dashboard.dart';
import 'windows_sidebar.dart';
import 'windows_top_bar.dart';
import '../../core/utils/app_utils.dart';

/// التطبيق الرئيسي لنظام Windows
/// يحتوي على تخطيط متقدم مع قائمة جانبية وشريط علوي ولوحة تحكم
class WindowsMainApp extends StatefulWidget {
  const WindowsMainApp({super.key});

  @override
  State<WindowsMainApp> createState() => _WindowsMainAppState();
}

class _WindowsMainAppState extends State<WindowsMainApp> with TickerProviderStateMixin {
  // التحكم في القائمة الجانبية
  bool _isSidebarExpanded = true;
  double _sidebarWidth = 280.0;
  
  // الشاشة النشطة حالياً
  String _currentScreen = 'dashboard';
  
  // تحكم في التبويبات
  late TabController _tabController;
  final List<WindowsTab> _openTabs = [];
  
  // اختصارات لوحة المفاتيح
  final Map<LogicalKeySet, Intent> _shortcuts = {
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyN): const NewItemIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyS): const SaveIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyF): const SearchIntent(),
    LogicalKeySet(LogicalKeyboardKey.f5): const RefreshIntent(),
    LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyW): const CloseTabIntent(),
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this);
    
    // إضافة التبويب الافتراضي (لوحة التحكم)
    _openTabs.add(const WindowsTab(
      id: 'dashboard',
      title: 'لوحة التحكم',
      icon: Icons.dashboard,
      content: WindowsDashboard(),
    ));
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Shortcuts(
      shortcuts: _shortcuts,
      child: Actions(
        actions: {
          NewItemIntent: CallbackAction<NewItemIntent>(
            onInvoke: (intent) => _handleNewItem(),
          ),
          SaveIntent: CallbackAction<SaveIntent>(
            onInvoke: (intent) => _handleSave(),
          ),
          SearchIntent: CallbackAction<SearchIntent>(
            onInvoke: (intent) => _handleSearch(),
          ),
          RefreshIntent: CallbackAction<RefreshIntent>(
            onInvoke: (intent) => _handleRefresh(),
          ),
          CloseTabIntent: CallbackAction<CloseTabIntent>(
            onInvoke: (intent) => _handleCloseTab(),
          ),
        },
        child: Scaffold(
          body: Column(
            children: [
              // الشريط العلوي
              WindowsTopBar(
                onMenuToggle: _toggleSidebar,
                onNewTab: _openNewTab,
                onSearch: _handleSearch,
                currentScreen: _currentScreen,
              ),
              
              // المحتوى الرئيسي
              Expanded(
                child: Row(
                  children: [
                    // القائمة الجانبية
                    WindowsSidebar(
                      isExpanded: _isSidebarExpanded,
                      width: _sidebarWidth,
                      currentScreen: _currentScreen,
                      onScreenChanged: _changeScreen,
                      onToggle: _toggleSidebar,
                    ),
                    
                    // المحتوى الرئيسي مع التبويبات
                    Expanded(
                      child: _buildMainContent(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء المحتوى الرئيسي مع التبويبات
  Widget _buildMainContent() {
    return Column(
      children: [
        // شريط التبويبات
        if (_openTabs.length > 1) _buildTabBar(),
        
        // محتوى التبويب النشط
        Expanded(
          child: _openTabs.isNotEmpty 
              ? _openTabs[_tabController.index].content
              : const Center(child: Text('لا توجد تبويبات مفتوحة')),
        ),
      ],
    );
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: _openTabs.map((tab) => _buildTab(tab)).toList(),
        onTap: (index) {
          setState(() {
            _currentScreen = _openTabs[index].id;
          });
        },
      ),
    );
  }

  /// بناء تبويب واحد
  Widget _buildTab(WindowsTab tab) {
    return Tab(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(tab.icon, size: 16),
          const SizedBox(width: 8),
          Text(tab.title),
          const SizedBox(width: 8),
          if (_openTabs.length > 1)
            InkWell(
              onTap: () => _closeTab(tab.id),
              child: const Icon(Icons.close, size: 16),
            ),
        ],
      ),
    );
  }

  /// تبديل حالة القائمة الجانبية
  void _toggleSidebar() {
    setState(() {
      _isSidebarExpanded = !_isSidebarExpanded;
      _sidebarWidth = _isSidebarExpanded ? 280.0 : 64.0;
    });
  }

  /// تغيير الشاشة النشطة
  void _changeScreen(String screenId) {
    setState(() {
      _currentScreen = screenId;
    });
    
    // البحث عن التبويب أو إنشاء جديد
    final existingTabIndex = _openTabs.indexWhere((tab) => tab.id == screenId);
    
    if (existingTabIndex != -1) {
      // التبويب موجود، انتقل إليه
      _tabController.animateTo(existingTabIndex);
    } else {
      // إنشاء تبويب جديد
      _openNewTabForScreen(screenId);
    }
  }

  /// فتح تبويب جديد لشاشة معينة
  void _openNewTabForScreen(String screenId) {
    final tabInfo = _getTabInfoForScreen(screenId);
    if (tabInfo != null) {
      setState(() {
        _openTabs.add(tabInfo);
        _tabController = TabController(length: _openTabs.length, vsync: this);
        _tabController.animateTo(_openTabs.length - 1);
      });
    }
  }

  /// الحصول على معلومات التبويب للشاشة
  WindowsTab? _getTabInfoForScreen(String screenId) {
    switch (screenId) {
      case 'dashboard':
        return const WindowsTab(
          id: 'dashboard',
          title: 'لوحة التحكم',
          icon: Icons.dashboard,
          content: WindowsDashboard(),
        );
      case 'inventory':
        return const WindowsTab(
          id: 'inventory',
          title: 'إدارة المخزون',
          icon: Icons.inventory,
          content: Center(child: Text('شاشة إدارة المخزون')),
        );
      case 'agents':
        return const WindowsTab(
          id: 'agents',
          title: 'إدارة الوكلاء',
          icon: Icons.people,
          content: Center(child: Text('شاشة إدارة الوكلاء')),
        );
      case 'accounting':
        return const WindowsTab(
          id: 'accounting',
          title: 'النظام المحاسبي',
          icon: Icons.receipt_long,
          content: Center(child: Text('النظام المحاسبي')),
        );
      case 'reports':
        return const WindowsTab(
          id: 'reports',
          title: 'التقارير',
          icon: Icons.analytics,
          content: Center(child: Text('شاشة التقارير')),
        );
      case 'settings':
        return const WindowsTab(
          id: 'settings',
          title: 'الإعدادات',
          icon: Icons.settings,
          content: Center(child: Text('شاشة الإعدادات')),
        );
      default:
        return null;
    }
  }

  /// فتح تبويب جديد
  void _openNewTab() {
    // يمكن إضافة منطق لفتح تبويب جديد
    AppUtils.showInfoSnackBar(context, 'فتح تبويب جديد');
  }

  /// إغلاق تبويب
  void _closeTab(String tabId) {
    if (_openTabs.length <= 1) return; // لا يمكن إغلاق آخر تبويب
    
    final tabIndex = _openTabs.indexWhere((tab) => tab.id == tabId);
    if (tabIndex != -1) {
      setState(() {
        _openTabs.removeAt(tabIndex);
        _tabController = TabController(length: _openTabs.length, vsync: this);
        
        // الانتقال للتبويب السابق أو التالي
        if (tabIndex > 0) {
          _tabController.animateTo(tabIndex - 1);
        } else if (_openTabs.isNotEmpty) {
          _tabController.animateTo(0);
        }
        
        // تحديث الشاشة النشطة
        if (_openTabs.isNotEmpty) {
          _currentScreen = _openTabs[_tabController.index].id;
        }
      });
    }
  }

  // معالجات اختصارات لوحة المفاتيح
  void _handleNewItem() {
    AppUtils.showInfoSnackBar(context, 'إنشاء عنصر جديد (Ctrl+N)');
  }

  void _handleSave() {
    AppUtils.showInfoSnackBar(context, 'حفظ (Ctrl+S)');
  }

  void _handleSearch() {
    AppUtils.showInfoSnackBar(context, 'بحث (Ctrl+F)');
  }

  void _handleRefresh() {
    AppUtils.showInfoSnackBar(context, 'تحديث (F5)');
  }

  void _handleCloseTab() {
    if (_openTabs.length > 1) {
      _closeTab(_openTabs[_tabController.index].id);
    }
  }
}

/// نموذج التبويب
class WindowsTab {
  final String id;
  final String title;
  final IconData icon;
  final Widget content;

  const WindowsTab({
    required this.id,
    required this.title,
    required this.icon,
    required this.content,
  });
}

// تعريف الأوامر (Intents) لاختصارات لوحة المفاتيح
class NewItemIntent extends Intent {
  const NewItemIntent();
}

class SaveIntent extends Intent {
  const SaveIntent();
}

class SearchIntent extends Intent {
  const SearchIntent();
}

class RefreshIntent extends Intent {
  const RefreshIntent();
}

class CloseTabIntent extends Intent {
  const CloseTabIntent();
}
