class SalesReport {
  final DateTime startDate;
  final DateTime endDate;
  final double totalSales;
  final double totalProfit;
  final int totalInvoices;
  final int totalItems;
  final Map<String, double> salesByAgent;
  final Map<String, double> salesByWarehouse;
  final Map<String, int> itemsSold;
  final List<Map<String, dynamic>> invoices;

  SalesReport({
    required this.startDate,
    required this.endDate,
    required this.totalSales,
    required this.totalProfit,
    required this.totalInvoices,
    required this.totalItems,
    required this.salesByAgent,
    required this.salesByWarehouse,
    required this.itemsSold,
    required this.invoices,
  });

  double get averageInvoiceValue => totalInvoices > 0 ? totalSales / totalInvoices : 0;
  double get profitMargin => totalSales > 0 ? (totalProfit / totalSales) * 100 : 0;

  Map<String, dynamic> toMap() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'totalSales': totalSales,
      'totalProfit': totalProfit,
      'totalInvoices': totalInvoices,
      'totalItems': totalItems,
      'salesByAgent': salesByAgent,
      'salesByWarehouse': salesByWarehouse,
      'itemsSold': itemsSold,
      'averageInvoiceValue': averageInvoiceValue,
      'profitMargin': profitMargin,
    };
  }
}

class InventoryReport {
  final int totalItems;
  final int availableItems;
  final int soldItems;
  final int reservedItems;
  final Map<String, int> itemsByWarehouse;
  final Map<String, int> itemsByCategory;
  final Map<String, int> itemsByStatus;
  final List<Map<String, dynamic>> items;

  InventoryReport({
    required this.totalItems,
    required this.availableItems,
    required this.soldItems,
    required this.reservedItems,
    required this.itemsByWarehouse,
    required this.itemsByCategory,
    required this.itemsByStatus,
    required this.items,
  });

  double get availabilityRate => totalItems > 0 ? (availableItems / totalItems) * 100 : 0;
  double get soldRate => totalItems > 0 ? (soldItems / totalItems) * 100 : 0;

  Map<String, dynamic> toMap() {
    return {
      'totalItems': totalItems,
      'availableItems': availableItems,
      'soldItems': soldItems,
      'reservedItems': reservedItems,
      'itemsByWarehouse': itemsByWarehouse,
      'itemsByCategory': itemsByCategory,
      'itemsByStatus': itemsByStatus,
      'availabilityRate': availabilityRate,
      'soldRate': soldRate,
    };
  }
}

class AgentReport {
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, AgentPerformance> agentPerformance;
  final List<Map<String, dynamic>> invoices;

  AgentReport({
    required this.startDate,
    required this.endDate,
    required this.agentPerformance,
    required this.invoices,
  });

  AgentPerformance? get topPerformer {
    if (agentPerformance.isEmpty) return null;
    
    return agentPerformance.values.reduce((a, b) => 
      a.totalSales > b.totalSales ? a : b
    );
  }

  double get totalSales => agentPerformance.values
      .fold(0.0, (sum, agent) => sum + agent.totalSales);

  double get totalProfit => agentPerformance.values
      .fold(0.0, (sum, agent) => sum + agent.totalProfit);

  Map<String, dynamic> toMap() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'agentPerformance': agentPerformance.map((key, value) => MapEntry(key, value.toMap())),
      'totalSales': totalSales,
      'totalProfit': totalProfit,
      'topPerformer': topPerformer?.toMap(),
    };
  }
}

class AgentPerformance {
  final String agentName;
  double totalSales;
  double totalProfit;
  int totalInvoices;
  int totalItems;

  AgentPerformance({
    required this.agentName,
    required this.totalSales,
    required this.totalProfit,
    required this.totalInvoices,
    required this.totalItems,
  });

  double get averageInvoiceValue => totalInvoices > 0 ? totalSales / totalInvoices : 0;
  double get profitMargin => totalSales > 0 ? (totalProfit / totalSales) * 100 : 0;

  Map<String, dynamic> toMap() {
    return {
      'agentName': agentName,
      'totalSales': totalSales,
      'totalProfit': totalProfit,
      'totalInvoices': totalInvoices,
      'totalItems': totalItems,
      'averageInvoiceValue': averageInvoiceValue,
      'profitMargin': profitMargin,
    };
  }

  factory AgentPerformance.fromMap(Map<String, dynamic> map) {
    return AgentPerformance(
      agentName: map['agentName'] ?? '',
      totalSales: (map['totalSales'] as num?)?.toDouble() ?? 0.0,
      totalProfit: (map['totalProfit'] as num?)?.toDouble() ?? 0.0,
      totalInvoices: (map['totalInvoices'] as num?)?.toInt() ?? 0,
      totalItems: (map['totalItems'] as num?)?.toInt() ?? 0,
    );
  }
}

class ReportFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? agentId;
  final String? warehouseId;
  final String? category;
  final String? status;

  ReportFilter({
    this.startDate,
    this.endDate,
    this.agentId,
    this.warehouseId,
    this.category,
    this.status,
  });

  Map<String, dynamic> toMap() {
    return {
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'agentId': agentId,
      'warehouseId': warehouseId,
      'category': category,
      'status': status,
    };
  }

  factory ReportFilter.fromMap(Map<String, dynamic> map) {
    return ReportFilter(
      startDate: map['startDate'] != null ? DateTime.parse(map['startDate']) : null,
      endDate: map['endDate'] != null ? DateTime.parse(map['endDate']) : null,
      agentId: map['agentId'],
      warehouseId: map['warehouseId'],
      category: map['category'],
      status: map['status'],
    );
  }
}

enum ReportType {
  sales,
  inventory,
  agent,
  profit,
  custom,
}

extension ReportTypeExtension on ReportType {
  String get displayName {
    switch (this) {
      case ReportType.sales:
        return 'تقرير المبيعات';
      case ReportType.inventory:
        return 'تقرير المخزون';
      case ReportType.agent:
        return 'تقرير الوكلاء';
      case ReportType.profit:
        return 'تقرير الأرباح';
      case ReportType.custom:
        return 'تقرير مخصص';
    }
  }

  String get description {
    switch (this) {
      case ReportType.sales:
        return 'تقرير شامل عن المبيعات والفواتير';
      case ReportType.inventory:
        return 'تقرير حالة المخزون والأصناف';
      case ReportType.agent:
        return 'تقرير أداء الوكلاء والمبيعات';
      case ReportType.profit:
        return 'تقرير الأرباح والهوامش';
      case ReportType.custom:
        return 'تقرير مخصص حسب المعايير المحددة';
    }
  }

  String get iconName {
    switch (this) {
      case ReportType.sales:
        return 'trending_up';
      case ReportType.inventory:
        return 'inventory';
      case ReportType.agent:
        return 'people';
      case ReportType.profit:
        return 'attach_money';
      case ReportType.custom:
        return 'tune';
    }
  }
}

class ReportHistory {
  final String id;
  final String title;
  final ReportType type;
  final String filePath;
  final DateTime createdAt;
  final Map<String, dynamic> metadata;

  ReportHistory({
    required this.id,
    required this.title,
    required this.type,
    required this.filePath,
    required this.createdAt,
    required this.metadata,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'type': type.name,
      'filePath': filePath,
      'createdAt': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory ReportHistory.fromMap(Map<String, dynamic> map) {
    return ReportHistory(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      type: ReportType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ReportType.custom,
      ),
      filePath: map['filePath'] ?? '',
      createdAt: DateTime.parse(map['createdAt']),
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }
}
