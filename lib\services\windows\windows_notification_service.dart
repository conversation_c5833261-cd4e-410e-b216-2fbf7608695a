import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:io';

/// خدمة الإشعارات المحسنة للويندوز
class WindowsNotificationService {
  static WindowsNotificationService? _instance;
  static WindowsNotificationService get instance => _instance ??= WindowsNotificationService._();
  
  WindowsNotificationService._();

  late FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;
  bool _isInitialized = false;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('🔔 Initializing Windows Notification Service...');
      }

      _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

      // Initialize settings for Windows
      const InitializationSettings initializationSettings = InitializationSettings(
        android: null, // Not used on Windows
        iOS: null,     // Not used on Windows
        macOS: null,   // Not used on Windows
        linux: LinuxInitializationSettings(
          defaultActionName: 'Open notification',
        ),
      );

      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationResponse,
      );

      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Windows Notification Service initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Windows Notification Service initialization failed: $e');
      }
    }
  }

  /// معالج استجابة الإشعارات
  void _onNotificationResponse(NotificationResponse response) {
    if (kDebugMode) {
      print('🔔 Notification clicked: ${response.payload}');
    }
    
    // Handle notification click
    // You can add navigation logic here
  }

  /// إرسال إشعار بسيط
  Future<void> showSimpleNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (!Platform.isWindows && !Platform.isLinux) {
        if (kDebugMode) {
          print('⚠️ Windows notifications only supported on Windows/Linux');
        }
        return;
      }

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        linux: LinuxNotificationDetails(
          urgency: LinuxNotificationUrgency.normal,
        ),
      );

      await _flutterLocalNotificationsPlugin.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );

      if (kDebugMode) {
        print('✅ Simple notification sent: $title');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending simple notification: $e');
      }
    }
  }

  /// إرسال إشعار مع أيقونة
  Future<void> showNotificationWithIcon({
    required String title,
    required String body,
    String? iconPath,
    String? payload,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (!Platform.isWindows && !Platform.isLinux) {
        if (kDebugMode) {
          print('⚠️ Windows notifications only supported on Windows/Linux');
        }
        return;
      }

      final NotificationDetails platformChannelSpecifics = NotificationDetails(
        linux: LinuxNotificationDetails(
          urgency: LinuxNotificationUrgency.normal,
          icon: iconPath != null ? LinuxNotificationIcon.file(iconPath) : null,
        ),
      );

      await _flutterLocalNotificationsPlugin.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );

      if (kDebugMode) {
        print('✅ Notification with icon sent: $title');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending notification with icon: $e');
      }
    }
  }

  /// إرسال إشعار مجدول
  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (!Platform.isWindows && !Platform.isLinux) {
        if (kDebugMode) {
          print('⚠️ Windows notifications only supported on Windows/Linux');
        }
        return;
      }

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        linux: LinuxNotificationDetails(
          urgency: LinuxNotificationUrgency.normal,
        ),
      );

      await _flutterLocalNotificationsPlugin.schedule(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        scheduledDate,
        platformChannelSpecifics,
        payload: payload,
      );

      if (kDebugMode) {
        print('✅ Scheduled notification: $title for $scheduledDate');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error scheduling notification: $e');
      }
    }
  }

  /// إرسال إشعار للمبيعات الجديدة
  Future<void> showNewSaleNotification({
    required String customerName,
    required String invoiceNumber,
    required double amount,
  }) async {
    await showSimpleNotification(
      title: 'مبيعة جديدة',
      body: 'فاتورة رقم $invoiceNumber للعميل $customerName بمبلغ $amount ج.م',
      payload: 'sale_$invoiceNumber',
    );
  }

  /// إرسال إشعار للدفعات الجديدة
  Future<void> showNewPaymentNotification({
    required String agentName,
    required double amount,
    required String paymentType,
  }) async {
    await showSimpleNotification(
      title: 'دفعة جديدة',
      body: 'دفعة $paymentType من الوكيل $agentName بمبلغ $amount ج.م',
      payload: 'payment_${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  /// إرسال إشعار لنقص المخزون
  Future<void> showLowStockNotification({
    required String itemName,
    required int currentStock,
    required int minStock,
  }) async {
    await showSimpleNotification(
      title: 'تحذير: نقص في المخزون',
      body: 'الصنف "$itemName" وصل إلى $currentStock قطعة (الحد الأدنى: $minStock)',
      payload: 'low_stock_$itemName',
    );
  }

  /// إرسال إشعار للمهام المعلقة
  Future<void> showPendingTaskNotification({
    required String taskDescription,
    required int taskCount,
  }) async {
    await showSimpleNotification(
      title: 'مهام معلقة',
      body: '$taskDescription - عدد المهام: $taskCount',
      payload: 'pending_tasks',
    );
  }

  /// إرسال إشعار للنسخ الاحتياطي
  Future<void> showBackupNotification({
    required bool isSuccess,
    String? errorMessage,
  }) async {
    if (isSuccess) {
      await showSimpleNotification(
        title: 'نسخ احتياطي',
        body: 'تم إنشاء النسخة الاحتياطية بنجاح',
        payload: 'backup_success',
      );
    } else {
      await showSimpleNotification(
        title: 'خطأ في النسخ الاحتياطي',
        body: errorMessage ?? 'فشل في إنشاء النسخة الاحتياطية',
        payload: 'backup_error',
      );
    }
  }

  /// إرسال إشعار للتحديثات
  Future<void> showUpdateNotification({
    required String version,
    required String updateDescription,
  }) async {
    await showSimpleNotification(
      title: 'تحديث متاح',
      body: 'الإصدار $version متاح الآن - $updateDescription',
      payload: 'update_$version',
    );
  }

  /// إرسال إشعار للأخطاء الهامة
  Future<void> showErrorNotification({
    required String errorTitle,
    required String errorMessage,
  }) async {
    await showSimpleNotification(
      title: 'خطأ: $errorTitle',
      body: errorMessage,
      payload: 'error_${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  /// إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      await _flutterLocalNotificationsPlugin.cancelAll();
      
      if (kDebugMode) {
        print('✅ All notifications cancelled');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error cancelling notifications: $e');
      }
    }
  }

  /// إلغاء إشعار محدد
  Future<void> cancelNotification(int id) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      await _flutterLocalNotificationsPlugin.cancel(id);
      
      if (kDebugMode) {
        print('✅ Notification $id cancelled');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error cancelling notification $id: $e');
      }
    }
  }

  /// التحقق من دعم الإشعارات
  bool get isSupported {
    return Platform.isWindows || Platform.isLinux;
  }

  /// الحصول على الإشعارات المعلقة
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      return await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting pending notifications: $e');
      }
      return [];
    }
  }
}
