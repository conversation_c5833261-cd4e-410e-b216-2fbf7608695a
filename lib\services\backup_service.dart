import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/utils/app_utils.dart';
import '../models/backup_model.dart';
import 'local_database_service.dart';

// Backup configuration class
class BackupConfig {
  final bool autoBackup;
  final int maxBackups;
  final Duration backupInterval;
  final bool compressBackups;
  final int retentionDays;

  BackupConfig({
    this.autoBackup = false,
    this.maxBackups = 5,
    this.backupInterval = const Duration(days: 1),
    this.compressBackups = true,
    this.retentionDays = 30,
  });

  Map<String, dynamic> toJson() => {
    'autoBackup': autoBackup,
    'maxBackups': maxBackups,
    'backupIntervalDays': backupInterval.inDays,
    'compressBackups': compressBackups,
    'retentionDays': retentionDays,
  };

  factory BackupConfig.fromJson(Map<String, dynamic> json) => BackupConfig(
    autoBackup: json['autoBackup'] ?? false,
    maxBackups: json['maxBackups'] ?? 5,
    backupInterval: Duration(days: json['backupIntervalDays'] ?? 1),
    compressBackups: json['compressBackups'] ?? true,
    retentionDays: json['retentionDays'] ?? 30,
  );
}

// Restore operation status
enum RestoreStatus { inProgress, completed, failed }

// Restore operation class
class RestoreOperation {
  final String id;
  final String backupId;
  final DateTime startedAt;
  final DateTime? completedAt;
  final RestoreStatus status;
  final String? errorMessage;
  final double? progress;
  final List<String>? restoredTables;

  RestoreOperation({
    required this.id,
    required this.backupId,
    required this.startedAt,
    this.completedAt,
    required this.status,
    this.errorMessage,
    this.progress,
    this.restoredTables,
  });
}

class BackupService {
  static BackupService? _instance;
  static BackupService get instance => _instance ??= BackupService._();
  BackupService._();

  final LocalDatabaseService _localDb = LocalDatabaseService.instance;

  // Backup configuration
  BackupConfig _config = BackupConfig();

  // Active operations
  final Map<String, RestoreOperation> _activeRestores = {};
  final List<BackupModel> _backupHistory = [];

  // Create full backup
  Future<String> createFullBackup() async {
    try {
      if (kDebugMode) {
        print('Starting full backup...');
      }

      final backupData = <String, dynamic>{};
      
      // Backup all tables
      final tables = [
        'users',
        'items',
        'warehouses',
        'invoices',
        'document_tracking',
        'agent_accounts',
        'payments',
      ];

      for (final table in tables) {
        try {
          final data = await _localDb.query(table);
          backupData[table] = data;
          
          if (kDebugMode) {
            print('Backed up $table: ${data.length} records');
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error backing up table $table: $e');
          }
          // Continue with other tables
        }
      }

      // Add metadata
      backupData['metadata'] = {
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'device_info': await _getDeviceInfo(),
        'backup_type': 'full',
      };

      // Convert to JSON and compress
      final jsonData = jsonEncode(backupData);
      final compressedData = gzip.encode(utf8.encode(jsonData));
      
      // Save to file
      final backupFile = await _saveBackupToFile(compressedData, 'full');
      
      // Upload to Firebase Storage if online
      await _uploadBackupToCloud(backupFile);
      
      if (kDebugMode) {
        print('Full backup completed: ${backupFile.path}');
      }

      return backupFile.path;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating full backup: $e');
      }
      rethrow;
    }
  }

  // Create incremental backup (only changed data)
  Future<String> createIncrementalBackup() async {
    try {
      if (kDebugMode) {
        print('Starting incremental backup...');
      }

      final prefs = await SharedPreferences.getInstance();
      final lastBackupTime = prefs.getString('last_backup_time');
      final cutoffTime = lastBackupTime != null 
          ? DateTime.parse(lastBackupTime)
          : DateTime.now().subtract(const Duration(days: 1));

      final backupData = <String, dynamic>{};
      
      // Backup only changed records
      final tables = [
        'users',
        'items',
        'warehouses',
        'invoices',
        'document_tracking',
        'agent_accounts',
        'payments',
      ];

      for (final table in tables) {
        try {
          final data = await _localDb.query(
            table,
            where: 'updated_at > ?',
            whereArgs: [cutoffTime.toIso8601String()],
          );
          
          if (data.isNotEmpty) {
            backupData[table] = data;
            
            if (kDebugMode) {
              print('Incremental backup $table: ${data.length} records');
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error backing up table $table: $e');
          }
        }
      }

      if (backupData.isEmpty) {
        if (kDebugMode) {
          print('No changes found for incremental backup');
        }
        return '';
      }

      // Add metadata
      backupData['metadata'] = {
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'device_info': await _getDeviceInfo(),
        'backup_type': 'incremental',
        'cutoff_time': cutoffTime.toIso8601String(),
      };

      // Convert to JSON and compress
      final jsonData = jsonEncode(backupData);
      final compressedData = gzip.encode(utf8.encode(jsonData));
      
      // Save to file
      final backupFile = await _saveBackupToFile(compressedData, 'incremental');
      
      // Upload to Firebase Storage if online
      await _uploadBackupToCloud(backupFile);
      
      // Update last backup time
      await prefs.setString('last_backup_time', DateTime.now().toIso8601String());
      
      if (kDebugMode) {
        print('Incremental backup completed: ${backupFile.path}');
      }

      return backupFile.path;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating incremental backup: $e');
      }
      rethrow;
    }
  }

  // Restore from backup file
  Future<void> restoreFromBackup(String backupFilePath) async {
    try {
      if (kDebugMode) {
        print('Starting restore from: $backupFilePath');
      }

      final file = File(backupFilePath);
      if (!await file.exists()) {
        throw Exception('Backup file not found');
      }

      // Read and decompress
      final compressedData = await file.readAsBytes();
      final jsonData = utf8.decode(gzip.decode(compressedData));
      final backupData = jsonDecode(jsonData) as Map<String, dynamic>;

      // Validate backup
      if (!_validateBackup(backupData)) {
        throw Exception('Invalid backup file');
      }

      // Clear existing data (with confirmation)
      await _clearExistingData();

      // Restore each table
      for (final entry in backupData.entries) {
        if (entry.key == 'metadata') continue;
        
        final tableName = entry.key;
        final tableData = entry.value as List<dynamic>;
        
        try {
          for (final record in tableData) {
            await _localDb.insert(tableName, record as Map<String, dynamic>);
          }
          
          if (kDebugMode) {
            print('Restored $tableName: ${tableData.length} records');
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error restoring table $tableName: $e');
          }
        }
      }

      if (kDebugMode) {
        print('Restore completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error restoring from backup: $e');
      }
      rethrow;
    }
  }

  // Get available backups
  Future<List<BackupModel>> getAvailableBackups() async {
    try {
      // Return backup history
      return getBackupHistory();
    } catch (e) {
      debugPrint('Failed to get available backups: $e');
      return [];
    }
  }

  // Schedule automatic backups
  Future<void> scheduleAutomaticBackups() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastAutoBackup = prefs.getString('last_auto_backup');
      
      final now = DateTime.now();
      final shouldBackup = lastAutoBackup == null ||
          now.difference(DateTime.parse(lastAutoBackup)).inHours >= 24;
      
      if (shouldBackup) {
        await createIncrementalBackup();
        await prefs.setString('last_auto_backup', now.toIso8601String());
        
        if (kDebugMode) {
          print('Automatic backup completed');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in automatic backup: $e');
      }
    }
  }

  // Clean up old backups
  Future<void> cleanupOldBackups({int keepCount = 10}) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      
      if (!await backupDir.exists()) return;
      
      final files = await backupDir.list().toList();
      final backupFiles = files
          .whereType<File>()
          .where((f) => f.path.endsWith('.backup'))
          .toList();
      
      if (backupFiles.length <= keepCount) return;
      
      // Sort by modification time
      backupFiles.sort((a, b) => 
          b.statSync().modified.compareTo(a.statSync().modified));
      
      // Delete old backups
      final filesToDelete = backupFiles.skip(keepCount);
      for (final file in filesToDelete) {
        await file.delete();
        if (kDebugMode) {
          print('Deleted old backup: ${file.path}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cleaning up old backups: $e');
      }
    }
  }

  // Helper methods
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
    };
  }

  Future<File> _saveBackupToFile(List<int> data, String type) async {
    final directory = await getApplicationDocumentsDirectory();
    final backupDir = Directory('${directory.path}/backups');
    
    if (!await backupDir.exists()) {
      await backupDir.create(recursive: true);
    }
    
    final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
    final filename = '${type}_backup_$timestamp.backup';
    final file = File('${backupDir.path}/$filename');
    
    await file.writeAsBytes(data);
    return file;
  }

  Future<void> _uploadBackupToCloud(File backupFile) async {
    try {
      // In a real implementation, you would upload to Firebase Storage
      // For now, we'll just log it
      if (kDebugMode) {
        print('Would upload backup to cloud: ${backupFile.path}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading backup to cloud: $e');
      }
    }
  }

  bool _validateBackup(Map<String, dynamic> backupData) {
    try {
      final metadata = backupData['metadata'] as Map<String, dynamic>?;
      if (metadata == null) return false;
      
      final version = metadata['version'] as String?;
      final timestamp = metadata['timestamp'] as String?;
      final backupType = metadata['backup_type'] as String?;
      
      return version != null && timestamp != null && backupType != null;
    } catch (e) {
      return false;
    }
  }

  Future<void> _clearExistingData() async {
    // This should be done with user confirmation
    final tables = [
      'users',
      'items',
      'warehouses',
      'invoices',
      'document_tracking',
      'agent_accounts',
      'payments',
    ];

    for (final table in tables) {
      try {
        await _localDb.delete(table, '1=1', []);
      } catch (e) {
        if (kDebugMode) {
          print('Error clearing table $table: $e');
        }
      }
    }
  }

  // Legacy backup info methods removed - use BackupModel instead

  /// Get local backups
  Future<List<Map<String, dynamic>>> getLocalBackups() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');

      if (!await backupDir.exists()) {
        return [];
      }

      final files = await backupDir.list().toList();
      final backups = <Map<String, dynamic>>[];

      for (final file in files) {
        if (file is File && file.path.endsWith('.json')) {
          final stat = await file.stat();
          backups.add({
            'fileName': file.path.split('/').last,
            'filePath': file.path,
            'size': stat.size,
            'createdAt': stat.modified.toIso8601String(),
            'type': file.path.contains('incremental') ? 'incremental' : 'full',
          });
        }
      }

      return backups;
    } catch (e) {
      debugPrint('Error getting local backups: $e');
      return [];
    }
  }

  /// Get cloud backups
  Future<List<Map<String, dynamic>>> getCloudBackups() async {
    try {
      return [];
    } catch (e) {
      debugPrint('Error getting cloud backups: $e');
      return [];
    }
  }

  /// Create incremental backup (enhanced version)
  Future<File> createIncrementalBackupEnhanced() async {
    try {
      final since = DateTime.now().subtract(const Duration(days: 7));

      final backupData = <String, dynamic>{};
      backupData['metadata'] = {
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'backup_type': 'incremental',
        'since': since.toIso8601String(),
      };

      final tables = ['users', 'items', 'warehouses', 'invoices'];

      for (final table in tables) {
        try {
          final data = await LocalDatabaseService.instance.query(table);
          backupData[table] = data;
        } catch (e) {
          backupData[table] = [];
        }
      }

      final directory = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${directory.path}/backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      final fileName = 'incremental_backup_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${backupDir.path}/$fileName');

      await file.writeAsString(json.encode(backupData));

      return file;
    } catch (e) {
      throw Exception('فشل في إنشاء النسخة الاحتياطية التدريجية: $e');
    }
  }

  /// Schedule automatic backup
  Future<void> scheduleAutomaticBackup() async {
    try {
      await createIncrementalBackup();
      debugPrint('Automatic backup scheduled');
    } catch (e) {
      debugPrint('Error scheduling automatic backup: $e');
    }
  }

  /// Download backup from cloud
  Future<File> downloadBackupFromCloud(String fileName) async {
    throw Exception('تحميل النسخ الاحتياطية من السحابة غير متاح حالياً');
  }

  /// Create enhanced backup with metadata
  Future<BackupModel> createEnhancedBackup({
    String? name,
    String? description,
    BackupType type = BackupType.manual,
    List<String>? includedTables,
  }) async {
    try {
      final backupId = AppUtils.generateId();
      final timestamp = DateTime.now();

      final backup = BackupModel(
        id: backupId,
        name: name ?? 'نسخة احتياطية ${AppUtils.formatDateTime(timestamp)}',
        description: description ?? 'نسخة احتياطية تم إنشاؤها في ${AppUtils.formatDateTime(timestamp)}',
        createdAt: timestamp,
        size: 0, // Will be updated after creation
        version: '1.0.0',
        type: type,
        status: BackupStatus.inProgress,
        metadata: {
          'app_version': '1.0.0',
          'created_by': 'system',
          'device_info': await _getDeviceInfo(),
        },
        includedTables: includedTables ?? _getDefaultTables(),
      );

      // Add to history
      _backupHistory.add(backup);

      // Create the actual backup
      final backupPath = await createFullBackup();
      final file = File(backupPath);
      final fileSize = await file.length();

      // Update backup with file info
      final completedBackup = backup.copyWith(
        status: BackupStatus.completed,
        size: fileSize,
        filePath: backupPath,
        checksum: await _calculateChecksum(file),
      );

      // Update in history
      final index = _backupHistory.indexWhere((b) => b.id == backupId);
      if (index != -1) {
        _backupHistory[index] = completedBackup;
      }

      debugPrint('✅ Enhanced backup created: ${completedBackup.name}');
      return completedBackup;
    } catch (e) {
      debugPrint('❌ Failed to create enhanced backup: $e');
      rethrow;
    }
  }

  /// Restore from backup model
  Future<RestoreOperation> restoreFromBackupModel(BackupModel backup) async {
    final operationId = AppUtils.generateId();

    try {
      final operation = RestoreOperation(
        id: operationId,
        backupId: backup.id,
        startedAt: DateTime.now(),
        status: RestoreStatus.inProgress,
      );

      _activeRestores[operationId] = operation;

      if (backup.filePath == null || !File(backup.filePath!).existsSync()) {
        throw Exception('ملف النسخة الاحتياطية غير موجود');
      }

      // Verify backup integrity
      if (backup.checksum != null) {
        final currentChecksum = await _calculateChecksum(File(backup.filePath!));
        if (currentChecksum != backup.checksum) {
          throw Exception('النسخة الاحتياطية تالفة - فشل في التحقق من التكامل');
        }
      }

      // Perform restore using existing method
      await restoreFromBackup(backup.filePath!);

      final completedOperation = RestoreOperation(
        id: operationId,
        backupId: backup.id,
        startedAt: operation.startedAt,
        completedAt: DateTime.now(),
        status: RestoreStatus.completed,
        progress: 100.0,
        restoredTables: backup.includedTables,
      );

      _activeRestores[operationId] = completedOperation;

      debugPrint('✅ Restore completed successfully');
      return completedOperation;
    } catch (e) {
      final failedOperation = RestoreOperation(
        id: operationId,
        backupId: backup.id,
        startedAt: DateTime.now(),
        completedAt: DateTime.now(),
        status: RestoreStatus.failed,
        errorMessage: e.toString(),
      );

      _activeRestores[operationId] = failedOperation;

      debugPrint('❌ Restore failed: $e');
      rethrow;
    }
  }

  /// Get backup history
  List<BackupModel> getBackupHistory() {
    return List.from(_backupHistory);
  }

  /// Get active restore operations
  List<RestoreOperation> getActiveRestoreOperations() {
    return _activeRestores.values.toList();
  }

  /// Delete backup
  Future<void> deleteBackup(BackupModel backup) async {
    try {
      // Delete file if exists
      if (backup.filePath != null) {
        final file = File(backup.filePath!);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // Remove from history
      _backupHistory.removeWhere((b) => b.id == backup.id);

      debugPrint('✅ Backup deleted: ${backup.name}');
    } catch (e) {
      debugPrint('❌ Failed to delete backup: $e');
      rethrow;
    }
  }

  /// Cleanup old backups (enhanced version)
  Future<void> cleanupOldBackupsEnhanced() async {
    try {
      final now = DateTime.now();
      final retentionDate = now.subtract(Duration(days: _config.retentionDays));

      final oldBackups = _backupHistory
          .where((backup) => backup.createdAt.isBefore(retentionDate))
          .toList();

      for (final backup in oldBackups) {
        await deleteBackup(backup);
      }

      // Keep only max number of backups
      if (_backupHistory.length > _config.maxBackups) {
        _backupHistory.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        final excessBackups = _backupHistory.skip(_config.maxBackups).toList();

        for (final backup in excessBackups) {
          await deleteBackup(backup);
        }
      }

      debugPrint('✅ Cleanup completed. Removed ${oldBackups.length} old backups');
    } catch (e) {
      debugPrint('❌ Failed to cleanup old backups: $e');
    }
  }

  /// Get backup configuration
  BackupConfig getConfig() => _config;

  /// Update backup configuration
  Future<void> updateConfig(BackupConfig config) async {
    try {
      _config = config;

      // Save to preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('backup_config', jsonEncode(config.toJson()));

      debugPrint('✅ Backup configuration updated');
    } catch (e) {
      debugPrint('❌ Failed to update backup configuration: $e');
    }
  }

  /// Load backup configuration
  Future<void> loadConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString('backup_config');

      if (configJson != null) {
        _config = BackupConfig.fromJson(jsonDecode(configJson));
      }

      debugPrint('✅ Backup configuration loaded');
    } catch (e) {
      debugPrint('❌ Failed to load backup configuration: $e');
    }
  }

  /// Helper methods
  List<String> _getDefaultTables() {
    return [
      'users',
      'items',
      'warehouses',
      'invoices',
      'document_tracking',
      'agent_accounts',
      'payments',
    ];
  }

  // Device info method already exists above

  Future<String> _calculateChecksum(File file) async {
    try {
      final bytes = await file.readAsBytes();
      // Simple checksum using file size and modification time
      final stat = await file.stat();
      final sizeHash = bytes.length.hashCode;
      final timeHash = stat.modified.millisecondsSinceEpoch.hashCode;
      return (sizeHash ^ timeHash).abs().toString();
    } catch (e) {
      debugPrint('Error calculating checksum: $e');
      return '0';
    }
  }
}
