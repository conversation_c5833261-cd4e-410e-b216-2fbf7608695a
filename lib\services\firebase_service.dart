import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';

class FirebaseService {
  static FirebaseService? _instance;
  static FirebaseService get instance => _instance ??= FirebaseService._();
  
  FirebaseService._();

  // Firebase instances
  FirebaseAuth get auth => FirebaseAuth.instance;
  FirebaseFirestore get firestore => FirebaseFirestore.instance;
  FirebaseMessaging get messaging => FirebaseMessaging.instance;

  // Initialize Firebase
  Future<void> initialize() async {
    try {
      await Firebase.initializeApp();
      
      // Configure Firestore for offline persistence
      await _configureFirestore();
      
      // Initialize messaging
      await _initializeMessaging();
      
      if (kDebugMode) {
        print('Firebase initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing Firebase: $e');
      }
      rethrow;
    }
  }

  // Configure Firestore settings
  Future<void> _configureFirestore() async {
    try {
      // Enable offline persistence
      firestore.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      // Enable network for real-time updates when online
      await firestore.enableNetwork();

      if (kDebugMode) {
        print('Firestore configured for offline persistence');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error configuring Firestore: $e');
        print('App will continue in offline mode');
      }
      // Continue without Firebase - app works offline
    }
  }

  // Initialize Firebase Messaging
  Future<void> _initializeMessaging() async {
    try {
      // Request permission for notifications
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        if (kDebugMode) {
          print('User granted permission for notifications');
        }
      } else {
        if (kDebugMode) {
          print('User declined or has not accepted permission for notifications');
        }
      }

      // Get FCM token
      try {
        String? token = await messaging.getToken();
        if (kDebugMode) {
          print('FCM Token: $token');
        }
      } catch (tokenError) {
        if (kDebugMode) {
          print('⚠️ FCM Token not available on this platform: $tokenError');
        }
      }

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        if (kDebugMode) {
          print('Received foreground message: ${message.notification?.title}');
        }
        // Handle foreground notification
        _handleForegroundMessage(message);
      });

      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

      // Handle notification taps
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        if (kDebugMode) {
          print('Notification tapped: ${message.notification?.title}');
        }
        // Handle notification tap
        _handleNotificationTap(message);
      });

    } catch (e) {
      if (kDebugMode) {
        print('Error initializing messaging: $e');
      }
    }
  }

  // Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    // This will be implemented when we create the notification service
    if (kDebugMode) {
      print('Handling foreground message: ${message.notification?.body}');
    }
  }

  // Handle notification taps
  void _handleNotificationTap(RemoteMessage message) {
    // This will be implemented when we create the navigation service
    if (kDebugMode) {
      print('Handling notification tap: ${message.data}');
    }
  }

  // Check if user is authenticated
  bool get isAuthenticated => auth.currentUser != null;

  // Get current user
  User? get currentUser => auth.currentUser;

  // Get current user ID
  String? get currentUserId => auth.currentUser?.uid;

  // Sign out
  Future<void> signOut() async {
    try {
      await auth.signOut();
      if (kDebugMode) {
        print('User signed out successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error signing out: $e');
      }
      rethrow;
    }
  }

  // Check network connectivity and sync status
  Future<bool> isOnline() async {
    try {
      // Try to get a document to check connectivity
      await firestore.collection('_connectivity_test').limit(1).get();
      return true;
    } catch (e) {
      return false;
    }
  }

  // Force sync when coming back online
  Future<void> forceSyncWhenOnline() async {
    try {
      if (await isOnline()) {
        await firestore.enableNetwork();
        if (kDebugMode) {
          print('Forced sync completed');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error during force sync: $e');
      }
    }
  }

  // Get FCM token for current user
  Future<String?> getFCMToken() async {
    try {
      return await messaging.getToken();
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ FCM Token not available on this platform: $e');
      }
      return null;
    }
  }

  // Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await messaging.subscribeToTopic(topic);
      if (kDebugMode) {
        print('Subscribed to topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error subscribing to topic $topic: $e');
      }
    }
  }

  // Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await messaging.unsubscribeFromTopic(topic);
      if (kDebugMode) {
        print('Unsubscribed from topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error unsubscribing from topic $topic: $e');
      }
    }
  }
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  if (kDebugMode) {
    print('Handling background message: ${message.notification?.title}');
  }
}
