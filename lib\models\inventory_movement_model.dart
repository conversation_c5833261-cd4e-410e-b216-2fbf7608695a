import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for tracking inventory movements between warehouses
class InventoryMovementModel {
  final String id;
  final String itemId;
  final String itemName;
  final String brand;
  final String model;
  final String movementType; // 'in', 'out', 'transfer'
  final int quantity;
  final String sourceWarehouseId;
  final String? targetWarehouseId; // For transfers
  final String sourceWarehouseName;
  final String? targetWarehouseName;
  final String reason; // 'purchase', 'sale', 'transfer', 'adjustment', 'return'
  final String? referenceId; // Invoice ID, Transfer ID, etc.
  final double? unitCost;
  final double? totalCost;
  final DateTime timestamp;
  final String createdBy;
  final String? notes;
  final Map<String, dynamic>? additionalData;

  InventoryMovementModel({
    required this.id,
    required this.itemId,
    required this.itemName,
    required this.brand,
    required this.model,
    required this.movementType,
    required this.quantity,
    required this.sourceWarehouseId,
    this.targetWarehouseId,
    required this.sourceWarehouseName,
    this.targetWarehouseName,
    required this.reason,
    this.referenceId,
    this.unitCost,
    this.totalCost,
    required this.timestamp,
    required this.createdBy,
    this.notes,
    this.additionalData,
  });

  // Factory constructor from Firestore document
  factory InventoryMovementModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return InventoryMovementModel(
      id: doc.id,
      itemId: data['itemId'] ?? '',
      itemName: data['itemName'] ?? '',
      brand: data['brand'] ?? '',
      model: data['model'] ?? '',
      movementType: data['movementType'] ?? '',
      quantity: data['quantity'] ?? 0,
      sourceWarehouseId: data['sourceWarehouseId'] ?? '',
      targetWarehouseId: data['targetWarehouseId'],
      sourceWarehouseName: data['sourceWarehouseName'] ?? '',
      targetWarehouseName: data['targetWarehouseName'],
      reason: data['reason'] ?? '',
      referenceId: data['referenceId'],
      unitCost: data['unitCost']?.toDouble(),
      totalCost: data['totalCost']?.toDouble(),
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      notes: data['notes'],
      additionalData: data['additionalData'] != null 
          ? Map<String, dynamic>.from(data['additionalData']) 
          : null,
    );
  }

  // Factory constructor from Map (for local database)
  factory InventoryMovementModel.fromMap(Map<String, dynamic> map) {
    return InventoryMovementModel(
      id: map['id'] ?? '',
      itemId: map['itemId'] ?? '',
      itemName: map['itemName'] ?? '',
      brand: map['brand'] ?? '',
      model: map['model'] ?? '',
      movementType: map['movementType'] ?? '',
      quantity: map['quantity'] ?? 0,
      sourceWarehouseId: map['sourceWarehouseId'] ?? '',
      targetWarehouseId: map['targetWarehouseId'],
      sourceWarehouseName: map['sourceWarehouseName'] ?? '',
      targetWarehouseName: map['targetWarehouseName'],
      reason: map['reason'] ?? '',
      referenceId: map['referenceId'],
      unitCost: map['unitCost']?.toDouble(),
      totalCost: map['totalCost']?.toDouble(),
      timestamp: DateTime.parse(map['timestamp']),
      createdBy: map['createdBy'] ?? '',
      notes: map['notes'],
      additionalData: _parseAdditionalData(map['additionalData']),
    );
  }

  // Convert to Map (for local database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'itemId': itemId,
      'itemName': itemName,
      'brand': brand,
      'model': model,
      'movementType': movementType,
      'quantity': quantity,
      'sourceWarehouseId': sourceWarehouseId,
      'targetWarehouseId': targetWarehouseId,
      'sourceWarehouseName': sourceWarehouseName,
      'targetWarehouseName': targetWarehouseName,
      'reason': reason,
      'referenceId': referenceId,
      'unitCost': unitCost,
      'totalCost': totalCost,
      'timestamp': timestamp.toIso8601String(),
      'createdBy': createdBy,
      'notes': notes,
      'additionalData': additionalData,
    };
  }

  // Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    return {
      'itemId': itemId,
      'itemName': itemName,
      'brand': brand,
      'model': model,
      'movementType': movementType,
      'quantity': quantity,
      'sourceWarehouseId': sourceWarehouseId,
      'targetWarehouseId': targetWarehouseId,
      'sourceWarehouseName': sourceWarehouseName,
      'targetWarehouseName': targetWarehouseName,
      'reason': reason,
      'referenceId': referenceId,
      'unitCost': unitCost,
      'totalCost': totalCost,
      'timestamp': Timestamp.fromDate(timestamp),
      'createdBy': createdBy,
      'notes': notes,
      'additionalData': additionalData,
    };
  }

  // Copy with method for updates
  InventoryMovementModel copyWith({
    String? id,
    String? itemId,
    String? itemName,
    String? brand,
    String? model,
    String? movementType,
    int? quantity,
    String? sourceWarehouseId,
    String? targetWarehouseId,
    String? sourceWarehouseName,
    String? targetWarehouseName,
    String? reason,
    String? referenceId,
    double? unitCost,
    double? totalCost,
    DateTime? timestamp,
    String? createdBy,
    String? notes,
    Map<String, dynamic>? additionalData,
  }) {
    return InventoryMovementModel(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      itemName: itemName ?? this.itemName,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      movementType: movementType ?? this.movementType,
      quantity: quantity ?? this.quantity,
      sourceWarehouseId: sourceWarehouseId ?? this.sourceWarehouseId,
      targetWarehouseId: targetWarehouseId ?? this.targetWarehouseId,
      sourceWarehouseName: sourceWarehouseName ?? this.sourceWarehouseName,
      targetWarehouseName: targetWarehouseName ?? this.targetWarehouseName,
      reason: reason ?? this.reason,
      referenceId: referenceId ?? this.referenceId,
      unitCost: unitCost ?? this.unitCost,
      totalCost: totalCost ?? this.totalCost,
      timestamp: timestamp ?? this.timestamp,
      createdBy: createdBy ?? this.createdBy,
      notes: notes ?? this.notes,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  // Helper methods
  bool get isInbound => movementType == 'in';
  bool get isOutbound => movementType == 'out';
  bool get isTransfer => movementType == 'transfer';
  
  bool get isPurchase => reason == 'purchase';
  bool get isSale => reason == 'sale';
  bool get isTransferReason => reason == 'transfer';
  bool get isAdjustment => reason == 'adjustment';
  bool get isReturn => reason == 'return';

  String get displayReason {
    switch (reason) {
      case 'purchase':
        return 'شراء';
      case 'sale':
        return 'بيع';
      case 'transfer':
        return 'تحويل';
      case 'adjustment':
        return 'تسوية';
      case 'return':
        return 'مرتجع';
      default:
        return reason;
    }
  }

  String get displayMovementType {
    switch (movementType) {
      case 'in':
        return 'وارد';
      case 'out':
        return 'صادر';
      case 'transfer':
        return 'تحويل';
      default:
        return movementType;
    }
  }

  // Helper method to parse additional data from different formats
  static Map<String, dynamic>? _parseAdditionalData(dynamic additionalDataValue) {
    if (additionalDataValue == null) return null;
    
    try {
      if (additionalDataValue is String) {
        return null;
      } else if (additionalDataValue is Map) {
        return Map<String, dynamic>.from(additionalDataValue);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'InventoryMovementModel(id: $id, itemName: $itemName, movementType: $movementType, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InventoryMovementModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Model for warehouse stock levels
class WarehouseStockModel {
  final String warehouseId;
  final String warehouseName;
  final String warehouseType; // 'main', 'showroom', 'agent'
  final String itemId;
  final String itemName;
  final String brand;
  final String model;
  final int currentStock;
  final int minStock;
  final int maxStock;
  final double? averageCost;
  final DateTime lastUpdated;
  final String? motorFingerprintText;

  WarehouseStockModel({
    required this.warehouseId,
    required this.warehouseName,
    required this.warehouseType,
    required this.itemId,
    required this.itemName,
    required this.brand,
    required this.model,
    required this.currentStock,
    required this.minStock,
    required this.maxStock,
    this.averageCost,
    required this.lastUpdated,
    this.motorFingerprintText,
  });

  // Helper methods
  bool get isLowStock => currentStock <= minStock;
  bool get isOverStock => currentStock >= maxStock;
  bool get isOutOfStock => currentStock <= 0;
  
  double get stockPercentage {
    if (maxStock <= 0) return 0.0;
    return (currentStock / maxStock).clamp(0.0, 1.0);
  }

  factory WarehouseStockModel.fromMap(Map<String, dynamic> map) {
    return WarehouseStockModel(
      warehouseId: map['warehouseId'] ?? '',
      warehouseName: map['warehouseName'] ?? '',
      warehouseType: map['warehouseType'] ?? '',
      itemId: map['itemId'] ?? '',
      itemName: map['itemName'] ?? '',
      brand: map['brand'] ?? '',
      model: map['model'] ?? '',
      currentStock: map['currentStock'] ?? 0,
      minStock: map['minStock'] ?? 0,
      maxStock: map['maxStock'] ?? 0,
      averageCost: map['averageCost']?.toDouble(),
      lastUpdated: DateTime.parse(map['lastUpdated']),
      motorFingerprintText: map['motorFingerprintText'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'warehouseId': warehouseId,
      'warehouseName': warehouseName,
      'warehouseType': warehouseType,
      'itemId': itemId,
      'itemName': itemName,
      'brand': brand,
      'model': model,
      'currentStock': currentStock,
      'minStock': minStock,
      'maxStock': maxStock,
      'averageCost': averageCost,
      'lastUpdated': lastUpdated.toIso8601String(),
      'motorFingerprintText': motorFingerprintText,
    };
  }

  @override
  String toString() {
    return 'WarehouseStockModel(warehouseName: $warehouseName, itemName: $itemName, currentStock: $currentStock)';
  }
}
