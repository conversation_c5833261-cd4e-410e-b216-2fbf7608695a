import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/user_model.dart';
import '../../services/windows_functional/windows_firebase_service.dart';
import '../../services/windows_functional/windows_data_service.dart';

/// مزود المصادقة للويندوز - متوافق وظيفياً مع نسخة Android
class WindowsAuthProvider extends ChangeNotifier {
  final WindowsFirebaseService _firebaseService = WindowsFirebaseService.instance;
  final WindowsDataService _dataService = WindowsDataService.instance;

  User? _firebaseUser;
  UserModel? _userModel;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters - same as Android
  bool get isAuthenticated => _firebaseUser != null;
  User? get firebaseUser => _firebaseUser;
  UserModel? get currentUser => _userModel;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // User role checks - same as Android
  bool get isSuperAdmin => _userModel?.role == 'super_admin';
  bool get isAdmin => _userModel?.role == 'admin' || isSuperAdmin;
  bool get isAgent => _userModel?.role == 'agent';
  bool get isShowroom => _userModel?.role == 'showroom';

  /// تهيئة مزود المصادقة
  WindowsAuthProvider() {
    _initializeAuthProvider();
  }

  /// تهيئة مزود المصادقة والاستماع لتغييرات حالة المصادقة
  void _initializeAuthProvider() {
    // Listen to auth state changes - same as Android
    _firebaseService.auth.authStateChanges().listen((User? user) async {
      _firebaseUser = user;
      
      if (user != null) {
        // Load user model from database
        await _loadUserModel(user.uid);
      } else {
        _userModel = null;
      }
      
      notifyListeners();
    });
  }

  /// تحميل نموذج المستخدم من قاعدة البيانات
  Future<void> _loadUserModel(String userId) async {
    try {
      _userModel = await _dataService.getUserById(userId);
      if (kDebugMode) {
        print('✅ User model loaded: ${_userModel?.fullName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading user model: $e');
      }
    }
  }

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<void> signInWithEmailAndPassword(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();

      // Sign in with Firebase - same as Android
      final credential = await _firebaseService.signInWithEmailAndPassword(email, password);
      
      if (credential.user != null) {
        // Load user model
        await _loadUserModel(credential.user!.uid);
        
        if (kDebugMode) {
          print('✅ User signed in successfully on Windows: ${credential.user!.email}');
        }
      }
    } catch (e) {
      _setError(_getErrorMessage(e));
      if (kDebugMode) {
        print('❌ Error signing in on Windows: $e');
      }
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// إنشاء حساب جديد
  Future<void> createUserWithEmailAndPassword(String email, String password, UserModel userModel) async {
    try {
      _setLoading(true);
      _clearError();

      // Create user with Firebase - same as Android
      final credential = await _firebaseService.createUserWithEmailAndPassword(email, password);
      
      if (credential.user != null) {
        // Create user model with Firebase UID
        final newUserModel = userModel.copyWith(id: credential.user!.uid);
        
        // Save user model to database
        await _dataService.addUser(newUserModel);
        
        // Load the created user model
        await _loadUserModel(credential.user!.uid);
        
        if (kDebugMode) {
          print('✅ User created successfully on Windows: ${credential.user!.email}');
        }
      }
    } catch (e) {
      _setError(_getErrorMessage(e));
      if (kDebugMode) {
        print('❌ Error creating user on Windows: $e');
      }
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      _setLoading(true);
      _clearError();

      await _firebaseService.signOut();
      
      _firebaseUser = null;
      _userModel = null;
      
      if (kDebugMode) {
        print('✅ User signed out successfully on Windows');
      }
    } catch (e) {
      _setError(_getErrorMessage(e));
      if (kDebugMode) {
        print('❌ Error signing out on Windows: $e');
      }
    } finally {
      _setLoading(false);
    }
  }

  /// إعادة تحميل بيانات المستخدم
  Future<void> reloadUser() async {
    try {
      if (_firebaseUser != null) {
        await _firebaseUser!.reload();
        _firebaseUser = _firebaseService.auth.currentUser;
        
        if (_firebaseUser != null) {
          await _loadUserModel(_firebaseUser!.uid);
        }
        
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error reloading user: $e');
      }
    }
  }

  /// تحديث بيانات المستخدم
  Future<void> updateUser(UserModel updatedUser) async {
    try {
      _setLoading(true);
      _clearError();

      // Update user in database
      await _dataService.addUser(updatedUser); // This will update if exists
      
      // Reload user model
      await _loadUserModel(updatedUser.id);
      
      if (kDebugMode) {
        print('✅ User updated successfully: ${updatedUser.id}');
      }
    } catch (e) {
      _setError(_getErrorMessage(e));
      if (kDebugMode) {
        print('❌ Error updating user: $e');
      }
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// إرسال رابط إعادة تعيين كلمة المرور
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      _setLoading(true);
      _clearError();

      await _firebaseService.auth.sendPasswordResetEmail(email: email);
      
      if (kDebugMode) {
        print('✅ Password reset email sent to: $email');
      }
    } catch (e) {
      _setError(_getErrorMessage(e));
      if (kDebugMode) {
        print('❌ Error sending password reset email: $e');
      }
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// التحقق من صحة كلمة المرور الحالية
  Future<bool> verifyCurrentPassword(String password) async {
    try {
      if (_firebaseUser?.email == null) return false;

      final credential = EmailAuthProvider.credential(
        email: _firebaseUser!.email!,
        password: password,
      );

      await _firebaseUser!.reauthenticateWithCredential(credential);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error verifying current password: $e');
      }
      return false;
    }
  }

  /// تغيير كلمة المرور
  Future<void> changePassword(String currentPassword, String newPassword) async {
    try {
      _setLoading(true);
      _clearError();

      // Verify current password first
      if (!await verifyCurrentPassword(currentPassword)) {
        throw 'كلمة المرور الحالية غير صحيحة';
      }

      // Update password
      await _firebaseUser!.updatePassword(newPassword);
      
      if (kDebugMode) {
        print('✅ Password changed successfully');
      }
    } catch (e) {
      _setError(_getErrorMessage(e));
      if (kDebugMode) {
        print('❌ Error changing password: $e');
      }
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة خطأ
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// مسح رسالة الخطأ
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// الحصول على رسالة خطأ مفهومة
  String _getErrorMessage(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-not-found':
          return 'لا يوجد مستخدم بهذا البريد الإلكتروني';
        case 'wrong-password':
          return 'كلمة المرور غير صحيحة';
        case 'email-already-in-use':
          return 'البريد الإلكتروني مستخدم بالفعل';
        case 'weak-password':
          return 'كلمة المرور ضعيفة';
        case 'invalid-email':
          return 'البريد الإلكتروني غير صحيح';
        case 'user-disabled':
          return 'تم تعطيل هذا الحساب';
        case 'too-many-requests':
          return 'تم تجاوز عدد المحاولات المسموح، حاول لاحقاً';
        case 'network-request-failed':
          return 'خطأ في الاتصال بالإنترنت';
        default:
          return 'حدث خطأ غير متوقع: ${error.message}';
      }
    }
    
    return error.toString();
  }

  @override
  void dispose() {
    super.dispose();
    if (kDebugMode) {
      print('🧹 Windows Auth Provider disposed');
    }
  }
}
