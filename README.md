# تطبيق آل فرحان للنقل الخفيف

تطبيق Flutter متكامل لإدارة أعمال شركة آل فرحان للنقل الخفيف في مصر، يشمل إدارة المخزون والمبيعات وحسابات الوكلاء مع دعم العمل بدون إنترنت.

## 🚀 المميزات الرئيسية

### 📱 نظام إدارة شامل
- **إدارة المخزون**: تتبع الأصناف عبر مخازن متعددة
- **نظام المبيعات**: إنشاء فواتير البيع مع تتبع الأرباح
- **إدارة الوكلاء**: متابعة حسابات الوكلاء والمدفوعات
- **تتبع الجوابات**: متابعة حالة الأوراق الرسمية للمركبات
- **التقارير**: تقارير شاملة للمبيعات والأرباح والمخزون

### 🔍 تقنية OCR المتقدمة
- **استخراج بصمة الموتور**: تصوير وقراءة بصمة الموتور تلقائياً
- **قراءة بطاقة الهوية**: استخراج بيانات العملاء من بطاقة الهوية
- **معالجة الصور**: ضغط وتحسين جودة الصور

### 🌐 العمل بدون إنترنت (Offline-First)
- **قاعدة بيانات محلية**: SQLite للعمل بدون إنترنت
- **مزامنة تلقائية**: مزامنة البيانات عند توفر الإنترنت
- **طابور المزامنة**: حفظ العمليات للمزامنة لاحقاً

### 🔐 نظام صلاحيات متقدم
- **مدير أعلى**: وصول كامل لجميع الوظائف
- **مدير إداري**: إدارة المخزون والفواتير والتقارير
- **وكيل**: الوصول لمخزنه وحساباته فقط
- **مستخدم معرض**: إدارة مخزن المعرض

## 🛠️ التقنيات المستخدمة

### Frontend
- **Flutter 3.24+**: إطار العمل الأساسي
- **Provider**: إدارة الحالة
- **Material Design 3**: تصميم واجهة المستخدم

### Backend & Database
- **Firebase Cloud Firestore**: قاعدة البيانات السحابية
- **Firebase Authentication**: نظام المصادقة
- **Firebase Cloud Messaging**: الإشعارات
- **SQLite**: قاعدة البيانات المحلية

### معالجة الصور و OCR
- **Google ML Kit**: تقنية OCR
- **Image Picker**: التقاط الصور
- **Cloudinary**: تخزين الصور السحابي

### مكتبات إضافية
- **Connectivity Plus**: مراقبة الاتصال بالإنترنت
- **Shared Preferences**: التخزين المحلي
- **Cached Network Image**: تحميل وحفظ الصور
- **PDF & Printing**: إنشاء وطباعة التقارير

## 📁 هيكل المشروع

```
lib/
├── core/                    # الملفات الأساسية
│   ├── constants/          # الثوابت
│   ├── theme/              # تصميم التطبيق
│   └── utils/              # الأدوات المساعدة
├── models/                 # نماذج البيانات
│   ├── user_model.dart
│   ├── item_model.dart
│   ├── warehouse_model.dart
│   ├── invoice_model.dart
│   ├── payment_model.dart
│   └── document_tracking_model.dart
├── services/               # الخدمات
│   ├── firebase_service.dart
│   ├── local_database_service.dart
│   ├── sync_service.dart
│   ├── auth_service.dart
│   ├── image_service.dart
│   ├── notification_service.dart
│   └── data_service.dart
├── providers/              # مزودي الحالة
│   └── auth_provider.dart
├── screens/                # الشاشات
│   ├── auth/              # شاشات المصادقة
│   ├── home/              # الشاشة الرئيسية
│   ├── inventory/         # شاشات المخزون
│   ├── sales/             # شاشات المبيعات
│   ├── agents/            # شاشات الوكلاء
│   ├── documents/         # شاشات تتبع الجوابات
│   └── reports/           # شاشات التقارير
└── main.dart              # نقطة البداية
```

## 🔧 إعداد المشروع

### المتطلبات
- Flutter SDK 3.24 أو أحدث
- Dart SDK 3.5 أو أحدث
- Android Studio / VS Code
- حساب Firebase

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd el_farhan_app
```

2. **تثبيت المكتبات**
```bash
flutter pub get
```

3. **إعداد Firebase**
- إنشاء مشروع Firebase جديد
- إضافة تطبيق Android/iOS
- تحميل ملفات التكوين:
  - `android/app/google-services.json`
  - `ios/Runner/GoogleService-Info.plist`

4. **إعداد قواعد Firestore**
```bash
# نسخ قواعد الأمان
cp firestore.rules [firebase-project]/firestore.rules
```

5. **إعداد Cloudinary**
- إنشاء حساب Cloudinary
- تحديث إعدادات الرفع في `lib/services/image_service.dart`

6. **تشغيل التطبيق**
```bash
flutter run
```

## 📊 قاعدة البيانات

### مجموعات Firestore الرئيسية
- `users`: بيانات المستخدمين والصلاحيات
- `warehouses`: المخازن ومعلوماتها
- `items`: الأصناف وبصمات الموتورات
- `invoices`: فواتير البيع والشراء
- `payments`: مدفوعات الوكلاء
- `document_tracking`: تتبع حالة الجوابات

### قاعدة البيانات المحلية (SQLite)
- نسخة محلية من جميع البيانات
- طابور المزامنة للعمليات غير المتزامنة
- فهرسة للبحث السريع

## 🔐 نظام الأمان

### Firebase Security Rules
- قواعد أمان شاملة حسب دور المستخدم
- تشفير البيانات الحساسة
- تسجيل جميع العمليات في Audit Logs

### المصادقة
- Firebase Authentication
- تسجيل الدخول بالاسم وكلمة المرور
- إدارة الجلسات والرموز المميزة

## 📱 الواجهات الرئيسية

### شاشة تسجيل الدخول
- تصميم احترافي مع شعار الشركة
- دعم اللغة العربية
- رسائل خطأ واضحة

### الشاشة الرئيسية
- لوحة تحكم حسب دور المستخدم
- إجراءات سريعة
- إحصائيات مهمة

### إدارة المخزون
- إضافة أصناف جديدة مع OCR
- البحث والفلترة المتقدمة
- تتبع حالة الأصناف

### نظام المبيعات
- إنشاء فواتير مع قراءة بطاقة الهوية
- حساب الأرباح تلقائياً
- تتبع المدفوعات

## 🚀 المميزات المستقبلية

- [ ] تطبيق ويب للإدارة
- [ ] تقارير PDF قابلة للتخصيص
- [ ] إشعارات push متقدمة
- [ ] تكامل مع أنظمة محاسبية
- [ ] تحليلات متقدمة بالذكاء الاصطناعي
- [ ] دعم عدة عملات
- [ ] نظام النسخ الاحتياطي التلقائي

## 📞 الدعم والتواصل

للدعم الفني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +20 xxx xxx xxxx

## 📄 الترخيص

هذا المشروع محمي بحقوق الطبع والنشر لشركة آل فرحان للنقل الخفيف.

---

**تم تطوير هذا التطبيق بواسطة فريق التطوير المتخصص لخدمة قطاع النقل الخفيف في مصر**
