import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
import '../models/invoice_model.dart';
import '../models/item_model.dart';
import '../models/user_model.dart';
import '../core/utils/app_utils.dart';
import 'data_service.dart';

class EnhancedNotificationService {
  static EnhancedNotificationService? _instance;
  static EnhancedNotificationService get instance {
    _instance ??= EnhancedNotificationService._internal();
    return _instance!;
  }
  
  EnhancedNotificationService._internal();

  final DataService _dataService = DataService.instance;

  bool _isInitialized = false;

  /// Initialize notification service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _initializeFCM();
      
      _isInitialized = true;
      debugPrint('✅ Enhanced notification service initialized successfully');
    } catch (e) {
      debugPrint('❌ Failed to initialize enhanced notification service: $e');
    }
  }

  /// Initialize FCM (simplified)
  Future<void> _initializeFCM() async {
    try {
      // For now, just log that FCM would be initialized
      debugPrint('✅ FCM initialization skipped - simplified version');
    } catch (e) {
      debugPrint('⚠️ FCM initialization failed: $e');
    }
  }

  /// Show local notification (simplified)
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    String? imageUrl,
  }) async {
    try {
      // Simple notification without platform-specific details
      debugPrint('📱 Showing local notification: $title - $body');
      
      // For now, just log the notification
      // In a real implementation, this would show actual notifications
      if (kDebugMode) {
        print('🔔 Notification: $title');
        print('📝 Message: $body');
        if (payload != null) {
          print('📦 Payload: $payload');
        }
      }
    } catch (e) {
      debugPrint('❌ Failed to show local notification: $e');
    }
  }

  /// Send invoice created notification
  Future<void> sendInvoiceCreatedNotification({
    required InvoiceModel invoice,
    required ItemModel item,
    required UserModel agent,
    String? compositeImageUrl,
  }) async {
    try {
      // Create notification model
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'فاتورة جديدة - ${invoice.invoiceNumber}',
        message: 'تم إنشاء فاتورة جديدة بواسطة ${agent.fullName}\n'
               'المركبة: ${item.brand} ${item.model}\n'
               'المبلغ: ${AppUtils.formatCurrency(invoice.sellingPrice)}',
        type: 'invoice_created',
        targetRole: 'manager', // Send to all managers
        relatedId: invoice.id,
        data: {
          'invoiceId': invoice.id,
          'invoiceNumber': invoice.invoiceNumber,
          'agentId': agent.id,
          'agentName': agent.fullName,
          'itemBrand': item.brand,
          'itemModel': item.model,
          'amount': invoice.sellingPrice,
          'compositeImageUrl': compositeImageUrl,
        },
        createdAt: DateTime.now(),
        createdBy: agent.id,
        isRead: false,
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification
      await showLocalNotification(
        title: notification.title,
        body: notification.message,
        payload: jsonEncode(notification.data ?? {}),
      );

      // Send push notification to managers
      await _sendPushNotificationToManagers(notification);

      debugPrint('✅ Invoice created notification sent successfully');
    } catch (e) {
      debugPrint('❌ Failed to send invoice created notification: $e');
    }
  }

  /// Send document status update notification
  Future<void> sendDocumentStatusUpdateNotification({
    required String agentId,
    required String itemBrand,
    required String itemModel,
    required String oldStatus,
    required String newStatus,
  }) async {
    try {
      final statusText = _getDocumentStatusText(newStatus);
      
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'تحديث حالة الجواب',
        message: 'تم تحديث حالة جواب $itemBrand $itemModel إلى: $statusText',
        type: 'document_status_update',
        targetUserId: agentId,
        data: {
          'itemBrand': itemBrand,
          'itemModel': itemModel,
          'oldStatus': oldStatus,
          'newStatus': newStatus,
          'statusText': statusText,
        },
        createdAt: DateTime.now(),
        createdBy: 'system',
        isRead: false,
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification
      await showLocalNotification(
        title: notification.title,
        body: notification.message,
        payload: jsonEncode(notification.data ?? {}),
      );

      // Send push notification to specific agent
      await _sendPushNotificationToUser(agentId, notification);

      debugPrint('✅ Document status update notification sent successfully');
    } catch (e) {
      debugPrint('❌ Failed to send document status update notification: $e');
    }
  }

  /// Send payment reminder notification
  Future<void> sendPaymentReminderNotification({
    required String agentId,
    required double amount,
    required int daysOverdue,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'تذكير بالدفع',
        message: 'لديك مبلغ مستحق ${AppUtils.formatCurrency(amount)}\n'
               'متأخر منذ $daysOverdue أيام',
        type: 'payment_reminder',
        targetUserId: agentId,
        data: {
          'amount': amount,
          'daysOverdue': daysOverdue,
        },
        createdAt: DateTime.now(),
        createdBy: 'system',
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification
      await showLocalNotification(
        title: notification.title,
        body: notification.message,
        payload: jsonEncode(notification.data ?? {}),
      );

      // Send push notification to specific agent
      await _sendPushNotificationToUser(agentId, notification);

      debugPrint('✅ Payment reminder notification sent successfully');
    } catch (e) {
      debugPrint('❌ Failed to send payment reminder notification: $e');
    }
  }

  /// Send system alert notification
  Future<void> sendSystemAlertNotification({
    required String title,
    required String message,
    String? recipientId,
    Map<String, dynamic>? data,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: title,
        message: message,
        type: 'system_alert',
        targetUserId: recipientId,
        data: data,
        createdAt: DateTime.now(),
        createdBy: 'system',
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification
      await showLocalNotification(
        title: notification.title,
        body: notification.message,
        payload: jsonEncode(notification.data ?? {}),
      );

      // Send push notification
      if (recipientId != null) {
        await _sendPushNotificationToUser(recipientId, notification);
      } else {
        await _sendPushNotificationToAll(notification);
      }

      debugPrint('✅ System alert notification sent successfully');
    } catch (e) {
      debugPrint('❌ Failed to send system alert notification: $e');
    }
  }

  /// Send item transfer notification to agent
  Future<void> sendItemTransferNotification({
    required String agentId,
    required ItemModel item,
    required String fromWarehouse,
    required String toWarehouse,
  }) async {
    try {
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: 'تحويل بضاعة جديدة',
        message: 'تم تحويل ${item.brand} ${item.model} إلى مخزنك\n'
               'البصمة: ${item.motorFingerprintText}\n'
               'من: $fromWarehouse',
        type: 'item_transfer',
        targetUserId: agentId,
        data: {
          'itemId': item.id,
          'itemBrand': item.brand,
          'itemModel': item.model,
          'motorFingerprint': item.motorFingerprintText,
          'fromWarehouse': fromWarehouse,
          'toWarehouse': toWarehouse,
          'transferDate': DateTime.now().toIso8601String(),
        },
        createdAt: DateTime.now(),
        createdBy: 'system',
        isRead: false,
      );

      // Save to database
      await _dataService.createNotification(notification);

      // Send local notification
      await showLocalNotification(
        title: notification.title,
        body: notification.message,
        payload: jsonEncode(notification.data ?? {}),
      );

      // Send push notification to specific agent
      await _sendPushNotificationToUser(agentId, notification);

      debugPrint('✅ Item transfer notification sent to agent $agentId');
    } catch (e) {
      debugPrint('❌ Failed to send item transfer notification: $e');
    }
  }

  /// Send push notification to managers
  Future<void> _sendPushNotificationToManagers(NotificationModel notification) async {
    try {
      // Get all managers (super_admin and admin roles)
      final superAdmins = await _dataService.getUsersByRole('super_admin');
      final admins = await _dataService.getUsersByRole('admin');
      final managers = [...superAdmins, ...admins];

      for (final manager in managers) {
        await _sendPushNotificationToUser(manager.id, notification);
      }
      
      if (kDebugMode) {
        print('Push notifications sent to ${managers.length} managers');
      }
    } catch (e) {
      debugPrint('❌ Failed to send push notification to managers: $e');
    }
  }

  /// Send push notification to specific user
  Future<void> _sendPushNotificationToUser(String userId, NotificationModel notification) async {
    try {
      // For now, just log the push notification
      debugPrint('📱 Push notification to user $userId: ${notification.title}');
    } catch (e) {
      debugPrint('❌ Failed to send push notification to user $userId: $e');
    }
  }

  /// Send push notification to all users
  Future<void> _sendPushNotificationToAll(NotificationModel notification) async {
    try {
      // For now, just log the push notification
      debugPrint('📱 Push notification to all users: ${notification.title}');
    } catch (e) {
      debugPrint('❌ Failed to send push notification to all users: $e');
    }
  }

  /// Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      await _dataService.markNotificationAsRead(notificationId);
      debugPrint('✅ Notification marked as read: $notificationId');
    } catch (e) {
      debugPrint('❌ Failed to mark notification as read: $e');
    }
  }

  /// Get document status text in Arabic
  String _getDocumentStatusText(String status) {
    switch (status) {
      case 'sent_to_manufacturer':
        return 'تم إرسال للشركة المصنعة';
      case 'received_from_manufacturer':
        return 'تم استلام من الشركة المصنعة';
      case 'ready_for_delivery':
        return 'جاهز للتسليم';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }
}
