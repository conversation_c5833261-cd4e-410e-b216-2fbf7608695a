import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/desktop_theme.dart';
import '../../widgets/desktop/desktop_dashboard_cards.dart';
import '../../widgets/desktop/desktop_data_table.dart';
import '../../widgets/desktop/desktop_image_widget.dart';
import '../../widgets/radical_image_widget.dart';
import '../../widgets/ultimate_image_widget.dart';
import '../../widgets/android_style_image_widget.dart';
import '../../widgets/http_only_image_widget.dart';
import '../../services/data_service.dart';
import '../../services/desktop/desktop_image_service.dart';
import '../../core/utils/app_utils.dart';
import '../../utils/image_diagnostics.dart';
import '../../utils/ssl_diagnostics.dart';
import '../../utils/image_url_fixer.dart';
import '../../core/network/radical_ssl_bypass.dart';
import '../../core/network/ultimate_ssl_bypass.dart';
import '../../models/item_model.dart';
import '../../models/warehouse_model.dart';

class DesktopInventoryScreen extends StatefulWidget {
  const DesktopInventoryScreen({super.key});

  @override
  State<DesktopInventoryScreen> createState() => _DesktopInventoryScreenState();
}

class _DesktopInventoryScreenState extends State<DesktopInventoryScreen> {
  bool _isLoading = true;
  List<ItemModel> _items = [];
  List<WarehouseModel> _warehouses = [];
  String _selectedWarehouse = 'all';
  String _selectedStatus = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      final items = await dataService.getItems();
      final warehouses = await dataService.getWarehouses();

      setState(() {
        _items = items;
        _warehouses = warehouses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          _buildHeaderSection(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // Statistics Cards
          _buildStatisticsCards(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // Filters and Search
          _buildFiltersSection(),

          const SizedBox(height: DesktopTheme.spacingLarge),

          // Items Table
          _buildItemsTable(),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(DesktopTheme.spacingXLarge),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            DesktopTheme.primaryBlue,
            DesktopTheme.primaryBlueDark,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(DesktopTheme.radiusXLarge),
        boxShadow: DesktopTheme.elevatedShadow,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة المخزون',
                  style: DesktopTheme.headingLarge.copyWith(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: DesktopTheme.spacingSmall),
                Text(
                  'إدارة شاملة لجميع الأصناف والمخازن',
                  style: DesktopTheme.titleMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: DesktopTheme.spacingMedium),
                Row(
                  children: [
                    _buildQuickStat('إجمالي الأصناف', _items.length.toString(), Icons.inventory_2),
                    const SizedBox(width: DesktopTheme.spacingXLarge),
                    _buildQuickStat('المخازن النشطة', _warehouses.length.toString(), Icons.warehouse),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesktopTheme.radiusLarge),
            ),
            child: const Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 20),
        ),
        const SizedBox(width: DesktopTheme.spacingSmall),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: DesktopTheme.titleLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: DesktopTheme.bodySmall.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatisticsCards() {
    final availableItems = _items.where((item) => item.status == 'متاح').length;
    final soldItems = _items.where((item) => item.status == 'مباع').length;
    final reservedItems = _items.where((item) => item.status == 'محجوز').length;
    final totalValue = _items.fold<double>(0, (sum, item) => sum + item.purchasePrice);

    return DesktopDashboardGrid(
      crossAxisCount: 4,
      childAspectRatio: 1.3,
      children: [
        DesktopDashboardCard(
          title: 'الأصناف المتاحة',
          value: availableItems.toString(),
          subtitle: 'جاهز للبيع',
          icon: Icons.check_circle_outline,
          iconColor: DesktopTheme.statusSuccess,
          trend: '+${((availableItems / _items.length) * 100).toStringAsFixed(1)}%',
          isPositiveTrend: true,
          onTap: () => _filterByStatus('متاح'),
        ),
        DesktopDashboardCard(
          title: 'الأصناف المباعة',
          value: soldItems.toString(),
          subtitle: 'تم البيع',
          icon: Icons.shopping_cart_outlined,
          iconColor: DesktopTheme.accentRed,
          trend: '${((soldItems / _items.length) * 100).toStringAsFixed(1)}%',
          isPositiveTrend: false,
          onTap: () => _filterByStatus('مباع'),
        ),
        DesktopDashboardCard(
          title: 'الأصناف المحجوزة',
          value: reservedItems.toString(),
          subtitle: 'قيد الحجز',
          icon: Icons.bookmark_outline,
          iconColor: DesktopTheme.accentOrange,
          onTap: () => _filterByStatus('محجوز'),
        ),
        DesktopDashboardCard(
          title: 'إجمالي القيمة',
          value: '${(totalValue / 1000).toStringAsFixed(1)}ك',
          subtitle: 'جنيه مصري',
          icon: Icons.account_balance_wallet_outlined,
          iconColor: DesktopTheme.accentPurple,
        ),
      ],
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
      decoration: DesktopTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, color: DesktopTheme.primaryBlue, size: 24),
              const SizedBox(width: DesktopTheme.spacingSmall),
              const Text(
                'فلاتر البحث والعرض',
                style: DesktopTheme.titleLarge,
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _addNewItem,
                icon: const Icon(Icons.add, size: 20),
                label: const Text('إضافة صنف جديد'),
                style: DesktopTheme.primaryButtonStyle,
              ),
              const SizedBox(width: DesktopTheme.spacingMedium),
              ElevatedButton.icon(
                onPressed: _transferItems,
                icon: const Icon(Icons.swap_horiz, size: 20),
                label: const Text('نقل بضائع'),
                style: DesktopTheme.secondaryButtonStyle,
              ),
            ],
          ),
          const SizedBox(height: DesktopTheme.spacingLarge),
          Row(
            children: [
              // Search Field
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _searchController,
                  decoration: DesktopTheme.getInputDecoration(
                    labelText: 'البحث في الأصناف...',
                    prefixIcon: const Icon(Icons.search),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              const SizedBox(width: DesktopTheme.spacingMedium),

              // Warehouse Filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedWarehouse,
                  decoration: DesktopTheme.getInputDecoration(
                    labelText: 'المخزن',
                    prefixIcon: const Icon(Icons.warehouse),
                  ),
                  items: [
                    const DropdownMenuItem(value: 'all', child: Text('جميع المخازن')),
                    ..._warehouses.map((warehouse) => DropdownMenuItem(
                      value: warehouse.id,
                      child: Text(warehouse.name),
                    )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedWarehouse = value!;
                    });
                  },
                ),
              ),
              const SizedBox(width: DesktopTheme.spacingMedium),

              // Status Filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: DesktopTheme.getInputDecoration(
                    labelText: 'الحالة',
                    prefixIcon: const Icon(Icons.info_outline),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'متاح', child: Text('متاح')),
                    DropdownMenuItem(value: 'مباع', child: Text('مباع')),
                    DropdownMenuItem(value: 'محجوز', child: Text('محجوز')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value!;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildItemsTable() {
    final filteredItems = _getFilteredItems();

    return Container(
      decoration: DesktopTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: const BoxDecoration(
              color: DesktopTheme.backgroundTertiary,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(DesktopTheme.radiusLarge),
                topRight: Radius.circular(DesktopTheme.radiusLarge),
              ),
              border: Border(
                bottom: BorderSide(
                  color: DesktopTheme.borderLight,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.table_chart, color: DesktopTheme.primaryBlue, size: 24),
                const SizedBox(width: DesktopTheme.spacingSmall),
                Text(
                  'جدول الأصناف (${filteredItems.length} صنف)',
                  style: DesktopTheme.titleLarge,
                ),
                const Spacer(),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: _refreshData,
                      tooltip: 'تحديث البيانات',
                    ),
                    IconButton(
                      icon: const Icon(Icons.file_download),
                      onPressed: _exportData,
                      tooltip: 'تصدير البيانات',
                    ),
                    IconButton(
                      icon: const Icon(Icons.print),
                      onPressed: _printData,
                      tooltip: 'طباعة',
                    ),
                    IconButton(
                      icon: const Icon(Icons.bug_report),
                      onPressed: _diagnoseImages,
                      tooltip: 'تشخيص الصور',
                    ),
                    IconButton(
                      icon: const Icon(Icons.security),
                      onPressed: _diagnoseSSL,
                      tooltip: 'تشخيص SSL',
                    ),
                    IconButton(
                      icon: const Icon(Icons.flash_on),
                      onPressed: _testRadicalBypass,
                      tooltip: 'اختبار الحل الجذري',
                    ),
                    IconButton(
                      icon: const Icon(Icons.link),
                      onPressed: _diagnoseImageUrls,
                      tooltip: 'تشخيص روابط الصور',
                    ),
                    IconButton(
                      icon: const Icon(Icons.rocket_launch),
                      onPressed: _testUltimateBypass,
                      tooltip: 'اختبار الحل النهائي',
                    ),
                    IconButton(
                      icon: const Icon(Icons.android),
                      onPressed: _testAndroidStyle,
                      tooltip: 'اختبار النمط الأندرويد',
                    ),
                    IconButton(
                      icon: const Icon(Icons.http),
                      onPressed: _testHttpOnly,
                      tooltip: 'اختبار HTTP فقط',
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Table Content
          if (filteredItems.isEmpty)
            Container(
              padding: const EdgeInsets.all(DesktopTheme.spacingXXLarge),
              child: Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.inventory_2_outlined,
                      size: 64,
                      color: DesktopTheme.textTertiary,
                    ),
                    const SizedBox(height: DesktopTheme.spacingMedium),
                    Text(
                      'لا توجد أصناف تطابق معايير البحث',
                      style: DesktopTheme.titleMedium.copyWith(
                        color: DesktopTheme.textTertiary,
                      ),
                    ),
                    const SizedBox(height: DesktopTheme.spacingMedium),
                    ElevatedButton.icon(
                      onPressed: _clearFilters,
                      icon: const Icon(Icons.clear_all),
                      label: const Text('مسح الفلاتر'),
                      style: DesktopTheme.secondaryButtonStyle,
                    ),
                  ],
                ),
              ),
            )
          else
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 100,
                child: DataTable(
                  headingRowColor: WidgetStateProperty.all(DesktopTheme.backgroundTertiary),
                  headingTextStyle: DesktopTheme.titleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  dataRowColor: WidgetStateProperty.resolveWith((states) {
                    if (states.contains(WidgetState.hovered)) {
                      return DesktopTheme.primaryBlue.withOpacity(0.05);
                    }
                    return DesktopTheme.backgroundSecondary;
                  }),
                  dataTextStyle: DesktopTheme.bodyMedium,
                  columns: const [
                    DataColumn(label: Text('الصورة')),
                    DataColumn(label: Text('الصنف')),
                    DataColumn(label: Text('الماركة')),
                    DataColumn(label: Text('الموديل')),
                    DataColumn(label: Text('اللون')),
                    DataColumn(label: Text('المخزن')),
                    DataColumn(label: Text('الحالة')),
                    DataColumn(label: Text('السعر')),
                    DataColumn(label: Text('الإجراءات')),
                  ],
                  rows: filteredItems.map((item) {
                    final warehouse = _warehouses.firstWhere(
                      (w) => w.id == item.currentWarehouseId,
                      orElse: () => WarehouseModel(
                        id: '',
                        name: 'غير محدد',
                        type: 'unknown',
                        address: '',
                        createdAt: DateTime.now(),
                        updatedAt: DateTime.now(),
                      ),
                    );

                    return DataRow(
                      cells: [
                        DataCell(
                          HttpOnlyItemImageWidget(
                            imageUrl: item.motorFingerprintImageUrl,
                            size: 50,
                            onTap: () => _viewItemImages(item),
                          ),
                        ),
                        DataCell(
                          Container(
                            constraints: const BoxConstraints(maxWidth: 150),
                            child: Text(
                              item.motorFingerprintText,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        DataCell(Text(item.brand)),
                        DataCell(Text(item.model)),
                        DataCell(Text(item.color)),
                        DataCell(Text(warehouse.name)),
                        DataCell(_buildStatusChip(item.status)),
                        DataCell(Text('${item.purchasePrice.toStringAsFixed(0)} ج.م')),
                        DataCell(_buildActionButtons(item)),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    IconData icon;

    switch (status) {
      case 'متاح':
        color = DesktopTheme.statusSuccess;
        icon = Icons.check_circle;
        break;
      case 'مباع':
        color = DesktopTheme.statusError;
        icon = Icons.shopping_cart;
        break;
      case 'محجوز':
        color = DesktopTheme.statusWarning;
        icon = Icons.bookmark;
        break;
      default:
        color = DesktopTheme.textTertiary;
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesktopTheme.spacingSmall,
        vertical: DesktopTheme.spacingXSmall,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: DesktopTheme.spacingXSmall),
          Text(
            status,
            style: DesktopTheme.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ItemModel item) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.visibility, size: 18),
          onPressed: () => _viewItemDetails(item),
          tooltip: 'عرض التفاصيل',
          style: IconButton.styleFrom(
            foregroundColor: DesktopTheme.primaryBlue,
          ),
        ),
        IconButton(
          icon: const Icon(Icons.edit, size: 18),
          onPressed: () => _editItem(item),
          tooltip: 'تعديل',
          style: IconButton.styleFrom(
            foregroundColor: DesktopTheme.accentOrange,
          ),
        ),
        if (item.status == 'متاح')
          IconButton(
            icon: const Icon(Icons.swap_horiz, size: 18),
            onPressed: () => _transferItem(item),
            tooltip: 'نقل',
            style: IconButton.styleFrom(
              foregroundColor: DesktopTheme.accentPurple,
            ),
          ),
      ],
    );
  }

  List<ItemModel> _getFilteredItems() {
    return _items.where((item) {
      // Filter by warehouse
      if (_selectedWarehouse != 'all' && item.currentWarehouseId != _selectedWarehouse) {
        return false;
      }

      // Filter by status
      if (_selectedStatus != 'all' && item.status != _selectedStatus) {
        return false;
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return item.motorFingerprintText.toLowerCase().contains(query) ||
               item.brand.toLowerCase().contains(query) ||
               item.model.toLowerCase().contains(query) ||
               item.color.toLowerCase().contains(query) ||
               item.chassisNumber.toLowerCase().contains(query);
      }

      return true;
    }).toList();
  }

  // Action Methods
  void _filterByStatus(String status) {
    setState(() {
      _selectedStatus = status;
    });
  }

  void _addNewItem() {
    showDialog(
      context: context,
      builder: (context) => _AddItemDialog(
        onItemAdded: () {
          _refreshData();
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _transferItems() {
    // TODO: Open transfer dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة نقل البضائع قيد التطوير')),
    );
  }

  void _refreshData() {
    setState(() {
      _isLoading = true;
    });
    _loadData();
  }

  void _exportData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير بيانات المخزون'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('اختر تنسيق التصدير:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.table_chart, color: Colors.green),
              title: const Text('Excel (.xlsx)'),
              subtitle: const Text('ملف Excel للتحليل والمعالجة'),
              onTap: () {
                Navigator.pop(context);
                _exportToExcel();
              },
            ),
            ListTile(
              leading: const Icon(Icons.description, color: Colors.red),
              title: const Text('PDF'),
              subtitle: const Text('ملف PDF للطباعة والمشاركة'),
              onTap: () {
                Navigator.pop(context);
                _exportToPDF();
              },
            ),
            ListTile(
              leading: const Icon(Icons.code, color: Colors.blue),
              title: const Text('CSV'),
              subtitle: const Text('ملف CSV للاستيراد في برامج أخرى'),
              onTap: () {
                Navigator.pop(context);
                _exportToCSV();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير البيانات إلى Excel...')),
    );
    // TODO: Implement Excel export functionality
  }

  void _exportToPDF() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير البيانات إلى PDF...')),
    );
    // TODO: Implement PDF export functionality
  }

  void _exportToCSV() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير البيانات إلى CSV...')),
    );
    // TODO: Implement CSV export functionality
  }

  void _printData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طباعة تقرير المخزون'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('اختر نوع التقرير للطباعة:'),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.inventory, color: Colors.blue),
              title: const Text('تقرير المخزون الكامل'),
              subtitle: Text('${_items.length} صنف'),
              onTap: () {
                Navigator.pop(context);
                _printInventoryReport();
              },
            ),
            ListTile(
              leading: const Icon(Icons.check_circle, color: Colors.green),
              title: const Text('الأصناف المتاحة فقط'),
              subtitle: Text('${_items.where((item) => item.status.trim() == 'متاح').length} صنف'),
              onTap: () {
                Navigator.pop(context);
                _printAvailableItemsReport();
              },
            ),
            ListTile(
              leading: const Icon(Icons.warehouse, color: Colors.orange),
              title: const Text('تقرير حسب المخزن'),
              subtitle: const Text('تفصيل لكل مخزن'),
              onTap: () {
                Navigator.pop(context);
                _printWarehouseReport();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _printInventoryReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تحضير تقرير المخزون للطباعة...')),
    );
    // TODO: Implement actual printing functionality
  }

  void _printAvailableItemsReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تحضير تقرير الأصناف المتاحة للطباعة...')),
    );
    // TODO: Implement actual printing functionality
  }

  void _printWarehouseReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تحضير تقرير المخازن للطباعة...')),
    );
    // TODO: Implement actual printing functionality
  }

  void _clearFilters() {
    setState(() {
      _selectedWarehouse = 'all';
      _selectedStatus = 'all';
      _searchQuery = '';
      _searchController.clear();
    });
  }

  void _viewItemDetails(ItemModel item) {
    // TODO: Open item details dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الصنف: ${item.motorFingerprintText}')),
    );
  }

  void _editItem(ItemModel item) {
    // TODO: Open edit item dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الصنف: ${item.motorFingerprintText}')),
    );
  }

  void _transferItem(ItemModel item) {
    // TODO: Open transfer item dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('نقل الصنف: ${item.motorFingerprintText}')),
    );
  }

  void _viewItemImages(ItemModel item) {
    final imageUrls = <String>[];
    final imageTitles = <String>[];

    // Add motor fingerprint image
    if (item.motorFingerprintImageUrl.isNotEmpty) {
      imageUrls.add(item.motorFingerprintImageUrl);
      imageTitles.add('صورة بصمة الموتور');
    }

    // Add chassis image
    if (item.chassisImageUrl.isNotEmpty) {
      imageUrls.add(item.chassisImageUrl);
      imageTitles.add('صورة الشاسيه');
    }

    if (imageUrls.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا توجد صور لهذا الصنف')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 800,
          height: 600,
          padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  const Icon(
                    Icons.photo_library,
                    color: DesktopTheme.primaryBlue,
                    size: 24,
                  ),
                  const SizedBox(width: DesktopTheme.spacingSmall),
                  Expanded(
                    child: Text(
                      'صور الصنف: ${item.motorFingerprintText}',
                      style: DesktopTheme.titleLarge,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: DesktopTheme.spacingLarge),

              // Image Gallery
              Expanded(
                child: DesktopImageGallery(
                  imageUrls: imageUrls,
                  imageTitles: imageTitles,
                ),
              ),

              const SizedBox(height: DesktopTheme.spacingLarge),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => _downloadItemImages(item),
                    icon: const Icon(Icons.download),
                    label: const Text('تحميل الصور'),
                  ),
                  const SizedBox(width: DesktopTheme.spacingMedium),
                  ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    label: const Text('إغلاق'),
                    style: DesktopTheme.secondaryButtonStyle,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _downloadItemImages(ItemModel item) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة تحميل الصور قيد التطوير')),
    );
  }

  Future<void> _diagnoseImages() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري تشخيص الصور...'),
          ],
        ),
      ),
    );

    try {
      final diagnostics = ImageDiagnostics();

      // Test network connectivity first
      final networkStatus = await diagnostics.getNetworkStatus();

      // Test a sample of item images
      final sampleItems = _items.take(5).toList();
      final reports = <Map<String, dynamic>>[];

      for (final item in sampleItems) {
        final report = await diagnostics.getItemImageDiagnostics(
          item.motorFingerprintImageUrl,
          item.chassisImageUrl,
        );
        report['itemId'] = item.id;
        report['itemName'] = item.motorFingerprintText;
        reports.add(report);
      }

      Navigator.of(context).pop(); // Close loading dialog

      // Show results
      showDialog(
        context: context,
        builder: (context) => _ImageDiagnosticsDialog(
          networkStatus: networkStatus,
          itemReports: reports,
        ),
      );

    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تشخيص الصور: $e')),
      );
    }
  }

  Future<void> _diagnoseSSL() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري تشخيص SSL والشبكة...'),
          ],
        ),
      ),
    );

    try {
      final sslDiagnostics = SSLDiagnostics();
      final results = await sslDiagnostics.runFullDiagnostics();

      Navigator.of(context).pop(); // Close loading dialog

      // Show results
      showDialog(
        context: context,
        builder: (context) => _SSLDiagnosticsDialog(results: results),
      );

    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تشخيص SSL: $e')),
      );
    }
  }

  Future<void> _testRadicalBypass() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري اختبار الحل الجذري...'),
          ],
        ),
      ),
    );

    try {
      final radicalBypass = RadicalSSLBypass();

      // Test connectivity
      final connectivityResult = await radicalBypass.testConnectivity();

      // Test actual image download
      const testImageUrl = 'https://res.cloudinary.com/demo/image/upload/sample.jpg';
      final imageBytes = await radicalBypass.downloadImage(testImageUrl);

      Navigator.of(context).pop(); // Close loading dialog

      // Show results
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('نتائج اختبار الحل الجذري'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    connectivityResult ? Icons.check_circle : Icons.error,
                    color: connectivityResult ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  Text('اختبار الاتصال: ${connectivityResult ? 'نجح' : 'فشل'}'),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    imageBytes != null ? Icons.check_circle : Icons.error,
                    color: imageBytes != null ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  Text('تحميل الصورة: ${imageBytes != null ? 'نجح (${imageBytes.length} بايت)' : 'فشل'}'),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                connectivityResult && imageBytes != null
                    ? '🎉 الحل الجذري يعمل بنجاح!'
                    : '❌ الحل الجذري لا يعمل',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: connectivityResult && imageBytes != null ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
            if (connectivityResult && imageBytes != null)
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _refreshData(); // Refresh to see images
                },
                child: const Text('تحديث البيانات'),
              ),
          ],
        ),
      );

    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في اختبار الحل الجذري: $e')),
      );
    }
  }

  Future<void> _diagnoseImageUrls() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري تشخيص روابط الصور...'),
          ],
        ),
      ),
    );

    try {
      final urlDiagnostics = <Map<String, dynamic>>[];

      // Analyze first 10 items
      final itemsToAnalyze = _items.take(10).toList();

      for (final item in itemsToAnalyze) {
        final urls = [
          item.motorFingerprintImageUrl,
          item.chassisImageUrl,
        ];

        final itemDiagnostic = {
          'itemId': item.id,
          'itemName': item.motorFingerprintText,
          'urls': urls.map((url) => ImageUrlFixer.getUrlDebugInfo(url)).toList(),
        };

        urlDiagnostics.add(itemDiagnostic);
      }

      Navigator.of(context).pop(); // Close loading dialog

      // Show results
      showDialog(
        context: context,
        builder: (context) => _ImageUrlDiagnosticsDialog(diagnostics: urlDiagnostics),
      );

    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تشخيص روابط الصور: $e')),
      );
    }
  }

  Future<void> _testUltimateBypass() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري اختبار الحل النهائي...'),
          ],
        ),
      ),
    );

    try {
      final ultimateBypass = UltimateSSLBypass();

      // Test connectivity
      final connectivityResult = await ultimateBypass.testConnectivity();

      // Test actual image download with HTTP conversion
      const testImageUrl = 'https://res.cloudinary.com/demo/image/upload/sample.jpg';
      final httpUrl = ultimateBypass.convertToHttp(testImageUrl);
      final imageBytes = await ultimateBypass.downloadImage(testImageUrl);

      Navigator.of(context).pop(); // Close loading dialog

      // Show results
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('نتائج اختبار الحل النهائي'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    connectivityResult ? Icons.check_circle : Icons.error,
                    color: connectivityResult ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  Text('اختبار الاتصال: ${connectivityResult ? 'نجح' : 'فشل'}'),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    imageBytes != null ? Icons.check_circle : Icons.error,
                    color: imageBytes != null ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  Text('تحميل الصورة: ${imageBytes != null ? 'نجح (${imageBytes.length} بايت)' : 'فشل'}'),
                ],
              ),
              const SizedBox(height: 8),
              const Text('تحويل URL:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('الأصلي: $testImageUrl', style: const TextStyle(fontSize: 12)),
              Text('HTTP: $httpUrl', style: const TextStyle(fontSize: 12, color: Colors.blue)),
              const SizedBox(height: 16),
              Text(
                connectivityResult && imageBytes != null
                    ? '🎉 الحل النهائي يعمل بنجاح!'
                    : '❌ الحل النهائي لا يعمل',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: connectivityResult && imageBytes != null ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
            if (connectivityResult && imageBytes != null)
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _refreshData(); // Refresh to see images
                },
                child: const Text('تحديث البيانات'),
              ),
          ],
        ),
      );

    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في اختبار الحل النهائي: $e')),
      );
    }
  }

  Future<void> _testAndroidStyle() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار النمط الأندرويد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختبار تحميل الصور بنفس طريقة النسخة الأندرويد:'),
            const SizedBox(height: 16),

            // Test with a sample Cloudinary image
            Container(
              width: 200,
              height: 150,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const AndroidStyleImageWidget(
                imageUrl: 'https://res.cloudinary.com/demo/image/upload/sample.jpg',
                fit: BoxFit.cover,
              ),
            ),

            const SizedBox(height: 16),
            const Text(
              'إذا ظهرت الصورة أعلاه، فإن النمط الأندرويد يعمل بنجاح!',
              style: TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _refreshData(); // Refresh to see images
            },
            child: const Text('تحديث البيانات'),
          ),
        ],
      ),
    );
  }

  Future<void> _testHttpOnly() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختبار HTTP فقط'),
        content: const SizedBox(
          width: 400,
          height: 300,
          child: HttpOnlyTestWidget(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _refreshData(); // Refresh to see images
            },
            child: const Text('تحديث البيانات'),
          ),
        ],
      ),
    );
  }
}

/// Dialog for adding new item with image support
class _AddItemDialog extends StatefulWidget {
  final VoidCallback onItemAdded;

  const _AddItemDialog({required this.onItemAdded});

  @override
  State<_AddItemDialog> createState() => _AddItemDialogState();
}

class _AddItemDialogState extends State<_AddItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _brandController = TextEditingController();
  final _modelController = TextEditingController();
  final _colorController = TextEditingController();
  final _chassisController = TextEditingController();
  final _priceController = TextEditingController();

  final DesktopImageService _imageService = DesktopImageService();

  Uint8List? _motorImage;
  Uint8List? _chassisImage;
  String _selectedWarehouse = '';
  bool _isLoading = false;

  List<WarehouseModel> _warehouses = [];

  @override
  void initState() {
    super.initState();
    _loadWarehouses();
  }

  @override
  void dispose() {
    _brandController.dispose();
    _modelController.dispose();
    _colorController.dispose();
    _chassisController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  Future<void> _loadWarehouses() async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);
      final warehouses = await dataService.getWarehouses();
      setState(() {
        _warehouses = warehouses;
        if (warehouses.isNotEmpty) {
          _selectedWarehouse = warehouses.first.id;
        }
      });
    } catch (e) {
      // Handle error
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 800,
        padding: const EdgeInsets.all(DesktopTheme.spacingXLarge),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  const Icon(
                    Icons.add_box,
                    color: DesktopTheme.primaryBlue,
                    size: 24,
                  ),
                  const SizedBox(width: DesktopTheme.spacingSmall),
                  const Text(
                    'إضافة صنف جديد',
                    style: DesktopTheme.headingMedium,
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: DesktopTheme.spacingXLarge),

              // Form fields
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _brandController,
                      decoration: DesktopTheme.getInputDecoration(
                        labelText: 'الماركة',
                        prefixIcon: const Icon(Icons.branding_watermark),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الماركة';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: DesktopTheme.spacingMedium),
                  Expanded(
                    child: TextFormField(
                      controller: _modelController,
                      decoration: DesktopTheme.getInputDecoration(
                        labelText: 'الموديل',
                        prefixIcon: const Icon(Icons.model_training),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الموديل';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: DesktopTheme.spacingLarge),

              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _colorController,
                      decoration: DesktopTheme.getInputDecoration(
                        labelText: 'اللون',
                        prefixIcon: const Icon(Icons.palette),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اللون';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: DesktopTheme.spacingMedium),
                  Expanded(
                    child: TextFormField(
                      controller: _priceController,
                      decoration: DesktopTheme.getInputDecoration(
                        labelText: 'السعر',
                        prefixIcon: const Icon(Icons.attach_money),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال السعر';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال رقم صحيح';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: DesktopTheme.spacingLarge),

              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _chassisController,
                      decoration: DesktopTheme.getInputDecoration(
                        labelText: 'رقم الشاسيه',
                        prefixIcon: const Icon(Icons.confirmation_number),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال رقم الشاسيه';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: DesktopTheme.spacingMedium),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedWarehouse.isNotEmpty ? _selectedWarehouse : null,
                      decoration: DesktopTheme.getInputDecoration(
                        labelText: 'المخزن',
                        prefixIcon: const Icon(Icons.warehouse),
                      ),
                      items: _warehouses.map((warehouse) => DropdownMenuItem(
                        value: warehouse.id,
                        child: Text(warehouse.name),
                      )).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedWarehouse = value!;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى اختيار المخزن';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: DesktopTheme.spacingXLarge),

              // Image selection
              const Text(
                'الصور',
                style: DesktopTheme.titleMedium,
              ),
              const SizedBox(height: DesktopTheme.spacingMedium),

              Row(
                children: [
                  // Motor image
                  Expanded(
                    child: _buildImageSelector(
                      title: 'صورة بصمة الموتور',
                      image: _motorImage,
                      onTap: () => _pickMotorImage(),
                    ),
                  ),
                  const SizedBox(width: DesktopTheme.spacingMedium),
                  // Chassis image
                  Expanded(
                    child: _buildImageSelector(
                      title: 'صورة الشاسيه',
                      image: _chassisImage,
                      onTap: () => _pickChassisImage(),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: DesktopTheme.spacingXXLarge),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: DesktopTheme.spacingMedium),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _saveItem,
                    icon: _isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.save),
                    label: Text(_isLoading ? 'جاري الحفظ...' : 'حفظ'),
                    style: DesktopTheme.primaryButtonStyle,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSelector({
    required String title,
    required Uint8List? image,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: DesktopTheme.bodyMedium,
        ),
        const SizedBox(height: DesktopTheme.spacingSmall),
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
          child: Container(
            width: double.infinity,
            height: 120,
            decoration: BoxDecoration(
              color: DesktopTheme.backgroundTertiary,
              borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
              border: Border.all(
                color: DesktopTheme.borderMedium,
                style: BorderStyle.solid,
              ),
            ),
            child: image != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium - 1),
                    child: Image.memory(
                      image,
                      fit: BoxFit.cover,
                    ),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.add_photo_alternate_outlined,
                        size: 32,
                        color: DesktopTheme.textTertiary,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'انقر لاختيار صورة',
                        style: DesktopTheme.bodySmall.copyWith(
                          color: DesktopTheme.textTertiary,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  Future<void> _pickMotorImage() async {
    try {
      final image = await _imageService.pickImageFromFiles(
        dialogTitle: 'اختيار صورة بصمة الموتور',
      );
      if (image != null) {
        setState(() {
          _motorImage = image;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في اختيار الصورة: $e')),
      );
    }
  }

  Future<void> _pickChassisImage() async {
    try {
      final image = await _imageService.pickImageFromFiles(
        dialogTitle: 'اختيار صورة الشاسيه',
      );
      if (image != null) {
        setState(() {
          _chassisImage = image;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في اختيار الصورة: $e')),
      );
    }
  }

  Future<void> _saveItem() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_motorImage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار صورة بصمة الموتور')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      // Upload motor image
      final motorImageUrl = await _imageService.uploadImage(
        _motorImage!,
        'motor_${DateTime.now().millisecondsSinceEpoch}.jpg',
        folder: 'motor_fingerprints',
      );

      if (motorImageUrl == null) {
        throw 'فشل في رفع صورة بصمة الموتور';
      }

      // Upload chassis image if selected
      String? chassisImageUrl;
      if (_chassisImage != null) {
        chassisImageUrl = await _imageService.uploadImage(
          _chassisImage!,
          'chassis_${DateTime.now().millisecondsSinceEpoch}.jpg',
          folder: 'chassis_images',
        );
      }

      // Create item model
      final item = ItemModel(
        id: 'item_${DateTime.now().millisecondsSinceEpoch}',
        type: 'motorcycle', // Add required type field
        brand: _brandController.text.trim(),
        model: _modelController.text.trim(),
        color: _colorController.text.trim(),
        chassisNumber: _chassisController.text.trim(),
        motorFingerprintText: '${_brandController.text} ${_modelController.text} ${_colorController.text}',
        motorFingerprintImageUrl: motorImageUrl,
        chassisImageUrl: chassisImageUrl ?? '',
        purchasePrice: double.parse(_priceController.text),
        suggestedSellingPrice: double.parse(_priceController.text) * 1.2, // 20% markup
        currentWarehouseId: _selectedWarehouse,
        status: 'متاح',
        countryOfOrigin: 'مصر', // Add default country
        yearOfManufacture: DateTime.now().year, // Add default year
        createdBy: 'admin_001',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save to database
      await dataService.createItem(item);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إضافة الصنف بنجاح')),
      );

      widget.onItemAdded();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في حفظ الصنف: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

/// Dialog for displaying image diagnostics results
class _ImageDiagnosticsDialog extends StatelessWidget {
  final Map<String, dynamic> networkStatus;
  final List<Map<String, dynamic>> itemReports;

  const _ImageDiagnosticsDialog({
    required this.networkStatus,
    required this.itemReports,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 800,
        height: 600,
        padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.bug_report,
                  color: DesktopTheme.primaryBlue,
                  size: 24,
                ),
                const SizedBox(width: DesktopTheme.spacingSmall),
                const Text(
                  'تشخيص الصور',
                  style: DesktopTheme.titleLarge,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: DesktopTheme.spacingLarge),

            // Network Status
            Container(
              padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
              decoration: BoxDecoration(
                color: networkStatus['isConnected']
                    ? DesktopTheme.statusSuccess.withOpacity(0.1)
                    : DesktopTheme.statusError.withOpacity(0.1),
                borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
                border: Border.all(
                  color: networkStatus['isConnected']
                      ? DesktopTheme.statusSuccess
                      : DesktopTheme.statusError,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        networkStatus['isConnected']
                            ? Icons.wifi
                            : Icons.wifi_off,
                        color: networkStatus['isConnected']
                            ? DesktopTheme.statusSuccess
                            : DesktopTheme.statusError,
                      ),
                      const SizedBox(width: DesktopTheme.spacingSmall),
                      const Text(
                        'حالة الشبكة',
                        style: DesktopTheme.titleMedium,
                      ),
                    ],
                  ),
                  const SizedBox(height: DesktopTheme.spacingSmall),
                  Text('الاتصال بالإنترنت: ${networkStatus['canReachGoogle'] ? '✅ متصل' : '❌ غير متصل'}'),
                  Text('الاتصال بـ Cloudinary: ${networkStatus['canReachCloudinary'] ? '✅ متصل' : '❌ غير متصل'}'),
                ],
              ),
            ),

            const SizedBox(height: DesktopTheme.spacingLarge),

            // Items Reports
            Text(
              'تقرير الأصناف (عينة من ${itemReports.length} أصناف)',
              style: DesktopTheme.titleMedium,
            ),

            const SizedBox(height: DesktopTheme.spacingMedium),

            Expanded(
              child: ListView.builder(
                itemCount: itemReports.length,
                itemBuilder: (context, index) {
                  final report = itemReports[index];
                  final status = report['overallStatus'];

                  Color statusColor;
                  IconData statusIcon;

                  switch (status) {
                    case 'good':
                      statusColor = DesktopTheme.statusSuccess;
                      statusIcon = Icons.check_circle;
                      break;
                    case 'partial':
                      statusColor = DesktopTheme.statusWarning;
                      statusIcon = Icons.warning;
                      break;
                    case 'bad':
                      statusColor = DesktopTheme.statusError;
                      statusIcon = Icons.error;
                      break;
                    default:
                      statusColor = DesktopTheme.textTertiary;
                      statusIcon = Icons.help;
                  }

                  return Card(
                    margin: const EdgeInsets.only(bottom: DesktopTheme.spacingMedium),
                    child: Padding(
                      padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(statusIcon, color: statusColor, size: 20),
                              const SizedBox(width: DesktopTheme.spacingSmall),
                              Expanded(
                                child: Text(
                                  report['itemName'] ?? 'صنف غير محدد',
                                  style: DesktopTheme.titleSmall,
                                ),
                              ),
                              Text(
                                '${report['accessibleImages']}/${report['totalImages']} صور',
                                style: DesktopTheme.bodySmall.copyWith(
                                  color: statusColor,
                                ),
                              ),
                            ],
                          ),

                          if (report['recommendations'] != null &&
                              report['recommendations'].isNotEmpty) ...[
                            const SizedBox(height: DesktopTheme.spacingSmall),
                            ...report['recommendations'].map<Widget>((rec) =>
                              Padding(
                                padding: const EdgeInsets.only(left: 24),
                                child: Text(
                                  '• $rec',
                                  style: DesktopTheme.bodySmall.copyWith(
                                    color: DesktopTheme.textSecondary,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _exportDiagnostics(),
                  icon: const Icon(Icons.file_download),
                  label: const Text('تصدير التقرير'),
                ),
                const SizedBox(width: DesktopTheme.spacingMedium),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إغلاق'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _exportDiagnostics() {
    // TODO: Implement export functionality
  }
}

/// Dialog for displaying SSL diagnostics results
class _SSLDiagnosticsDialog extends StatelessWidget {
  final Map<String, dynamic> results;

  const _SSLDiagnosticsDialog({required this.results});

  @override
  Widget build(BuildContext context) {
    final tests = results['tests'] as Map<String, dynamic>;

    return Dialog(
      child: Container(
        width: 900,
        height: 700,
        padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.security,
                  color: DesktopTheme.primaryBlue,
                  size: 24,
                ),
                const SizedBox(width: DesktopTheme.spacingSmall),
                const Text(
                  'تشخيص SSL والشبكة',
                  style: DesktopTheme.titleLarge,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: DesktopTheme.spacingLarge),

            // Summary
            Container(
              padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
              decoration: BoxDecoration(
                color: DesktopTheme.backgroundTertiary,
                borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
                border: Border.all(color: DesktopTheme.borderLight),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص التشخيص',
                    style: DesktopTheme.titleMedium,
                  ),
                  const SizedBox(height: DesktopTheme.spacingSmall),
                  Text('الوقت: ${results['timestamp']}'),
                  Text('النظام: ${results['platform']}'),
                  Text('عدد الاختبارات: ${tests.length}'),
                ],
              ),
            ),

            const SizedBox(height: DesktopTheme.spacingLarge),

            // Test Results
            const Text(
              'نتائج الاختبارات',
              style: DesktopTheme.titleMedium,
            ),

            const SizedBox(height: DesktopTheme.spacingMedium),

            Expanded(
              child: ListView.builder(
                itemCount: tests.length,
                itemBuilder: (context, index) {
                  final testName = tests.keys.elementAt(index);
                  final test = tests[testName] as Map<String, dynamic>;
                  final status = test['status'] as String;

                  Color statusColor;
                  IconData statusIcon;

                  switch (status) {
                    case 'success':
                      statusColor = DesktopTheme.statusSuccess;
                      statusIcon = Icons.check_circle;
                      break;
                    case 'partial':
                      statusColor = DesktopTheme.statusWarning;
                      statusIcon = Icons.warning;
                      break;
                    case 'failed':
                      statusColor = DesktopTheme.statusError;
                      statusIcon = Icons.error;
                      break;
                    default:
                      statusColor = DesktopTheme.textTertiary;
                      statusIcon = Icons.help;
                  }

                  return Card(
                    margin: const EdgeInsets.only(bottom: DesktopTheme.spacingMedium),
                    child: ExpansionTile(
                      leading: Icon(statusIcon, color: statusColor, size: 20),
                      title: Text(
                        test['name'] ?? testName,
                        style: DesktopTheme.titleSmall,
                      ),
                      subtitle: Text(
                        'الحالة: ${status.toUpperCase()}',
                        style: DesktopTheme.bodySmall.copyWith(color: statusColor),
                      ),
                      children: [
                        if (test['details'] != null)
                          Padding(
                            padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
                            child: _buildTestDetails(test['details'] as Map<String, dynamic>),
                          ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _copyResults(),
                  icon: const Icon(Icons.copy),
                  label: const Text('نسخ النتائج'),
                ),
                const SizedBox(width: DesktopTheme.spacingMedium),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إغلاق'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestDetails(Map<String, dynamic> details) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: details.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 120,
                child: Text(
                  '${entry.key}:',
                  style: DesktopTheme.bodySmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  entry.value.toString(),
                  style: DesktopTheme.bodySmall,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  void _copyResults() {
    // TODO: Implement copy to clipboard
  }
}

/// Dialog for displaying image URL diagnostics
class _ImageUrlDiagnosticsDialog extends StatelessWidget {
  final List<Map<String, dynamic>> diagnostics;

  const _ImageUrlDiagnosticsDialog({required this.diagnostics});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 1000,
        height: 700,
        padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.link,
                  color: DesktopTheme.primaryBlue,
                  size: 24,
                ),
                const SizedBox(width: DesktopTheme.spacingSmall),
                const Text(
                  'تشخيص روابط الصور',
                  style: DesktopTheme.titleLarge,
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: DesktopTheme.spacingLarge),

            // Summary
            Container(
              padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
              decoration: BoxDecoration(
                color: DesktopTheme.backgroundTertiary,
                borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
                border: Border.all(color: DesktopTheme.borderLight),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص التشخيص',
                    style: DesktopTheme.titleMedium,
                  ),
                  const SizedBox(height: DesktopTheme.spacingSmall),
                  Text('عدد الأصناف المفحوصة: ${diagnostics.length}'),
                  Text('إجمالي الروابط: ${diagnostics.fold<int>(0, (sum, item) => sum + (item['urls'] as List).length)}'),
                ],
              ),
            ),

            const SizedBox(height: DesktopTheme.spacingLarge),

            // Items List
            const Text(
              'تفاصيل الأصناف',
              style: DesktopTheme.titleMedium,
            ),

            const SizedBox(height: DesktopTheme.spacingMedium),

            Expanded(
              child: ListView.builder(
                itemCount: diagnostics.length,
                itemBuilder: (context, index) {
                  final item = diagnostics[index];
                  final urls = item['urls'] as List<Map<String, dynamic>>;

                  return Card(
                    margin: const EdgeInsets.only(bottom: DesktopTheme.spacingMedium),
                    child: ExpansionTile(
                      title: Text(
                        item['itemName'] ?? 'صنف غير محدد',
                        style: DesktopTheme.titleSmall,
                      ),
                      subtitle: Text(
                        'ID: ${item['itemId']} - ${urls.length} روابط',
                        style: DesktopTheme.bodySmall,
                      ),
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
                          child: Column(
                            children: urls.asMap().entries.map((entry) {
                              final urlIndex = entry.key;
                              final urlInfo = entry.value;
                              final urlType = urlIndex == 0 ? 'بصمة الموتور' : 'الشاسيه';

                              return Container(
                                margin: const EdgeInsets.only(bottom: DesktopTheme.spacingSmall),
                                padding: const EdgeInsets.all(DesktopTheme.spacingSmall),
                                decoration: BoxDecoration(
                                  color: urlInfo['isValid'] ? Colors.green.shade50 : Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall),
                                  border: Border.all(
                                    color: urlInfo['isValid'] ? DesktopTheme.statusSuccess : DesktopTheme.statusError,
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          urlInfo['isValid'] ? Icons.check_circle : Icons.error,
                                          color: urlInfo['isValid'] ? DesktopTheme.statusSuccess : DesktopTheme.statusError,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          urlType,
                                          style: DesktopTheme.bodyMedium.copyWith(fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    if (urlInfo['isEmpty'])
                                      Text('رابط فارغ', style: DesktopTheme.bodySmall.copyWith(color: DesktopTheme.statusError))
                                    else ...[
                                      Text('الرابط الأصلي:', style: DesktopTheme.bodySmall.copyWith(fontWeight: FontWeight.bold)),
                                      Text(urlInfo['original'], style: DesktopTheme.bodySmall),
                                      if (urlInfo['needsFixing']) ...[
                                        const SizedBox(height: 4),
                                        Text('الرابط المُصحح:', style: DesktopTheme.bodySmall.copyWith(fontWeight: FontWeight.bold)),
                                        Text(urlInfo['fixed'], style: DesktopTheme.bodySmall.copyWith(color: DesktopTheme.primaryBlue)),
                                      ],
                                    ],
                                  ],
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إغلاق'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
