import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../core/network/custom_http_client.dart';

/// SSL and network diagnostics utility
class SSLDiagnostics {
  static final SSLDiagnostics _instance = SSLDiagnostics._internal();
  factory SSLDiagnostics() => _instance;
  SSLDiagnostics._internal();

  /// Run comprehensive SSL and network diagnostics
  Future<Map<String, dynamic>> runFullDiagnostics() async {
    if (kDebugMode) {
      print('🔍 Starting comprehensive SSL diagnostics...');
    }

    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'platform': Platform.operatingSystem,
      'tests': <String, dynamic>{},
    };

    // Test 1: Basic network connectivity
    results['tests']['basic_connectivity'] = await _testBasicConnectivity();

    // Test 2: Standard HTTP client
    results['tests']['standard_http'] = await _testStandardHttpClient();

    // Test 3: Custom HTTP client
    results['tests']['custom_http'] = await _testCustomHttpClient();

    // Test 4: Cloudinary specific tests
    results['tests']['cloudinary'] = await _testCloudinaryConnectivity();

    // Test 5: SSL certificate details
    results['tests']['ssl_details'] = await _getSSLCertificateDetails();

    // Test 6: System SSL configuration
    results['tests']['system_ssl'] = await _checkSystemSSLConfiguration();

    if (kDebugMode) {
      print('🔍 SSL diagnostics completed');
      _printDiagnosticsReport(results);
    }

    return results;
  }

  Future<Map<String, dynamic>> _testBasicConnectivity() async {
    final test = <String, dynamic>{
      'name': 'Basic Network Connectivity',
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    try {
      // Test Google (should always work)
      final response = await http.get(Uri.parse('https://www.google.com'))
          .timeout(const Duration(seconds: 10));
      
      test['status'] = response.statusCode == 200 ? 'success' : 'failed';
      test['details']['google_status'] = response.statusCode;
      test['details']['google_headers'] = response.headers;
    } catch (e) {
      test['status'] = 'failed';
      test['details']['error'] = e.toString();
    }

    return test;
  }

  Future<Map<String, dynamic>> _testStandardHttpClient() async {
    final test = <String, dynamic>{
      'name': 'Standard HTTP Client (Cloudinary)',
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    try {
      final response = await http.get(
        Uri.parse('https://res.cloudinary.com/demo/image/upload/sample.jpg'),
        headers: {'User-Agent': 'El-Farhan-Desktop/1.0'},
      ).timeout(const Duration(seconds: 15));
      
      test['status'] = response.statusCode == 200 ? 'success' : 'failed';
      test['details']['status_code'] = response.statusCode;
      test['details']['content_length'] = response.contentLength;
      test['details']['content_type'] = response.headers['content-type'];
    } catch (e) {
      test['status'] = 'failed';
      test['details']['error'] = e.toString();
      test['details']['error_type'] = e.runtimeType.toString();
    }

    return test;
  }

  Future<Map<String, dynamic>> _testCustomHttpClient() async {
    final test = <String, dynamic>{
      'name': 'Custom HTTP Client (SSL Bypass)',
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    try {
      final customClient = CustomHttpClient();
      final success = await customClient.testCloudinaryConnection();
      
      test['status'] = success ? 'success' : 'failed';
      test['details']['connection_test'] = success;
    } catch (e) {
      test['status'] = 'failed';
      test['details']['error'] = e.toString();
    }

    return test;
  }

  Future<Map<String, dynamic>> _testCloudinaryConnectivity() async {
    final test = <String, dynamic>{
      'name': 'Cloudinary Specific Tests',
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    final cloudinaryUrls = [
      'https://res.cloudinary.com/demo/image/upload/sample.jpg',
      'https://res.cloudinary.com/dzh4fpnnw/image/upload/v1752011839/motor_fingerprints/motor_fingerprint_1752011837121.jpg',
      'https://api.cloudinary.com/v1_1/demo/image/upload',
    ];

    final results = <String, dynamic>{};
    int successCount = 0;

    for (final url in cloudinaryUrls) {
      try {
        final response = await http.head(Uri.parse(url))
            .timeout(const Duration(seconds: 10));
        
        final urlKey = Uri.parse(url).host;
        results[urlKey] = {
          'status_code': response.statusCode,
          'success': response.statusCode == 200 || response.statusCode == 405, // 405 is OK for API endpoint
        };
        
        if (response.statusCode == 200 || response.statusCode == 405) {
          successCount++;
        }
      } catch (e) {
        final urlKey = Uri.parse(url).host;
        results[urlKey] = {
          'error': e.toString(),
          'success': false,
        };
      }
    }

    test['status'] = successCount > 0 ? 'partial' : 'failed';
    if (successCount == cloudinaryUrls.length) {
      test['status'] = 'success';
    }
    
    test['details']['url_tests'] = results;
    test['details']['success_count'] = successCount;
    test['details']['total_count'] = cloudinaryUrls.length;

    return test;
  }

  Future<Map<String, dynamic>> _getSSLCertificateDetails() async {
    final test = <String, dynamic>{
      'name': 'SSL Certificate Details',
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    try {
      final socket = await SecureSocket.connect(
        'res.cloudinary.com',
        443,
        timeout: const Duration(seconds: 10),
      );

      final cert = socket.peerCertificate;
      if (cert != null) {
        test['status'] = 'success';
        test['details']['subject'] = cert.subject;
        test['details']['issuer'] = cert.issuer;
        test['details']['start_validity'] = cert.startValidity.toIso8601String();
        test['details']['end_validity'] = cert.endValidity.toIso8601String();
        test['details']['is_valid'] = DateTime.now().isBefore(cert.endValidity) && 
                                     DateTime.now().isAfter(cert.startValidity);
      } else {
        test['status'] = 'failed';
        test['details']['error'] = 'No certificate found';
      }

      await socket.close();
    } catch (e) {
      test['status'] = 'failed';
      test['details']['error'] = e.toString();
    }

    return test;
  }

  Future<Map<String, dynamic>> _checkSystemSSLConfiguration() async {
    final test = <String, dynamic>{
      'name': 'System SSL Configuration',
      'status': 'unknown',
      'details': <String, dynamic>{},
    };

    try {
      test['details']['platform'] = Platform.operatingSystem;
      test['details']['platform_version'] = Platform.operatingSystemVersion;
      test['details']['dart_version'] = Platform.version;
      
      // Check if running in debug mode
      test['details']['debug_mode'] = kDebugMode;
      
      // Check environment variables
      test['details']['environment'] = {
        'FLUTTER_ROOT': Platform.environment['FLUTTER_ROOT'],
        'PATH': Platform.environment['PATH']?.split(Platform.pathSeparator).take(5).toList(),
      };

      test['status'] = 'success';
    } catch (e) {
      test['status'] = 'failed';
      test['details']['error'] = e.toString();
    }

    return test;
  }

  void _printDiagnosticsReport(Map<String, dynamic> results) {
    print('\n🔍 === SSL DIAGNOSTICS REPORT ===');
    print('📅 Timestamp: ${results['timestamp']}');
    print('💻 Platform: ${results['platform']}');
    print('');

    final tests = results['tests'] as Map<String, dynamic>;
    for (final testName in tests.keys) {
      final test = tests[testName] as Map<String, dynamic>;
      final status = test['status'] as String;
      final emoji = status == 'success' ? '✅' : status == 'partial' ? '⚠️' : '❌';
      
      print('$emoji ${test['name']}: ${status.toUpperCase()}');
      
      if (test['details'] != null) {
        final details = test['details'] as Map<String, dynamic>;
        for (final key in details.keys) {
          print('   • $key: ${details[key]}');
        }
      }
      print('');
    }
    
    print('🔍 === END REPORT ===\n');
  }

  /// Quick SSL test for UI
  Future<bool> quickSSLTest() async {
    try {
      final customClient = CustomHttpClient();
      return await customClient.testCloudinaryConnection();
    } catch (e) {
      return false;
    }
  }
}
