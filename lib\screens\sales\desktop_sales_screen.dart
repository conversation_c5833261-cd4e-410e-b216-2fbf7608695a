import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/desktop_theme.dart';
import '../../widgets/desktop/desktop_dashboard_cards.dart';
import '../../widgets/desktop/desktop_image_widget.dart';
import '../../widgets/radical_image_widget.dart';
import '../../widgets/ultimate_image_widget.dart';
import '../../widgets/android_style_image_widget.dart';
import '../../widgets/http_only_image_widget.dart';
import '../../services/data_service.dart';
import '../../models/invoice_model.dart';
import '../../models/user_model.dart';
import '../../models/item_model.dart';

class DesktopSalesScreen extends StatefulWidget {
  const DesktopSalesScreen({super.key});

  @override
  State<DesktopSalesScreen> createState() => _DesktopSalesScreenState();
}

class _DesktopSalesScreenState extends State<DesktopSalesScreen> {
  bool _isLoading = true;
  List<InvoiceModel> _invoices = [];
  List<UserModel> _customers = [];
  String _selectedPeriod = 'today';
  String _selectedStatus = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      final invoices = await dataService.getInvoices();
      final users = await dataService.getUsers();
      final customers = users.where((user) => user.role == 'customer').toList();

      setState(() {
        _invoices = invoices;
        _customers = customers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          _buildHeaderSection(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // Sales Statistics
          _buildSalesStatistics(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // Quick Actions
          _buildQuickActions(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // Filters and Search
          _buildFiltersSection(),

          const SizedBox(height: DesktopTheme.spacingLarge),

          // Invoices Table
          _buildInvoicesTable(),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(DesktopTheme.spacingXLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DesktopTheme.accentGreen,
            DesktopTheme.accentGreen.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(DesktopTheme.radiusXLarge),
        boxShadow: DesktopTheme.elevatedShadow,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة المبيعات',
                  style: DesktopTheme.headingLarge.copyWith(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: DesktopTheme.spacingSmall),
                Text(
                  'إدارة شاملة للفواتير والمبيعات والعملاء',
                  style: DesktopTheme.titleMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: DesktopTheme.spacingMedium),
                Row(
                  children: [
                    _buildQuickStat('إجمالي الفواتير', _invoices.length.toString(), Icons.receipt_long),
                    const SizedBox(width: DesktopTheme.spacingXLarge),
                    _buildQuickStat('العملاء', _customers.length.toString(), Icons.people),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesktopTheme.radiusLarge),
            ),
            child: const Icon(
              Icons.point_of_sale_outlined,
              size: 64,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 20),
        ),
        const SizedBox(width: DesktopTheme.spacingSmall),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: DesktopTheme.titleLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: DesktopTheme.bodySmall.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSalesStatistics() {
    final todayInvoices = _getInvoicesByPeriod('today');
    final weekInvoices = _getInvoicesByPeriod('week');
    final monthInvoices = _getInvoicesByPeriod('month');
    final totalRevenue = _invoices.fold<double>(0, (sum, invoice) => sum + invoice.sellingPrice);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات المبيعات',
          style: DesktopTheme.headingMedium,
        ),
        const SizedBox(height: DesktopTheme.spacingLarge),
        DesktopDashboardGrid(
          crossAxisCount: 4,
          childAspectRatio: 1.3,
          children: [
            DesktopDashboardCard(
              title: 'مبيعات اليوم',
              value: todayInvoices.length.toString(),
              subtitle: 'فاتورة',
              icon: Icons.today_outlined,
              iconColor: DesktopTheme.statusSuccess,
              trend: '+12%',
              isPositiveTrend: true,
              onTap: () => _filterByPeriod('today'),
            ),
            DesktopDashboardCard(
              title: 'مبيعات الأسبوع',
              value: weekInvoices.length.toString(),
              subtitle: 'فاتورة',
              icon: Icons.date_range_outlined,
              iconColor: DesktopTheme.primaryBlue,
              trend: '+8%',
              isPositiveTrend: true,
              onTap: () => _filterByPeriod('week'),
            ),
            DesktopDashboardCard(
              title: 'مبيعات الشهر',
              value: monthInvoices.length.toString(),
              subtitle: 'فاتورة',
              icon: Icons.calendar_month_outlined,
              iconColor: DesktopTheme.accentOrange,
              trend: '+15%',
              isPositiveTrend: true,
              onTap: () => _filterByPeriod('month'),
            ),
            DesktopDashboardCard(
              title: 'إجمالي الإيرادات',
              value: '${(totalRevenue / 1000).toStringAsFixed(1)}ك',
              subtitle: 'جنيه مصري',
              icon: Icons.account_balance_wallet_outlined,
              iconColor: DesktopTheme.accentPurple,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: DesktopTheme.headingMedium,
        ),
        const SizedBox(height: DesktopTheme.spacingLarge),
        DesktopDashboardGrid(
          crossAxisCount: 4,
          childAspectRatio: 1.2,
          children: [
            DesktopQuickActionCard(
              title: 'إنشاء فاتورة جديدة',
              description: 'إنشاء فاتورة مبيعات جديدة',
              icon: Icons.add_shopping_cart_outlined,
              iconColor: DesktopTheme.accentGreen,
              onTap: _createNewInvoice,
            ),
            DesktopQuickActionCard(
              title: 'إدارة العملاء',
              description: 'عرض وإدارة بيانات العملاء',
              icon: Icons.people_outlined,
              iconColor: DesktopTheme.primaryBlue,
              onTap: _manageCustomers,
            ),
            DesktopQuickActionCard(
              title: 'تقارير المبيعات',
              description: 'عرض تقارير مفصلة للمبيعات',
              icon: Icons.analytics_outlined,
              iconColor: DesktopTheme.accentOrange,
              onTap: _viewSalesReports,
            ),
            DesktopQuickActionCard(
              title: 'الفواتير المعلقة',
              description: 'متابعة الفواتير غير المدفوعة',
              icon: Icons.pending_actions_outlined,
              iconColor: DesktopTheme.accentRed,
              onTap: _viewPendingInvoices,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
      decoration: DesktopTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, color: DesktopTheme.primaryBlue, size: 24),
              const SizedBox(width: DesktopTheme.spacingSmall),
              const Text(
                'فلاتر البحث والعرض',
                style: DesktopTheme.titleLarge,
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _createNewInvoice,
                icon: const Icon(Icons.add, size: 20),
                label: const Text('فاتورة جديدة'),
                style: DesktopTheme.primaryButtonStyle,
              ),
            ],
          ),
          const SizedBox(height: DesktopTheme.spacingLarge),
          Row(
            children: [
              // Search Field
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _searchController,
                  decoration: DesktopTheme.getInputDecoration(
                    labelText: 'البحث في الفواتير...',
                    prefixIcon: const Icon(Icons.search),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              const SizedBox(width: DesktopTheme.spacingMedium),

              // Period Filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPeriod,
                  decoration: DesktopTheme.getInputDecoration(
                    labelText: 'الفترة الزمنية',
                    prefixIcon: const Icon(Icons.date_range),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الفترات')),
                    DropdownMenuItem(value: 'today', child: Text('اليوم')),
                    DropdownMenuItem(value: 'week', child: Text('هذا الأسبوع')),
                    DropdownMenuItem(value: 'month', child: Text('هذا الشهر')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedPeriod = value!;
                    });
                  },
                ),
              ),
              const SizedBox(width: DesktopTheme.spacingMedium),

              // Status Filter
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: DesktopTheme.getInputDecoration(
                    labelText: 'حالة الفاتورة',
                    prefixIcon: const Icon(Icons.info_outline),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'paid', child: Text('مدفوعة')),
                    DropdownMenuItem(value: 'pending', child: Text('معلقة')),
                    DropdownMenuItem(value: 'cancelled', child: Text('ملغية')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedStatus = value!;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInvoicesTable() {
    final filteredInvoices = _getFilteredInvoices();

    return Container(
      decoration: DesktopTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: const BoxDecoration(
              color: DesktopTheme.backgroundTertiary,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(DesktopTheme.radiusLarge),
                topRight: Radius.circular(DesktopTheme.radiusLarge),
              ),
              border: Border(
                bottom: BorderSide(
                  color: DesktopTheme.borderLight,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.receipt_long, color: DesktopTheme.primaryBlue, size: 24),
                const SizedBox(width: DesktopTheme.spacingSmall),
                Text(
                  'جدول الفواتير (${filteredInvoices.length} فاتورة)',
                  style: DesktopTheme.titleLarge,
                ),
                const Spacer(),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: _refreshData,
                      tooltip: 'تحديث البيانات',
                    ),
                    IconButton(
                      icon: const Icon(Icons.file_download),
                      onPressed: _exportData,
                      tooltip: 'تصدير البيانات',
                    ),
                    IconButton(
                      icon: const Icon(Icons.print),
                      onPressed: _printData,
                      tooltip: 'طباعة',
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Table Content
          if (filteredInvoices.isEmpty)
            Container(
              padding: const EdgeInsets.all(DesktopTheme.spacingXXLarge),
              child: Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.receipt_long_outlined,
                      size: 64,
                      color: DesktopTheme.textTertiary,
                    ),
                    const SizedBox(height: DesktopTheme.spacingMedium),
                    Text(
                      'لا توجد فواتير تطابق معايير البحث',
                      style: DesktopTheme.titleMedium.copyWith(
                        color: DesktopTheme.textTertiary,
                      ),
                    ),
                    const SizedBox(height: DesktopTheme.spacingMedium),
                    ElevatedButton.icon(
                      onPressed: _clearFilters,
                      icon: const Icon(Icons.clear_all),
                      label: const Text('مسح الفلاتر'),
                      style: DesktopTheme.secondaryButtonStyle,
                    ),
                  ],
                ),
              ),
            )
          else
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width: MediaQuery.of(context).size.width - 100,
                child: DataTable(
                  headingRowColor: WidgetStateProperty.all(DesktopTheme.backgroundTertiary),
                  headingTextStyle: DesktopTheme.titleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  dataRowColor: WidgetStateProperty.resolveWith((states) {
                    if (states.contains(WidgetState.hovered)) {
                      return DesktopTheme.accentGreen.withOpacity(0.05);
                    }
                    return DesktopTheme.backgroundSecondary;
                  }),
                  dataTextStyle: DesktopTheme.bodyMedium,
                  columns: const [
                    DataColumn(label: Text('رقم الفاتورة')),
                    DataColumn(label: Text('العميل')),
                    DataColumn(label: Text('الصورة')),
                    DataColumn(label: Text('الصنف')),
                    DataColumn(label: Text('التاريخ')),
                    DataColumn(label: Text('المبلغ')),
                    DataColumn(label: Text('الحالة')),
                    DataColumn(label: Text('الإجراءات')),
                  ],
                  rows: filteredInvoices.map((invoice) {
                    final customer = _customers.firstWhere(
                      (c) => c.id == invoice.customerId,
                      orElse: () => UserModel(
                        id: '',
                        username: 'unknown',
                        fullName: 'عميل غير محدد',
                        email: '',
                        phone: '',
                        role: 'customer',
                        createdAt: DateTime.now(),
                        updatedAt: DateTime.now(),
                      ),
                    );

                    return DataRow(
                      cells: [
                        DataCell(Text(invoice.id.split('_').first)),
                        DataCell(Text(customer.fullName)),
                        DataCell(_buildInvoiceItemImage(invoice.itemId)),
                        DataCell(
                          Container(
                            constraints: const BoxConstraints(maxWidth: 150),
                            child: Text(
                              invoice.itemId,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        DataCell(Text(_formatDate(invoice.createdAt))),
                        DataCell(Text('${invoice.sellingPrice.toStringAsFixed(0)} ج.م')),
                        DataCell(_buildInvoiceStatusChip(invoice)),
                        DataCell(_buildInvoiceActionButtons(invoice)),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInvoiceStatusChip(InvoiceModel invoice) {
    // Determine status based on invoice data
    String status = 'مدفوعة'; // Default status
    Color color = DesktopTheme.statusSuccess;
    IconData icon = Icons.check_circle;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesktopTheme.spacingSmall,
        vertical: DesktopTheme.spacingXSmall,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: DesktopTheme.spacingXSmall),
          Text(
            status,
            style: DesktopTheme.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceActionButtons(InvoiceModel invoice) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.visibility, size: 18),
          onPressed: () => _viewInvoiceDetails(invoice),
          tooltip: 'عرض التفاصيل',
          style: IconButton.styleFrom(
            foregroundColor: DesktopTheme.primaryBlue,
          ),
        ),
        IconButton(
          icon: const Icon(Icons.print, size: 18),
          onPressed: () => _printInvoice(invoice),
          tooltip: 'طباعة',
          style: IconButton.styleFrom(
            foregroundColor: DesktopTheme.accentGreen,
          ),
        ),
        IconButton(
          icon: const Icon(Icons.edit, size: 18),
          onPressed: () => _editInvoice(invoice),
          tooltip: 'تعديل',
          style: IconButton.styleFrom(
            foregroundColor: DesktopTheme.accentOrange,
          ),
        ),
      ],
    );
  }

  // Helper Methods
  List<InvoiceModel> _getInvoicesByPeriod(String period) {
    final now = DateTime.now();
    return _invoices.where((invoice) {
      switch (period) {
        case 'today':
          return invoice.createdAt.year == now.year &&
                 invoice.createdAt.month == now.month &&
                 invoice.createdAt.day == now.day;
        case 'week':
          final weekStart = now.subtract(Duration(days: now.weekday - 1));
          return invoice.createdAt.isAfter(weekStart);
        case 'month':
          return invoice.createdAt.year == now.year &&
                 invoice.createdAt.month == now.month;
        default:
          return true;
      }
    }).toList();
  }

  List<InvoiceModel> _getFilteredInvoices() {
    return _invoices.where((invoice) {
      // Filter by period
      if (_selectedPeriod != 'all') {
        final periodInvoices = _getInvoicesByPeriod(_selectedPeriod);
        if (!periodInvoices.contains(invoice)) {
          return false;
        }
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final customer = _customers.firstWhere(
          (c) => c.id == invoice.customerId,
          orElse: () => UserModel(
            id: '',
            username: 'unknown',
            fullName: '',
            email: '',
            phone: '',
            role: 'customer',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        return invoice.id.toLowerCase().contains(query) ||
               invoice.itemId.toLowerCase().contains(query) ||
               customer.fullName.toLowerCase().contains(query);
      }

      return true;
    }).toList();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Action Methods
  void _filterByPeriod(String period) {
    setState(() {
      _selectedPeriod = period;
    });
  }

  void _createNewInvoice() {
    showDialog(
      context: context,
      builder: (context) => _CreateInvoiceDialog(
        onInvoiceCreated: () {
          _refreshData();
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _manageCustomers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة إدارة العملاء قيد التطوير')),
    );
  }

  void _viewSalesReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة تقارير المبيعات قيد التطوير')),
    );
  }

  void _viewPendingInvoices() {
    setState(() {
      _selectedStatus = 'pending';
    });
  }

  void _refreshData() {
    setState(() {
      _isLoading = true;
    });
    _loadData();
  }

  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة تصدير البيانات قيد التطوير')),
    );
  }

  void _printData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة الطباعة قيد التطوير')),
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedPeriod = 'all';
      _selectedStatus = 'all';
      _searchQuery = '';
      _searchController.clear();
    });
  }

  void _viewInvoiceDetails(InvoiceModel invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الفاتورة: ${invoice.id}')),
    );
  }

  void _printInvoice(InvoiceModel invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('طباعة الفاتورة: ${invoice.id}')),
    );
  }

  void _editInvoice(InvoiceModel invoice) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل الفاتورة: ${invoice.id}')),
    );
  }

  Widget _buildInvoiceItemImage(String itemId) {
    // Find the item to get its image
    final dataService = Provider.of<DataService>(context, listen: false);

    return FutureBuilder<List<ItemModel>>(
      future: dataService.getItems(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          final item = snapshot.data!.firstWhere(
            (item) => item.id == itemId,
            orElse: () => ItemModel(
              id: '',
              type: '',
              brand: '',
              model: '',
              color: '',
              chassisNumber: '',
              motorFingerprintText: '',
              motorFingerprintImageUrl: '',
              chassisImageUrl: '',
              purchasePrice: 0,
              suggestedSellingPrice: 0,
              currentWarehouseId: '',
              status: '',
              countryOfOrigin: '',
              yearOfManufacture: DateTime.now().year,
              createdBy: 'unknown',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          return HttpOnlyItemImageWidget(
            imageUrl: item.motorFingerprintImageUrl,
            size: 40,
          );
        }

        return Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: DesktopTheme.backgroundTertiary,
            borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall),
          ),
          child: const Icon(
            Icons.motorcycle_outlined,
            size: 20,
            color: DesktopTheme.textTertiary,
          ),
        );
      },
    );
  }
}

/// Dialog for creating new sales invoice
class _CreateInvoiceDialog extends StatefulWidget {
  final VoidCallback onInvoiceCreated;

  const _CreateInvoiceDialog({required this.onInvoiceCreated});

  @override
  State<_CreateInvoiceDialog> createState() => _CreateInvoiceDialogState();
}

class _CreateInvoiceDialogState extends State<_CreateInvoiceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _priceController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedCustomer = '';
  String _selectedItem = '';
  bool _isLoading = false;

  List<UserModel> _customers = [];
  List<ItemModel> _availableItems = [];
  ItemModel? _selectedItemModel;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _priceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      final users = await dataService.getUsers();
      final items = await dataService.getItems();

      setState(() {
        _customers = users.where((user) => user.role == 'customer').toList();
        _availableItems = items.where((item) => item.status == 'متاح').toList();
      });
    } catch (e) {
      // Handle error
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 700,
        padding: const EdgeInsets.all(DesktopTheme.spacingXLarge),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  const Icon(
                    Icons.receipt_long,
                    color: DesktopTheme.accentGreen,
                    size: 24,
                  ),
                  const SizedBox(width: DesktopTheme.spacingSmall),
                  const Text(
                    'إنشاء فاتورة مبيعات جديدة',
                    style: DesktopTheme.headingMedium,
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: DesktopTheme.spacingXLarge),

              // Customer selection
              DropdownButtonFormField<String>(
                value: _selectedCustomer.isNotEmpty ? _selectedCustomer : null,
                decoration: DesktopTheme.getInputDecoration(
                  labelText: 'العميل',
                  prefixIcon: const Icon(Icons.person),
                ),
                items: _customers.map((customer) => DropdownMenuItem(
                  value: customer.id,
                  child: Text(customer.fullName),
                )).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCustomer = value!;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى اختيار العميل';
                  }
                  return null;
                },
              ),

              const SizedBox(height: DesktopTheme.spacingLarge),

              // Item selection
              DropdownButtonFormField<String>(
                value: _selectedItem.isNotEmpty ? _selectedItem : null,
                decoration: DesktopTheme.getInputDecoration(
                  labelText: 'الصنف',
                  prefixIcon: const Icon(Icons.motorcycle),
                ),
                items: _availableItems.map((item) => DropdownMenuItem(
                  value: item.id,
                  child: Row(
                    children: [
                      HttpOnlyItemImageWidget(
                        imageUrl: item.motorFingerprintImageUrl,
                        size: 30,
                      ),
                      const SizedBox(width: DesktopTheme.spacingSmall),
                      Expanded(
                        child: Text(
                          '${item.brand} ${item.model} - ${item.color}',
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                )).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedItem = value!;
                    _selectedItemModel = _availableItems.firstWhere((item) => item.id == value);
                    _priceController.text = _selectedItemModel!.purchasePrice.toString();
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى اختيار الصنف';
                  }
                  return null;
                },
              ),

              const SizedBox(height: DesktopTheme.spacingLarge),

              // Selected item preview
              if (_selectedItemModel != null)
                Container(
                  padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
                  decoration: BoxDecoration(
                    color: DesktopTheme.backgroundTertiary,
                    borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
                    border: Border.all(color: DesktopTheme.borderLight),
                  ),
                  child: Row(
                    children: [
                      HttpOnlyItemImageWidget(
                        imageUrl: _selectedItemModel!.motorFingerprintImageUrl,
                        size: 60,
                      ),
                      const SizedBox(width: DesktopTheme.spacingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${_selectedItemModel!.brand} ${_selectedItemModel!.model}',
                              style: DesktopTheme.titleMedium,
                            ),
                            Text(
                              'اللون: ${_selectedItemModel!.color}',
                              style: DesktopTheme.bodyMedium,
                            ),
                            Text(
                              'رقم الشاسيه: ${_selectedItemModel!.chassisNumber}',
                              style: DesktopTheme.bodySmall.copyWith(
                                color: DesktopTheme.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

              const SizedBox(height: DesktopTheme.spacingLarge),

              // Price
              TextFormField(
                controller: _priceController,
                decoration: DesktopTheme.getInputDecoration(
                  labelText: 'سعر البيع',
                  prefixIcon: const Icon(Icons.attach_money),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال سعر البيع';
                  }
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  return null;
                },
              ),

              const SizedBox(height: DesktopTheme.spacingLarge),

              // Notes
              TextFormField(
                controller: _notesController,
                decoration: DesktopTheme.getInputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  prefixIcon: const Icon(Icons.note),
                ),
                maxLines: 3,
              ),

              const SizedBox(height: DesktopTheme.spacingXXLarge),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: DesktopTheme.spacingMedium),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _saveInvoice,
                    icon: _isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.save),
                    label: Text(_isLoading ? 'جاري الحفظ...' : 'إنشاء الفاتورة'),
                    style: DesktopTheme.primaryButtonStyle,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      // Calculate profit and shares
      final sellingPrice = double.parse(_priceController.text);
      final itemCost = _selectedItemModel!.purchasePrice;
      final profitAmount = sellingPrice - itemCost;
      final companyProfitShare = profitAmount * 0.7; // 70% للشركة
      final agentProfitShare = profitAmount * 0.3; // 30% للوكيل

      // Create invoice model
      final invoice = InvoiceModel(
        id: 'invoice_${DateTime.now().millisecondsSinceEpoch}',
        invoiceNumber: 'INV-${DateTime.now().millisecondsSinceEpoch}',
        type: 'sale',
        customerId: _selectedCustomer,
        itemId: _selectedItem,
        warehouseId: _selectedItemModel!.currentWarehouseId,
        itemCost: itemCost,
        sellingPrice: sellingPrice,
        profitAmount: profitAmount,
        companyProfitShare: companyProfitShare,
        agentProfitShare: agentProfitShare,
        createdBy: 'admin_001',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save invoice
      await dataService.createInvoice(invoice);

      // Update item status to sold
      if (_selectedItemModel != null) {
        final updatedItem = ItemModel(
          id: _selectedItemModel!.id,
          type: _selectedItemModel!.type,
          brand: _selectedItemModel!.brand,
          model: _selectedItemModel!.model,
          color: _selectedItemModel!.color,
          chassisNumber: _selectedItemModel!.chassisNumber,
          motorFingerprintText: _selectedItemModel!.motorFingerprintText,
          motorFingerprintImageUrl: _selectedItemModel!.motorFingerprintImageUrl,
          chassisImageUrl: _selectedItemModel!.chassisImageUrl,
          purchasePrice: _selectedItemModel!.purchasePrice,
          suggestedSellingPrice: _selectedItemModel!.suggestedSellingPrice,
          currentWarehouseId: _selectedItemModel!.currentWarehouseId,
          status: 'مباع', // Update status to sold
          countryOfOrigin: _selectedItemModel!.countryOfOrigin,
          yearOfManufacture: _selectedItemModel!.yearOfManufacture,
          createdBy: _selectedItemModel!.createdBy,
          createdAt: _selectedItemModel!.createdAt,
          updatedAt: DateTime.now(),
        );

        await dataService.updateItem(updatedItem);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم إنشاء الفاتورة بنجاح')),
      );

      widget.onInvoiceCreated();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في إنشاء الفاتورة: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
