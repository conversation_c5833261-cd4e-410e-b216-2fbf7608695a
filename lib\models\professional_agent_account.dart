import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج معاملة الوكيل الاحترافي
class ProfessionalAgentTransaction {
  final String id;
  final String type; // 'goods_received', 'goods_withdrawn', 'customer_sale', 'payment_received', 'credit_given'
  final double amount;
  final String description;
  final DateTime timestamp;
  final String? invoiceId;
  final String? itemId;
  final String? customerId;
  final String createdBy;
  final Map<String, dynamic> metadata;
  final bool isVerified;

  const ProfessionalAgentTransaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.description,
    required this.timestamp,
    this.invoiceId,
    this.itemId,
    this.customerId,
    required this.createdBy,
    this.metadata = const {},
    this.isVerified = false,
  });

  factory ProfessionalAgentTransaction.fromMap(Map<String, dynamic> map) {
    return ProfessionalAgentTransaction(
      id: map['id'] ?? '',
      type: map['type'] ?? '',
      amount: (map['amount'] ?? 0.0).toDouble(),
      description: map['description'] ?? '',
      timestamp: DateTime.parse(map['timestamp']),
      invoiceId: map['invoiceId'],
      itemId: map['itemId'],
      customerId: map['customerId'],
      createdBy: map['createdBy'] ?? '',
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      isVerified: (map['isVerified'] ?? 0) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'amount': amount,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'invoiceId': invoiceId,
      'itemId': itemId,
      'customerId': customerId,
      'createdBy': createdBy,
      'metadata': metadata,
      'isVerified': isVerified ? 1 : 0,
    };
  }
}

/// نموذج حساب الوكيل الاحترافي الموحد
/// يدعم جميع العمليات المحاسبية بدقة ووضوح
class ProfessionalAgentAccount {
  final String id;
  final String agentId;
  final String agentName;
  final String? agentPhone;
  final String? agentEmail;
  
  // === الحسابات الأساسية ===
  final double totalGoodsReceived;      // إجمالي قيمة البضاعة المستلمة (بسعر الشراء)
  final double totalGoodsWithdrawn;     // إجمالي قيمة البضاعة المسحوبة (بسعر الشراء)
  final double totalCustomerSales;      // إجمالي مبيعات العملاء (بسعر البيع)
  final double totalAgentCommission;    // إجمالي عمولة الوكيل
  final double totalCompanyProfits;     // إجمالي أرباح الشركة
  final double totalPaymentsReceived;   // إجمالي المدفوعات المستلمة من الوكيل
  final double totalCreditsGiven;       // إجمالي الائتمانات الممنوحة للوكيل
  
  // === الحسابات المحسوبة ===
  final double currentBalance;          // الرصيد الحالي (موجب = مدين للشركة، سالب = دائن للوكيل)
  final double totalDebt;              // إجمالي المديونية
  final double availableCredit;        // الرصيد الدائن المتاح
  
  // === المعاملات والتتبع ===
  final List<ProfessionalAgentTransaction> transactions;
  final int transactionCount;
  final DateTime? lastTransactionDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final String lastUpdatedBy;
  final bool isActive;
  final bool isVerified;
  
  // === معلومات إضافية ===
  final Map<String, dynamic> metadata;
  final String? notes;

  const ProfessionalAgentAccount({
    required this.id,
    required this.agentId,
    required this.agentName,
    this.agentPhone,
    this.agentEmail,
    this.totalGoodsReceived = 0.0,
    this.totalGoodsWithdrawn = 0.0,
    this.totalCustomerSales = 0.0,
    this.totalAgentCommission = 0.0,
    this.totalCompanyProfits = 0.0,
    this.totalPaymentsReceived = 0.0,
    this.totalCreditsGiven = 0.0,
    this.currentBalance = 0.0,
    this.totalDebt = 0.0,
    this.availableCredit = 0.0,
    this.transactions = const [],
    this.transactionCount = 0,
    this.lastTransactionDate,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    required this.lastUpdatedBy,
    this.isActive = true,
    this.isVerified = false,
    this.metadata = const {},
    this.notes,
  });

  /// حساب الرصيد الحالي بناءً على المعادلة المحاسبية الصحيحة
  /// الرصيد = (البضاعة المستلمة - البضاعة المسحوبة) - (المبيعات + العمولة) + المدفوعات - الائتمانات
  double calculateCurrentBalance() {
    final netGoodsValue = totalGoodsReceived - totalGoodsWithdrawn;
    final totalEarnings = totalCustomerSales + totalAgentCommission;
    final netPayments = totalPaymentsReceived - totalCreditsGiven;
    return netGoodsValue - totalEarnings + netPayments;
  }

  /// حساب المديونية الحالية (إذا كان الرصيد موجب)
  double calculateCurrentDebt() {
    final balance = calculateCurrentBalance();
    return balance > 0 ? balance : 0.0;
  }

  /// حساب الرصيد الدائن (إذا كان الرصيد سالب)
  double calculateCreditBalance() {
    final balance = calculateCurrentBalance();
    return balance < 0 ? balance.abs() : 0.0;
  }

  /// حساب إجمالي الأرباح (عمولة الوكيل + أرباح الشركة)
  double calculateTotalProfits() {
    return totalAgentCommission + totalCompanyProfits;
  }

  /// حساب نسبة العمولة من إجمالي المبيعات
  double calculateCommissionPercentage() {
    if (totalCustomerSales == 0) return 0.0;
    return (totalAgentCommission / totalCustomerSales) * 100;
  }

  /// التحقق من صحة الحساب
  bool validateAccount() {
    // التحقق من أن الحسابات منطقية
    if (totalGoodsReceived < 0 || totalGoodsWithdrawn < 0 || 
        totalCustomerSales < 0 || totalAgentCommission < 0 ||
        totalCompanyProfits < 0 || totalPaymentsReceived < 0) {
      return false;
    }
    
    // التحقق من أن البضاعة المسحوبة لا تزيد عن المستلمة
    if (totalGoodsWithdrawn > totalGoodsReceived) {
      return false;
    }
    
    // التحقق من أن الرصيد المحسوب يطابق الرصيد المخزن
    final calculatedBalance = calculateCurrentBalance();
    const tolerance = 0.01; // تسامح 1 قرش
    return (calculatedBalance - currentBalance).abs() <= tolerance;
  }

  /// الحصول على حالة الحساب
  String getAccountStatus() {
    if (!isActive) return 'غير نشط';
    if (!isVerified) return 'غير مُتحقق منه';
    
    final balance = calculateCurrentBalance();
    if (balance > 0) return 'مدين';
    if (balance < 0) return 'دائن';
    return 'متوازن';
  }

  /// الحصول على ملخص الحساب
  Map<String, dynamic> getAccountSummary() {
    return {
      'agentName': agentName,
      'currentBalance': currentBalance,
      'balanceStatus': getAccountStatus(),
      'totalSales': totalCustomerSales,
      'totalCommission': totalAgentCommission,
      'totalPayments': totalPaymentsReceived,
      'transactionCount': transactionCount,
      'lastTransaction': lastTransactionDate?.toIso8601String(),
      'isValid': validateAccount(),
    };
  }

  /// إنشاء من Firestore
  factory ProfessionalAgentAccount.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ProfessionalAgentAccount(
      id: doc.id,
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      agentPhone: data['agentPhone'],
      agentEmail: data['agentEmail'],
      totalGoodsReceived: (data['totalGoodsReceived'] ?? 0.0).toDouble(),
      totalGoodsWithdrawn: (data['totalGoodsWithdrawn'] ?? 0.0).toDouble(),
      totalCustomerSales: (data['totalCustomerSales'] ?? 0.0).toDouble(),
      totalAgentCommission: (data['totalAgentCommission'] ?? 0.0).toDouble(),
      totalCompanyProfits: (data['totalCompanyProfits'] ?? 0.0).toDouble(),
      totalPaymentsReceived: (data['totalPaymentsReceived'] ?? 0.0).toDouble(),
      totalCreditsGiven: (data['totalCreditsGiven'] ?? 0.0).toDouble(),
      currentBalance: (data['currentBalance'] ?? 0.0).toDouble(),
      totalDebt: (data['totalDebt'] ?? 0.0).toDouble(),
      availableCredit: (data['availableCredit'] ?? 0.0).toDouble(),
      transactions: (data['transactions'] as List<dynamic>?)
          ?.map((t) => ProfessionalAgentTransaction.fromMap(t))
          .toList() ?? [],
      transactionCount: data['transactionCount'] ?? 0,
      lastTransactionDate: data['lastTransactionDate'] != null
          ? (data['lastTransactionDate'] as Timestamp).toDate()
          : null,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      lastUpdatedBy: data['lastUpdatedBy'] ?? '',
      isActive: data['isActive'] ?? true,
      isVerified: data['isVerified'] ?? false,
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      notes: data['notes'],
    );
  }

  /// إنشاء من Map (قاعدة البيانات المحلية)
  factory ProfessionalAgentAccount.fromMap(Map<String, dynamic> map) {
    return ProfessionalAgentAccount(
      id: map['id'] ?? '',
      agentId: map['agentId'] ?? '',
      agentName: map['agentName'] ?? '',
      agentPhone: map['agentPhone'],
      agentEmail: map['agentEmail'],
      totalGoodsReceived: (map['totalGoodsReceived'] ?? 0.0).toDouble(),
      totalGoodsWithdrawn: (map['totalGoodsWithdrawn'] ?? 0.0).toDouble(),
      totalCustomerSales: (map['totalCustomerSales'] ?? 0.0).toDouble(),
      totalAgentCommission: (map['totalAgentCommission'] ?? 0.0).toDouble(),
      totalCompanyProfits: (map['totalCompanyProfits'] ?? 0.0).toDouble(),
      totalPaymentsReceived: (map['totalPaymentsReceived'] ?? 0.0).toDouble(),
      totalCreditsGiven: (map['totalCreditsGiven'] ?? 0.0).toDouble(),
      currentBalance: (map['currentBalance'] ?? 0.0).toDouble(),
      totalDebt: (map['totalDebt'] ?? 0.0).toDouble(),
      availableCredit: (map['availableCredit'] ?? 0.0).toDouble(),
      transactions: (map['transactions'] as List<dynamic>?)
          ?.map((t) => ProfessionalAgentTransaction.fromMap(t))
          .toList() ?? [],
      transactionCount: map['transactionCount'] ?? 0,
      lastTransactionDate: map['lastTransactionDate'] != null
          ? DateTime.parse(map['lastTransactionDate'])
          : null,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      createdBy: map['createdBy'] ?? '',
      lastUpdatedBy: map['lastUpdatedBy'] ?? '',
      isActive: (map['isActive'] ?? 1) == 1,
      isVerified: (map['isVerified'] ?? 0) == 1,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      notes: map['notes'],
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'agentId': agentId,
      'agentName': agentName,
      'agentPhone': agentPhone,
      'agentEmail': agentEmail,
      'totalGoodsReceived': totalGoodsReceived,
      'totalGoodsWithdrawn': totalGoodsWithdrawn,
      'totalCustomerSales': totalCustomerSales,
      'totalAgentCommission': totalAgentCommission,
      'totalCompanyProfits': totalCompanyProfits,
      'totalPaymentsReceived': totalPaymentsReceived,
      'totalCreditsGiven': totalCreditsGiven,
      'currentBalance': currentBalance,
      'totalDebt': totalDebt,
      'availableCredit': availableCredit,
      'transactions': transactions.map((t) => t.toMap()).toList(),
      'transactionCount': transactionCount,
      'lastTransactionDate': lastTransactionDate?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'lastUpdatedBy': lastUpdatedBy,
      'isActive': isActive ? 1 : 0,
      'isVerified': isVerified ? 1 : 0,
      'metadata': metadata,
      'notes': notes,
    };
  }

  /// تحويل إلى Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'agentId': agentId,
      'agentName': agentName,
      'agentPhone': agentPhone,
      'agentEmail': agentEmail,
      'totalGoodsReceived': totalGoodsReceived,
      'totalGoodsWithdrawn': totalGoodsWithdrawn,
      'totalCustomerSales': totalCustomerSales,
      'totalAgentCommission': totalAgentCommission,
      'totalCompanyProfits': totalCompanyProfits,
      'totalPaymentsReceived': totalPaymentsReceived,
      'totalCreditsGiven': totalCreditsGiven,
      'currentBalance': currentBalance,
      'totalDebt': totalDebt,
      'availableCredit': availableCredit,
      'transactions': transactions.map((t) => t.toMap()).toList(),
      'transactionCount': transactionCount,
      'lastTransactionDate': lastTransactionDate != null 
          ? Timestamp.fromDate(lastTransactionDate!) 
          : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'lastUpdatedBy': lastUpdatedBy,
      'isActive': isActive,
      'isVerified': isVerified,
      'metadata': metadata,
      'notes': notes,
    };
  }
}
