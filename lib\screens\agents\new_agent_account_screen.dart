import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_financial_account_model.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/agent_financial_service.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';

/// شاشة كشف حساب الوكيل الجديدة والمحسنة
/// تعرض تفاصيل دقيقة لحساب الوكيل مع المؤسسة
class NewAgentAccountScreen extends StatefulWidget {
  final UserModel agent;
  final AgentFinancialAccount account;

  const NewAgentAccountScreen({
    super.key,
    required this.agent,
    required this.account,
  });

  @override
  State<NewAgentAccountScreen> createState() => _NewAgentAccountScreenState();
}

class _NewAgentAccountScreenState extends State<NewAgentAccountScreen> {
  final AgentFinancialService _financialService = AgentFinancialService();
  
  late AgentFinancialAccount _currentAccount;
  bool _isLoading = false;
  String? _error;
  String _selectedFilter = 'all'; // all, goods_received, customer_sale, payment_received

  @override
  void initState() {
    super.initState();
    _currentAccount = widget.account;
    _refreshAccount();
  }

  /// تحديث بيانات الحساب
  Future<void> _refreshAccount() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
      if (currentUser == null) return;

      // إعادة حساب الحساب للحصول على أحدث البيانات
      final updatedAccount = await _financialService.recalculateAgentAccount(
        widget.agent.id,
        currentUser.id,
      );

      setState(() {
        _currentAccount = updatedAccount;
        _isLoading = false;
      });

      if (kDebugMode) {
        print('✅ Account refreshed for ${widget.agent.fullName}');
        print('   Balance: ${_currentAccount.currentBalance}');
        print('   Transactions: ${_currentAccount.transactions.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error refreshing account: $e');
      }
      setState(() {
        _error = 'خطأ في تحديث البيانات: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text('كشف حساب ${widget.agent.fullName}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshAccount,
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: _exportToPDF,
            tooltip: 'تصدير PDF',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget(message: 'جاري تحديث بيانات الحساب...');
    }

    if (_error != null) {
      return CustomErrorWidget(
        message: _error!,
        onRetry: _refreshAccount,
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshAccount,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAccountSummary(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildFinancialBreakdown(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildTransactionsSection(),
          ],
        ),
      ),
    );
  }

  /// بناء ملخص الحساب
  Widget _buildAccountSummary() {
    final isDebt = _currentAccount.currentBalance > 0;
    final isCredit = _currentAccount.currentBalance < 0;
    final balanceColor = isDebt ? Colors.red : isCredit ? Colors.green : Colors.grey;
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).primaryColor,
                  child: Text(
                    widget.agent.fullName.isNotEmpty ? widget.agent.fullName[0] : 'و',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.agent.fullName,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (widget.agent.phone.isNotEmpty)
                        Text(
                          widget.agent.phone,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      const SizedBox(height: AppConstants.smallPadding),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: widget.agent.isActive ? Colors.green : Colors.grey,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          widget.agent.isActive ? 'وكيل نشط' : 'وكيل غير نشط',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            const Divider(),
            
            // الرصيد الحالي
            Center(
              child: Column(
                children: [
                  Text(
                    'الرصيد الحالي',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    AppUtils.formatCurrency(_currentAccount.currentBalance.abs()),
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: balanceColor,
                    ),
                  ),
                  Text(
                    isDebt ? 'مدين للمؤسسة' : isCredit ? 'دائن لدى المؤسسة' : 'متوازن',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: balanceColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تفصيل الحسابات المالية
  Widget _buildFinancialBreakdown() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفصيل الحسابات المالية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: AppConstants.smallPadding,
              mainAxisSpacing: AppConstants.smallPadding,
              childAspectRatio: 1.8,
              children: [
                _buildFinancialCard(
                  'البضاعة المستلمة',
                  _currentAccount.totalGoodsReceived,
                  Icons.inventory,
                  Colors.orange,
                ),
                _buildFinancialCard(
                  'البضاعة المرتجعة',
                  _currentAccount.totalGoodsReturned,
                  Icons.keyboard_return,
                  Colors.red,
                ),
                _buildFinancialCard(
                  'مبيعات العملاء',
                  _currentAccount.totalCustomerSales,
                  Icons.point_of_sale,
                  Colors.blue,
                ),
                _buildFinancialCard(
                  'أرباح الوكيل',
                  _currentAccount.totalAgentProfits,
                  Icons.trending_up,
                  Colors.green,
                ),
                _buildFinancialCard(
                  'أرباح المؤسسة',
                  _currentAccount.totalCompanyProfits,
                  Icons.business,
                  Colors.purple,
                ),
                _buildFinancialCard(
                  'المدفوعات المستلمة',
                  _currentAccount.totalPaymentsReceived,
                  Icons.payment,
                  Colors.teal,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة مالية
  Widget _buildFinancialCard(String title, double amount, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            AppUtils.formatCurrency(amount),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// بناء قسم المعاملات
  Widget _buildTransactionsSection() {
    final filteredTransactions = _getFilteredTransactions();

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'سجل المعاملات',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                Text(
                  '${filteredTransactions.length} معاملة',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // فلتر المعاملات
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('الكل', 'all'),
                  _buildFilterChip('استلام بضاعة', 'goods_received'),
                  _buildFilterChip('مبيعات عملاء', 'customer_sale'),
                  _buildFilterChip('دفعات', 'payment_received'),
                  _buildFilterChip('إرجاع بضاعة', 'goods_returned'),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // قائمة المعاملات
            if (filteredTransactions.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.largePadding),
                  child: Column(
                    children: [
                      Icon(
                        Icons.receipt_long,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      Text(
                        'لا توجد معاملات',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: filteredTransactions.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final transaction = filteredTransactions[index];
                  return _buildTransactionItem(transaction);
                },
              ),
          ],
        ),
      ),
    );
  }

  /// الحصول على المعاملات المفلترة
  List<AgentTransaction> _getFilteredTransactions() {
    if (_selectedFilter == 'all') {
      return _currentAccount.transactions;
    }

    return _currentAccount.transactions
        .where((transaction) => transaction.type == _selectedFilter)
        .toList();
  }

  /// بناء رقاقة الفلتر
  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;

    return Padding(
      padding: const EdgeInsets.only(right: AppConstants.smallPadding),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = value;
          });
        },
        selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
        checkmarkColor: Theme.of(context).primaryColor,
      ),
    );
  }

  /// بناء عنصر المعاملة
  Widget _buildTransactionItem(AgentTransaction transaction) {
    final isPositive = transaction.amount > 0;
    final amountColor = isPositive ? Colors.red : Colors.green;
    final icon = _getTransactionIcon(transaction.type);
    final iconColor = _getTransactionIconColor(transaction.type);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: iconColor.withOpacity(0.2),
        child: Icon(icon, color: iconColor),
      ),
      title: Text(
        transaction.description,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppUtils.formatDate(transaction.createdAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          Text(
            'الرصيد بعد المعاملة: ${AppUtils.formatCurrency(transaction.balanceAfter)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '${isPositive ? '+' : ''}${AppUtils.formatCurrency(transaction.amount)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: amountColor,
            ),
          ),
          Text(
            _getTransactionTypeLabel(transaction.type),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: iconColor,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة المعاملة
  IconData _getTransactionIcon(String type) {
    switch (type) {
      case 'goods_received':
        return Icons.inventory;
      case 'goods_returned':
        return Icons.keyboard_return;
      case 'customer_sale':
        return Icons.point_of_sale;
      case 'payment_received':
        return Icons.payment;
      default:
        return Icons.receipt;
    }
  }

  /// الحصول على لون أيقونة المعاملة
  Color _getTransactionIconColor(String type) {
    switch (type) {
      case 'goods_received':
        return Colors.orange;
      case 'goods_returned':
        return Colors.red;
      case 'customer_sale':
        return Colors.blue;
      case 'payment_received':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على تسمية نوع المعاملة
  String _getTransactionTypeLabel(String type) {
    switch (type) {
      case 'goods_received':
        return 'استلام بضاعة';
      case 'goods_returned':
        return 'إرجاع بضاعة';
      case 'customer_sale':
        return 'بيع عميل';
      case 'payment_received':
        return 'دفعة';
      default:
        return 'معاملة';
    }
  }

  /// تصدير كشف الحساب إلى PDF
  void _exportToPDF() {
    // TODO: تنفيذ تصدير PDF
    AppUtils.showSnackBar(
      context,
      'ميزة تصدير PDF قيد التطوير',
      isError: true,
    );
  }
}
