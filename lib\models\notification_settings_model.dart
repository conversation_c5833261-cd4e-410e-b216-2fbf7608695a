import 'package:flutter/material.dart';

/// إعدادات الإشعارات المتقدمة
class NotificationSettings {
  final bool enabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool showInApp;
  final bool showOnLockScreen;
  final bool groupSimilar;
  final Set<String> enabledTypes;
  final Map<String, NotificationTypeSettings> typeSettings;
  final QuietHours? quietHours;
  final int maxNotificationsPerHour;
  final bool smartFiltering;
  final bool aiSuggestions;
  final String language;

  const NotificationSettings({
    this.enabled = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.showInApp = true,
    this.showOnLockScreen = true,
    this.groupSimilar = true,
    this.enabledTypes = const {
      'invoice_created',
      'payment_received',
      'payment_reminder',
      'inventory_added',
      'document_updated',
      'general',
    },
    this.typeSettings = const {},
    this.quietHours,
    this.maxNotificationsPerHour = 10,
    this.smartFiltering = true,
    this.aiSuggestions = true,
    this.language = 'ar',
  });

  NotificationSettings copyWith({
    bool? enabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    bool? showInApp,
    bool? showOnLockScreen,
    bool? groupSimilar,
    Set<String>? enabledTypes,
    Map<String, NotificationTypeSettings>? typeSettings,
    QuietHours? quietHours,
    int? maxNotificationsPerHour,
    bool? smartFiltering,
    bool? aiSuggestions,
    String? language,
  }) {
    return NotificationSettings(
      enabled: enabled ?? this.enabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      showInApp: showInApp ?? this.showInApp,
      showOnLockScreen: showOnLockScreen ?? this.showOnLockScreen,
      groupSimilar: groupSimilar ?? this.groupSimilar,
      enabledTypes: enabledTypes ?? this.enabledTypes,
      typeSettings: typeSettings ?? this.typeSettings,
      quietHours: quietHours ?? this.quietHours,
      maxNotificationsPerHour: maxNotificationsPerHour ?? this.maxNotificationsPerHour,
      smartFiltering: smartFiltering ?? this.smartFiltering,
      aiSuggestions: aiSuggestions ?? this.aiSuggestions,
      language: language ?? this.language,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'showInApp': showInApp,
      'showOnLockScreen': showOnLockScreen,
      'groupSimilar': groupSimilar,
      'enabledTypes': enabledTypes.toList(),
      'typeSettings': typeSettings.map((key, value) => MapEntry(key, value.toJson())),
      'quietHours': quietHours?.toJson(),
      'maxNotificationsPerHour': maxNotificationsPerHour,
      'smartFiltering': smartFiltering,
      'aiSuggestions': aiSuggestions,
      'language': language,
    };
  }

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      enabled: json['enabled'] ?? true,
      soundEnabled: json['soundEnabled'] ?? true,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      showInApp: json['showInApp'] ?? true,
      showOnLockScreen: json['showOnLockScreen'] ?? true,
      groupSimilar: json['groupSimilar'] ?? true,
      enabledTypes: Set<String>.from(json['enabledTypes'] ?? []),
      typeSettings: (json['typeSettings'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(key, NotificationTypeSettings.fromJson(value)),
      ) ?? {},
      quietHours: json['quietHours'] != null 
          ? QuietHours.fromJson(json['quietHours']) 
          : null,
      maxNotificationsPerHour: json['maxNotificationsPerHour'] ?? 10,
      smartFiltering: json['smartFiltering'] ?? true,
      aiSuggestions: json['aiSuggestions'] ?? true,
      language: json['language'] ?? 'ar',
    );
  }

  @override
  String toString() {
    return 'NotificationSettings(enabled: $enabled, types: ${enabledTypes.length})';
  }
}

/// إعدادات نوع إشعار محدد
class NotificationTypeSettings {
  final bool enabled;
  final String priority; // 'low', 'medium', 'high', 'critical'
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String? customSound;
  final Color? color;
  final Duration? delay;

  const NotificationTypeSettings({
    this.enabled = true,
    this.priority = 'medium',
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.customSound,
    this.color,
    this.delay,
  });

  NotificationTypeSettings copyWith({
    bool? enabled,
    String? priority,
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? customSound,
    Color? color,
    Duration? delay,
  }) {
    return NotificationTypeSettings(
      enabled: enabled ?? this.enabled,
      priority: priority ?? this.priority,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      customSound: customSound ?? this.customSound,
      color: color ?? this.color,
      delay: delay ?? this.delay,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'priority': priority,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'customSound': customSound,
      'color': color?.value,
      'delay': delay?.inMilliseconds,
    };
  }

  factory NotificationTypeSettings.fromJson(Map<String, dynamic> json) {
    return NotificationTypeSettings(
      enabled: json['enabled'] ?? true,
      priority: json['priority'] ?? 'medium',
      soundEnabled: json['soundEnabled'] ?? true,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      customSound: json['customSound'],
      color: json['color'] != null ? Color(json['color']) : null,
      delay: json['delay'] != null ? Duration(milliseconds: json['delay']) : null,
    );
  }
}

/// ساعات الهدوء
class QuietHours {
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final Set<int> enabledDays; // 1-7 (Monday-Sunday)
  final bool allowCritical;

  const QuietHours({
    required this.startTime,
    required this.endTime,
    this.enabledDays = const {1, 2, 3, 4, 5, 6, 7},
    this.allowCritical = true,
  });

  bool isQuietTime(DateTime dateTime) {
    final time = TimeOfDay.fromDateTime(dateTime);
    final day = dateTime.weekday;
    
    if (!enabledDays.contains(day)) return false;
    
    // Handle overnight quiet hours
    if (endTime.hour < startTime.hour) {
      return time.hour >= startTime.hour || time.hour <= endTime.hour;
    } else {
      return time.hour >= startTime.hour && time.hour <= endTime.hour;
    }
  }

  QuietHours copyWith({
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    Set<int>? enabledDays,
    bool? allowCritical,
  }) {
    return QuietHours(
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      enabledDays: enabledDays ?? this.enabledDays,
      allowCritical: allowCritical ?? this.allowCritical,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startTime': {
        'hour': startTime.hour,
        'minute': startTime.minute,
      },
      'endTime': {
        'hour': endTime.hour,
        'minute': endTime.minute,
      },
      'enabledDays': enabledDays.toList(),
      'allowCritical': allowCritical,
    };
  }

  factory QuietHours.fromJson(Map<String, dynamic> json) {
    return QuietHours(
      startTime: TimeOfDay(
        hour: json['startTime']['hour'],
        minute: json['startTime']['minute'],
      ),
      endTime: TimeOfDay(
        hour: json['endTime']['hour'],
        minute: json['endTime']['minute'],
      ),
      enabledDays: Set<int>.from(json['enabledDays'] ?? []),
      allowCritical: json['allowCritical'] ?? true,
    );
  }
}

/// تحليلات الإشعارات
class NotificationAnalytics {
  final String id;
  final String type;
  final DateTime timestamp;
  final String action; // 'sent', 'received', 'opened', 'dismissed'
  final String? userId;
  final Map<String, dynamic>? metadata;

  const NotificationAnalytics({
    required this.id,
    required this.type,
    required this.timestamp,
    required this.action,
    this.userId,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'timestamp': timestamp.toIso8601String(),
      'action': action,
      'userId': userId,
      'metadata': metadata,
    };
  }

  factory NotificationAnalytics.fromJson(Map<String, dynamic> json) {
    return NotificationAnalytics(
      id: json['id'],
      type: json['type'],
      timestamp: DateTime.parse(json['timestamp']),
      action: json['action'],
      userId: json['userId'],
      metadata: json['metadata'],
    );
  }
}

/// تكوين قناة الإشعارات
class NotificationChannelConfig {
  final String id;
  final String name;
  final String description;
  final Importance importance;
  final Priority priority;
  final Color color;
  final String? sound;

  const NotificationChannelConfig({
    required this.id,
    required this.name,
    required this.description,
    required this.importance,
    required this.priority,
    required this.color,
    this.sound,
  });
}

/// أولويات الإشعارات
enum Importance {
  none,
  min,
  low,
  defaultImportance,
  high,
  max,
}

enum Priority {
  min,
  low,
  defaultPriority,
  high,
  max,
}
