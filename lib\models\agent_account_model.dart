import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Model for agent account management with variable debt system
class AgentAccountModel {
  final String id;
  final String agentId; // User ID of the agent
  final String agentName;
  final String agentPhone;
  final double totalDebt; // Total amount owed by agent
  final double totalPaid; // Total amount paid by agent
  final double currentBalance; // Current balance (debt - paid)
  final List<AgentTransaction> transactions;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final Map<String, dynamic>? additionalData;

  AgentAccountModel({
    required this.id,
    required this.agentId,
    required this.agentName,
    required this.agentPhone,
    required this.totalDebt,
    required this.totalPaid,
    required this.currentBalance,
    required this.transactions,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.additionalData,
  });

  // Factory constructor from Firestore document
  factory AgentAccountModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AgentAccountModel(
      id: doc.id,
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      agentPhone: data['agentPhone'] ?? '',
      totalDebt: (data['totalDebt'] ?? 0.0).toDouble(),
      totalPaid: (data['totalPaid'] ?? 0.0).toDouble(),
      currentBalance: (data['currentBalance'] ?? 0.0).toDouble(),
      transactions: _parseTransactionsFromFirestore(data['transactions']),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      additionalData: data['additionalData'] != null 
          ? Map<String, dynamic>.from(data['additionalData']) 
          : null,
    );
  }

  // Factory constructor from Map (for local database)
  factory AgentAccountModel.fromMap(Map<String, dynamic> map) {
    return AgentAccountModel(
      id: map['id'] ?? '',
      agentId: map['agentId'] ?? '',
      agentName: map['agentName'] ?? '',
      agentPhone: map['agentPhone'] ?? '',
      totalDebt: (map['totalDebt'] ?? 0.0).toDouble(),
      totalPaid: (map['totalPaid'] ?? 0.0).toDouble(),
      currentBalance: (map['currentBalance'] ?? 0.0).toDouble(),
      transactions: _parseTransactions(map['transactions']),
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: _parseDateTime(map['updatedAt']),
      createdBy: map['createdBy'] ?? '',
      additionalData: _parseAdditionalData(map['additionalData']),
    );
  }

  // Convert to Map (for local database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'agentId': agentId,
      'agentName': agentName,
      'agentPhone': agentPhone,
      'totalDebt': totalDebt,
      'totalPaid': totalPaid,
      'currentBalance': currentBalance,
      'transactions': json.encode(transactions.map((t) => t.toMap()).toList()),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'additionalData': additionalData,
    };
  }

  // Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    return {
      'agentId': agentId,
      'agentName': agentName,
      'agentPhone': agentPhone,
      'totalDebt': totalDebt,
      'totalPaid': totalPaid,
      'currentBalance': currentBalance,
      'transactions': transactions.map((t) => t.toFirestore()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'additionalData': additionalData,
    };
  }

  // Copy with method for updates
  AgentAccountModel copyWith({
    String? id,
    String? agentId,
    String? agentName,
    String? agentPhone,
    double? totalDebt,
    double? totalPaid,
    double? currentBalance,
    List<AgentTransaction>? transactions,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    Map<String, dynamic>? additionalData,
  }) {
    return AgentAccountModel(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      agentPhone: agentPhone ?? this.agentPhone,
      totalDebt: totalDebt ?? this.totalDebt,
      totalPaid: totalPaid ?? this.totalPaid,
      currentBalance: currentBalance ?? this.currentBalance,
      transactions: transactions ?? this.transactions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  // Helper methods
  bool get hasDebt => currentBalance > 0;
  bool get hasCredit => currentBalance < 0;
  double get debtAmount => hasDebt ? currentBalance : 0.0;
  double get creditAmount => hasCredit ? currentBalance.abs() : 0.0;

  // Calculate correct balance from transactions (Android logic)
  double get calculatedBalance {
    double totalDebt = 0.0;
    double totalPaid = 0.0;
    double totalCredits = 0.0;

    for (final transaction in transactions) {
      switch (transaction.type) {
        case 'debt':
        case 'transfer':
        case 'goods':
          // Agent owes money for goods transferred or company profit share
          totalDebt += transaction.amount;
          break;
        case 'payment':
          // Agent paid money
          totalPaid += transaction.amount;
          break;
        case 'credit':
        case 'sale':
        case 'customer':
          // Agent gets credit for sales made (reduces debt)
          totalCredits += transaction.amount;
          break;
      }
    }

    // Balance = Total Debt - Total Paid - Total Credits
    // Positive balance = Agent owes money
    // Negative balance = Agent has credit
    return totalDebt - totalPaid - totalCredits;
  }

  // Get accurate totals from transactions
  Map<String, double> get transactionTotals {
    double totalDebt = 0.0;
    double totalPaid = 0.0;
    double totalCredits = 0.0;

    for (final transaction in transactions) {
      switch (transaction.type) {
        case 'debt':
        case 'transfer':
        case 'goods':
          totalDebt += transaction.amount;
          break;
        case 'payment':
          totalPaid += transaction.amount;
          break;
        case 'credit':
        case 'sale':
        case 'customer':
          totalCredits += transaction.amount;
          break;
      }
    }

    return {
      'totalDebt': totalDebt,
      'totalPaid': totalPaid,
      'totalCredits': totalCredits,
      'balance': totalDebt - totalPaid - totalCredits,
    };
  }

  // Get recent transactions
  List<AgentTransaction> getRecentTransactions({int limit = 10}) {
    final sortedTransactions = List<AgentTransaction>.from(transactions);
    sortedTransactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sortedTransactions.take(limit).toList();
  }

  // Helper method to parse transactions from different formats
  static List<AgentTransaction> _parseTransactions(dynamic transactionsData) {
    if (transactionsData == null) return [];

    try {
      List<dynamic> transactionsList;

      if (transactionsData is String) {
        // Data from local database (JSON string)
        if (transactionsData.isEmpty) return [];
        final decoded = json.decode(transactionsData);
        transactionsList = decoded is List ? decoded : [];
      } else if (transactionsData is List) {
        // Data already as List
        transactionsList = transactionsData;
      } else {
        return [];
      }

      return transactionsList
          .map((item) => AgentTransaction.fromMap(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing transactions: $e');
      }
      return [];
    }
  }

  // Helper method to parse transactions from Firestore (handles both String and List)
  static List<AgentTransaction> _parseTransactionsFromFirestore(dynamic transactionsData) {
    if (transactionsData == null) return [];

    try {
      if (transactionsData is String) {
        // If stored as JSON string in Firestore
        if (transactionsData.isEmpty) return [];
        final List<dynamic> decoded = json.decode(transactionsData);
        return decoded.map((item) => AgentTransaction.fromMap(item as Map<String, dynamic>)).toList();
      } else if (transactionsData is List) {
        // If stored as array in Firestore
        return transactionsData.map((item) {
          if (item is Map<String, dynamic>) {
            return AgentTransaction.fromMap(item);
          } else {
            return AgentTransaction.fromMap(Map<String, dynamic>.from(item));
          }
        }).toList();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing transactions from Firestore: $e');
        print('Transactions data type: ${transactionsData.runtimeType}');
        print('Transactions data: $transactionsData');
      }
    }

    return [];
  }

  // Helper method to parse DateTime from various formats
  static DateTime _parseDateTime(dynamic dateTime) {
    if (dateTime == null) return DateTime.now();

    if (dateTime is DateTime) {
      return dateTime;
    } else if (dateTime is String) {
      try {
        return DateTime.parse(dateTime);
      } catch (e) {
        if (kDebugMode) {
          print('Error parsing DateTime from string: $e');
        }
        return DateTime.now();
      }
    } else if (dateTime is Timestamp) {
      return dateTime.toDate();
    } else if (dateTime is int) {
      return DateTime.fromMillisecondsSinceEpoch(dateTime);
    }

    return DateTime.now();
  }

  // Helper method to parse additional data from different formats
  static Map<String, dynamic>? _parseAdditionalData(dynamic additionalDataValue) {
    if (additionalDataValue == null) return null;
    
    try {
      if (additionalDataValue is String) {
        return null; // Skip JSON parsing for now
      } else if (additionalDataValue is Map) {
        return Map<String, dynamic>.from(additionalDataValue);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'AgentAccountModel(id: $id, agentName: $agentName, currentBalance: $currentBalance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AgentAccountModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Model for agent transactions (debt and payment records)
class AgentTransaction {
  final String id;
  final String type; // 'debt', 'payment', 'transfer'
  final double amount;
  final String description;
  final String? invoiceId; // Related invoice ID for debt transactions
  final String? itemId; // Related item ID for transfer transactions
  final DateTime timestamp;
  final String createdBy;
  final Map<String, dynamic>? metadata;

  AgentTransaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.description,
    this.invoiceId,
    this.itemId,
    required this.timestamp,
    required this.createdBy,
    this.metadata,
  });

  factory AgentTransaction.fromMap(Map<String, dynamic> map) {
    return AgentTransaction(
      id: map['id'] ?? '',
      type: map['type'] ?? '',
      amount: (map['amount'] ?? 0.0).toDouble(),
      description: map['description'] ?? '',
      invoiceId: map['invoiceId'],
      itemId: map['itemId'],
      timestamp: map['timestamp'] is Timestamp
          ? (map['timestamp'] as Timestamp).toDate()
          : DateTime.parse(map['timestamp']),
      createdBy: map['createdBy'] ?? '',
      metadata: map['metadata'] != null
          ? Map<String, dynamic>.from(map['metadata'])
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'amount': amount,
      'description': description,
      'invoiceId': invoiceId,
      'itemId': itemId,
      'timestamp': timestamp.toIso8601String(),
      'createdBy': createdBy,
      'metadata': metadata,
    };
  }

  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'type': type,
      'amount': amount,
      'description': description,
      'invoiceId': invoiceId,
      'itemId': itemId,
      'timestamp': Timestamp.fromDate(timestamp),
      'createdBy': createdBy,
      'metadata': metadata,
    };
  }

  @override
  String toString() {
    return 'AgentTransaction(id: $id, type: $type, amount: $amount)';
  }
}
