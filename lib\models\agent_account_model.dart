import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج مؤقت لحساب الوكيل لحل مشاكل البناء
class AgentAccountModel {
  final String id;
  final String agentId;
  final String agentName;
  final String? agentPhone;
  final double totalDebt;
  final double totalPaid;
  final double currentBalance;
  final List<AgentTransaction> transactions;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final String? createdBy;

  AgentAccountModel({
    required this.id,
    required this.agentId,
    required this.agentName,
    this.agentPhone,
    this.totalDebt = 0.0,
    this.totalPaid = 0.0,
    this.currentBalance = 0.0,
    this.transactions = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.createdBy,
  });

  factory AgentAccountModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AgentAccountModel(
      id: doc.id,
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      agentPhone: data['agentPhone'],
      totalDebt: (data['totalDebt'] ?? 0.0).toDouble(),
      totalPaid: (data['totalPaid'] ?? 0.0).toDouble(),
      currentBalance: (data['currentBalance'] ?? 0.0).toDouble(),
      transactions: [],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      isActive: data['isActive'] ?? true,
      createdBy: data['createdBy'],
    );
  }

  factory AgentAccountModel.fromMap(Map<String, dynamic> map) {
    return AgentAccountModel(
      id: map['id'] ?? '',
      agentId: map['agentId'] ?? '',
      agentName: map['agentName'] ?? '',
      agentPhone: map['agentPhone'],
      totalDebt: (map['totalDebt'] ?? 0.0).toDouble(),
      totalPaid: (map['totalPaid'] ?? 0.0).toDouble(),
      currentBalance: (map['currentBalance'] ?? 0.0).toDouble(),
      transactions: [],
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      isActive: (map['isActive'] ?? 1) == 1,
      createdBy: map['createdBy'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'agentId': agentId,
      'agentName': agentName,
      'agentPhone': agentPhone,
      'totalDebt': totalDebt,
      'totalPaid': totalPaid,
      'currentBalance': currentBalance,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isActive': isActive ? 1 : 0,
      'createdBy': createdBy,
    };
  }

  Map<String, dynamic> toFirestore() {
    return {
      'agentId': agentId,
      'agentName': agentName,
      'agentPhone': agentPhone,
      'totalDebt': totalDebt,
      'totalPaid': totalPaid,
      'currentBalance': currentBalance,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isActive': isActive,
      'createdBy': createdBy,
    };
  }

  AgentAccountModel copyWith({
    String? id,
    String? agentId,
    String? agentName,
    String? agentPhone,
    double? totalDebt,
    double? totalPaid,
    double? currentBalance,
    List<AgentTransaction>? transactions,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    String? createdBy,
  }) {
    return AgentAccountModel(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      agentPhone: agentPhone ?? this.agentPhone,
      totalDebt: totalDebt ?? this.totalDebt,
      totalPaid: totalPaid ?? this.totalPaid,
      currentBalance: currentBalance ?? this.currentBalance,
      transactions: transactions ?? this.transactions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
    );
  }
}

/// نموذج مؤقت لمعاملة الوكيل
class AgentTransaction {
  final String id;
  final String type;
  final double amount;
  final String description;
  final DateTime timestamp;
  final String? invoiceId;
  final String? itemId;
  final String? createdBy;

  AgentTransaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.description,
    required this.timestamp,
    this.invoiceId,
    this.itemId,
    this.createdBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type,
      'amount': amount,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'invoiceId': invoiceId,
      'itemId': itemId,
      'createdBy': createdBy,
    };
  }
}
