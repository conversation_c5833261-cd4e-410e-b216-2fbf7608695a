import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/utils/app_utils.dart';
import '../../models/professional_agent_account.dart';
import '../../services/professional_agent_service.dart';
import 'agent_payment_screen.dart';
import 'modern_agent_details_screen.dart';

/// شاشة إدارة الوكلاء العصرية
/// تصميم حديث مع تبويبات منظمة ومفصلة
class ModernAgentManagementScreen extends StatefulWidget {
  const ModernAgentManagementScreen({super.key});

  @override
  State<ModernAgentManagementScreen> createState() => _ModernAgentManagementScreenState();
}

class _ModernAgentManagementScreenState extends State<ModernAgentManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ProfessionalAgentService _agentService = ProfessionalAgentService();
  
  List<ProfessionalAgentAccount> _allAccounts = [];
  List<ProfessionalAgentAccount> _filteredAccounts = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedFilter = 'all';

  // إحصائيات سريعة
  double _totalBalance = 0.0;
  double _totalDebt = 0.0;
  double _totalCredits = 0.0;
  int _activeAgents = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accounts = await _agentService.getAllAgentAccounts();
      setState(() {
        _allAccounts = accounts;
        _filteredAccounts = accounts;
        _calculateStatistics();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    }
  }

  void _calculateStatistics() {
    _totalBalance = 0.0;
    _totalDebt = 0.0;
    _totalCredits = 0.0;
    _activeAgents = 0;

    for (final account in _allAccounts) {
      if (account.currentBalance >= 0) {
        _totalCredits += account.currentBalance;
      } else {
        _totalDebt += account.currentBalance.abs();
      }
      _totalBalance += account.currentBalance;
      
      if (account.isActive) {
        _activeAgents++;
      }
    }
  }

  void _filterAccounts() {
    setState(() {
      _filteredAccounts = _allAccounts.where((account) {
        // فلترة البحث
        final matchesSearch = _searchQuery.isEmpty ||
            account.agentName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            (account.agentPhone?.contains(_searchQuery) ?? false);

        // فلترة الحالة
        final matchesFilter = _selectedFilter == 'all' ||
            (_selectedFilter == 'active' && account.isActive) ||
            (_selectedFilter == 'debt' && account.currentBalance < 0) ||
            (_selectedFilter == 'credit' && account.currentBalance > 0) ||
            (_selectedFilter == 'zero' && account.currentBalance == 0);

        return matchesSearch && matchesFilter;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildStatisticsCards(),
          _buildSearchAndFilter(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAgentsListTab(),
                _buildDebtorsTab(),
                _buildCreditorsTab(),
                _buildAnalyticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'إدارة الوكلاء',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadData,
        ),
        IconButton(
          icon: const Icon(Icons.analytics),
          onPressed: () {
            _tabController.animateTo(3);
          },
        ),
      ],
    );
  }

  Widget _buildStatisticsCards() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي الرصيد',
              '${_totalBalance.toStringAsFixed(0)} ج.م',
              Icons.account_balance_wallet,
              _totalBalance >= 0 ? Colors.green : Colors.red,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'المديونية',
              '${_totalDebt.toStringAsFixed(0)} ج.م',
              Icons.trending_down,
              Colors.red,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'الأرصدة الدائنة',
              '${_totalCredits.toStringAsFixed(0)} ج.م',
              Icons.trending_up,
              Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'الوكلاء النشطون',
              '$_activeAgents',
              Icons.people,
              AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              _filterAccounts();
            },
            decoration: InputDecoration(
              hintText: 'البحث عن وكيل...',
              prefixIcon: const Icon(Icons.search, color: AppColors.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // فلاتر سريعة
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('الكل', 'all'),
                const SizedBox(width: 8),
                _buildFilterChip('نشط', 'active'),
                const SizedBox(width: 8),
                _buildFilterChip('مديون', 'debt'),
                const SizedBox(width: 8),
                _buildFilterChip('دائن', 'credit'),
                const SizedBox(width: 8),
                _buildFilterChip('صفر', 'zero'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : AppColors.primary,
          fontSize: 12,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
        _filterAccounts();
      },
      backgroundColor: Colors.white,
      selectedColor: AppColors.primary,
      checkmarkColor: Colors.white,
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
        unselectedLabelStyle: const TextStyle(fontSize: 12),
        tabs: const [
          Tab(
            icon: Icon(Icons.people, size: 20),
            text: 'جميع الوكلاء',
          ),
          Tab(
            icon: Icon(Icons.trending_down, size: 20),
            text: 'المديونون',
          ),
          Tab(
            icon: Icon(Icons.trending_up, size: 20),
            text: 'الدائنون',
          ),
          Tab(
            icon: Icon(Icons.analytics, size: 20),
            text: 'التحليلات',
          ),
        ],
      ),
    );
  }

  Widget _buildAgentsListTab() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل بيانات الوكلاء...'),
          ],
        ),
      );
    }

    if (_filteredAccounts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty ? 'لا توجد نتائج للبحث' : 'لا توجد حسابات وكلاء',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredAccounts.length,
      itemBuilder: (context, index) {
        final account = _filteredAccounts[index];
        return _buildModernAgentCard(account);
      },
    );
  }

  Widget _buildDebtorsTab() {
    final debtors = _allAccounts.where((account) => account.currentBalance < 0).toList();

    if (debtors.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.sentiment_satisfied, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text(
              'لا توجد ديون!',
              style: TextStyle(fontSize: 18, color: Colors.green),
            ),
            Text(
              'جميع الوكلاء لديهم أرصدة إيجابية',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: debtors.length,
      itemBuilder: (context, index) {
        final account = debtors[index];
        return _buildDebtorCard(account);
      },
    );
  }

  Widget _buildCreditorsTab() {
    final creditors = _allAccounts.where((account) => account.currentBalance > 0).toList();

    if (creditors.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.account_balance_wallet_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد أرصدة دائنة',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: creditors.length,
      itemBuilder: (context, index) {
        final account = creditors[index];
        return _buildCreditorCard(account);
      },
    );
  }

  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildAnalyticsCard(
            'توزيع الأرصدة',
            Icons.pie_chart,
            [
              _buildAnalyticsRow('إجمالي الوكلاء', '${_allAccounts.length}'),
              _buildAnalyticsRow('الوكلاء النشطون', '$_activeAgents'),
              _buildAnalyticsRow('المديونون', '${_allAccounts.where((a) => a.currentBalance < 0).length}'),
              _buildAnalyticsRow('الدائنون', '${_allAccounts.where((a) => a.currentBalance > 0).length}'),
            ],
          ),

          const SizedBox(height: 16),

          _buildAnalyticsCard(
            'الإحصائيات المالية',
            Icons.monetization_on,
            [
              _buildAnalyticsRow('إجمالي المبيعات', '${_allAccounts.fold(0.0, (sum, a) => sum + a.totalCustomerSales).toStringAsFixed(0)} ج.م'),
              _buildAnalyticsRow('إجمالي العمولات', '${_allAccounts.fold(0.0, (sum, a) => sum + a.totalAgentCommission).toStringAsFixed(0)} ج.م'),
              _buildAnalyticsRow('إجمالي المدفوعات', '${_allAccounts.fold(0.0, (sum, a) => sum + a.totalPaymentsReceived).toStringAsFixed(0)} ج.م'),
              _buildAnalyticsRow('متوسط الرصيد', '${(_totalBalance / (_allAccounts.isNotEmpty ? _allAccounts.length : 1)).toStringAsFixed(0)} ج.م'),
            ],
          ),

          const SizedBox(height: 16),

          _buildAnalyticsCard(
            'أداء الوكلاء',
            Icons.trending_up,
            [
              _buildAnalyticsRow('أعلى مبيعات', '${_allAccounts.isNotEmpty ? _allAccounts.map((a) => a.totalCustomerSales).reduce((a, b) => a > b ? a : b).toStringAsFixed(0) : '0'} ج.م'),
              _buildAnalyticsRow('أعلى عمولة', '${_allAccounts.isNotEmpty ? _allAccounts.map((a) => a.totalAgentCommission).reduce((a, b) => a > b ? a : b).toStringAsFixed(0) : '0'} ج.م'),
              _buildAnalyticsRow('متوسط المعاملات', '${_allAccounts.isNotEmpty ? (_allAccounts.fold(0, (sum, a) => sum + a.transactionCount) / _allAccounts.length).toStringAsFixed(1) : '0'}'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModernAgentCard(ProfessionalAgentAccount account) {
    final balanceColor = account.currentBalance >= 0 ? Colors.green : Colors.red;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _openAgentDetails(account),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // الصف الأول: معلومات الوكيل
              Row(
                children: [
                  // صورة الوكيل
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.7)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Center(
                      child: Text(
                        account.agentName.isNotEmpty ? account.agentName[0] : 'و',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // معلومات الوكيل
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          account.agentName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        if (account.agentPhone != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            account.agentPhone!,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // الرصيد
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: balanceColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: balanceColor.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      '${account.currentBalance.toStringAsFixed(0)} ج.م',
                      style: TextStyle(
                        color: balanceColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: _buildActionButton(
                      'كشف الحساب',
                      Icons.receipt_long,
                      AppColors.primary,
                      () => _openAgentDetails(account),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildActionButton(
                      'تسجيل دفعة',
                      Icons.payment,
                      Colors.green,
                      () => _addPayment(account),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDebtorCard(ProfessionalAgentAccount account) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.red, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        account.agentName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        'مديونية: ${account.currentBalance.abs().toStringAsFixed(0)} ج.م',
                        style: const TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildActionButton(
                  'تسجيل دفعة',
                  Icons.payment,
                  Colors.red,
                  () => _addPayment(account),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreditorCard(ProfessionalAgentAccount account) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.account_balance_wallet, color: Colors.green, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        account.agentName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        'رصيد دائن: ${account.currentBalance.toStringAsFixed(0)} ج.م',
                        style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildActionButton(
                  'كشف الحساب',
                  Icons.receipt_long,
                  Colors.green,
                  () => _openAgentDetails(account),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsCard(String title, IconData icon, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildAnalyticsRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 14),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 14),
      label: Text(
        label,
        style: const TextStyle(fontSize: 10),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () {
        AppUtils.showSnackBar(context, 'إضافة وكيل جديد قيد التطوير');
      },
      backgroundColor: AppColors.primary,
      child: const Icon(Icons.add, color: Colors.white),
    );
  }

  void _openAgentDetails(ProfessionalAgentAccount account) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ModernAgentDetailsScreen(account: account),
      ),
    );
  }

  void _addPayment(ProfessionalAgentAccount account) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AgentPaymentScreen(account: account),
      ),
    );

    // إذا تم تسجيل الدفعة بنجاح، أعد تحميل البيانات
    if (result == true) {
      _loadData();
    }
  }
}
