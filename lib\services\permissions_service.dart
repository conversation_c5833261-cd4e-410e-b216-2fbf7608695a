import '../models/user_model.dart';

/// Service for managing role-based permissions and UI visibility
class PermissionsService {
  static const String _superAdminRole = 'super_admin';
  static const String _adminRole = 'admin';
  static const String _agentRole = 'agent';
  static const String _showroomRole = 'showroom';

  /// Check if user can access a specific feature
  static bool canAccess(UserModel user, String feature) {
    switch (feature) {
      // Super Admin only features
      case 'user_management':
      case 'system_settings':
      case 'all_reports':
      case 'profit_reports':
      case 'company_settings':

        return user.role == _superAdminRole;

      // Admin and Super Admin features
      case 'inventory_management':
      case 'main_warehouse':
      case 'showroom_warehouse':
      case 'agent_warehouses':
      case 'transfer_goods':
      case 'agent_payments':
      case 'invoice_review':
      case 'document_status_update':
        return user.role == _superAdminRole || user.role == _adminRole;

      // Agent specific features
      case 'agent_inventory':
      case 'agent_sales':
      case 'agent_account':
      case 'agent_payments_view':
        return user.role == _agentRole;

      // Showroom specific features
      case 'showroom_inventory':
      case 'showroom_sales':
        return user.role == _showroomRole;

      // Common features (all roles)
      case 'create_customer_invoice':
      case 'view_own_invoices':
      case 'view_notifications':
      case 'document_tracking_view':
        return true;

      default:
        return false;
    }
  }

  /// Check if user can update document status
  static bool canUpdateDocumentStatus(UserModel user, String currentStatus, String newStatus) {
    // Super Admin and Admin can update any status
    if (user.role == _superAdminRole || user.role == _adminRole) {
      return true;
    }

    // Agent and Showroom can only update to final status
    if (user.role == _agentRole || user.role == _showroomRole) {
      return newStatus == 'ready_for_pickup';
    }

    return false;
  }

  /// Get available menu items for user role
  static List<MenuItem> getMenuItems(UserModel user) {
    List<MenuItem> items = [];

    // Dashboard (always visible)
    items.add(MenuItem(
      title: 'الرئيسية',
      icon: 'home',
      route: '/home',
      isVisible: true,
    ));

    // Sales
    if (canAccess(user, 'create_customer_invoice')) {
      items.add(MenuItem(
        title: 'المبيعات',
        icon: 'sales',
        route: '/sales',
        isVisible: true,
        children: [
          MenuItem(
            title: 'إنشاء فاتورة عميل',
            icon: 'invoice',
            route: '/sales/create-invoice',
            isVisible: true,
          ),
          MenuItem(
            title: 'عرض الفواتير',
            icon: 'list',
            route: '/sales/invoices',
            isVisible: true,
          ),
        ],
      ));
    }

    // Inventory Management
    if (canAccess(user, 'inventory_management')) {
      items.add(MenuItem(
        title: 'إدارة المخزون',
        icon: 'inventory',
        route: '/inventory',
        isVisible: true,
        children: [
          MenuItem(
            title: 'المخزن الرئيسي',
            icon: 'warehouse',
            route: '/inventory/main',
            isVisible: canAccess(user, 'main_warehouse'),
          ),
          MenuItem(
            title: 'مخزن المعرض',
            icon: 'showroom',
            route: '/inventory/showroom',
            isVisible: canAccess(user, 'showroom_warehouse'),
          ),
          MenuItem(
            title: 'مخازن الوكلاء',
            icon: 'agents',
            route: '/inventory/agents',
            isVisible: canAccess(user, 'agent_warehouses'),
          ),
          MenuItem(
            title: 'تحويل البضاعة',
            icon: 'transfer',
            route: '/inventory/transfer',
            isVisible: canAccess(user, 'transfer_goods'),
          ),
        ],
      ));
    }

    // Agent specific inventory
    if (canAccess(user, 'agent_inventory')) {
      items.add(MenuItem(
        title: 'مخزني',
        icon: 'my_inventory',
        route: '/agent/inventory',
        isVisible: true,
      ));
    }

    // Showroom specific inventory
    if (canAccess(user, 'showroom_inventory')) {
      items.add(MenuItem(
        title: 'مخزن المعرض',
        icon: 'showroom_inventory',
        route: '/showroom/inventory',
        isVisible: true,
      ));
    }

    // Agent Management (for admins)
    if (canAccess(user, 'agent_payments')) {
      items.add(MenuItem(
        title: 'إدارة الوكلاء',
        icon: 'agents_management',
        route: '/agents',
        isVisible: true,
        children: [
          MenuItem(
            title: 'حسابات الوكلاء',
            icon: 'accounts',
            route: '/agents/accounts',
            isVisible: true,
          ),
          MenuItem(
            title: 'تسجيل دفعات',
            icon: 'payments',
            route: '/agents/payments',
            isVisible: true,
          ),
          MenuItem(
            title: 'كشوف الحساب',
            icon: 'statements',
            route: '/agents/statements',
            isVisible: true,
          ),
        ],
      ));
    }

    // Agent account (for agents)
    if (canAccess(user, 'agent_account')) {
      items.add(MenuItem(
        title: 'حسابي',
        icon: 'my_account',
        route: '/agent/account',
        isVisible: true,
        children: [
          MenuItem(
            title: 'كشف الحساب',
            icon: 'statement',
            route: '/agent/statement',
            isVisible: true,
          ),
          MenuItem(
            title: 'دفعاتي',
            icon: 'my_payments',
            route: '/agent/payments',
            isVisible: true,
          ),
        ],
      ));
    }

    // Document Tracking
    items.add(MenuItem(
      title: 'تتبع الجوابات',
      icon: 'tracking',
      route: '/documents/tracking',
      isVisible: true,
    ));

    // Reports
    if (canAccess(user, 'all_reports')) {
      items.add(MenuItem(
        title: 'التقارير',
        icon: 'reports',
        route: '/reports',
        isVisible: true,
        children: [
          MenuItem(
            title: 'تقارير المبيعات',
            icon: 'sales_reports',
            route: '/reports/sales',
            isVisible: true,
          ),
          MenuItem(
            title: 'تقارير الأرباح',
            icon: 'profit_reports',
            route: '/reports/profits',
            isVisible: canAccess(user, 'profit_reports'),
          ),
          MenuItem(
            title: 'تقارير المخزون',
            icon: 'inventory_reports',
            route: '/reports/inventory',
            isVisible: true,
          ),
          MenuItem(
            title: 'استعلام العملاء الشامل',
            icon: 'customer_inquiry',
            route: '/reports/customer-inquiry',
            isVisible: true,
          ),
        ],
      ));
    }

    // User Management (Super Admin only)
    if (canAccess(user, 'user_management')) {
      items.add(MenuItem(
        title: 'إدارة المستخدمين',
        icon: 'users',
        route: '/users',
        isVisible: true,
        children: [
          MenuItem(
            title: 'إدارة الوكلاء',
            icon: 'agents_management',
            route: '/admin/agents',
            isVisible: true,
          ),
          MenuItem(
            title: 'جميع المستخدمين',
            icon: 'users',
            route: '/users',
            isVisible: true,
          ),
        ],
      ));
    }

    // Settings
    if (canAccess(user, 'system_settings')) {
      items.add(MenuItem(
        title: 'الإعدادات',
        icon: 'settings',
        route: '/settings',
        isVisible: true,
      ));
    }



    // Company Poster (Super Admin only)
    if (canAccess(user, 'system_settings')) {
      items.add(MenuItem(
        title: 'تحديث بوستر المؤسسة',
        icon: 'company_poster',
        route: '/admin/company-poster',
        isVisible: true,
      ));
    }

    // Notifications (always visible)
    items.add(MenuItem(
      title: 'الإشعارات',
      icon: 'notifications',
      route: '/notifications',
      isVisible: true,
    ));

    return items.where((item) => item.isVisible).toList();
  }

  /// Get warehouse access for user
  static List<String> getWarehouseAccess(UserModel user) {
    switch (user.role) {
      case _superAdminRole:
      case _adminRole:
        return ['main', 'showroom', 'agents'];
      case _agentRole:
        return ['agent_${user.id}'];
      case _showroomRole:
        return ['showroom'];
      default:
        return [];
    }
  }

  /// Check if user can view specific data
  static bool canViewData(UserModel user, String dataType, {String? ownerId}) {
    switch (dataType) {
      case 'all_invoices':
        return user.role == _superAdminRole || user.role == _adminRole;
      case 'own_invoices':
        return true;
      case 'agent_invoices':
        return user.role == _superAdminRole || user.role == _adminRole || 
               (user.role == _agentRole && ownerId == user.id);
      case 'profit_data':
        return user.role == _superAdminRole;
      case 'agent_accounts':
        return user.role == _superAdminRole || user.role == _adminRole;
      default:
        return false;
    }
  }
}

class MenuItem {
  final String title;
  final String icon;
  final String route;
  final bool isVisible;
  final List<MenuItem>? children;

  MenuItem({
    required this.title,
    required this.icon,
    required this.route,
    required this.isVisible,
    this.children,
  });
}
