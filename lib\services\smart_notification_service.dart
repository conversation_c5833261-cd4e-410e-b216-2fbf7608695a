import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/notification_model.dart';
import '../core/utils/app_utils.dart';
import '../core/constants/app_constants.dart';
import 'data_service.dart';
import 'auth_service.dart';

/// خدمة الإشعارات الذكية مع منع التكرار والتفاعل
class SmartNotificationService {
  static final SmartNotificationService _instance = SmartNotificationService._internal();
  factory SmartNotificationService() => _instance;
  SmartNotificationService._internal();

  final DataService _dataService = DataService.instance;
  final AuthService _authService = AuthService.instance;
  
  // تخزين مؤقت للإشعارات المرسلة لمنع التكرار
  final Set<String> _sentNotificationHashes = <String>{};
  
  // مفاتيح الإشعارات الفريدة لمنع التكرار
  final Map<String, DateTime> _notificationKeys = <String, DateTime>{};
  
  // مدة انتظار لمنع التكرار (5 دقائق)
  static const Duration _duplicatePreventionDuration = Duration(minutes: 5);

  /// إنشاء مفتاح فريد للإشعار لمنع التكرار
  String _createNotificationKey({
    required String type,
    required String targetUserId,
    required String? relatedId,
    Map<String, dynamic>? additionalData,
  }) {
    final keyData = {
      'type': type,
      'targetUserId': targetUserId,
      'relatedId': relatedId ?? '',
      'additionalData': additionalData?.toString() ?? '',
    };
    return AppUtils.generateHash(jsonEncode(keyData));
  }

  /// فحص إذا كان الإشعار مكرر
  bool _isDuplicateNotification(String notificationKey) {
    final lastSent = _notificationKeys[notificationKey];
    if (lastSent == null) return false;
    
    final timeDifference = DateTime.now().difference(lastSent);
    return timeDifference < _duplicatePreventionDuration;
  }

  /// تسجيل إشعار كمرسل
  void _markNotificationAsSent(String notificationKey) {
    _notificationKeys[notificationKey] = DateTime.now();
    
    // تنظيف المفاتيح القديمة
    _cleanupOldKeys();
  }

  /// تنظيف المفاتيح القديمة
  void _cleanupOldKeys() {
    final now = DateTime.now();
    _notificationKeys.removeWhere((key, timestamp) {
      return now.difference(timestamp) > _duplicatePreventionDuration;
    });
  }

  /// إرسال إشعار ذكي مع منع التكرار
  Future<bool> sendSmartNotification({
    required String title,
    required String message,
    required String type,
    required String targetUserId,
    String? relatedId,
    Map<String, dynamic>? data,
    bool forceOverrideDuplicate = false,
  }) async {
    try {
      // إنشاء مفتاح فريد للإشعار
      final notificationKey = _createNotificationKey(
        type: type,
        targetUserId: targetUserId,
        relatedId: relatedId,
        additionalData: data,
      );

      // فحص التكرار
      if (!forceOverrideDuplicate && _isDuplicateNotification(notificationKey)) {
        if (kDebugMode) {
          print('🚫 Duplicate notification prevented: $type for user $targetUserId');
        }
        return false;
      }

      // إنشاء الإشعار
      final notification = NotificationModel(
        id: AppUtils.generateId(),
        title: title,
        message: message,
        type: type,
        targetUserId: targetUserId,
        relatedId: relatedId,
        data: data,
        isRead: false,
        createdAt: DateTime.now(),
        createdBy: _authService.currentUser?.id ?? 'system',
      );

      // حفظ في قاعدة البيانات
      await _dataService.createNotification(notification);

      // تسجيل كمرسل
      _markNotificationAsSent(notificationKey);

      if (kDebugMode) {
        print('✅ Smart notification sent: $type to $targetUserId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to send smart notification: $e');
      }
      return false;
    }
  }

  /// إشعار تحويل البضاعة (محسن ضد التكرار)
  Future<void> sendTransferNotification({
    required String agentId,
    required String itemName,
    required String itemId,
    required String fromWarehouse,
    required String toWarehouse,
    required String transferType, // 'incoming' or 'outgoing'
  }) async {
    final isIncoming = transferType == 'incoming';
    
    final title = isIncoming 
        ? 'استلام بضاعة في مخزنك'
        : 'سحب بضاعة من مخزنك';
    
    final message = isIncoming
        ? 'تم تحويل $itemName إلى مخزنك من $fromWarehouse'
        : 'تم سحب $itemName من مخزنك إلى $toWarehouse';

    await sendSmartNotification(
      title: title,
      message: message,
      type: 'warehouse_transfer',
      targetUserId: agentId,
      relatedId: itemId,
      data: {
        'itemId': itemId,
        'itemName': itemName,
        'fromWarehouse': fromWarehouse,
        'toWarehouse': toWarehouse,
        'transferType': transferType,
        'action': 'view_warehouse_movements',
        'targetScreen': 'warehouse_movements',
      },
    );
  }

  /// إشعار دفعة جديدة
  Future<void> sendPaymentNotification({
    required String agentId,
    required String agentName,
    required double amount,
    required String paymentType,
    required String paymentId,
  }) async {
    await sendSmartNotification(
      title: 'تسجيل دفعة جديدة',
      message: 'تم تسجيل $paymentType بمبلغ ${AppUtils.formatCurrency(amount)}',
      type: 'payment_received',
      targetUserId: agentId,
      relatedId: paymentId,
      data: {
        'paymentId': paymentId,
        'agentId': agentId,
        'agentName': agentName,
        'amount': amount,
        'paymentType': paymentType,
        'action': 'view_account_statement',
        'targetScreen': 'agent_account',
      },
    );
  }

  /// إشعار فاتورة جديدة
  Future<void> sendInvoiceNotification({
    required String invoiceId,
    required String invoiceNumber,
    required String agentId,
    required String agentName,
    required String customerName,
    required double amount,
  }) async {
    // إشعار للمديرين
    final managers = await _dataService.getAllUsers();
    final managerUsers = managers.where((user) => 
        user.role == AppConstants.adminRole || 
        user.role == AppConstants.superAdminRole).toList();

    for (final manager in managerUsers) {
      await sendSmartNotification(
        title: 'فاتورة بيع جديدة',
        message: 'بيع جديد من الوكيل $agentName للعميل $customerName بمبلغ ${AppUtils.formatCurrency(amount)}',
        type: 'new_invoice',
        targetUserId: manager.id,
        relatedId: invoiceId,
        data: {
          'invoiceId': invoiceId,
          'invoiceNumber': invoiceNumber,
          'agentId': agentId,
          'agentName': agentName,
          'customerName': customerName,
          'amount': amount,
          'action': 'view_invoice_details',
          'targetScreen': 'document_tracking',
        },
      );
    }
  }

  /// إشعار تحديث حالة الجواب
  Future<void> sendDocumentStatusNotification({
    required String agentId,
    required String invoiceNumber,
    required String documentStatus,
    required String customerName,
  }) async {
    await sendSmartNotification(
      title: 'تحديث حالة الجواب',
      message: 'تم تحديث حالة جواب الفاتورة $invoiceNumber للعميل $customerName إلى: $documentStatus',
      type: 'document_status_update',
      targetUserId: agentId,
      relatedId: invoiceNumber,
      data: {
        'invoiceNumber': invoiceNumber,
        'documentStatus': documentStatus,
        'customerName': customerName,
        'action': 'view_document_tracking',
        'targetScreen': 'document_tracking',
      },
    );
  }

  /// إشعار خصم مديونية
  Future<void> sendDebtReductionNotification({
    required String agentId,
    required String agentName,
    required double amount,
    required String itemName,
    required String creditInvoiceId,
  }) async {
    await sendSmartNotification(
      title: 'خصم من المديونية',
      message: 'تم خصم ${AppUtils.formatCurrency(amount)} من مديونيتك نتيجة سحب $itemName من مخزنك',
      type: 'debt_reduction',
      targetUserId: agentId,
      relatedId: creditInvoiceId,
      data: {
        'creditInvoiceId': creditInvoiceId,
        'agentId': agentId,
        'agentName': agentName,
        'amount': amount,
        'itemName': itemName,
        'action': 'view_account_statement',
        'targetScreen': 'agent_account',
      },
    );
  }

  /// معالجة النقر على الإشعار (التفاعل)
  static void handleNotificationTap(BuildContext context, NotificationModel notification) {
    if (notification.data == null) return;

    final action = notification.data!['action'] as String?;
    final targetScreen = notification.data!['targetScreen'] as String?;

    if (kDebugMode) {
      print('🔔 Handling notification tap: $action -> $targetScreen');
    }

    switch (action) {
      case 'view_warehouse_movements':
        _navigateToWarehouseMovements(context, notification.data!);
        break;
      case 'view_account_statement':
        _navigateToAccountStatement(context, notification.data!);
        break;
      case 'view_invoice_details':
        _navigateToInvoiceDetails(context, notification.data!);
        break;
      case 'view_document_tracking':
        _navigateToDocumentTracking(context, notification.data!);
        break;
      default:
        _showNotificationDetails(context, notification);
    }
  }

  /// التنقل لشاشة حركات المخازن
  static void _navigateToWarehouseMovements(BuildContext context, Map<String, dynamic> data) {
    try {
      Navigator.pushNamed(context, '/inventory_movements');
      if (kDebugMode) {
        print('📦 Navigated to warehouse movements: ${data['itemId']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to navigate to warehouse movements: $e');
      }
      _showNotificationMessage(context, 'تم فتح شاشة حركات المخازن');
    }
  }

  /// التنقل لكشف حساب الوكيل
  static void _navigateToAccountStatement(BuildContext context, Map<String, dynamic> data) {
    try {
      Navigator.pushNamed(context, '/agent_account');
      if (kDebugMode) {
        print('💰 Navigated to account statement: ${data['agentId']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to navigate to account statement: $e');
      }
      _showNotificationMessage(context, 'تم فتح كشف حساب الوكيل');
    }
  }

  /// التنقل لتفاصيل الفاتورة
  static void _navigateToInvoiceDetails(BuildContext context, Map<String, dynamic> data) {
    try {
      Navigator.pushNamed(context, '/document_tracking');
      if (kDebugMode) {
        print('🧾 Navigated to invoice details: ${data['invoiceId']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to navigate to invoice details: $e');
      }
      _showNotificationMessage(context, 'تم فتح تفاصيل الفاتورة');
    }
  }

  /// التنقل لتتبع الجوابات
  static void _navigateToDocumentTracking(BuildContext context, Map<String, dynamic> data) {
    try {
      Navigator.pushNamed(context, '/document_tracking');
      if (kDebugMode) {
        print('📄 Navigated to document tracking: ${data['invoiceNumber']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to navigate to document tracking: $e');
      }
      _showNotificationMessage(context, 'تم فتح تتبع الجوابات');
    }
  }

  /// عرض رسالة للمستخدم
  static void _showNotificationMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// عرض تفاصيل الإشعار
  static void _showNotificationDetails(BuildContext context, NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Text(notification.message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
