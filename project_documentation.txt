===============================================================================
                    وثائق مشروع نظام إدارة النقل - شركة آل فرحان
                          El Farhan Transport Management System
===============================================================================

📋 معلومات المشروع الأساسية:
===============================================================================
اسم المشروع: نظام إدارة النقل الشامل - شركة آل فرحان
النوع: تطبيق Flutter للأندرويد (النسخة الأصلية)
اللغة: Dart/Flutter
قاعدة البيانات: Firebase Firestore + SQLite المحلية
التخزين السحابي: Cloudinary للصور
الإشعارات: Firebase Cloud Messaging
الهوية: Firebase Authentication

🎯 الهدف من المشروع:
===============================================================================
نظام إدارة شامل لشركة نقل يتضمن:
- إدارة المخزون والأصناف
- إدارة المبيعات والفواتير
- إدارة الوكلاء وحساباتهم
- نظام التقارير والإحصائيات
- تتبع الوثائق والمستندات
- إدارة المخازن والتحويلات
- نظام الإشعارات المتقدم

👥 أنواع المستخدمين والصلاحيات:
===============================================================================

1. المدير الأعلى (super_admin):
   - صلاحية كاملة على جميع أجزاء النظام
   - إدارة المستخدمين والصلاحيات
   - الوصول لجميع التقارير والإحصائيات
   - إدارة إعدادات النظام

2. الموظف الإداري (admin):
   - إدارة المخزون والأصناف
   - إنشاء وإدارة الفواتير
   - إدارة حسابات الوكلاء
   - الوصول للتقارير الأساسية

3. الوكيل (agent):
   - عرض حساباته مع الشركة
   - تسجيل المبيعات الخاصة به
   - عرض كشف حساب مفصل
   - تحميل تقارير PDF

4. مستخدم المعرض (showroom_user):
   - إدارة مخزون المعرض
   - تسجيل مبيعات المعرض
   - عرض تقارير المعرض

🏗️ هيكل المشروع والمجلدات:
===============================================================================

lib/
├── core/                          # الملفات الأساسية
│   ├── constants/                 # الثوابت والإعدادات
│   ├── theme/                     # ألوان وتصميم التطبيق
│   ├── utils/                     # الأدوات المساعدة
│   └── network/                   # إعدادات الشبكة
├── models/                        # نماذج البيانات
├── services/                      # الخدمات والمنطق
│   ├── android/                   # خدمات خاصة بالأندرويد
│   └── firebase/                  # خدمات Firebase
├── providers/                     # مزودي البيانات (Provider)
├── screens/                       # شاشات التطبيق
│   ├── auth/                      # شاشات المصادقة
│   ├── home/                      # الشاشة الرئيسية
│   ├── inventory/                 # إدارة المخزون
│   ├── sales/                     # إدارة المبيعات
│   ├── agents/                    # إدارة الوكلاء
│   ├── reports/                   # التقارير
│   ├── admin/                     # إدارة النظام
│   ├── documents/                 # إدارة الوثائق
│   └── notifications/             # الإشعارات
└── widgets/                       # العناصر المشتركة

📱 الشاشات الرئيسية والوظائف:
===============================================================================

1. شاشة تسجيل الدخول (LoginScreen):
   ────────────────────────────────────
   الوظائف:
   - تسجيل دخول ذكي بالبريد الإلكتروني أو اسم المستخدم
   - تذكر بيانات الدخول
   - مصادقة Firebase
   - حفظ الجلسة محلياً
   - دعم تسجيل الدخول التلقائي

2. الشاشة الرئيسية (HomeScreen):
   ────────────────────────────────
   الوظائف:
   - لوحة تحكم شاملة
   - إحصائيات سريعة
   - قائمة جانبية للتنقل
   - شريط علوي مع الإشعارات
   - أزرار سريعة للعمليات الشائعة

3. شاشات إدارة المخزون:
   ────────────────────────
   
   أ) شاشة المخزون الرئيسية (InventoryScreen):
      - عرض جميع الأصناف
      - البحث والفلترة
      - إحصائيات المخزون
      - تنبيهات النفاد
   
   ب) شاشة إضافة صنف (AddItemScreen):
      - إدخال بيانات الصنف
      - التقاط صور بصمة الموتور
      - التقاط صور رقم الشاسية
      - استخراج النصوص بـ OCR
      - رفع الصور للسحابة
   
   ج) شاشة تحويل البضائع (TransferGoodsScreen):
      - تحويل بين المخازن
      - تتبع عمليات التحويل
      - تأكيد الاستلام

4. شاشات إدارة المبيعات:
   ─────────────────────────
   
   أ) شاشة المبيعات (SalesScreen):
      - عرض جميع الفواتير
      - إحصائيات المبيعات
      - البحث والفلترة
   
   ب) شاشة إنشاء فاتورة (CreateInvoiceScreen):
      - إنشاء فاتورة جديدة
      - إضافة أصناف للفاتورة
      - حساب الإجماليات
      - طباعة الفاتورة
      - تصوير هوية العميل
      - استخراج بيانات العميل

5. شاشات إدارة الوكلاء:
   ──────────────────────
   
   أ) الإدارة الاحترافية للوكلاء (ProfessionalAgentManagementScreen):
      - عرض جميع حسابات الوكلاء
      - إحصائيات مفصلة لكل وكيل
      - تسجيل الدفعات
      - تصدير كشوف الحساب PDF
      - إدارة الائتمان والديون
   
   ب) شاشة كشف حساب الوكيل (AgentAccountDetailScreen):
      - كشف حساب مفصل
      - تاريخ جميع المعاملات
      - الرصيد الحالي
      - تصدير PDF بالعربية

6. شاشات التقارير والإحصائيات:
   ─────────────────────────────
   
   أ) شاشة التقارير (ReportsScreen):
      - تقارير المبيعات
      - تقارير المخزون
      - تقارير الوكلاء
      - تقارير مالية
   
   ب) شاشة استعلام العملاء (CustomerInquiryScreen):
      - البحث عن العملاء
      - تاريخ المشتريات
      - تحميل الصور والوثائق

7. شاشات إدارة النظام:
   ────────────────────
   
   أ) إعدادات المدير (AdminSettingsScreen):
      - إدارة المستخدمين
      - إعدادات النظام
      - النسخ الاحتياطي
   
   ب) إدارة المخازن (WarehouseManagementScreen):
      - إنشاء وتعديل المخازن
      - تعيين المسؤولين
      - إعدادات المخزن

8. شاشات الوثائق:
   ───────────────
   
   أ) تتبع الوثائق (DocumentTrackingScreen):
      - تتبع حالة الوثائق
      - رفع المستندات
      - تحديث الحالات
   
   ب) لوحة الوثائق (DocumentDashboardScreen):
      - إحصائيات الوثائق
      - التنبيهات والمواعيد

9. شاشة الإشعارات المتقدمة (EnhancedNotificationsScreen):
   ──────────────────────────────────────────────────
   - عرض جميع الإشعارات
   - تصنيف الإشعارات
   - إشعارات فورية
   - إشعارات مجدولة

🗄️ نماذج البيانات الرئيسية:
===============================================================================

1. نموذج المستخدم (UserModel):
   ─────────────────────────
   - id: معرف المستخدم
   - name: الاسم
   - email: البريد الإلكتروني
   - role: الدور (super_admin, admin, agent, showroom_user)
   - warehouseId: معرف المخزن المخصص
   - isActive: حالة النشاط
   - createdAt: تاريخ الإنشاء

2. نموذج الصنف (ItemModel):
   ─────────────────────
   - id: معرف الصنف
   - name: اسم الصنف
   - description: الوصف
   - category: الفئة
   - price: السعر
   - quantity: الكمية
   - motorFingerprintImage: صورة بصمة الموتور
   - chassisImage: صورة رقم الشاسية
   - motorFingerprint: رقم بصمة الموتور
   - chassisNumber: رقم الشاسية
   - warehouseId: معرف المخزن

3. نموذج الفاتورة (InvoiceModel):
   ──────────────────────────
   - id: معرف الفاتورة
   - invoiceNumber: رقم الفاتورة
   - customerId: معرف العميل
   - customerName: اسم العميل
   - items: قائمة الأصناف
   - totalAmount: المبلغ الإجمالي
   - discount: الخصم
   - tax: الضريبة
   - paymentMethod: طريقة الدفع
   - status: حالة الفاتورة
   - createdAt: تاريخ الإنشاء
   - createdBy: منشئ الفاتورة

4. نموذج حساب الوكيل الاحترافي (ProfessionalAgentAccount):
   ──────────────────────────────────────────────────────
   - id: معرف الحساب
   - agentId: معرف الوكيل
   - agentName: اسم الوكيل
   - totalCustomerSales: إجمالي مبيعات العملاء
   - totalPaymentsReceived: إجمالي المدفوعات المستلمة
   - totalGoodsWithdrawn: إجمالي البضاعة المسحوبة
   - totalAgentCommission: إجمالي عمولة الوكيل
   - currentBalance: الرصيد الحالي
   - creditLimit: حد الائتمان
   - transactions: قائمة المعاملات
   - lastTransactionDate: تاريخ آخر معاملة

5. نموذج معاملة الوكيل (ProfessionalAgentTransaction):
   ─────────────────────────────────────────────────
   - id: معرف المعاملة
   - type: نوع المعاملة (sale, payment, withdrawal, commission)
   - amount: المبلغ
   - description: الوصف
   - referenceId: معرف المرجع
   - createdAt: تاريخ الإنشاء
   - createdBy: منشئ المعاملة

6. نموذج المخزن (WarehouseModel):
   ──────────────────────────
   - id: معرف المخزن
   - name: اسم المخزن
   - location: الموقع
   - managerId: معرف المدير
   - type: نوع المخزن (main, agent, showroom)
   - isActive: حالة النشاط

🔄 الخدمات الرئيسية:
===============================================================================

1. خدمة البيانات (DataService):
   ────────────────────────
   - إدارة جميع عمليات قاعدة البيانات
   - المزامنة مع Firebase
   - التخزين المحلي
   - إدارة المستخدمين والأصناف والفواتير

2. خدمة الوكلاء الاحترافية (ProfessionalAgentService):
   ─────────────────────────────────────────────────
   - إدارة حسابات الوكلاء
   - حساب الأرصدة والعمولات
   - تسجيل المعاملات
   - إنشاء كشوف الحساب

3. خدمة الصور للأندرويد (AndroidImageService):
   ──────────────────────────────────────────
   - التقاط الصور من الكاميرا
   - اختيار الصور من المعرض
   - رفع الصور لـ Cloudinary
   - ضغط وتحسين الصور

4. خدمة OCR للأندرويد (AndroidOcrService):
   ─────────────────────────────────────
   - استخراج النصوص من الصور
   - التعرف على النصوص العربية والإنجليزية
   - استخراج أرقام بصمة الموتور
   - استخراج أرقام الشاسية

5. خدمة Firebase (FirebaseService):
   ──────────────────────────────
   - إدارة الاتصال بـ Firebase
   - المصادقة والأمان
   - المزامنة التلقائية
   - إدارة الإشعارات

6. خدمة المزامنة التلقائية (AutoSyncService):
   ──────────────────────────────────────────
   - مزامنة دورية للبيانات
   - مزامنة عند الاتصال بالإنترنت
   - حل تعارضات البيانات
   - النسخ الاحتياطي التلقائي

7. خدمة الإشعارات المتقدمة (EnhancedNotificationService):
   ────────────────────────────────────────────────────
   - إرسال الإشعارات المحلية
   - استقبال إشعارات Firebase
   - جدولة الإشعارات
   - تصنيف الإشعارات

8. خدمة الأمان (SecurityService):
   ──────────────────────────
   - تشفير البيانات الحساسة
   - إدارة الجلسات
   - التحقق من الصلاحيات
   - حماية البيانات المحلية

🔗 علاقات البيانات:
===============================================================================

1. علاقة المستخدمين والمخازن:
   ─────────────────────────
   - كل مستخدم مرتبط بمخزن واحد (One-to-One)
   - المخزن الواحد يمكن أن يكون له عدة مستخدمين (One-to-Many)
   - الوكلاء لهم مخازن خاصة بهم

2. علاقة الأصناف والمخازن:
   ─────────────────────
   - كل صنف موجود في مخزن محدد (Many-to-One)
   - المخزن الواحد يحتوي على عدة أصناف (One-to-Many)
   - تتبع الكميات لكل مخزن

3. علاقة الفواتير والأصناف:
   ─────────────────────
   - الفاتورة الواحدة تحتوي على عدة أصناف (One-to-Many)
   - الصنف الواحد يمكن أن يكون في عدة فواتير (Many-to-Many)
   - تخزين الكمية والسعر لكل صنف في الفاتورة

4. علاقة الوكلاء والمعاملات:
   ─────────────────────
   - كل وكيل له حساب واحد (One-to-One)
   - الحساب الواحد له عدة معاملات (One-to-Many)
   - المعاملات مرتبطة بالفواتير والدفعات

5. علاقة المستخدمين والفواتير:
   ──────────────────────
   - كل فاتورة منشأة بواسطة مستخدم (Many-to-One)
   - المستخدم الواحد ينشئ عدة فواتير (One-to-Many)
   - تتبع منشئ كل فاتورة

⚙️ العمليات الرئيسية:
===============================================================================

1. عمليات إدارة المخزون:
   ──────────────────────
   - إضافة صنف جديد
   - تعديل بيانات الصنف
   - حذف صنف
   - البحث والفلترة
   - تحديث الكميات
   - تحويل بين المخازن
   - تتبع حركة المخزون

2. عمليات المبيعات:
   ─────────────────
   - إنشاء فاتورة جديدة
   - إضافة أصناف للفاتورة
   - حساب الإجماليات والضرائب
   - تطبيق الخصومات
   - حفظ الفاتورة
   - طباعة الفاتورة
   - إلغاء الفاتورة

3. عمليات إدارة الوكلاء:
   ──────────────────────
   - إنشاء حساب وكيل جديد
   - تسجيل مبيعة للوكيل
   - تسجيل دفعة من الوكيل
   - تسجيل سحب بضاعة
   - حساب العمولات
   - إنشاء كشف حساب
   - تصدير PDF

4. عمليات التقارير:
   ─────────────────
   - تقرير المبيعات اليومية
   - تقرير المخزون
   - تقرير أداء الوكلاء
   - تقرير الأرباح والخسائر
   - تقرير حركة المخزون
   - تصدير التقارير PDF/Excel

5. عمليات المزامنة:
   ─────────────────
   - مزامنة البيانات مع Firebase
   - رفع البيانات المحلية
   - تحميل البيانات الجديدة
   - حل تعارضات البيانات
   - النسخ الاحتياطي

6. عمليات الأمان:
   ──────────────
   - تسجيل الدخول
   - التحقق من الصلاحيات
   - تشفير البيانات
   - إدارة الجلسات
   - تسجيل الخروج

🔧 التقنيات المستخدمة:
===============================================================================

1. Frontend:
   ─────────
   - Flutter 3.5+
   - Dart 3.5+
   - Material Design
   - Provider للإدارة الحالة

2. Backend:
   ─────────
   - Firebase Firestore
   - Firebase Authentication
   - Firebase Cloud Messaging
   - Firebase Storage

3. قاعدة البيانات المحلية:
   ──────────────────────
   - SQLite
   - sqflite package

4. معالجة الصور:
   ──────────────
   - image_picker للكاميرا والمعرض
   - Google ML Kit للـ OCR
   - Cloudinary للتخزين السحابي

5. إنشاء PDF:
   ───────────
   - pdf package
   - دعم الخطوط العربية
   - تخطيط RTL

6. الإشعارات:
   ───────────
   - Firebase Cloud Messaging
   - flutter_local_notifications

📊 إحصائيات المشروع:
===============================================================================
- عدد الشاشات: 25+ شاشة
- عدد النماذج: 15+ نموذج
- عدد الخدمات: 12+ خدمة
- عدد العمليات: 50+ عملية
- دعم اللغات: العربية والإنجليزية
- المنصات المدعومة: Android (النسخة الأصلية)

🚀 الميزات المتقدمة:
===============================================================================
- نظام OCR متقدم لاستخراج النصوص
- تصدير PDF بالعربية
- مزامنة تلقائية ذكية
- نظام إشعارات متقدم
- إدارة احترافية للوكلاء
- تتبع شامل للمخزون
- تقارير تفصيلية
- أمان متقدم للبيانات
- واجهة مستخدم عربية
- دعم العمل بدون إنترنت

🔍 تفاصيل الشاشات المتقدمة:
===============================================================================

1. شاشة إضافة الأصناف - تفاصيل متقدمة:
   ────────────────────────────────────

   أ) قسم البيانات الأساسية:
      - اسم الصنف (مطلوب)
      - الوصف التفصيلي
      - الفئة (قائمة منسدلة)
      - السعر (مع التحقق من صحة الإدخال)
      - الكمية الأولية
      - الحد الأدنى للتنبيه
      - وحدة القياس
      - الباركود (اختياري)

   ب) قسم الصور والتوثيق:
      - صورة بصمة الموتور:
        * التقاط من الكاميرا
        * اختيار من المعرض
        * معاينة الصورة
        * استخراج رقم البصمة تلقائياً
        * التحقق من جودة الصورة

      - صورة رقم الشاسية:
        * التقاط من الكاميرا
        * اختيار من المعرض
        * معاينة الصورة
        * استخراج رقم الشاسية تلقائياً
        * التحقق من وضوح الرقم

   ج) قسم OCR المتقدم:
      - خوارزمية التعرف على النصوص العربية والإنجليزية
      - تحسين جودة الصورة قبل المعالجة
      - فلترة النتائج وإزالة الضوضاء
      - التحقق من صحة الأرقام المستخرجة
      - إمكانية التعديل اليدوي

   د) قسم الحفظ والتحقق:
      - التحقق من اكتمال البيانات
      - رفع الصور للسحابة
      - حفظ البيانات محلياً
      - مزامنة مع Firebase
      - رسائل تأكيد النجاح

2. شاشة إنشاء الفاتورة - تفاصيل متقدمة:
   ─────────────────────────────────────

   أ) قسم بيانات العميل:
      - اسم العميل (مطلوب)
      - رقم الهاتف
      - العنوان
      - البريد الإلكتروني (اختياري)
      - تصوير هوية العميل:
        * الوجه الأمامي
        * الوجه الخلفي
        * استخراج البيانات تلقائياً
        * التحقق من صحة البيانات

   ب) قسم الأصناف:
      - البحث في الأصناف المتاحة
      - إضافة صنف للفاتورة
      - تحديد الكمية
      - تطبيق خصم على الصنف
      - حساب الإجمالي الفرعي
      - إزالة صنف من الفاتورة
      - التحقق من توفر الكمية

   ج) قسم الحسابات:
      - الإجمالي الفرعي
      - الخصم العام (نسبة أو مبلغ ثابت)
      - الضريبة (قابلة للتخصيص)
      - الإجمالي النهائي
      - المبلغ المدفوع
      - المبلغ المتبقي

   د) قسم الدفع:
      - طريقة الدفع (نقدي، بطاقة، تحويل، آجل)
      - تفاصيل الدفع
      - تاريخ الاستحقاق (للدفع الآجل)
      - ملاحظات الدفع

   هـ) قسم الطباعة والحفظ:
      - معاينة الفاتورة
      - طباعة الفاتورة
      - إرسال بالبريد الإلكتروني
      - حفظ كـ PDF
      - مشاركة الفاتورة

3. شاشة إدارة الوكلاء الاحترافية - تفاصيل متقدمة:
   ──────────────────────────────────────────────

   أ) لوحة المعلومات الرئيسية:
      - عدد الوكلاء النشطين
      - إجمالي المبيعات الشهرية
      - إجمالي المستحقات
      - إجمالي العمولات
      - مؤشرات الأداء الرئيسية

   ب) قائمة الوكلاء:
      - عرض جدولي مع:
        * اسم الوكيل
        * رقم الهاتف
        * المنطقة
        * الرصيد الحالي
        * آخر معاملة
        * حالة الحساب
      - فلترة حسب:
        * المنطقة
        * حالة الحساب
        * نوع الوكيل
        * تاريخ التسجيل
      - ترتيب حسب:
        * الاسم
        * الرصيد
        * تاريخ آخر معاملة
        * حجم المبيعات

   ج) تفاصيل حساب الوكيل:
      - المعلومات الشخصية
      - ملخص الحساب:
        * إجمالي المبيعات
        * إجمالي المدفوعات
        * إجمالي السحوبات
        * العمولات المستحقة
        * الرصيد الحالي
      - تاريخ المعاملات:
        * المبيعات
        * الدفعات
        * السحوبات
        * العمولات
      - الرسوم البيانية:
        * أداء المبيعات الشهرية
        * توزيع أنواع المعاملات
        * مقارنة الأداء

   د) عمليات الحساب:
      - تسجيل دفعة جديدة:
        * المبلغ
        * طريقة الدفع
        * تاريخ الدفع
        * ملاحظات
        * إيصال الدفع
      - تسجيل سحب بضاعة:
        * الأصناف المسحوبة
        * الكميات
        * القيم
        * تاريخ السحب
        * ملاحظات
      - تعديل الرصيد:
        * سبب التعديل
        * المبلغ
        * الموافقات المطلوبة

   هـ) التقارير والتصدير:
      - كشف حساب مفصل
      - تقرير المبيعات
      - تقرير الدفعات
      - تصدير PDF بالعربية:
        * رأس الشركة
        * بيانات الوكيل
        * ملخص الحساب
        * تفاصيل المعاملات
        * الرسوم البيانية
        * التوقيعات والأختام

4. شاشة التقارير والإحصائيات - تفاصيل متقدمة:
   ──────────────────────────────────────────────

   أ) تقارير المبيعات:
      - تقرير المبيعات اليومية:
        * عدد الفواتير
        * إجمالي المبيعات
        * متوسط قيمة الفاتورة
        * أفضل الأصناف مبيعاً
        * توزيع طرق الدفع

      - تقرير المبيعات الشهرية:
        * مقارنة بالشهر السابق
        * نمو المبيعات
        * الاتجاهات والتوقعات
        * تحليل الموسمية

      - تقرير المبيعات السنوية:
        * الأداء السنوي
        * مقارنة بالسنوات السابقة
        * تحليل النمو
        * التوقعات المستقبلية

   ب) تقارير المخزون:
      - تقرير حالة المخزون:
        * الأصناف المتوفرة
        * الأصناف المنخفضة
        * الأصناف المنتهية
        * قيمة المخزون الإجمالية

      - تقرير حركة المخزون:
        * الوارد والصادر
        * معدل دوران المخزون
        * الأصناف بطيئة الحركة
        * تحليل ABC للمخزون

      - تقرير التحويلات:
        * التحويلات بين المخازن
        * الأصناف المحولة
        * أسباب التحويل
        * حالة التحويلات

   ج) تقارير الوكلاء:
      - تقرير أداء الوكلاء:
        * ترتيب الوكلاء حسب المبيعات
        * معدل نمو كل وكيل
        * الوكلاء الأكثر نشاطاً
        * تحليل الربحية

      - تقرير مستحقات الوكلاء:
        * الأرصدة المدينة والدائنة
        * المستحقات المتأخرة
        * العمولات المستحقة
        * تحليل المخاطر الائتمانية

   د) التقارير المالية:
      - تقرير الأرباح والخسائر:
        * الإيرادات
        * التكاليف
        * الأرباح الصافية
        * هوامش الربح

      - تقرير التدفق النقدي:
        * النقد الوارد
        * النقد الصادر
        * الرصيد النقدي
        * توقعات التدفق

   هـ) الرسوم البيانية التفاعلية:
      - رسوم بيانية خطية للاتجاهات
      - رسوم بيانية دائرية للتوزيعات
      - رسوم بيانية عمودية للمقارنات
      - خرائط حرارية للأداء
      - مؤشرات الأداء الرئيسية

🔐 نظام الأمان والصلاحيات المتقدم:
===============================================================================

1. مستويات الأمان:
   ─────────────────

   أ) أمان التطبيق:
      - تشفير قاعدة البيانات المحلية
      - تشفير البيانات الحساسة
      - حماية من SQL Injection
      - تشفير الاتصالات

   ب) أمان المصادقة:
      - مصادقة ثنائية العامل (اختيارية)
      - كلمات مرور قوية
      - انتهاء صلاحية الجلسات
      - تسجيل محاولات الدخول

   ج) أمان البيانات:
      - نسخ احتياطية مشفرة
      - تتبع تغييرات البيانات
      - سجل العمليات (Audit Log)
      - حماية البيانات الشخصية

2. نظام الصلاحيات المفصل:
   ─────────────────────────

   أ) صلاحيات المدير الأعلى:
      - إدارة المستخدمين (إضافة، تعديل، حذف)
      - إدارة الصلاحيات
      - الوصول لجميع التقارير
      - إعدادات النظام
      - النسخ الاحتياطي والاستعادة
      - مراجعة سجل العمليات

   ب) صلاحيات الموظف الإداري:
      - إدارة المخزون (إضافة، تعديل الأصناف)
      - إنشاء وتعديل الفواتير
      - إدارة حسابات الوكلاء
      - عرض التقارير الأساسية
      - تحويل البضائع

   ج) صلاحيات الوكيل:
      - عرض حسابه الشخصي فقط
      - تسجيل مبيعاته
      - عرض كشف حسابه
      - تحميل تقاريره الشخصية
      - تحديث بياناته الشخصية

   د) صلاحيات مستخدم المعرض:
      - إدارة مخزون المعرض فقط
      - تسجيل مبيعات المعرض
      - عرض تقارير المعرض
      - إدارة عملاء المعرض

3. تتبع العمليات والمراجعة:
   ─────────────────────────

   أ) سجل العمليات:
      - تسجيل جميع العمليات الحساسة
      - معلومات المستخدم والوقت
      - تفاصيل العملية
      - البيانات قبل وبعد التغيير

   ب) تقارير المراجعة:
      - تقرير نشاط المستخدمين
      - تقرير التغييرات الحساسة
      - تقرير محاولات الدخول
      - تقرير الأخطاء والمشاكل

🌐 نظام المزامنة والعمل بدون إنترنت:
===============================================================================

1. استراتيجية المزامنة:
   ──────────────────────

   أ) المزامنة التلقائية:
      - مزامنة كل 5 دقائق عند توفر الإنترنت
      - مزامنة فورية للعمليات الحساسة
      - مزامنة عند بدء التطبيق
      - مزامنة عند العودة للاتصال

   ب) المزامنة اليدوية:
      - زر مزامنة في الشاشة الرئيسية
      - مزامنة شاملة لجميع البيانات
      - مزامنة انتقائية لبيانات محددة
      - إعادة مزامنة البيانات المتعارضة

   ج) حل التعارضات:
      - أولوية للبيانات الأحدث
      - دمج التغييرات غير المتعارضة
      - تنبيه المستخدم للتعارضات الحرجة
      - خيارات الحل اليدوي

2. العمل بدون إنترنت:
   ────────────────────

   أ) البيانات المتاحة محلياً:
      - جميع الأصناف والمخزون
      - بيانات الوكلاء والعملاء
      - الفواتير المحفوظة
      - التقارير الأساسية

   ب) العمليات المتاحة بدون إنترنت:
      - إنشاء فواتير جديدة
      - إضافة أصناف جديدة
      - تسجيل دفعات الوكلاء
      - عرض التقارير المحلية
      - البحث في البيانات

   ج) المزامنة عند العودة للاتصال:
      - رفع جميع البيانات المحلية
      - تحديث البيانات من الخادم
      - حل التعارضات تلقائياً
      - تنبيه بنجاح المزامنة

📱 تحسينات الأداء والتجربة:
===============================================================================

1. تحسينات الأداء:
   ─────────────────

   أ) تحسين بدء التطبيق:
      - تهيئة متوازية للخدمات
      - تحميل البيانات الأساسية فقط
      - تأجيل تحميل البيانات الثقيلة
      - شاشة تحميل تفاعلية

   ب) تحسين الذاكرة:
      - إدارة ذكية للصور
      - ضغط الصور تلقائياً
      - تنظيف الذاكرة دورياً
      - تحميل البيانات عند الحاجة

   ج) تحسين قاعدة البيانات:
      - فهرسة الجداول المهمة
      - استعلامات محسنة
      - تجميع العمليات
      - تنظيف البيانات القديمة

2. تحسينات تجربة المستخدم:
   ─────────────────────────

   أ) الواجهة:
      - تصميم متجاوب لجميع الشاشات
      - ألوان متناسقة ومريحة للعين
      - خطوط عربية واضحة
      - أيقونات معبرة ومفهومة

   ب) التفاعل:
      - استجابة سريعة للمس
      - رسائل تأكيد واضحة
      - مؤشرات التحميل
      - رسائل الخطأ المفيدة

   ج) التنقل:
      - قائمة تنقل سهلة
      - أزرار الرجوع واضحة
      - اختصارات للعمليات الشائعة
      - بحث سريع وفعال

🔧 إعدادات النظام المتقدمة:
===============================================================================

1. إعدادات عامة:
   ──────────────
   - لغة التطبيق (عربي/إنجليزي)
   - العملة الافتراضية
   - تنسيق التاريخ والوقت
   - المنطقة الزمنية
   - نسبة الضريبة الافتراضية
   - طريقة حساب الخصومات

2. إعدادات المخزون:
   ─────────────────
   - الحد الأدنى للتنبيه
   - طريقة تقييم المخزون (FIFO/LIFO/متوسط)
   - تفعيل تتبع الأرقام التسلسلية
   - إعدادات الباركود
   - تصنيفات الأصناف
   - وحدات القياس

3. إعدادات الوكلاء:
   ─────────────────
   - نسبة العمولة الافتراضية
   - حد الائتمان الافتراضي
   - فترة السماح للدفع
   - طريقة حساب العمولات
   - إعدادات التنبيهات
   - قوالب كشوف الحساب

4. إعدادات التقارير:
   ──────────────────
   - تنسيق التقارير
   - الشعار والرأسية
   - معلومات الشركة
   - إعدادات PDF
   - ألوان الرسوم البيانية
   - تنسيق الطباعة

===============================================================================
                            تم إكمال الوثائق الشاملة
                     نظام إدارة النقل - شركة آل فرحان
===============================================================================
