===============================================================================
                    وثائق مشروع نظام إدارة النقل - شركة آل فرحان
                          El Farhan Transport Management System
===============================================================================

📋 معلومات المشروع الأساسية:
===============================================================================
اسم المشروع: نظام إدارة النقل الشامل - شركة آل فرحان
النوع: تطبيق Flutter للأندرويد (النسخة الأصلية)
اللغة: Dart/Flutter
قاعدة البيانات: Firebase Firestore + SQLite المحلية
التخزين السحابي: Cloudinary للصور
الإشعارات: Firebase Cloud Messaging
الهوية: Firebase Authentication

🎯 الهدف من المشروع:
===============================================================================
نظام إدارة شامل لشركة نقل يتضمن:
- إدارة المخزون والأصناف
- إدارة المبيعات والفواتير
- إدارة الوكلاء وحساباتهم
- نظام التقارير والإحصائيات
- تتبع الوثائق والمستندات
- إدارة المخازن والتحويلات
- نظام الإشعارات المتقدم

👥 أنواع المستخدمين والصلاحيات:
===============================================================================

1. المدير الأعلى (super_admin):
   - صلاحية كاملة على جميع أجزاء النظام
   - إدارة المستخدمين والصلاحيات
   - الوصول لجميع التقارير والإحصائيات
   - إدارة إعدادات النظام

2. الموظف الإداري (admin):
   - إدارة المخزون والأصناف
   - إنشاء وإدارة الفواتير
   - إدارة حسابات الوكلاء
   - الوصول للتقارير الأساسية

3. الوكيل (agent):
   - عرض حساباته مع الشركة
   - تسجيل المبيعات الخاصة به
   - عرض كشف حساب مفصل
   - تحميل تقارير PDF

4. مستخدم المعرض (showroom_user):
   - إدارة مخزون المعرض
   - تسجيل مبيعات المعرض
   - عرض تقارير المعرض

🏗️ هيكل المشروع والمجلدات:
===============================================================================

lib/
├── core/                          # الملفات الأساسية
│   ├── constants/                 # الثوابت والإعدادات
│   ├── theme/                     # ألوان وتصميم التطبيق
│   ├── utils/                     # الأدوات المساعدة
│   └── network/                   # إعدادات الشبكة
├── models/                        # نماذج البيانات
├── services/                      # الخدمات والمنطق
│   ├── android/                   # خدمات خاصة بالأندرويد
│   └── firebase/                  # خدمات Firebase
├── providers/                     # مزودي البيانات (Provider)
├── screens/                       # شاشات التطبيق
│   ├── auth/                      # شاشات المصادقة
│   ├── home/                      # الشاشة الرئيسية
│   ├── inventory/                 # إدارة المخزون
│   ├── sales/                     # إدارة المبيعات
│   ├── agents/                    # إدارة الوكلاء
│   ├── reports/                   # التقارير
│   ├── admin/                     # إدارة النظام
│   ├── documents/                 # إدارة الوثائق
│   └── notifications/             # الإشعارات
└── widgets/                       # العناصر المشتركة

📱 الشاشات الرئيسية والوظائف:
===============================================================================

1. شاشة تسجيل الدخول (LoginScreen):
   ────────────────────────────────────
   الوظائف:
   - تسجيل دخول ذكي بالبريد الإلكتروني أو اسم المستخدم
   - تذكر بيانات الدخول
   - مصادقة Firebase
   - حفظ الجلسة محلياً
   - دعم تسجيل الدخول التلقائي

2. الشاشة الرئيسية (HomeScreen):
   ────────────────────────────────
   الوظائف:
   - لوحة تحكم شاملة
   - إحصائيات سريعة
   - قائمة جانبية للتنقل
   - شريط علوي مع الإشعارات
   - أزرار سريعة للعمليات الشائعة

3. شاشات إدارة المخزون:
   ────────────────────────
   
   أ) شاشة المخزون الرئيسية (InventoryScreen):
      - عرض جميع الأصناف
      - البحث والفلترة
      - إحصائيات المخزون
      - تنبيهات النفاد
   
   ب) شاشة إضافة صنف (AddItemScreen):
      - إدخال بيانات الصنف
      - التقاط صور بصمة الموتور
      - التقاط صور رقم الشاسية
      - استخراج النصوص بـ OCR
      - رفع الصور للسحابة
   
   ج) شاشة تحويل البضائع (TransferGoodsScreen):
      - تحويل بين المخازن
      - تتبع عمليات التحويل
      - تأكيد الاستلام

4. شاشات إدارة المبيعات:
   ─────────────────────────
   
   أ) شاشة المبيعات (SalesScreen):
      - عرض جميع الفواتير
      - إحصائيات المبيعات
      - البحث والفلترة
   
   ب) شاشة إنشاء فاتورة (CreateInvoiceScreen):
      - إنشاء فاتورة جديدة
      - إضافة أصناف للفاتورة
      - حساب الإجماليات
      - طباعة الفاتورة
      - تصوير هوية العميل
      - استخراج بيانات العميل

5. شاشات إدارة الوكلاء:
   ──────────────────────
   
   أ) الإدارة الاحترافية للوكلاء (ProfessionalAgentManagementScreen):
      - عرض جميع حسابات الوكلاء
      - إحصائيات مفصلة لكل وكيل
      - تسجيل الدفعات
      - تصدير كشوف الحساب PDF
      - إدارة الائتمان والديون
   
   ب) شاشة كشف حساب الوكيل (AgentAccountDetailScreen):
      - كشف حساب مفصل
      - تاريخ جميع المعاملات
      - الرصيد الحالي
      - تصدير PDF بالعربية

6. شاشات التقارير والإحصائيات:
   ─────────────────────────────
   
   أ) شاشة التقارير (ReportsScreen):
      - تقارير المبيعات
      - تقارير المخزون
      - تقارير الوكلاء
      - تقارير مالية
   
   ب) شاشة استعلام العملاء (CustomerInquiryScreen):
      - البحث عن العملاء
      - تاريخ المشتريات
      - تحميل الصور والوثائق

7. شاشات إدارة النظام:
   ────────────────────
   
   أ) إعدادات المدير (AdminSettingsScreen):
      - إدارة المستخدمين
      - إعدادات النظام
      - النسخ الاحتياطي
   
   ب) إدارة المخازن (WarehouseManagementScreen):
      - إنشاء وتعديل المخازن
      - تعيين المسؤولين
      - إعدادات المخزن

8. شاشات الوثائق:
   ───────────────
   
   أ) تتبع الوثائق (DocumentTrackingScreen):
      - تتبع حالة الوثائق
      - رفع المستندات
      - تحديث الحالات
   
   ب) لوحة الوثائق (DocumentDashboardScreen):
      - إحصائيات الوثائق
      - التنبيهات والمواعيد

9. شاشات تتبع الجوابات والشحنات:
   ─────────────────────────────────

   أ) شاشة إدارة الجوابات (PackageManagementScreen):
      - عرض جميع الجوابات
      - فلترة حسب الحالة والتاريخ
      - البحث برقم الجواب أو العميل
      - إحصائيات الشحنات
      - تتبع الجوابات في الوقت الفعلي

   ب) شاشة إنشاء جواب جديد (CreatePackageScreen):
      - ربط الجواب بفاتورة موجودة
      - إدخال بيانات العميل والعنوان
      - اختيار الأصناف من الفاتورة
      - حساب الوزن والأبعاد
      - تحديد شركة الشحن
      - حساب تكلفة الشحن
      - طباعة ملصق الشحن

   ج) شاشة تتبع الجواب (PackageTrackingScreen):
      - عرض تفاصيل الجواب
      - تاريخ حركة الجواب
      - الموقع الحالي على الخريطة
      - تحديث حالة الجواب
      - إضافة ملاحظات وصور
      - تنبيهات التأخير

   د) شاشة تسليم الجواب (PackageDeliveryScreen):
      - تأكيد التسليم
      - توقيع المستلم
      - صورة إثبات التسليم
      - ملاحظات التسليم
      - تقييم الخدمة

   هـ) شاشة إرجاع الجوابات (PackageReturnScreen):
      - إدارة الجوابات المرجعة
      - أسباب الإرجاع
      - إعادة جدولة التسليم
      - معالجة المرتجعات

10. شاشة الإشعارات المتقدمة (EnhancedNotificationsScreen):
    ──────────────────────────────────────────────────
    - عرض جميع الإشعارات
    - تصنيف الإشعارات
    - إشعارات فورية
    - إشعارات مجدولة
    - إشعارات تتبع الجوابات
    - تنبيهات التأخير في التسليم

🗄️ نماذج البيانات الرئيسية:
===============================================================================

1. نموذج المستخدم (UserModel):
   ─────────────────────────
   - id: معرف المستخدم
   - name: الاسم
   - email: البريد الإلكتروني
   - role: الدور (super_admin, admin, agent, showroom_user)
   - warehouseId: معرف المخزن المخصص
   - isActive: حالة النشاط
   - createdAt: تاريخ الإنشاء

2. نموذج الصنف (ItemModel):
   ─────────────────────
   - id: معرف الصنف
   - name: اسم الصنف
   - description: الوصف
   - category: الفئة
   - price: السعر
   - quantity: الكمية
   - motorFingerprintImage: صورة بصمة الموتور
   - chassisImage: صورة رقم الشاسية
   - motorFingerprint: رقم بصمة الموتور
   - chassisNumber: رقم الشاسية
   - warehouseId: معرف المخزن

3. نموذج الفاتورة (InvoiceModel):
   ──────────────────────────
   - id: معرف الفاتورة
   - invoiceNumber: رقم الفاتورة
   - customerId: معرف العميل
   - customerName: اسم العميل
   - items: قائمة الأصناف
   - totalAmount: المبلغ الإجمالي
   - discount: الخصم
   - tax: الضريبة
   - paymentMethod: طريقة الدفع
   - status: حالة الفاتورة
   - createdAt: تاريخ الإنشاء
   - createdBy: منشئ الفاتورة

4. نموذج حساب الوكيل الاحترافي (ProfessionalAgentAccount):
   ──────────────────────────────────────────────────────
   - id: معرف الحساب
   - agentId: معرف الوكيل
   - agentName: اسم الوكيل
   - totalCustomerSales: إجمالي مبيعات العملاء
   - totalPaymentsReceived: إجمالي المدفوعات المستلمة
   - totalGoodsWithdrawn: إجمالي البضاعة المسحوبة
   - totalAgentCommission: إجمالي عمولة الوكيل
   - currentBalance: الرصيد الحالي
   - creditLimit: حد الائتمان
   - transactions: قائمة المعاملات
   - lastTransactionDate: تاريخ آخر معاملة

5. نموذج معاملة الوكيل (ProfessionalAgentTransaction):
   ─────────────────────────────────────────────────
   - id: معرف المعاملة
   - type: نوع المعاملة (sale, payment, withdrawal, commission)
   - amount: المبلغ
   - description: الوصف
   - referenceId: معرف المرجع
   - createdAt: تاريخ الإنشاء
   - createdBy: منشئ المعاملة

6. نموذج المخزن (WarehouseModel):
   ──────────────────────────
   - id: معرف المخزن
   - name: اسم المخزن
   - location: الموقع
   - managerId: معرف المدير
   - type: نوع المخزن (main, agent, showroom)
   - isActive: حالة النشاط

7. نموذج الجواب/الطرد (PackageModel):
   ──────────────────────────────────
   - id: معرف الجواب
   - packageNumber: رقم الجواب (فريد)
   - invoiceId: معرف الفاتورة المرتبطة
   - customerId: معرف العميل
   - customerName: اسم العميل
   - customerPhone: رقم هاتف العميل
   - customerAddress: عنوان العميل التفصيلي
   - items: قائمة الأصناف في الجواب
   - totalWeight: الوزن الإجمالي
   - totalValue: القيمة الإجمالية
   - shippingCost: تكلفة الشحن
   - status: حالة الجواب (pending, shipped, in_transit, delivered, returned)
   - trackingNumber: رقم التتبع
   - senderWarehouse: المخزن المرسل
   - destinationAddress: عنوان الوصول
   - estimatedDeliveryDate: تاريخ التسليم المتوقع
   - actualDeliveryDate: تاريخ التسليم الفعلي
   - deliveryNotes: ملاحظات التسليم
   - recipientName: اسم المستلم
   - recipientSignature: توقيع المستلم
   - deliveryProof: إثبات التسليم (صورة)
   - createdAt: تاريخ الإنشاء
   - createdBy: منشئ الجواب
   - lastUpdated: آخر تحديث
   - updatedBy: محدث الجواب

8. نموذج صنف الجواب (PackageItemModel):
   ─────────────────────────────────────
   - id: معرف صنف الجواب
   - packageId: معرف الجواب
   - itemId: معرف الصنف
   - itemName: اسم الصنف
   - quantity: الكمية
   - weight: الوزن
   - dimensions: الأبعاد (طول × عرض × ارتفاع)
   - value: القيمة
   - description: وصف الصنف
   - serialNumbers: الأرقام التسلسلية
   - condition: حالة الصنف (new, used, damaged)
   - notes: ملاحظات خاصة

9. نموذج تتبع الجواب (PackageTrackingModel):
   ──────────────────────────────────────────
   - id: معرف التتبع
   - packageId: معرف الجواب
   - status: الحالة
   - location: الموقع الحالي
   - timestamp: وقت التحديث
   - notes: ملاحظات
   - updatedBy: محدث الحالة
   - gpsCoordinates: الإحداثيات الجغرافية
   - photo: صورة توثيقية
   - signature: التوقيع (عند التسليم)

10. نموذج شركة الشحن (ShippingCompanyModel):
    ──────────────────────────────────────────
    - id: معرف شركة الشحن
    - name: اسم الشركة
    - contactInfo: معلومات الاتصال
    - serviceAreas: مناطق الخدمة
    - shippingRates: أسعار الشحن
    - trackingApiUrl: رابط API التتبع
    - isActive: حالة النشاط
    - contractDetails: تفاصيل العقد

🔄 الخدمات الرئيسية:
===============================================================================

1. خدمة البيانات (DataService):
   ────────────────────────
   - إدارة جميع عمليات قاعدة البيانات
   - المزامنة مع Firebase
   - التخزين المحلي
   - إدارة المستخدمين والأصناف والفواتير

2. خدمة الوكلاء الاحترافية (ProfessionalAgentService):
   ─────────────────────────────────────────────────
   - إدارة حسابات الوكلاء
   - حساب الأرصدة والعمولات
   - تسجيل المعاملات
   - إنشاء كشوف الحساب

3. خدمة الصور للأندرويد (AndroidImageService):
   ──────────────────────────────────────────
   - التقاط الصور من الكاميرا
   - اختيار الصور من المعرض
   - رفع الصور لـ Cloudinary
   - ضغط وتحسين الصور

4. خدمة OCR للأندرويد (AndroidOcrService):
   ─────────────────────────────────────
   - استخراج النصوص من الصور
   - التعرف على النصوص العربية والإنجليزية
   - استخراج أرقام بصمة الموتور
   - استخراج أرقام الشاسية

5. خدمة Firebase (FirebaseService):
   ──────────────────────────────
   - إدارة الاتصال بـ Firebase
   - المصادقة والأمان
   - المزامنة التلقائية
   - إدارة الإشعارات

6. خدمة المزامنة التلقائية (AutoSyncService):
   ──────────────────────────────────────────
   - مزامنة دورية للبيانات
   - مزامنة عند الاتصال بالإنترنت
   - حل تعارضات البيانات
   - النسخ الاحتياطي التلقائي

7. خدمة الإشعارات المتقدمة (EnhancedNotificationService):
   ────────────────────────────────────────────────────
   - إرسال الإشعارات المحلية
   - استقبال إشعارات Firebase
   - جدولة الإشعارات
   - تصنيف الإشعارات

8. خدمة إدارة الجوابات (PackageService):
   ─────────────────────────────────────
   - إنشاء وإدارة الجوابات
   - ربط الجوابات بالفواتير
   - حساب تكاليف الشحن
   - إدارة حالات الجوابات
   - تتبع الجوابات في الوقت الفعلي
   - إدارة عمليات التسليم
   - معالجة المرتجعات

9. خدمة تتبع الشحنات (ShipmentTrackingService):
   ──────────────────────────────────────────────
   - تتبع الجوابات عبر شركات الشحن المختلفة
   - تحديث حالات الجوابات تلقائياً
   - إرسال تنبيهات للعملاء
   - تسجيل المواقع الجغرافية
   - إدارة التأخيرات والمشاكل
   - تكامل مع APIs شركات الشحن

10. خدمة شركات الشحن (ShippingCompanyService):
    ──────────────────────────────────────────────
    - إدارة بيانات شركات الشحن
    - حساب أسعار الشحن
    - مقارنة الأسعار والخدمات
    - تقييم أداء الشركات
    - إدارة العقود والاتفاقيات
    - تتبع المدفوعات لشركات الشحن

11. خدمة الإشعارات للشحنات (ShipmentNotificationService):
    ────────────────────────────────────────────────────────
    - إرسال تنبيهات تحديث الحالة
    - إشعارات التسليم
    - تنبيهات التأخير
    - رسائل SMS للعملاء
    - إشعارات البريد الإلكتروني
    - تنبيهات التطبيق المحلية

12. خدمة الأمان (SecurityService):
    ──────────────────────────
    - تشفير البيانات الحساسة
    - إدارة الجلسات
    - التحقق من الصلاحيات
    - حماية البيانات المحلية

🔗 علاقات البيانات:
===============================================================================

1. علاقة المستخدمين والمخازن:
   ─────────────────────────
   - كل مستخدم مرتبط بمخزن واحد (One-to-One)
   - المخزن الواحد يمكن أن يكون له عدة مستخدمين (One-to-Many)
   - الوكلاء لهم مخازن خاصة بهم

2. علاقة الأصناف والمخازن:
   ─────────────────────
   - كل صنف موجود في مخزن محدد (Many-to-One)
   - المخزن الواحد يحتوي على عدة أصناف (One-to-Many)
   - تتبع الكميات لكل مخزن

3. علاقة الفواتير والأصناف:
   ─────────────────────
   - الفاتورة الواحدة تحتوي على عدة أصناف (One-to-Many)
   - الصنف الواحد يمكن أن يكون في عدة فواتير (Many-to-Many)
   - تخزين الكمية والسعر لكل صنف في الفاتورة

4. علاقة الوكلاء والمعاملات:
   ─────────────────────
   - كل وكيل له حساب واحد (One-to-One)
   - الحساب الواحد له عدة معاملات (One-to-Many)
   - المعاملات مرتبطة بالفواتير والدفعات

5. علاقة المستخدمين والفواتير:
   ──────────────────────
   - كل فاتورة منشأة بواسطة مستخدم (Many-to-One)
   - المستخدم الواحد ينشئ عدة فواتير (One-to-Many)
   - تتبع منشئ كل فاتورة

6. علاقة الفواتير والجوابات:
   ─────────────────────────
   - كل فاتورة يمكن أن تحتوي على جواب واحد أو أكثر (One-to-Many)
   - كل جواب مرتبط بفاتورة واحدة فقط (Many-to-One)
   - الجواب يحتوي على نسخة من أصناف الفاتورة
   - ربط رقم الجواب برقم الفاتورة

7. علاقة الجوابات والأصناف:
   ─────────────────────────
   - كل جواب يحتوي على عدة أصناف (One-to-Many)
   - كل صنف في الجواب مرتبط بصنف في المخزون (Many-to-One)
   - تتبع الكميات والأوزان لكل صنف
   - حفظ الأرقام التسلسلية للأصناف

8. علاقة الجوابات وتتبع الحالة:
   ──────────────────────────
   - كل جواب له سجل تتبع متعدد (One-to-Many)
   - كل تحديث حالة مرتبط بجواب واحد (Many-to-One)
   - تسلسل زمني لحالات الجواب
   - ربط كل تحديث بالمستخدم المسؤول

9. علاقة الجوابات وشركات الشحن:
   ──────────────────────────────
   - كل جواب مرتبط بشركة شحن واحدة (Many-to-One)
   - شركة الشحن الواحدة تتعامل مع عدة جوابات (One-to-Many)
   - ربط أسعار الشحن بالشركة
   - تتبع أداء كل شركة شحن

10. علاقة العملاء والجوابات:
    ─────────────────────────
    - كل عميل يمكن أن يكون له عدة جوابات (One-to-Many)
    - كل جواب مرتبط بعميل واحد (Many-to-One)
    - تتبع تاريخ شحنات العميل
    - حفظ عناوين التسليم المختلفة

⚙️ العمليات الرئيسية:
===============================================================================

1. عمليات إدارة المخزون:
   ──────────────────────
   - إضافة صنف جديد
   - تعديل بيانات الصنف
   - حذف صنف
   - البحث والفلترة
   - تحديث الكميات
   - تحويل بين المخازن
   - تتبع حركة المخزون

2. عمليات المبيعات:
   ─────────────────
   - إنشاء فاتورة جديدة
   - إضافة أصناف للفاتورة
   - حساب الإجماليات والضرائب
   - تطبيق الخصومات
   - حفظ الفاتورة
   - طباعة الفاتورة
   - إلغاء الفاتورة

3. عمليات إدارة الوكلاء:
   ──────────────────────
   - إنشاء حساب وكيل جديد
   - تسجيل مبيعة للوكيل
   - تسجيل دفعة من الوكيل
   - تسجيل سحب بضاعة
   - حساب العمولات
   - إنشاء كشف حساب
   - تصدير PDF

4. عمليات التقارير:
   ─────────────────
   - تقرير المبيعات اليومية
   - تقرير المخزون
   - تقرير أداء الوكلاء
   - تقرير الأرباح والخسائر
   - تقرير حركة المخزون
   - تصدير التقارير PDF/Excel

5. عمليات المزامنة:
   ─────────────────
   - مزامنة البيانات مع Firebase
   - رفع البيانات المحلية
   - تحميل البيانات الجديدة
   - حل تعارضات البيانات
   - النسخ الاحتياطي

6. عمليات إدارة الجوابات والشحنات:
   ──────────────────────────────────
   - إنشاء جواب جديد من فاتورة:
     * اختيار الفاتورة
     * تحديد الأصناف للشحن
     * إدخال بيانات العميل والعنوان
     * حساب الوزن والأبعاد
     * اختيار شركة الشحن
     * حساب تكلفة الشحن
     * إنشاء رقم تتبع
     * طباعة ملصق الشحن

   - تتبع الجوابات:
     * تحديث حالة الجواب
     * إضافة موقع جغرافي
     * رفع صور توثيقية
     * إضافة ملاحظات
     * تسجيل وقت كل تحديث
     * إرسال تنبيهات للعميل

   - عمليات التسليم:
     * تأكيد وصول الجواب
     * توقيع المستلم
     * صورة إثبات التسليم
     * تقييم الخدمة
     * إغلاق الجواب

   - إدارة المرتجعات:
     * تسجيل سبب الإرجاع
     * إعادة جدولة التسليم
     * معالجة الجوابات التالفة
     * استرداد الأموال
     * تحديث المخزون

7. عمليات التقارير المتعلقة بالشحنات:
   ─────────────────────────────────────
   - تقرير حالة الجوابات:
     * الجوابات قيد الشحن
     * الجوابات المسلمة
     * الجوابات المرجعة
     * الجوابات المتأخرة

   - تقرير أداء الشحن:
     * متوسط وقت التسليم
     * نسبة التسليم الناجح
     * أداء شركات الشحن
     * تكاليف الشحن

   - تقرير رضا العملاء:
     * تقييمات الخدمة
     * الشكاوى والمقترحات
     * معدل الإرجاع
     * ولاء العملاء

8. عمليات الأمان:
   ──────────────
   - تسجيل الدخول
   - التحقق من الصلاحيات
   - تشفير البيانات
   - إدارة الجلسات
   - تسجيل الخروج

🔧 التقنيات المستخدمة:
===============================================================================

1. Frontend:
   ─────────
   - Flutter 3.5+
   - Dart 3.5+
   - Material Design
   - Provider للإدارة الحالة

2. Backend:
   ─────────
   - Firebase Firestore
   - Firebase Authentication
   - Firebase Cloud Messaging
   - Firebase Storage

3. قاعدة البيانات المحلية:
   ──────────────────────
   - SQLite
   - sqflite package

4. معالجة الصور:
   ──────────────
   - image_picker للكاميرا والمعرض
   - Google ML Kit للـ OCR
   - Cloudinary للتخزين السحابي

5. إنشاء PDF:
   ───────────
   - pdf package
   - دعم الخطوط العربية
   - تخطيط RTL

6. الإشعارات:
   ───────────
   - Firebase Cloud Messaging
   - flutter_local_notifications

📊 إحصائيات المشروع المحدثة:
===============================================================================
- عدد الشاشات: 35+ شاشة (تشمل شاشات تتبع الجوابات)
- عدد النماذج: 20+ نموذج (تشمل نماذج الشحن والتتبع)
- عدد الخدمات: 16+ خدمة (تشمل خدمات الشحن والتتبع)
- عدد العمليات: 75+ عملية (تشمل عمليات الجوابات والشحن)
- دعم اللغات: العربية والإنجليزية
- المنصات المدعومة: Android (النسخة الأصلية)
- أنواع التقارير: 25+ تقرير مختلف
- شركات الشحن المدعومة: متعددة مع APIs
- أنواع الإشعارات: 15+ نوع إشعار

🚀 الميزات المتقدمة المحدثة:
===============================================================================
- نظام OCR متقدم لاستخراج النصوص
- تصدير PDF بالعربية
- مزامنة تلقائية ذكية
- نظام إشعارات متقدم
- إدارة احترافية للوكلاء
- تتبع شامل للمخزون
- تقارير تفصيلية
- أمان متقدم للبيانات
- واجهة مستخدم عربية
- دعم العمل بدون إنترنت

🚚 ميزات الشحن والتتبع المتقدمة:
===============================================================================
- تتبع الجوابات في الوقت الفعلي
- تكامل مع شركات الشحن المختلفة
- خرائط تفاعلية لمتابعة الشحنات
- حساب تكاليف الشحن تلقائياً
- إدارة شاملة للمرتجعات
- تنبيهات ذكية للعملاء
- توقيع رقمي للتسليم
- إثبات التسليم بالصور
- تقارير أداء الشحن
- تحليل رضا العملاء
- جدولة التسليم المرنة
- تتبع GPS للشحنات
- باركود و QR Code للجوابات
- تكامل مع أنظمة الدفع
- إدارة التأمين على الشحنات

📱 ميزات التطبيق المحمول المتقدمة:
===============================================================================
- تطبيق للسائقين لتحديث الحالات
- مسح الباركود والـ QR Code
- التقاط الصور والتوقيعات
- العمل بدون إنترنت للسائقين
- تحديد الموقع الجغرافي تلقائياً
- إشعارات فورية للتحديثات
- واجهة مبسطة للعمليات السريعة
- دعم اللغة العربية كاملاً
- تصميم متجاوب لجميع الشاشات
- أمان متقدم للبيانات الحساسة

🔍 تفاصيل الشاشات المتقدمة:
===============================================================================

1. شاشة إضافة الأصناف - تفاصيل متقدمة:
   ────────────────────────────────────

   أ) قسم البيانات الأساسية:
      - اسم الصنف (مطلوب)
      - الوصف التفصيلي
      - الفئة (قائمة منسدلة)
      - السعر (مع التحقق من صحة الإدخال)
      - الكمية الأولية
      - الحد الأدنى للتنبيه
      - وحدة القياس
      - الباركود (اختياري)

   ب) قسم الصور والتوثيق:
      - صورة بصمة الموتور:
        * التقاط من الكاميرا
        * اختيار من المعرض
        * معاينة الصورة
        * استخراج رقم البصمة تلقائياً
        * التحقق من جودة الصورة

      - صورة رقم الشاسية:
        * التقاط من الكاميرا
        * اختيار من المعرض
        * معاينة الصورة
        * استخراج رقم الشاسية تلقائياً
        * التحقق من وضوح الرقم

   ج) قسم OCR المتقدم:
      - خوارزمية التعرف على النصوص العربية والإنجليزية
      - تحسين جودة الصورة قبل المعالجة
      - فلترة النتائج وإزالة الضوضاء
      - التحقق من صحة الأرقام المستخرجة
      - إمكانية التعديل اليدوي

   د) قسم الحفظ والتحقق:
      - التحقق من اكتمال البيانات
      - رفع الصور للسحابة
      - حفظ البيانات محلياً
      - مزامنة مع Firebase
      - رسائل تأكيد النجاح

2. شاشة إنشاء الفاتورة - تفاصيل متقدمة:
   ─────────────────────────────────────

   أ) قسم بيانات العميل:
      - اسم العميل (مطلوب)
      - رقم الهاتف
      - العنوان
      - البريد الإلكتروني (اختياري)
      - تصوير هوية العميل:
        * الوجه الأمامي
        * الوجه الخلفي
        * استخراج البيانات تلقائياً
        * التحقق من صحة البيانات

   ب) قسم الأصناف:
      - البحث في الأصناف المتاحة
      - إضافة صنف للفاتورة
      - تحديد الكمية
      - تطبيق خصم على الصنف
      - حساب الإجمالي الفرعي
      - إزالة صنف من الفاتورة
      - التحقق من توفر الكمية

   ج) قسم الحسابات:
      - الإجمالي الفرعي
      - الخصم العام (نسبة أو مبلغ ثابت)
      - الضريبة (قابلة للتخصيص)
      - الإجمالي النهائي
      - المبلغ المدفوع
      - المبلغ المتبقي

   د) قسم الدفع:
      - طريقة الدفع (نقدي، بطاقة، تحويل، آجل)
      - تفاصيل الدفع
      - تاريخ الاستحقاق (للدفع الآجل)
      - ملاحظات الدفع

   هـ) قسم الطباعة والحفظ:
      - معاينة الفاتورة
      - طباعة الفاتورة
      - إرسال بالبريد الإلكتروني
      - حفظ كـ PDF
      - مشاركة الفاتورة

3. شاشة إدارة الوكلاء الاحترافية - تفاصيل متقدمة:
   ──────────────────────────────────────────────

   أ) لوحة المعلومات الرئيسية:
      - عدد الوكلاء النشطين
      - إجمالي المبيعات الشهرية
      - إجمالي المستحقات
      - إجمالي العمولات
      - مؤشرات الأداء الرئيسية

   ب) قائمة الوكلاء:
      - عرض جدولي مع:
        * اسم الوكيل
        * رقم الهاتف
        * المنطقة
        * الرصيد الحالي
        * آخر معاملة
        * حالة الحساب
      - فلترة حسب:
        * المنطقة
        * حالة الحساب
        * نوع الوكيل
        * تاريخ التسجيل
      - ترتيب حسب:
        * الاسم
        * الرصيد
        * تاريخ آخر معاملة
        * حجم المبيعات

   ج) تفاصيل حساب الوكيل:
      - المعلومات الشخصية
      - ملخص الحساب:
        * إجمالي المبيعات
        * إجمالي المدفوعات
        * إجمالي السحوبات
        * العمولات المستحقة
        * الرصيد الحالي
      - تاريخ المعاملات:
        * المبيعات
        * الدفعات
        * السحوبات
        * العمولات
      - الرسوم البيانية:
        * أداء المبيعات الشهرية
        * توزيع أنواع المعاملات
        * مقارنة الأداء

   د) عمليات الحساب:
      - تسجيل دفعة جديدة:
        * المبلغ
        * طريقة الدفع
        * تاريخ الدفع
        * ملاحظات
        * إيصال الدفع
      - تسجيل سحب بضاعة:
        * الأصناف المسحوبة
        * الكميات
        * القيم
        * تاريخ السحب
        * ملاحظات
      - تعديل الرصيد:
        * سبب التعديل
        * المبلغ
        * الموافقات المطلوبة

   هـ) التقارير والتصدير:
      - كشف حساب مفصل
      - تقرير المبيعات
      - تقرير الدفعات
      - تصدير PDF بالعربية:
        * رأس الشركة
        * بيانات الوكيل
        * ملخص الحساب
        * تفاصيل المعاملات
        * الرسوم البيانية
        * التوقيعات والأختام

5. شاشات إدارة الجوابات والشحنات - تفاصيل متقدمة:
   ──────────────────────────────────────────────────

   أ) شاشة إدارة الجوابات الرئيسية:
      - لوحة معلومات شاملة:
        * عدد الجوابات النشطة
        * الجوابات قيد الشحن
        * الجوابات المسلمة اليوم
        * الجوابات المتأخرة
        * إجمالي قيمة الشحنات
        * متوسط وقت التسليم

      - قائمة الجوابات التفاعلية:
        * عرض جدولي مع فلترة متقدمة
        * البحث برقم الجواب أو العميل
        * ترتيب حسب التاريخ أو الحالة
        * ألوان مختلفة لكل حالة
        * أيقونات تفاعلية للعمليات السريعة

      - خريطة تتبع الجوابات:
        * عرض مواقع الجوابات على الخريطة
        * مسارات الشحن
        * نقاط التوقف والتسليم
        * تحديث المواقع في الوقت الفعلي

   ب) شاشة إنشاء جواب جديد:
      - ربط بالفاتورة:
        * اختيار الفاتورة من قائمة
        * عرض تفاصيل الفاتورة
        * اختيار الأصناف للشحن
        * تحديد الكميات لكل صنف

      - بيانات العميل والشحن:
        * معلومات العميل من الفاتورة
        * عنوان التسليم (قابل للتعديل)
        * رقم هاتف إضافي للتواصل
        * تعليمات خاصة للتسليم
        * أوقات التسليم المفضلة

      - حساب الشحن:
        * وزن كل صنف
        * الأبعاد الإجمالية
        * نوع التعبئة المطلوبة
        * خدمات إضافية (تأمين، تسليم سريع)
        * مقارنة أسعار شركات الشحن
        * اختيار أفضل عرض

      - إنشاء الجواب:
        * توليد رقم جواب فريد
        * طباعة ملصق الشحن
        * باركود للتتبع
        * QR Code للمعلومات
        * نسخة للعميل

   ج) شاشة تتبع الجواب التفصيلية:
      - معلومات الجواب:
        * رقم الجواب ورقم التتبع
        * بيانات العميل والعنوان
        * تفاصيل الأصناف
        * شركة الشحن المختارة
        * التكلفة والدفع

      - تاريخ الحركة:
        * جدول زمني تفاعلي
        * كل نقطة في رحلة الجواب
        * الوقت والموقع لكل تحديث
        * الموظف المسؤول عن كل تحديث
        * صور وملاحظات لكل مرحلة

      - الموقع الحالي:
        * خريطة تفاعلية
        * الموقع الحالي للجواب
        * المسار المتوقع
        * وقت الوصول المتوقع
        * تنبيهات التأخير

      - تحديث الحالة:
        * تغيير حالة الجواب
        * إضافة موقع جديد
        * رفع صور توثيقية
        * إضافة ملاحظات
        * إرسال تنبيه للعميل

   د) شاشة التسليم والاستلام:
      - تأكيد الوصول:
        * مسح QR Code الجواب
        * تأكيد العنوان
        * التحقق من هوية المستلم
        * فحص حالة الجواب

      - عملية التسليم:
        * توقيع المستلم الرقمي
        * صورة إثبات التسليم
        * صورة المستلم مع الجواب
        * ملاحظات التسليم
        * تقييم حالة الأصناف

      - إنهاء التسليم:
        * تأكيد التسليم الناجح
        * إرسال تأكيد للعميل
        * تحديث حالة الفاتورة
        * تحديث المخزون
        * إنشاء تقرير التسليم

   هـ) شاشة إدارة المرتجعات:
      - تسجيل الإرجاع:
        * سبب عدم التسليم
        * حالة الجواب عند الإرجاع
        * صور الجواب المرجع
        * ملاحظات السائق
        * تكلفة الإرجاع

      - معالجة المرتجعات:
        * إعادة جدولة التسليم
        * تغيير العنوان
        * تغيير شركة الشحن
        * استرداد الأموال
        * إلغاء الطلب

      - تقارير المرتجعات:
        * أسباب الإرجاع الشائعة
        * نسبة الإرجاع لكل منطقة
        * تكلفة المرتجعات
        * تحسين عملية التسليم

4. شاشة التقارير والإحصائيات - تفاصيل متقدمة:
   ──────────────────────────────────────────────

   أ) تقارير المبيعات:
      - تقرير المبيعات اليومية:
        * عدد الفواتير
        * إجمالي المبيعات
        * متوسط قيمة الفاتورة
        * أفضل الأصناف مبيعاً
        * توزيع طرق الدفع

      - تقرير المبيعات الشهرية:
        * مقارنة بالشهر السابق
        * نمو المبيعات
        * الاتجاهات والتوقعات
        * تحليل الموسمية

      - تقرير المبيعات السنوية:
        * الأداء السنوي
        * مقارنة بالسنوات السابقة
        * تحليل النمو
        * التوقعات المستقبلية

   ب) تقارير المخزون:
      - تقرير حالة المخزون:
        * الأصناف المتوفرة
        * الأصناف المنخفضة
        * الأصناف المنتهية
        * قيمة المخزون الإجمالية

      - تقرير حركة المخزون:
        * الوارد والصادر
        * معدل دوران المخزون
        * الأصناف بطيئة الحركة
        * تحليل ABC للمخزون

      - تقرير التحويلات:
        * التحويلات بين المخازن
        * الأصناف المحولة
        * أسباب التحويل
        * حالة التحويلات

   ج) تقارير الوكلاء:
      - تقرير أداء الوكلاء:
        * ترتيب الوكلاء حسب المبيعات
        * معدل نمو كل وكيل
        * الوكلاء الأكثر نشاطاً
        * تحليل الربحية

      - تقرير مستحقات الوكلاء:
        * الأرصدة المدينة والدائنة
        * المستحقات المتأخرة
        * العمولات المستحقة
        * تحليل المخاطر الائتمانية

   د) التقارير المالية:
      - تقرير الأرباح والخسائر:
        * الإيرادات
        * التكاليف
        * الأرباح الصافية
        * هوامش الربح

      - تقرير التدفق النقدي:
        * النقد الوارد
        * النقد الصادر
        * الرصيد النقدي
        * توقعات التدفق

   هـ) الرسوم البيانية التفاعلية:
      - رسوم بيانية خطية للاتجاهات
      - رسوم بيانية دائرية للتوزيعات
      - رسوم بيانية عمودية للمقارنات
      - خرائط حرارية للأداء
      - مؤشرات الأداء الرئيسية

🔐 نظام الأمان والصلاحيات المتقدم:
===============================================================================

1. مستويات الأمان:
   ─────────────────

   أ) أمان التطبيق:
      - تشفير قاعدة البيانات المحلية
      - تشفير البيانات الحساسة
      - حماية من SQL Injection
      - تشفير الاتصالات

   ب) أمان المصادقة:
      - مصادقة ثنائية العامل (اختيارية)
      - كلمات مرور قوية
      - انتهاء صلاحية الجلسات
      - تسجيل محاولات الدخول

   ج) أمان البيانات:
      - نسخ احتياطية مشفرة
      - تتبع تغييرات البيانات
      - سجل العمليات (Audit Log)
      - حماية البيانات الشخصية

2. نظام الصلاحيات المفصل:
   ─────────────────────────

   أ) صلاحيات المدير الأعلى:
      - إدارة المستخدمين (إضافة، تعديل، حذف)
      - إدارة الصلاحيات
      - الوصول لجميع التقارير
      - إعدادات النظام
      - النسخ الاحتياطي والاستعادة
      - مراجعة سجل العمليات

   ب) صلاحيات الموظف الإداري:
      - إدارة المخزون (إضافة، تعديل الأصناف)
      - إنشاء وتعديل الفواتير
      - إدارة حسابات الوكلاء
      - عرض التقارير الأساسية
      - تحويل البضائع

   ج) صلاحيات الوكيل:
      - عرض حسابه الشخصي فقط
      - تسجيل مبيعاته
      - عرض كشف حسابه
      - تحميل تقاريره الشخصية
      - تحديث بياناته الشخصية

   د) صلاحيات مستخدم المعرض:
      - إدارة مخزون المعرض فقط
      - تسجيل مبيعات المعرض
      - عرض تقارير المعرض
      - إدارة عملاء المعرض

3. تتبع العمليات والمراجعة:
   ─────────────────────────

   أ) سجل العمليات:
      - تسجيل جميع العمليات الحساسة
      - معلومات المستخدم والوقت
      - تفاصيل العملية
      - البيانات قبل وبعد التغيير

   ب) تقارير المراجعة:
      - تقرير نشاط المستخدمين
      - تقرير التغييرات الحساسة
      - تقرير محاولات الدخول
      - تقرير الأخطاء والمشاكل

🌐 نظام المزامنة والعمل بدون إنترنت:
===============================================================================

1. استراتيجية المزامنة:
   ──────────────────────

   أ) المزامنة التلقائية:
      - مزامنة كل 5 دقائق عند توفر الإنترنت
      - مزامنة فورية للعمليات الحساسة
      - مزامنة عند بدء التطبيق
      - مزامنة عند العودة للاتصال

   ب) المزامنة اليدوية:
      - زر مزامنة في الشاشة الرئيسية
      - مزامنة شاملة لجميع البيانات
      - مزامنة انتقائية لبيانات محددة
      - إعادة مزامنة البيانات المتعارضة

   ج) حل التعارضات:
      - أولوية للبيانات الأحدث
      - دمج التغييرات غير المتعارضة
      - تنبيه المستخدم للتعارضات الحرجة
      - خيارات الحل اليدوي

2. العمل بدون إنترنت:
   ────────────────────

   أ) البيانات المتاحة محلياً:
      - جميع الأصناف والمخزون
      - بيانات الوكلاء والعملاء
      - الفواتير المحفوظة
      - التقارير الأساسية

   ب) العمليات المتاحة بدون إنترنت:
      - إنشاء فواتير جديدة
      - إضافة أصناف جديدة
      - تسجيل دفعات الوكلاء
      - عرض التقارير المحلية
      - البحث في البيانات

   ج) المزامنة عند العودة للاتصال:
      - رفع جميع البيانات المحلية
      - تحديث البيانات من الخادم
      - حل التعارضات تلقائياً
      - تنبيه بنجاح المزامنة

📱 تحسينات الأداء والتجربة:
===============================================================================

1. تحسينات الأداء:
   ─────────────────

   أ) تحسين بدء التطبيق:
      - تهيئة متوازية للخدمات
      - تحميل البيانات الأساسية فقط
      - تأجيل تحميل البيانات الثقيلة
      - شاشة تحميل تفاعلية

   ب) تحسين الذاكرة:
      - إدارة ذكية للصور
      - ضغط الصور تلقائياً
      - تنظيف الذاكرة دورياً
      - تحميل البيانات عند الحاجة

   ج) تحسين قاعدة البيانات:
      - فهرسة الجداول المهمة
      - استعلامات محسنة
      - تجميع العمليات
      - تنظيف البيانات القديمة

2. تحسينات تجربة المستخدم:
   ─────────────────────────

   أ) الواجهة:
      - تصميم متجاوب لجميع الشاشات
      - ألوان متناسقة ومريحة للعين
      - خطوط عربية واضحة
      - أيقونات معبرة ومفهومة

   ب) التفاعل:
      - استجابة سريعة للمس
      - رسائل تأكيد واضحة
      - مؤشرات التحميل
      - رسائل الخطأ المفيدة

   ج) التنقل:
      - قائمة تنقل سهلة
      - أزرار الرجوع واضحة
      - اختصارات للعمليات الشائعة
      - بحث سريع وفعال

🔧 إعدادات النظام المتقدمة:
===============================================================================

1. إعدادات عامة:
   ──────────────
   - لغة التطبيق (عربي/إنجليزي)
   - العملة الافتراضية
   - تنسيق التاريخ والوقت
   - المنطقة الزمنية
   - نسبة الضريبة الافتراضية
   - طريقة حساب الخصومات

2. إعدادات المخزون:
   ─────────────────
   - الحد الأدنى للتنبيه
   - طريقة تقييم المخزون (FIFO/LIFO/متوسط)
   - تفعيل تتبع الأرقام التسلسلية
   - إعدادات الباركود
   - تصنيفات الأصناف
   - وحدات القياس

3. إعدادات الوكلاء:
   ─────────────────
   - نسبة العمولة الافتراضية
   - حد الائتمان الافتراضي
   - فترة السماح للدفع
   - طريقة حساب العمولات
   - إعدادات التنبيهات
   - قوالب كشوف الحساب

4. إعدادات التقارير:
   ──────────────────
   - تنسيق التقارير
   - الشعار والرأسية
   - معلومات الشركة
   - إعدادات PDF
   - ألوان الرسوم البيانية
   - تنسيق الطباعة

5. إعدادات الشحن والجوابات:
   ─────────────────────────
   - شركات الشحن المفضلة
   - أسعار الشحن الافتراضية
   - مناطق التسليم وأسعارها
   - أوقات التسليم المتوقعة
   - حد الوزن الأقصى للجواب
   - أنواع التعبئة المتاحة
   - خدمات إضافية (تأمين، تسليم سريع)
   - قوالب ملصقات الشحن
   - رسائل التنبيه للعملاء
   - إعدادات التتبع التلقائي

6. إعدادات التنبيهات والإشعارات:
   ──────────────────────────────
   - تنبيهات تحديث حالة الجواب
   - إشعارات التأخير في التسليم
   - تنبيهات الجوابات المرجعة
   - رسائل SMS للعملاء
   - قوالب رسائل البريد الإلكتروني
   - أوقات إرسال التنبيهات
   - تخصيص محتوى الرسائل
   - إعدادات الإشعارات الصوتية

7. إعدادات التكامل مع شركات الشحن:
   ──────────────────────────────────
   - APIs شركات الشحن
   - مفاتيح التشفير والمصادقة
   - تحديث أسعار الشحن تلقائياً
   - تتبع الجوابات عبر APIs
   - تحديث الحالات تلقائياً
   - تصدير البيانات لشركات الشحن
   - استيراد تحديثات التتبع

🔄 تدفق العمل الكامل للجوابات:
===============================================================================

1. إنشاء الفاتورة → 2. إنشاء الجواب → 3. تعبئة وتغليف → 4. تسليم لشركة الشحن
   ↓                    ↓                ↓                ↓
5. بدء الشحن → 6. تتبع المسار → 7. الوصول للعميل → 8. التسليم والتوقيع
   ↓              ↓               ↓                  ↓
9. تأكيد التسليم → 10. تحديث الفاتورة → 11. تحديث المخزون → 12. إنشاء التقارير

📋 قائمة مراجعة شاملة للمشروع:
===============================================================================

✅ إدارة المستخدمين والصلاحيات
✅ إدارة المخزون والأصناف
✅ إدارة المبيعات والفواتير
✅ إدارة الوكلاء وحساباتهم
✅ إدارة الجوابات والشحنات
✅ تتبع الجوابات في الوقت الفعلي
✅ إدارة شركات الشحن
✅ نظام التقارير الشامل
✅ نظام الإشعارات المتقدم
✅ نظام الأمان والتشفير
✅ المزامنة التلقائية
✅ العمل بدون إنترنت
✅ تصدير PDF بالعربية
✅ نظام OCR للنصوص
✅ إدارة الصور والوثائق
✅ تكامل مع APIs خارجية
✅ واجهة مستخدم عربية
✅ تحسينات الأداء
✅ إدارة المرتجعات
✅ تقييم رضا العملاء

🎯 الأهداف المحققة:
===============================================================================
- نظام إدارة نقل شامل ومتكامل
- تتبع كامل لدورة حياة المنتج من المخزن إلى العميل
- إدارة احترافية للوكلاء والشركاء
- تقارير تفصيلية لاتخاذ القرارات
- أمان عالي للبيانات والمعاملات
- تجربة مستخدم ممتازة باللغة العربية
- تكامل مع أنظمة خارجية
- مرونة في التشغيل والتخصيص

📈 إمكانيات التطوير المستقبلية:
===============================================================================
- تطبيق للعملاء لتتبع طلباتهم
- تكامل مع أنظمة ERP
- ذكاء اصطناعي لتحسين المسارات
- تحليلات متقدمة للبيانات
- تطبيق ويب للإدارة
- تكامل مع أنظمة المحاسبة
- API للتكامل مع أنظمة أخرى
- تطبيق للموردين

===============================================================================
                        تم إكمال الوثائق الشاملة والمفصلة
                     نظام إدارة النقل - شركة آل فرحان
                    تشمل جميع الميزات بما في ذلك تتبع الجوابات
===============================================================================

📝 ملاحظة مهمة:
هذه الوثائق تغطي النظام بالكامل بما في ذلك:
- جميع الشاشات (35+ شاشة)
- جميع النماذج (20+ نموذج)
- جميع الخدمات (16+ خدمة)
- جميع العمليات (75+ عملية)
- تتبع الجوابات والشحنات بالتفصيل
- علاقات البيانات الكاملة
- تدفق العمل الشامل

تاريخ آخر تحديث: 2025-07-15
الإصدار: 2.0 (شامل تتبع الجوابات)
