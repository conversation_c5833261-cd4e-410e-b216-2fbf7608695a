import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/windows/windows_image_picker_service.dart';
import '../../services/windows/windows_file_service.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';

/// شاشة الإعدادات المحسنة لـ Windows
class WindowsSettingsScreen extends StatefulWidget {
  const WindowsSettingsScreen({Key? key}) : super(key: key);

  @override
  State<WindowsSettingsScreen> createState() => _WindowsSettingsScreenState();
}

class _WindowsSettingsScreenState extends State<WindowsSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _companyNameController = TextEditingController();
  final _companyAddressController = TextEditingController();
  final _companyPhoneController = TextEditingController();
  final _companyEmailController = TextEditingController();
  
  bool _isLoading = false;
  String? _companyLogoPath;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل إعدادات الشركة من SharedPreferences أو Firebase
      // هذا مثال - يمكن تطويره ليتصل بالخدمات الحقيقية
      _companyNameController.text = 'آل فرحان للنقل الخفيف';
      _companyAddressController.text = 'الرياض، المملكة العربية السعودية';
      _companyPhoneController.text = '+966 50 123 4567';
      _companyEmailController.text = '<EMAIL>';
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل الإعدادات: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _pickCompanyLogo() async {
    try {
      final pickedFile = await WindowsImagePickerService.instance.pickImage();
      
      if (pickedFile != null) {
        setState(() {
          _companyLogoPath = pickedFile.path;
        });
        
        AppUtils.showSnackBar(context, 'تم اختيار شعار الشركة بنجاح');
      }
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في اختيار الشعار: $e', isError: true);
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // حفظ الإعدادات في SharedPreferences أو Firebase
      // هذا مثال - يمكن تطويره ليتصل بالخدمات الحقيقية
      
      AppUtils.showSnackBar(context, 'تم حفظ الإعدادات بنجاح');
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في حفظ الإعدادات: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _exportData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // تصدير البيانات باستخدام WindowsFileService
      final success = await WindowsFileService.instance.exportData();
      
      if (success) {
        AppUtils.showSnackBar(context, 'تم تصدير البيانات بنجاح');
      } else {
        AppUtils.showSnackBar(context, 'فشل في تصدير البيانات', isError: true);
      }
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في تصدير البيانات: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _importData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // استيراد البيانات باستخدام WindowsFileService
      final success = await WindowsFileService.instance.importData();
      
      if (success) {
        AppUtils.showSnackBar(context, 'تم استيراد البيانات بنجاح');
      } else {
        AppUtils.showSnackBar(context, 'فشل في استيراد البيانات', isError: true);
      }
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في استيراد البيانات: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: Colors.grey[700],
        foregroundColor: Colors.white,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildCompanyInfoSection(),
                    const SizedBox(height: AppConstants.largePadding),
                    _buildDataManagementSection(),
                    const SizedBox(height: AppConstants.largePadding),
                    _buildSystemInfoSection(),
                    const SizedBox(height: AppConstants.largePadding),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildCompanyInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الشركة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // شعار الشركة
            Row(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: _companyLogoPath != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.asset(
                            _companyLogoPath!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(Icons.business, size: 40);
                            },
                          ),
                        )
                      : const Icon(Icons.business, size: 40),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                ElevatedButton.icon(
                  onPressed: _pickCompanyLogo,
                  icon: const Icon(Icons.upload),
                  label: const Text('اختيار شعار'),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // اسم الشركة
            TextFormField(
              controller: _companyNameController,
              decoration: const InputDecoration(
                labelText: 'اسم الشركة',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم الشركة';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // عنوان الشركة
            TextFormField(
              controller: _companyAddressController,
              decoration: const InputDecoration(
                labelText: 'عنوان الشركة',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // هاتف الشركة
            TextFormField(
              controller: _companyPhoneController,
              decoration: const InputDecoration(
                labelText: 'هاتف الشركة',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // بريد الشركة
            TextFormField(
              controller: _companyEmailController,
              decoration: const InputDecoration(
                labelText: 'بريد الشركة الإلكتروني',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataManagementSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إدارة البيانات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _exportData,
                    icon: const Icon(Icons.download),
                    label: const Text('تصدير البيانات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _importData,
                    icon: const Icon(Icons.upload),
                    label: const Text('استيراد البيانات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات النظام',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            _buildInfoRow('النسخة', '1.0.0'),
            _buildInfoRow('المنصة', 'Windows'),
            _buildInfoRow('نوع النسخة', 'Complete Android Replica'),
            _buildInfoRow('حالة Firebase', 'متصل'),
            _buildInfoRow('حالة قاعدة البيانات', 'متصلة'),
            
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return _buildInfoRow(
                  'المستخدم الحالي', 
                  authProvider.currentUser?.fullName ?? 'غير محدد'
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _saveSettings,
            icon: const Icon(Icons.save),
            label: const Text('حفظ الإعدادات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        ElevatedButton.icon(
          onPressed: _loadSettings,
          icon: const Icon(Icons.refresh),
          label: const Text('إعادة تحميل'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    _companyAddressController.dispose();
    _companyPhoneController.dispose();
    _companyEmailController.dispose();
    super.dispose();
  }
}
