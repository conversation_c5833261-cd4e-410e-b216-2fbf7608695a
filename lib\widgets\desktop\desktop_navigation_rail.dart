import 'package:flutter/material.dart';
import '../../core/theme/desktop_theme.dart';

/// Professional desktop navigation rail similar to Microsoft Office navigation
class DesktopNavigationRail extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int>? onDestinationSelected;
  final List<DesktopNavigationDestination> destinations;
  final Widget? header;
  final Widget? footer;
  final bool extended;
  final double? width;
  final Color? backgroundColor;

  const DesktopNavigationRail({
    super.key,
    required this.selectedIndex,
    required this.destinations,
    this.onDestinationSelected,
    this.header,
    this.footer,
    this.extended = true,
    this.width,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveWidth = width ?? (extended ? 280.0 : 72.0);
    
    return Container(
      width: effectiveWidth,
      decoration: BoxDecoration(
        color: backgroundColor ?? DesktopTheme.backgroundSecondary,
        border: Border(
          right: BorderSide(
            color: DesktopTheme.borderLight,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          if (header != null) ...[
            header!,
            Divider(
              color: DesktopTheme.borderLight,
              height: 1,
              thickness: 1,
            ),
          ],
          
          // Navigation Items
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: DesktopTheme.spacingSmall),
              itemCount: destinations.length,
              itemBuilder: (context, index) {
                return _buildNavigationItem(
                  destinations[index],
                  index,
                  selectedIndex == index,
                );
              },
            ),
          ),
          
          // Footer
          if (footer != null) ...[
            Divider(
              color: DesktopTheme.borderLight,
              height: 1,
              thickness: 1,
            ),
            footer!,
          ],
        ],
      ),
    );
  }

  Widget _buildNavigationItem(
    DesktopNavigationDestination destination,
    int index,
    bool isSelected,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: DesktopTheme.spacingSmall,
        vertical: 2,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onDestinationSelected?.call(index),
          borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
          child: Container(
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
              color: isSelected
                  ? DesktopTheme.primaryBlue.withOpacity(0.1)
                  : null,
              border: isSelected
                  ? Border.all(
                      color: DesktopTheme.primaryBlue.withOpacity(0.3),
                      width: 1,
                    )
                  : null,
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  width: 48,
                  height: 48,
                  child: Icon(
                    destination.icon,
                    size: 20,
                    color: isSelected
                        ? DesktopTheme.primaryBlue
                        : DesktopTheme.textSecondary,
                  ),
                ),
                
                // Label (only shown when extended)
                if (extended) ...[
                  const SizedBox(width: DesktopTheme.spacingSmall),
                  Expanded(
                    child: Text(
                      destination.label,
                      style: DesktopTheme.titleSmall.copyWith(
                        color: isSelected
                            ? DesktopTheme.primaryBlue
                            : DesktopTheme.textPrimary,
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.w400,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  
                  // Badge (if any)
                  if (destination.badge != null) ...[
                    const SizedBox(width: DesktopTheme.spacingSmall),
                    destination.badge!,
                  ],
                  
                  const SizedBox(width: DesktopTheme.spacingSmall),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Represents a navigation destination in the rail
class DesktopNavigationDestination {
  final String label;
  final IconData icon;
  final Widget? badge;
  final String? tooltip;
  final List<DesktopNavigationDestination>? children;

  const DesktopNavigationDestination({
    required this.label,
    required this.icon,
    this.badge,
    this.tooltip,
    this.children,
  });
}

/// Expandable navigation rail for hierarchical navigation
class ExpandableDesktopNavigationRail extends StatefulWidget {
  final int selectedIndex;
  final ValueChanged<int>? onDestinationSelected;
  final List<DesktopNavigationDestination> destinations;
  final Widget? header;
  final Widget? footer;
  final bool extended;
  final double? width;
  final Color? backgroundColor;

  const ExpandableDesktopNavigationRail({
    super.key,
    required this.selectedIndex,
    required this.destinations,
    this.onDestinationSelected,
    this.header,
    this.footer,
    this.extended = true,
    this.width,
    this.backgroundColor,
  });

  @override
  State<ExpandableDesktopNavigationRail> createState() =>
      _ExpandableDesktopNavigationRailState();
}

class _ExpandableDesktopNavigationRailState
    extends State<ExpandableDesktopNavigationRail> {
  final Set<int> _expandedItems = <int>{};

  @override
  Widget build(BuildContext context) {
    final effectiveWidth = widget.width ?? (widget.extended ? 280.0 : 72.0);
    
    return Container(
      width: effectiveWidth,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? DesktopTheme.backgroundSecondary,
        border: Border(
          right: BorderSide(
            color: DesktopTheme.borderLight,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          if (widget.header != null) ...[
            widget.header!,
            Divider(
              color: DesktopTheme.borderLight,
              height: 1,
              thickness: 1,
            ),
          ],
          
          // Navigation Items
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: DesktopTheme.spacingSmall),
              itemCount: _getTotalItemCount(),
              itemBuilder: (context, index) {
                return _buildNavigationItemRecursive(index);
              },
            ),
          ),
          
          // Footer
          if (widget.footer != null) ...[
            Divider(
              color: DesktopTheme.borderLight,
              height: 1,
              thickness: 1,
            ),
            widget.footer!,
          ],
        ],
      ),
    );
  }

  int _getTotalItemCount() {
    int count = 0;
    for (int i = 0; i < widget.destinations.length; i++) {
      count++; // Parent item
      if (_expandedItems.contains(i) && widget.destinations[i].children != null) {
        count += widget.destinations[i].children!.length;
      }
    }
    return count;
  }

  Widget _buildNavigationItemRecursive(int flatIndex) {
    int currentIndex = 0;
    
    for (int i = 0; i < widget.destinations.length; i++) {
      if (currentIndex == flatIndex) {
        // This is a parent item
        return _buildParentNavigationItem(widget.destinations[i], i);
      }
      currentIndex++;
      
      if (_expandedItems.contains(i) && widget.destinations[i].children != null) {
        final children = widget.destinations[i].children!;
        for (int j = 0; j < children.length; j++) {
          if (currentIndex == flatIndex) {
            // This is a child item
            return _buildChildNavigationItem(children[j], i, j);
          }
          currentIndex++;
        }
      }
    }
    
    return const SizedBox.shrink();
  }

  Widget _buildParentNavigationItem(
    DesktopNavigationDestination destination,
    int index,
  ) {
    final isSelected = widget.selectedIndex == index;
    final hasChildren = destination.children != null && destination.children!.isNotEmpty;
    final isExpanded = _expandedItems.contains(index);
    
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: DesktopTheme.spacingSmall,
        vertical: 2,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (hasChildren) {
              setState(() {
                if (isExpanded) {
                  _expandedItems.remove(index);
                } else {
                  _expandedItems.add(index);
                }
              });
            } else {
              widget.onDestinationSelected?.call(index);
            }
          },
          borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
          child: Container(
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
              color: isSelected
                  ? DesktopTheme.primaryBlue.withOpacity(0.1)
                  : null,
              border: isSelected
                  ? Border.all(
                      color: DesktopTheme.primaryBlue.withOpacity(0.3),
                      width: 1,
                    )
                  : null,
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  width: 48,
                  height: 48,
                  child: Icon(
                    destination.icon,
                    size: 20,
                    color: isSelected
                        ? DesktopTheme.primaryBlue
                        : DesktopTheme.textSecondary,
                  ),
                ),
                
                // Label (only shown when extended)
                if (widget.extended) ...[
                  const SizedBox(width: DesktopTheme.spacingSmall),
                  Expanded(
                    child: Text(
                      destination.label,
                      style: DesktopTheme.titleSmall.copyWith(
                        color: isSelected
                            ? DesktopTheme.primaryBlue
                            : DesktopTheme.textPrimary,
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.w400,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  
                  // Expand/Collapse Icon
                  if (hasChildren)
                    Icon(
                      isExpanded
                          ? Icons.keyboard_arrow_down
                          : Icons.keyboard_arrow_right,
                      size: 16,
                      color: DesktopTheme.textSecondary,
                    ),
                  
                  const SizedBox(width: DesktopTheme.spacingSmall),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChildNavigationItem(
    DesktopNavigationDestination destination,
    int parentIndex,
    int childIndex,
  ) {
    // Calculate the flat index for this child
    int flatIndex = parentIndex + 1;
    for (int i = 0; i < parentIndex; i++) {
      if (_expandedItems.contains(i) && widget.destinations[i].children != null) {
        flatIndex += widget.destinations[i].children!.length;
      }
    }
    flatIndex += childIndex;
    
    final isSelected = widget.selectedIndex == flatIndex;
    
    return Container(
      margin: const EdgeInsets.only(
        left: DesktopTheme.spacingLarge + DesktopTheme.spacingSmall,
        right: DesktopTheme.spacingSmall,
        top: 2,
        bottom: 2,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => widget.onDestinationSelected?.call(flatIndex),
          borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
          child: Container(
            height: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
              color: isSelected
                  ? DesktopTheme.primaryBlue.withOpacity(0.1)
                  : null,
              border: isSelected
                  ? Border.all(
                      color: DesktopTheme.primaryBlue.withOpacity(0.3),
                      width: 1,
                    )
                  : null,
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  width: 40,
                  height: 40,
                  child: Icon(
                    destination.icon,
                    size: 16,
                    color: isSelected
                        ? DesktopTheme.primaryBlue
                        : DesktopTheme.textSecondary,
                  ),
                ),
                
                // Label (only shown when extended)
                if (widget.extended) ...[
                  const SizedBox(width: DesktopTheme.spacingSmall),
                  Expanded(
                    child: Text(
                      destination.label,
                      style: DesktopTheme.bodyMedium.copyWith(
                        color: isSelected
                            ? DesktopTheme.primaryBlue
                            : DesktopTheme.textPrimary,
                        fontWeight: isSelected
                            ? FontWeight.w500
                            : FontWeight.w400,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  
                  const SizedBox(width: DesktopTheme.spacingSmall),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
