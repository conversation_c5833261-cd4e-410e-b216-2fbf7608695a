import 'package:flutter/material.dart';

class DetailedSalesReport extends StatefulWidget {
  const DetailedSalesReport({super.key});

  @override
  State<DetailedSalesReport> createState() => _DetailedSalesReportState();
}

class _DetailedSalesReportState extends State<DetailedSalesReport> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقرير المبيعات التفصيلي'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.point_of_sale,
              size: 64,
              color: Colors.blue,
            ),
            SizedBox(height: 16),
            Text(
              'تقرير المبيعات التفصيلي',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'قريباً...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
