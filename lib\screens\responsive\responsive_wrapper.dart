import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:io' show Platform;
import '../desktop/desktop_home_screen.dart';
import '../home/<USER>';

class ResponsiveWrapper extends StatelessWidget {
  const ResponsiveWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine if we should use desktop layout
        final isDesktop = _shouldUseDesktopLayout(constraints);
        
        if (isDesktop) {
          return const DesktopHomeScreen();
        } else {
          return const HomeScreen();
        }
      },
    );
  }

  bool _shouldUseDesktopLayout(BoxConstraints constraints) {
    // Use desktop layout if:
    // 1. Running on Windows/macOS/Linux
    // 2. Screen width is large enough (>= 1024px)
    // 3. Not on mobile platforms
    
    if (kIsWeb) {
      return constraints.maxWidth >= 1024;
    }
    
    try {
      if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        return true;
      }
    } catch (e) {
      // Platform check failed, fallback to screen size
    }
    
    return constraints.maxWidth >= 1024;
  }
}

class ResponsiveBuilder extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget desktop;

  const ResponsiveBuilder({
    super.key,
    required this.mobile,
    this.tablet,
    required this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1024) {
          return desktop;
        } else if (constraints.maxWidth >= 768 && tablet != null) {
          return tablet!;
        } else {
          return mobile;
        }
      },
    );
  }
}

class ScreenBreakpoints {
  static const double mobile = 768;
  static const double tablet = 1024;
  static const double desktop = 1200;
  
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < tablet;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tablet;
  }
}
