import 'package:flutter/foundation.dart';

class SimpleDataService {
  static SimpleDataService? _instance;
  static SimpleDataService get instance => _instance ??= SimpleDataService._();

  SimpleDataService._();

  // Sample data for demo (using simple maps instead of complex models)
  List<Map<String, dynamic>> _items = [];
  List<Map<String, dynamic>> _warehouses = [];
  List<Map<String, dynamic>> _invoices = [];
  List<Map<String, dynamic>> _users = [];

  // Initialize with sample data
  Future<void> initialize() async {
    if (kDebugMode) {
      print('🔄 Initializing SimpleDataService...');
    }

    await _initializeSampleData();

    if (kDebugMode) {
      print('✅ SimpleDataService initialized with sample data');
    }
  }

  Future<void> _initializeSampleData() async {
    // Sample warehouses
    _warehouses = [
      {
        'id': '1',
        'name': 'المخزن الرئيسي',
        'type': 'main',
        'address': 'القاهرة - مدينة نصر',
        'isActive': true,
        'createdAt': DateTime.now(),
        'updatedAt': DateTime.now(),
      },
      {
        'id': '2',
        'name': 'صالة العرض',
        'type': 'showroom',
        'address': 'القاهرة - التجمع الخامس',
        'isActive': true,
        'createdAt': DateTime.now(),
        'updatedAt': DateTime.now(),
      },
      {
        'id': '3',
        'name': 'مخزن الوكيل أحمد',
        'type': 'agent',
        'address': 'الجيزة - الهرم',
        'ownerId': 'agent1',
        'isActive': true,
        'createdAt': DateTime.now(),
        'updatedAt': DateTime.now(),
      },
    ];

    // Sample items
    _items = [
      {
        'id': '1',
        'name': 'دراجة نارية هوندا 150',
        'type': 'دراجة نارية',
        'model': 'هوندا 150CC',
        'color': 'أحمر',
        'brand': 'هوندا',
        'purchasePrice': 25000.0,
        'sellingPrice': 30000.0,
        'chassisNumber': 'HD150-2024-001',
        'warehouseId': '1',
        'status': 'متاح',
        'createdAt': DateTime.now(),
        'updatedAt': DateTime.now(),
      },
      {
        'id': '2',
        'name': 'دراجة نارية ياماها 125',
        'type': 'دراجة نارية',
        'model': 'ياماها 125CC',
        'color': 'أزرق',
        'brand': 'ياماها',
        'purchasePrice': 22000.0,
        'sellingPrice': 27000.0,
        'chassisNumber': 'YM125-2024-002',
        'warehouseId': '1',
        'status': 'متاح',
        'createdAt': DateTime.now(),
        'updatedAt': DateTime.now(),
      },
      {
        'id': '3',
        'name': 'دراجة نارية سوزوكي 200',
        'type': 'دراجة نارية',
        'model': 'سوزوكي 200CC',
        'color': 'أسود',
        'brand': 'سوزوكي',
        'purchasePrice': 35000.0,
        'sellingPrice': 42000.0,
        'chassisNumber': 'SZ200-2024-003',
        'warehouseId': '2',
        'status': 'مباع',
        'createdAt': DateTime.now(),
        'updatedAt': DateTime.now(),
      },
    ];

    // Sample invoices
    _invoices = [
      {
        'id': '1',
        'invoiceNumber': 'INV-2024-001',
        'customerId': 'CUST-001',
        'customerName': 'محمد أحمد علي',
        'customerPhone': '01234567890',
        'customerAddress': 'القاهرة - مصر الجديدة',
        'itemId': '3',
        'totalAmount': 42000.0,
        'profitAmount': 7000.0,
        'agentId': 'agent1',
        'agentName': 'محمد أحمد',
        'warehouseId': '2',
        'status': 'completed',
        'createdAt': DateTime.now().subtract(const Duration(days: 5)),
        'updatedAt': DateTime.now().subtract(const Duration(days: 5)),
      },
    ];

    // Sample users
    _users = [
      {
        'id': '1',
        'username': 'ahmed',
        'fullName': 'أحمد محمد',
        'email': '<EMAIL>',
        'phone': '01234567890',
        'role': 'super_admin',
        'isActive': true,
        'createdAt': DateTime.now(),
        'updatedAt': DateTime.now(),
      },
      {
        'id': '2',
        'username': 'agent1',
        'fullName': 'محمد أحمد',
        'email': '<EMAIL>',
        'phone': '01234567891',
        'role': 'agent',
        'warehouseId': '3',
        'isActive': true,
        'createdAt': DateTime.now(),
        'updatedAt': DateTime.now(),
      },
    ];
  }

  // Get dashboard statistics
  Future<Map<String, dynamic>> getDashboardStats() async {
    final totalItems = _items.length;
    final availableItems = _items.where((item) => item['status'] == 'متاح').length;
    final soldItems = _items.where((item) => item['status'] == 'مباع').length;
    final totalInvoices = _invoices.length;
    final totalRevenue = _invoices.fold<double>(0, (sum, invoice) => sum + (invoice['totalAmount'] as double));
    final totalProfit = _invoices.fold<double>(0, (sum, invoice) => sum + (invoice['profitAmount'] as double));

    return {
      'totalItems': totalItems,
      'availableItems': availableItems,
      'soldItems': soldItems,
      'totalInvoices': totalInvoices,
      'totalRevenue': totalRevenue,
      'totalProfit': totalProfit,
      'totalWarehouses': _warehouses.length,
      'totalUsers': _users.length,
    };
  }

  // Get items
  Future<List<Map<String, dynamic>>> getItems({String? warehouseId, String? status}) async {
    var items = List<Map<String, dynamic>>.from(_items);

    if (warehouseId != null) {
      items = items.where((item) => item['warehouseId'] == warehouseId).toList();
    }

    if (status != null) {
      items = items.where((item) => item['status'] == status).toList();
    }

    return items;
  }

  // Get warehouses
  Future<List<Map<String, dynamic>>> getWarehouses() async {
    return List<Map<String, dynamic>>.from(_warehouses);
  }

  // Get invoices
  Future<List<Map<String, dynamic>>> getInvoices({String? agentId}) async {
    var invoices = List<Map<String, dynamic>>.from(_invoices);

    if (agentId != null) {
      invoices = invoices.where((invoice) => invoice['agentId'] == agentId).toList();
    }

    return invoices;
  }

  // Get users
  Future<List<Map<String, dynamic>>> getUsers() async {
    return List<Map<String, dynamic>>.from(_users);
  }

  // Get recent activities (mock data)
  Future<List<Map<String, dynamic>>> getRecentActivities() async {
    return [
      {
        'id': '1',
        'type': 'sale',
        'title': 'بيع دراجة نارية',
        'description': 'تم بيع دراجة سوزوكي 200 للعميل محمد أحمد علي',
        'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
        'icon': 'sale',
        'color': 'green',
      },
      {
        'id': '2',
        'type': 'transfer',
        'title': 'تحويل بضاعة',
        'description': 'تم تحويل 3 دراجات من المخزن الرئيسي إلى صالة العرض',
        'timestamp': DateTime.now().subtract(const Duration(hours: 5)),
        'icon': 'transfer',
        'color': 'blue',
      },
      {
        'id': '3',
        'type': 'payment',
        'title': 'دفعة من وكيل',
        'description': 'تم استلام دفعة 15000 جنيه من الوكيل محمد أحمد',
        'timestamp': DateTime.now().subtract(const Duration(days: 1)),
        'icon': 'payment',
        'color': 'orange',
      },
    ];
  }

  // Add item (mock)
  Future<bool> addItem(Map<String, dynamic> item) async {
    _items.add(item);
    if (kDebugMode) {
      print('✅ Item added: ${item['name']}');
    }
    return true;
  }

  // Update item (mock)
  Future<bool> updateItem(Map<String, dynamic> item) async {
    final index = _items.indexWhere((i) => i['id'] == item['id']);
    if (index != -1) {
      _items[index] = item;
      if (kDebugMode) {
        print('✅ Item updated: ${item['name']}');
      }
      return true;
    }
    return false;
  }

  // Delete item (mock)
  Future<bool> deleteItem(String itemId) async {
    final index = _items.indexWhere((i) => i['id'] == itemId);
    if (index != -1) {
      final item = _items.removeAt(index);
      if (kDebugMode) {
        print('✅ Item deleted: ${item['name']}');
      }
      return true;
    }
    return false;
  }
}
