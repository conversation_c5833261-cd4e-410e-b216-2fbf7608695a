org.gradle.jvmargs=-Xmx6G -XX:MaxMetaspaceSize=3G -XX:ReservedCodeCacheSize=1G -XX:+HeapDumpOnOutOfMemoryError -XX:+UseG1GC
android.useAndroidX=true
android.enableJetifier=true

# Kotlin daemon settings - Optimized
kotlin.daemon.jvmargs=-Xmx3G -XX:MaxMetaspaceSize=1G -XX:+UseG1GC
kotlin.incremental=true
kotlin.caching.enabled=true
kotlin.compiler.execution.strategy=daemon

# Gradle daemon settings - Optimized
org.gradle.daemon=true
org.gradle.parallel=false
org.gradle.configureondemand=false
org.gradle.workers.max=2

# Android settings
android.enableR8.fullMode=false
android.useFullClasspathForDexingTransform=true

# Build performance
org.gradle.caching=true
org.gradle.vfs.watch=false
