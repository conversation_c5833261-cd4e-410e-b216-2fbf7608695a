import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// أنواع الحسابات
enum AccountType {
  asset,     // أصول
  liability, // خصوم
  equity,    // حقوق الملكية
  revenue,   // إيرادات
  expense,   // مصروفات
}

/// فئات الحسابات
enum AccountCategory {
  // الأصول
  currentAssets,    // أصول متداولة
  fixedAssets,      // أصول ثابتة
  intangibleAssets, // أصول غير ملموسة
  
  // الخصوم
  currentLiabilities, // خصوم متداولة
  longTermLiabilities, // خصوم طويلة الأجل
  
  // حقوق الملكية
  capital,           // رأس المال
  retainedEarnings,  // الأرباح المحتجزة
  
  // الإيرادات
  operatingRevenue,  // إيرادات تشغيلية
  otherRevenue,      // إيرادات أخرى
  
  // المصروفات
  operatingExpenses, // مصروفات تشغيلية
  administrativeExpenses, // مصروفات إدارية
  financialExpenses, // مصروفات مالية
  otherExpenses,     // مصروفات أخرى
}

/// حالات القيد المحاسبي
enum JournalEntryStatus {
  draft,     // مسودة
  posted,    // مرحل
  approved,  // معتمد
  rejected,  // مرفوض
  cancelled, // ملغي
}

/// نموذج الحساب المحاسبي
class AccountModel {
  final String id;
  final String code;
  final String nameAr;
  final String nameEn;
  final AccountType type;
  final AccountCategory category;
  final bool isActive;
  final double currentBalance;
  final String currency;
  final DateTime createdAt;
  final String createdBy;
  final int version;

  const AccountModel({
    required this.id,
    required this.code,
    required this.nameAr,
    required this.nameEn,
    required this.type,
    required this.category,
    this.isActive = true,
    this.currentBalance = 0.0,
    this.currency = 'EGP',
    required this.createdAt,
    required this.createdBy,
    this.version = 1,
  });

  AccountModel copyWith({
    String? id,
    String? code,
    String? nameAr,
    String? nameEn,
    AccountType? type,
    AccountCategory? category,
    bool? isActive,
    double? currentBalance,
    String? currency,
    DateTime? createdAt,
    String? createdBy,
    int? version,
  }) {
    return AccountModel(
      id: id ?? this.id,
      code: code ?? this.code,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      type: type ?? this.type,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      currentBalance: currentBalance ?? this.currentBalance,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      version: version ?? this.version,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'code': code,
      'nameAr': nameAr,
      'nameEn': nameEn,
      'type': type.name,
      'category': category.name,
      'isActive': isActive,
      'currentBalance': currentBalance,
      'currency': currency,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'version': version,
      'lastModified': Timestamp.now(),
      'isDeleted': false,
    };
  }

  factory AccountModel.fromFirestore(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>?;
      if (data == null) throw Exception('Document data is null');

      return AccountModel(
        id: data['id'] ?? doc.id,
        code: data['code'] ?? '',
        nameAr: data['nameAr'] ?? '',
        nameEn: data['nameEn'] ?? '',
        type: AccountType.values.firstWhere(
          (e) => e.name == data['type'],
          orElse: () => AccountType.asset,
        ),
        category: AccountCategory.values.firstWhere(
          (e) => e.name == data['category'],
          orElse: () => AccountCategory.currentAssets,
        ),
        isActive: data['isActive'] ?? true,
        currentBalance: (data['currentBalance'] ?? 0.0).toDouble(),
        currency: data['currency'] ?? 'EGP',
        createdAt: data['createdAt'] != null 
            ? (data['createdAt'] as Timestamp).toDate()
            : DateTime.now(),
        createdBy: data['createdBy'] ?? '',
        version: data['version'] ?? 1,
      );
    } catch (e) {
      if (kDebugMode) print('Error parsing AccountModel: $e');
      rethrow;
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'nameAr': nameAr,
      'nameEn': nameEn,
      'type': type.name,
      'category': category.name,
      'isActive': isActive,
      'currentBalance': currentBalance,
      'currency': currency,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'version': version,
    };
  }

  factory AccountModel.fromMap(Map<String, dynamic> map) {
    return AccountModel(
      id: map['id'] ?? '',
      code: map['code'] ?? '',
      nameAr: map['nameAr'] ?? '',
      nameEn: map['nameEn'] ?? '',
      type: AccountType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => AccountType.asset,
      ),
      category: AccountCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => AccountCategory.currentAssets,
      ),
      isActive: map['isActive'] ?? true,
      currentBalance: (map['currentBalance'] ?? 0.0).toDouble(),
      currency: map['currency'] ?? 'EGP',
      createdAt: map['createdAt'] is DateTime 
          ? map['createdAt']
          : DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      createdBy: map['createdBy'] ?? '',
      version: map['version'] ?? 1,
    );
  }
}

/// نموذج بند القيد المحاسبي
class JournalEntryLineModel {
  final String id;
  final String accountId;
  final String accountCode;
  final String accountName;
  final double debitAmount;
  final double creditAmount;
  final String description;
  final int lineNumber;

  const JournalEntryLineModel({
    required this.id,
    required this.accountId,
    required this.accountCode,
    required this.accountName,
    this.debitAmount = 0.0,
    this.creditAmount = 0.0,
    required this.description,
    this.lineNumber = 1,
  });

  double get amount => debitAmount > 0 ? debitAmount : creditAmount;
  bool get isDebit => debitAmount > 0;
  bool get isCredit => creditAmount > 0;
  bool get isValid => (debitAmount > 0) != (creditAmount > 0) && accountId.isNotEmpty;

  JournalEntryLineModel copyWith({
    String? id,
    String? accountId,
    String? accountCode,
    String? accountName,
    double? debitAmount,
    double? creditAmount,
    String? description,
    int? lineNumber,
  }) {
    return JournalEntryLineModel(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId,
      accountCode: accountCode ?? this.accountCode,
      accountName: accountName ?? this.accountName,
      debitAmount: debitAmount ?? this.debitAmount,
      creditAmount: creditAmount ?? this.creditAmount,
      description: description ?? this.description,
      lineNumber: lineNumber ?? this.lineNumber,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'accountId': accountId,
      'accountCode': accountCode,
      'accountName': accountName,
      'debitAmount': debitAmount,
      'creditAmount': creditAmount,
      'description': description,
      'lineNumber': lineNumber,
    };
  }

  factory JournalEntryLineModel.fromMap(Map<String, dynamic> map) {
    return JournalEntryLineModel(
      id: map['id'] ?? '',
      accountId: map['accountId'] ?? '',
      accountCode: map['accountCode'] ?? '',
      accountName: map['accountName'] ?? '',
      debitAmount: (map['debitAmount'] ?? 0.0).toDouble(),
      creditAmount: (map['creditAmount'] ?? 0.0).toDouble(),
      description: map['description'] ?? '',
      lineNumber: map['lineNumber'] ?? 1,
    );
  }
}

/// نموذج القيد المحاسبي
class JournalEntryModel {
  final String id;
  final String entryNumber;
  final DateTime date;
  final String description;
  final String reference;
  final List<JournalEntryLineModel> lines;
  final double totalDebit;
  final double totalCredit;
  final JournalEntryStatus status;
  final String currency;
  final DateTime createdAt;
  final String createdBy;
  final int version;

  const JournalEntryModel({
    required this.id,
    required this.entryNumber,
    required this.date,
    required this.description,
    required this.reference,
    required this.lines,
    required this.totalDebit,
    required this.totalCredit,
    this.status = JournalEntryStatus.draft,
    this.currency = 'EGP',
    required this.createdAt,
    required this.createdBy,
    this.version = 1,
  });

  bool get isBalanced => (totalDebit - totalCredit).abs() < 0.01;
  bool get canEdit => status == JournalEntryStatus.draft;
  bool get canDelete => status == JournalEntryStatus.draft;
  bool get canPost => status == JournalEntryStatus.draft && isBalanced && lines.length >= 2;

  JournalEntryModel copyWith({
    String? id,
    String? entryNumber,
    DateTime? date,
    String? description,
    String? reference,
    List<JournalEntryLineModel>? lines,
    double? totalDebit,
    double? totalCredit,
    JournalEntryStatus? status,
    String? currency,
    DateTime? createdAt,
    String? createdBy,
    int? version,
  }) {
    return JournalEntryModel(
      id: id ?? this.id,
      entryNumber: entryNumber ?? this.entryNumber,
      date: date ?? this.date,
      description: description ?? this.description,
      reference: reference ?? this.reference,
      lines: lines ?? this.lines,
      totalDebit: totalDebit ?? this.totalDebit,
      totalCredit: totalCredit ?? this.totalCredit,
      status: status ?? this.status,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      version: version ?? this.version,
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'entryNumber': entryNumber,
      'date': Timestamp.fromDate(date),
      'description': description,
      'reference': reference,
      'lines': lines.map((line) => line.toMap()).toList(),
      'totalDebit': totalDebit,
      'totalCredit': totalCredit,
      'status': status.name,
      'currency': currency,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'version': version,
      'lastModified': Timestamp.now(),
      'isDeleted': false,
      'isBalanced': isBalanced,
    };
  }

  factory JournalEntryModel.fromFirestore(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>?;
      if (data == null) throw Exception('Document data is null');

      return JournalEntryModel(
        id: data['id'] ?? doc.id,
        entryNumber: data['entryNumber'] ?? '',
        date: data['date'] != null 
            ? (data['date'] as Timestamp).toDate()
            : DateTime.now(),
        description: data['description'] ?? '',
        reference: data['reference'] ?? '',
        lines: (data['lines'] as List<dynamic>? ?? [])
            .map((line) => JournalEntryLineModel.fromMap(line as Map<String, dynamic>))
            .toList(),
        totalDebit: (data['totalDebit'] ?? 0.0).toDouble(),
        totalCredit: (data['totalCredit'] ?? 0.0).toDouble(),
        status: JournalEntryStatus.values.firstWhere(
          (e) => e.name == data['status'],
          orElse: () => JournalEntryStatus.draft,
        ),
        currency: data['currency'] ?? 'EGP',
        createdAt: data['createdAt'] != null 
            ? (data['createdAt'] as Timestamp).toDate()
            : DateTime.now(),
        createdBy: data['createdBy'] ?? '',
        version: data['version'] ?? 1,
      );
    } catch (e) {
      if (kDebugMode) print('Error parsing JournalEntryModel: $e');
      rethrow;
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'entryNumber': entryNumber,
      'date': date.toIso8601String(),
      'description': description,
      'reference': reference,
      'lines': lines.map((line) => line.toMap()).toList(),
      'totalDebit': totalDebit,
      'totalCredit': totalCredit,
      'status': status.name,
      'currency': currency,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'version': version,
    };
  }

  factory JournalEntryModel.fromMap(Map<String, dynamic> map) {
    return JournalEntryModel(
      id: map['id'] ?? '',
      entryNumber: map['entryNumber'] ?? '',
      date: map['date'] is DateTime 
          ? map['date']
          : DateTime.parse(map['date'] ?? DateTime.now().toIso8601String()),
      description: map['description'] ?? '',
      reference: map['reference'] ?? '',
      lines: (map['lines'] as List<dynamic>? ?? [])
          .map((line) => JournalEntryLineModel.fromMap(line as Map<String, dynamic>))
          .toList(),
      totalDebit: (map['totalDebit'] ?? 0.0).toDouble(),
      totalCredit: (map['totalCredit'] ?? 0.0).toDouble(),
      status: JournalEntryStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => JournalEntryStatus.draft,
      ),
      currency: map['currency'] ?? 'EGP',
      createdAt: map['createdAt'] is DateTime 
          ? map['createdAt']
          : DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      createdBy: map['createdBy'] ?? '',
      version: map['version'] ?? 1,
    );
  }
}

/// نماذج إضافية للمحاسبة
class JournalStatistics {
  final int totalEntries;
  final int draftEntries;
  final int postedEntries;
  final int approvedEntries;
  final int rejectedEntries;
  final double totalDebits;
  final double totalCredits;
  final int unbalancedEntries;
  final DateTime lastEntryDate;
  final String lastEntryNumber;
  final Map<String, int> entriesByMonth;
  final DateTime generatedAt;

  const JournalStatistics({
    required this.totalEntries,
    required this.draftEntries,
    required this.postedEntries,
    required this.approvedEntries,
    required this.rejectedEntries,
    required this.totalDebits,
    required this.totalCredits,
    required this.unbalancedEntries,
    required this.lastEntryDate,
    required this.lastEntryNumber,
    required this.entriesByMonth,
    required this.generatedAt,
  });

  bool get isBalanced => (totalDebits - totalCredits).abs() < 0.01;
}
