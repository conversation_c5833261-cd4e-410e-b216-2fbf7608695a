import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../widgets/ultimate_image_widget.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/item_model.dart';
import '../../models/warehouse_model.dart';
import '../../services/data_service.dart';
import '../../providers/auth_provider.dart';
import 'add_item_screen.dart';
import 'item_details_screen.dart';

class InventoryScreen extends StatefulWidget {
  const InventoryScreen({super.key});

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen> {
  final DataService _dataService = DataService.instance;
  final TextEditingController _searchController = TextEditingController();

  List<ItemModel> _items = [];
  List<ItemModel> _filteredItems = [];
  List<WarehouseModel> _warehouses = [];
  String? _selectedWarehouseId;
  String? _selectedStatus = 'متاح'; // Default to 'متاح' (Available)
  String? _selectedType;
  bool _isLoading = false;

  final List<String> _statusOptions = [
    'الكل',
    'متاح',
    'مباع',
    'محول',
    'مرتجع',
  ];

  final List<String> _typeOptions = [
    'الكل',
    'موتوسيكل',
    'تروسيكل',
    'سكوتر كهرباء',
    'توكتوك',
  ];

  @override
  void initState() {
    super.initState();
    _loadData();
    _searchController.addListener(_filterItems);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      // Load warehouses based on user permissions
      if (currentUser != null && currentUser.isAgent) {
        // Agents should only see their own warehouses
        _warehouses = await _dataService.getWarehousesByOwnerId(currentUser.id);

        if (kDebugMode) {
          print('Agent ${currentUser.fullName} loaded ${_warehouses.length} warehouses');
        }
      } else {
        // Admins and Super Admins can see ALL warehouses
        _warehouses = await _dataService.getWarehouses(isActive: true);

        if (kDebugMode) {
          print('Admin/Manager loaded ${_warehouses.length} warehouses');
          for (final warehouse in _warehouses) {
            print('- ${warehouse.name} (${warehouse.type}) - Owner: ${warehouse.ownerId ?? 'Company'}');
          }
        }
      }

      // Load items
      await _loadItems();
      
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadItems() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;
      List<ItemModel> items;

      if (currentUser != null && currentUser.isAgent) {
        // Agents should only see items from their own warehouses
        final agentWarehouses = await _dataService.getWarehousesByOwnerId(currentUser.id);
        items = [];

        for (final warehouse in agentWarehouses) {
          final warehouseItems = await _dataService.getItems(warehouseId: warehouse.id);
          items.addAll(warehouseItems);
        }

        if (kDebugMode) {
          print('Agent ${currentUser.fullName} loaded ${items.length} items from ${agentWarehouses.length} warehouses');
        }
      } else {
        // Admins can see items from selected warehouse or all warehouses
        if (_selectedWarehouseId != null && _selectedWarehouseId != 'الكل') {
          items = await _dataService.getItems(warehouseId: _selectedWarehouseId);
        } else {
          items = await _dataService.getItems();
        }
      }

      setState(() {
        _items = items;
        _filterItems();
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل الأصناف: $e', isError: true);
      }
    }
  }

  void _filterItems() {
    List<ItemModel> filtered = List.from(_items);

    // Filter by search text
    final searchText = _searchController.text.toLowerCase().trim();
    if (searchText.isNotEmpty) {
      filtered = filtered.where((item) {
        return item.motorFingerprintText.toLowerCase().contains(searchText) ||
               item.brand.toLowerCase().contains(searchText) ||
               item.model.toLowerCase().contains(searchText) ||
               item.color.toLowerCase().contains(searchText) ||
               item.type.toLowerCase().contains(searchText) ||
               item.countryOfOrigin.toLowerCase().contains(searchText) ||
               item.chassisNumber.toLowerCase().contains(searchText) ||
               item.id.toLowerCase().contains(searchText);
      }).toList();
    }
    
    // Filter by status
    if (_selectedStatus != null && _selectedStatus != 'الكل') {
      switch (_selectedStatus) {
        case 'متاح':
          filtered = filtered.where((item) => item.isAvailable).toList();
          break;
        case 'مباع':
          filtered = filtered.where((item) => item.isSold).toList();
          break;
        case 'محول':
          filtered = filtered.where((item) => item.isTransferred).toList();
          break;
        case 'مرتجع':
          filtered = filtered.where((item) => item.isReturned).toList();
          break;
      }
    }
    
    // Filter by type
    if (_selectedType != null && _selectedType != 'الكل') {
      filtered = filtered.where((item) => item.type == _selectedType).toList();
    }
    
    setState(() {
      _filteredItems = filtered;
    });
  }

  Future<void> _navigateToAddItem() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AddItemScreen()),
    );
    
    if (result == true) {
      _loadItems();
    }
  }

  Future<void> _navigateToItemDetails(ItemModel item) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ItemDetailsScreen(item: item),
      ),
    );
    
    if (result == true) {
      _loadItems();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المخزون'),
        actions: [
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              if (authProvider.canManageInventory) {
                return IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: _navigateToAddItem,
                  tooltip: 'إضافة صنف جديد',
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFiltersSection(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildItemsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Card(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // Search field
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                labelText: 'البحث',
                hintText: 'ابحث بالبصمة، الماركة، الموديل، أو اللون',
                prefixIcon: Icon(Icons.search),
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Filters row
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Warehouse filter - only show for admins, not agents
                  if (_warehouses.isNotEmpty && !Provider.of<AuthProvider>(context, listen: false).isAgent) ...[
                    _buildFilterDropdown(
                      label: 'المخزن',
                      value: _selectedWarehouseId == null
                          ? 'الكل'
                          : _warehouses.firstWhere(
                              (w) => w.id == _selectedWarehouseId,
                              orElse: () => _warehouses.first,
                            ).name,
                      items: ['الكل', ..._warehouses.map((w) => w.name)],
                      onChanged: (value) {
                        setState(() {
                          if (value == 'الكل') {
                            _selectedWarehouseId = null;
                          } else {
                            final warehouse = _warehouses.firstWhere((w) => w.name == value);
                            _selectedWarehouseId = warehouse.id;
                          }
                        });
                        _loadItems();
                      },
                    ),
                    const SizedBox(width: AppConstants.defaultPadding),
                  ],
                  
                  // Status filter
                  _buildFilterDropdown(
                    label: 'الحالة',
                    value: _selectedStatus,
                    items: _statusOptions,
                    onChanged: (value) {
                      setState(() {
                        _selectedStatus = value;
                      });
                      _filterItems();
                    },
                  ),
                  
                  const SizedBox(width: AppConstants.defaultPadding),
                  
                  // Type filter
                  _buildFilterDropdown(
                    label: 'النوع',
                    value: _selectedType,
                    items: _typeOptions,
                    onChanged: (value) {
                      setState(() {
                        _selectedType = value;
                      });
                      _filterItems();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterDropdown({
    required String label,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Flexible(
      child: Container(
        constraints: const BoxConstraints(minWidth: 80, maxWidth: 150),
        child: DropdownButtonFormField<String>(
          value: value,
          decoration: InputDecoration(
            labelText: label,
            isDense: true,
            contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          ),
          items: items.map((item) {
            return DropdownMenuItem(
              value: item,
              child: SizedBox(
                width: 100,
                child: Text(
                  item,
                  style: const TextStyle(fontSize: 11),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            );
          }).toList(),
          onChanged: onChanged,
          isExpanded: true,
        ),
      ),
    );
  }

  Widget _buildItemsList() {
    if (_filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'لا توجد أصناف',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'لم يتم العثور على أصناف تطابق المعايير المحددة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadItems,
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: _filteredItems.length,
        itemBuilder: (context, index) {
          final item = _filteredItems[index];
          return _buildItemCard(item);
        },
      ),
    );
  }

  Widget _buildItemCard(ItemModel item) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: InkWell(
        onTap: () => _navigateToItemDetails(item),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Row(
            children: [
              // Item image
              ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                child: SizedBox(
                  width: 80,
                  height: 80,
                  child: UltimateImageWidget(
                    imageUrl: item.motorFingerprintImageUrl,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  ),
                ),
              ),
              
              const SizedBox(width: AppConstants.defaultPadding),
              
              // Item details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Brand and model
                    Text(
                      '${item.brand} ${item.model}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Type and color
                    Text(
                      '${item.type} - ${item.color}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Motor fingerprint
                    Text(
                      'البصمة: ${item.motorFingerprintText}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Price and status
                    SizedBox(
                      width: double.infinity,
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              AppUtils.formatCurrency(item.suggestedSellingPrice),
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),

                          const SizedBox(width: 8),

                          _buildStatusChip(item.status),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color chipColor;
    String statusText;

    switch (status) {
      case 'available':
      case 'متاح':
        chipColor = Colors.green;
        statusText = 'متاح';
        break;
      case 'sold':
      case 'مباع':
        chipColor = Colors.blue;
        statusText = 'مباع';
        break;
      case 'transferred':
      case 'محول':
        chipColor = Colors.orange;
        statusText = 'محول';
        break;
      case 'returned':
      case 'مرتجع':
        chipColor = Colors.red;
        statusText = 'مرتجع';
        break;
      default:
        chipColor = Colors.grey;
        statusText = status;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
