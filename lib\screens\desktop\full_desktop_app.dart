import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/user_model.dart';
import '../../providers/simple_auth_provider.dart';
import '../auth/simple_login_screen.dart';
import 'simple_desktop_dashboard.dart';
import 'simple_placeholder_screen.dart';
import '../inventory/inventory_screen.dart';
import '../sales/sales_screen.dart';
import '../agents/agents_screen.dart';
import '../warehouses/warehouses_screen.dart';
import '../reports/reports_screen.dart';
import '../notifications/enhanced_notifications_screen.dart';
import '../documents/document_tracking_screen.dart';
import '../settings/settings_screen.dart';
import '../users/user_management_screen.dart';

class FullDesktopApp extends StatefulWidget {
  const FullDesktopApp({super.key});

  @override
  State<FullDesktopApp> createState() => _FullDesktopAppState();
}

class _FullDesktopAppState extends State<FullDesktopApp> {
  int _selectedIndex = 0;
  bool _isCollapsed = false;

  final List<DesktopMenuItem> _menuItems = [
    DesktopMenuItem(
      icon: Icons.dashboard,
      title: 'لوحة التحكم',
      screen: const SimpleDesktopDashboard(),
    ),
    DesktopMenuItem(
      icon: Icons.inventory,
      title: 'المخزون',
      screen: const SimplePlaceholderScreen(
        title: 'إدارة المخزون',
        icon: Icons.inventory,
        description: 'إدارة شاملة للمخزون والبضائع مع تتبع الحركة والكميات',
        color: Colors.green,
      ),
    ),
    DesktopMenuItem(
      icon: Icons.point_of_sale,
      title: 'المبيعات',
      screen: const SimplePlaceholderScreen(
        title: 'نظام المبيعات',
        icon: Icons.point_of_sale,
        description: 'إدارة المبيعات والفواتير مع تتبع العملاء والمدفوعات',
        color: Colors.orange,
      ),
    ),
    DesktopMenuItem(
      icon: Icons.people,
      title: 'الوكلاء',
      screen: const SimplePlaceholderScreen(
        title: 'إدارة الوكلاء',
        icon: Icons.people,
        description: 'إدارة الوكلاء وحساباتهم ونسب الأرباح والمدفوعات',
        color: Colors.purple,
      ),
      requiredRoles: ['admin', 'super_admin'],
    ),
    DesktopMenuItem(
      icon: Icons.warehouse,
      title: 'المخازن',
      screen: const SimplePlaceholderScreen(
        title: 'إدارة المخازن',
        icon: Icons.warehouse,
        description: 'إدارة المخازن والتحويلات بين المخازن المختلفة',
        color: Colors.brown,
      ),
      requiredRoles: ['admin', 'super_admin'],
    ),
    DesktopMenuItem(
      icon: Icons.description,
      title: 'تتبع الجوابات',
      screen: const SimplePlaceholderScreen(
        title: 'تتبع الجوابات',
        icon: Icons.description,
        description: 'تتبع حالة الوثائق والجوابات مع الجهات المختلفة',
        color: Colors.indigo,
      ),
    ),
    DesktopMenuItem(
      icon: Icons.bar_chart,
      title: 'التقارير',
      screen: const SimplePlaceholderScreen(
        title: 'التقارير والإحصائيات',
        icon: Icons.bar_chart,
        description: 'تقارير مفصلة وإحصائيات شاملة للمبيعات والأرباح',
        color: Colors.teal,
      ),
    ),
    DesktopMenuItem(
      icon: Icons.notifications,
      title: 'الإشعارات',
      screen: const SimplePlaceholderScreen(
        title: 'الإشعارات',
        icon: Icons.notifications,
        description: 'إدارة الإشعارات والتنبيهات للأحداث المهمة',
        color: Colors.red,
      ),
    ),
    DesktopMenuItem(
      icon: Icons.manage_accounts,
      title: 'إدارة المستخدمين',
      screen: const SimplePlaceholderScreen(
        title: 'إدارة المستخدمين',
        icon: Icons.manage_accounts,
        description: 'إدارة المستخدمين والصلاحيات ونظام الأمان',
        color: Colors.pink,
      ),
      requiredRoles: ['super_admin'],
    ),
    DesktopMenuItem(
      icon: Icons.settings,
      title: 'الإعدادات',
      screen: const SimplePlaceholderScreen(
        title: 'الإعدادات',
        icon: Icons.settings,
        description: 'إعدادات النظام والتخصيص والتفضيلات',
        color: Colors.grey,
      ),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<SimpleAuthProvider>(
      builder: (context, authProvider, child) {
        final currentUser = authProvider.currentUser;

        if (currentUser == null) {
          return const SimpleLoginScreen();
        }

        // Filter menu items based on user role
        final filteredMenuItems = _menuItems.where((item) {
          if (item.requiredRoles == null) return true;
          return item.requiredRoles!.contains(currentUser.role);
        }).toList();

        return Scaffold(
          body: Row(
            children: [
              // Sidebar Navigation
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: _isCollapsed ? 70 : 280,
                child: _buildSidebar(currentUser, filteredMenuItems),
              ),
              
              // Main Content Area
              Expanded(
                child: Column(
                  children: [
                    // Top App Bar
                    _buildTopAppBar(currentUser, filteredMenuItems),
                    
                    // Content
                    Expanded(
                      child: Container(
                        color: Colors.grey[50],
                        child: filteredMenuItems[_selectedIndex].screen,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSidebar(UserModel currentUser, List<DesktopMenuItem> menuItems) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.blue.shade800,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Logo and Company Name
          Container(
            height: 80,
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.local_shipping,
                    color: Colors.blue.shade800,
                    size: 24,
                  ),
                ),
                if (!_isCollapsed) ...[
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'الفرحان للنقل الخفيف',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          const Divider(color: Colors.white24, height: 1),
          
          // Menu Items
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: menuItems.length,
              itemBuilder: (context, index) {
                final item = menuItems[index];
                final isSelected = _selectedIndex == index;
                
                return _buildMenuItem(item, index, isSelected);
              },
            ),
          ),
          
          // User Info (when collapsed)
          if (_isCollapsed) ...[
            const Divider(color: Colors.white24, height: 1),
            Container(
              padding: const EdgeInsets.all(8),
              child: CircleAvatar(
                radius: 20,
                backgroundColor: Colors.white,
                child: Text(
                  currentUser.fullName.isNotEmpty 
                    ? currentUser.fullName[0].toUpperCase()
                    : 'U',
                  style: TextStyle(
                    color: Colors.blue.shade800,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
          
          // Collapse Button
          Container(
            padding: const EdgeInsets.all(8),
            child: IconButton(
              onPressed: () {
                setState(() {
                  _isCollapsed = !_isCollapsed;
                });
              },
              icon: Icon(
                _isCollapsed ? Icons.menu : Icons.menu_open,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(DesktopMenuItem item, int index, bool isSelected) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Material(
        color: isSelected ? Colors.white.withOpacity(0.2) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            setState(() {
              _selectedIndex = index;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Icon(
                  item.icon,
                  color: Colors.white,
                  size: 20,
                ),
                if (!_isCollapsed) ...[
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      item.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopAppBar(UserModel currentUser, List<DesktopMenuItem> menuItems) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const SizedBox(width: 20),
          
          // Page Title
          Text(
            menuItems[_selectedIndex].title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade800,
            ),
          ),
          
          const Spacer(),
          
          // Quick Actions
          Row(
            children: [
              // Notifications
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedIndex = menuItems.indexWhere((item) => item.title == 'الإشعارات');
                  });
                },
                icon: Stack(
                  children: [
                    const Icon(Icons.notifications_outlined),
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(width: 8),
              
              // User Profile
              PopupMenuButton<String>(
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.blue.shade800,
                      child: Text(
                        currentUser.fullName.isNotEmpty 
                          ? currentUser.fullName[0].toUpperCase()
                          : 'U',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          currentUser.fullName,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          _getRoleDisplayName(currentUser.role),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(width: 4),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'profile',
                    child: Row(
                      children: [
                        Icon(Icons.person),
                        SizedBox(width: 8),
                        Text('الملف الشخصي'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: Row(
                      children: [
                        Icon(Icons.settings),
                        SizedBox(width: 8),
                        Text('الإعدادات'),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, color: Colors.red),
                        SizedBox(width: 8),
                        Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
                onSelected: (value) {
                  switch (value) {
                    case 'profile':
                      // TODO: Show profile dialog
                      break;
                    case 'settings':
                      setState(() {
                        _selectedIndex = menuItems.indexWhere((item) => item.title == 'الإعدادات');
                      });
                      break;
                    case 'logout':
                      _handleLogout();
                      break;
                  }
                },
              ),
              
              const SizedBox(width: 20),
            ],
          ),
        ],
      ),
    );
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<SimpleAuthProvider>(context, listen: false).signOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'super_admin':
        return 'المدير الأعلى';
      case 'admin':
        return 'مدير';
      case 'agent':
        return 'وكيل';
      case 'showroom':
        return 'صالة عرض';
      default:
        return 'مستخدم';
    }
  }
}

class DesktopMenuItem {
  final IconData icon;
  final String title;
  final Widget screen;
  final List<String>? requiredRoles;

  DesktopMenuItem({
    required this.icon,
    required this.title,
    required this.screen,
    this.requiredRoles,
  });
}

class DesktopDashboardScreen extends StatefulWidget {
  const DesktopDashboardScreen({super.key});

  @override
  State<DesktopDashboardScreen> createState() => _DesktopDashboardScreenState();
}

class _DesktopDashboardScreenState extends State<DesktopDashboardScreen> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Section
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'مرحباً بك في نظام الفرحان للنقل الخفيف',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'نسخة سطح المكتب - نظام إدارة شامل للمخزون والمبيعات والوكلاء',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.desktop_windows,
                  size: 48,
                  color: Colors.blue.shade800,
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Stats Cards
          Expanded(
            child: GridView.count(
              crossAxisCount: 4,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
              children: [
                _buildStatsCard(
                  'إجمالي المخزون',
                  '1,234',
                  Icons.inventory,
                  Colors.green,
                ),
                _buildStatsCard(
                  'المبيعات اليوم',
                  '45',
                  Icons.point_of_sale,
                  Colors.orange,
                ),
                _buildStatsCard(
                  'عدد الوكلاء',
                  '12',
                  Icons.people,
                  Colors.purple,
                ),
                _buildStatsCard(
                  'الإشعارات',
                  '8',
                  Icons.notifications,
                  Colors.red,
                ),
                _buildStatsCard(
                  'المخازن',
                  '5',
                  Icons.warehouse,
                  Colors.blue,
                ),
                _buildStatsCard(
                  'الجوابات المعلقة',
                  '3',
                  Icons.description,
                  Colors.amber,
                ),
                _buildStatsCard(
                  'المستخدمين',
                  '25',
                  Icons.manage_accounts,
                  Colors.teal,
                ),
                _buildStatsCard(
                  'التقارير',
                  '156',
                  Icons.bar_chart,
                  Colors.indigo,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Quick Actions
          Row(
            children: [
              const Text(
                'إجراءات سريعة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Navigate to inventory
                },
                icon: const Icon(Icons.add),
                label: const Text('إضافة صنف جديد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: () {
                  // TODO: Navigate to sales
                },
                icon: const Icon(Icons.receipt),
                label: const Text('فاتورة جديدة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const Spacer(),
              const Icon(
                Icons.trending_up,
                color: Colors.green,
                size: 16,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
