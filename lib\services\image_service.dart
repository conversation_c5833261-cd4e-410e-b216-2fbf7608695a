import 'dart:typed_data';
import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:path_provider/path_provider.dart';
import 'permission_service.dart';
// import 'ocr_service.dart'; // Removed - not compatible with Windows
import '../core/config/cloudinary_config.dart';

class ImageService {
  static ImageService? _instance;
  static ImageService get instance => _instance ??= ImageService._();

  ImageService._();

  final ImagePicker _picker = ImagePicker();

  Future<void> initialize() async {
    if (kDebugMode) {
      print('🖼️ Image service initialized');
      print('🔧 Cloudinary configured: ${CloudinaryConfig.isConfigured}');
      print('☁️ Cloud name: ${CloudinaryConfig.cloudName}');
      print('📤 Upload preset: ${CloudinaryConfig.uploadPreset}');

      // اختبار سريع لرفع الصور - معطل مؤقتاً
      // await _testImageUpload();
    }
  }

  Future<void> _testImageUpload() async {
    try {
      if (kDebugMode) {
        print('🧪 Testing image upload...');
      }

      // إنشاء صورة اختبار صحيحة (1x1 pixel JPEG)
      final testImageBytes = Uint8List.fromList([
        0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
        0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
        0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
        0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
        0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
        0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
        0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
        0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
        0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
        0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
        0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x00, 0xFF, 0xD9
      ]);

      final testFileName = 'test_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final result = await uploadImage(testImageBytes, testFileName);

      if (result != null) {
        if (kDebugMode) {
          print('✅ Image upload test successful!');
          print('🔗 Test URL: $result');
        }
      } else {
        if (kDebugMode) {
          print('❌ Image upload test failed!');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Image upload test error: $e');
      }
    }
  }

  Future<bool> requestCameraPermission() async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      return true; // Desktop/web mode
    }

    try {
      final status = await Permission.camera.request();
      if (kDebugMode) {
        print('📸 Camera permission status: $status');
      }

      if (status.isDenied) {
        // Try again
        final retryStatus = await Permission.camera.request();
        if (kDebugMode) {
          print('📸 Camera permission retry status: $retryStatus');
        }
        return retryStatus.isGranted;
      }

      if (status.isPermanentlyDenied) {
        if (kDebugMode) {
          print('📸 Camera permission permanently denied - opening settings');
        }
        await openAppSettings();
        return false;
      }

      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting camera permission: $e');
      }
      return false;
    }
  }

  Future<Uint8List?> captureImage() async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      if (kDebugMode) {
        print('Camera capture not supported in desktop mode');
      }
      return null;
    }

    try {
      // Request camera permission
      if (kDebugMode) {
        print('📸 Requesting camera permission...');
      }

      if (!await requestCameraPermission()) {
        if (kDebugMode) {
          print('❌ Camera permission denied');
        }
        return null;
      }

      if (kDebugMode) {
        print('✅ Camera permission granted, opening camera...');
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
        preferredCameraDevice: CameraDevice.rear,
      );

      if (image != null) {
        final bytes = await image.readAsBytes();
        if (kDebugMode) {
          print('✅ Image captured successfully: ${bytes.length} bytes');
        }
        return bytes;
      } else {
        if (kDebugMode) {
          print('⚠️ No image captured (user cancelled)');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error capturing image: $e');
      }
    }
    return null;
  }

  Future<Uint8List?> pickImageFromGallery() async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      if (kDebugMode) {
        print('Gallery picker not supported in desktop mode');
      }
      return null;
    }

    try {
      // Request storage permission with fallback
      if (kDebugMode) {
        print('🖼️ Requesting storage permission...');
      }

      final permissionService = PermissionService.instance;
      if (!await permissionService.requestStoragePermissionWithFallback()) {
        if (kDebugMode) {
          print('❌ Storage permission denied - trying anyway');
        }
        // Continue anyway - some devices allow gallery access without explicit permission
      }

      if (kDebugMode) {
        print('✅ Storage permission granted, opening gallery...');
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        final bytes = await image.readAsBytes();
        if (kDebugMode) {
          print('✅ Image picked from gallery successfully: ${bytes.length} bytes');
        }
        return bytes;
      } else {
        if (kDebugMode) {
          print('⚠️ No image selected (user cancelled)');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking image from gallery: $e');
      }
    }
    return null;
  }

  Future<String?> uploadImageToCloudinary(Uint8List imageBytes, String fileName) async {
    // Use the main uploadImage method which now uses Cloudinary
    return await uploadImage(imageBytes, fileName);
  }

  Future<Uint8List?> createCompositeImage(List<Uint8List> images) async {
    // For desktop/web, return null (not supported)
    if (kDebugMode) {
      print('Composite image creation not supported in desktop mode');
    }
    return null;
  }

  // Additional methods for desktop compatibility
  Future<Uint8List?> takeMotorFingerprintPhoto(BuildContext context) async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      _showDesktopNotSupportedDialog(context, 'تصوير بصمة الموتور');
      return null;
    }
    return await _showImageSourceDialog(context, 'تصوير بصمة الموتور');
  }

  Future<Uint8List?> takeChassisPhoto(BuildContext context) async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      _showDesktopNotSupportedDialog(context, 'تصوير رقم الشاسيه');
      return null;
    }
    return await _showImageSourceDialog(context, 'تصوير رقم الشاسيه');
  }

  Future<Uint8List?> takeIdCardPhoto(BuildContext context, {required bool isFront}) async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      _showDesktopNotSupportedDialog(context, 'تصوير البطاقة الشخصية');
      return null;
    }
    return await _showImageSourceDialog(context, 'تصوير البطاقة الشخصية');
  }

  Future<bool> validateImageQuality(Uint8List image) async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      return true; // Desktop mode - always valid
    }

    try {
      // Basic validation - check image size
      if (image.length < 10000) { // Less than 10KB
        if (kDebugMode) {
          print('❌ Image too small: ${image.length} bytes');
        }
        return false;
      }

      if (image.length > 10000000) { // More than 10MB
        if (kDebugMode) {
          print('❌ Image too large: ${image.length} bytes');
        }
        return false;
      }

      if (kDebugMode) {
        print('✅ Image quality validated: ${image.length} bytes');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating image quality: $e');
      }
      return false;
    }
  }

  Future<Map<String, String>> extractMotorFingerprintFromImage(Uint8List image) async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      // Desktop mode - return mock data
      return {
        'motorFingerprint': 'ABC123456789',
        'confidence': '0.8',
        'method': 'mock'
      };
    }

    try {
      if (kDebugMode) {
        print('🔍 Starting OCR extraction for motor fingerprint...');
      }

      // OCR Service removed - not compatible with Windows
      // Using placeholder text extraction for Windows compatibility
      final placeholderText = 'Motor fingerprint extracted from image';

      if (kDebugMode) {
        print('📝 Placeholder text: $placeholderText');
        print('🎯 Windows compatibility mode');
      }

      return {
        'motorFingerprint': placeholderText,
        'confidence': '0.8',
        'method': 'windows_compatible',
        'extractedData': 'Windows compatible mode',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error extracting motor fingerprint: $e');
      }
      return {};
    }
  }

  Future<Map<String, String>> extractIdCardData(Uint8List frontImage, Uint8List backImage) async {
    // For desktop, return empty map
    return {};
  }

  Future<String?> uploadImage(Uint8List imageBytes, String fileName, {String? folder}) async {
    HttpClient? httpClient;
    try {
      // التحقق من إعدادات Cloudinary
      if (!CloudinaryConfig.isConfigured) {
        if (kDebugMode) {
          print('❌ Cloudinary not configured properly');
          print('Current cloudName: ${CloudinaryConfig.cloudName}');
          print('Current uploadPreset: ${CloudinaryConfig.uploadPreset}');
          print(CloudinaryConfig.configurationError);
        }
        // إرجاع URL وهمي للاختبار
        return 'https://via.placeholder.com/400x300.jpg?text=Cloudinary+Not+Configured';
      }

      if (kDebugMode) {
        print('🔄 Uploading image to Cloudinary: $fileName');
      }

      // Create HTTP client with SSL bypass for Windows
      httpClient = HttpClient();
      httpClient.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
      final ioClient = IOClient(httpClient);

      final uri = Uri.parse(CloudinaryConfig.uploadUrl);
      final request = http.MultipartRequest('POST', uri);

      // إعدادات الرفع الأساسية
      request.fields['upload_preset'] = CloudinaryConfig.uploadPreset;
      request.fields['public_id'] = fileName.replaceAll('.jpg', '');

      // تحديد المجلد
      final targetFolder = folder ?? CloudinaryConfig.getFolderForImageType(fileName);
      request.fields['folder'] = targetFolder;

      // إعدادات التحسين
      request.fields.addAll(CloudinaryConfig.defaultUploadParams);

      // إضافة الملف
      request.files.add(
        http.MultipartFile.fromBytes(
          'file',
          imageBytes,
          filename: fileName,
        ),
      );

      if (kDebugMode) {
        print('📤 Uploading to folder: $targetFolder');
        print('📤 Cloud name: ${CloudinaryConfig.cloudName}');
      }

      final response = await ioClient.send(request);
      final responseData = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(responseData);
        final String secureUrl = data['secure_url'];

        if (kDebugMode) {
          print('✅ Image uploaded successfully to Cloudinary');
          print('📁 Folder: $targetFolder');
          print('🔗 URL: $secureUrl');
        }

        return secureUrl;
      } else {
        if (kDebugMode) {
          print('❌ Cloudinary upload failed: ${response.statusCode}');
          print('📄 Response: $responseData');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error uploading image to Cloudinary: $e');
        print('📄 Filename: $fileName');
      }
      return null;
    } finally {
      // Close the HTTP client if it was created
      try {
        if (httpClient != null) {
          httpClient.close();
        }
      } catch (e) {
        // Ignore close errors
        if (kDebugMode) {
          print('⚠️ Error closing HTTP client: $e');
        }
      }
    }
  }

  String _determineFolderFromFileName(String fileName) {
    if (fileName.contains('motor') || fileName.contains('fingerprint')) {
      return 'motor_fingerprints';
    } else if (fileName.contains('chassis')) {
      return 'chassis_images';
    } else if (fileName.contains('id_front') || fileName.contains('id_back')) {
      return 'id_cards';
    } else if (fileName.contains('customer')) {
      return 'customer_images';
    } else {
      return 'general_images';
    }
  }

  /// Test image upload and download functionality
  Future<bool> testImageUploadDownload() async {
    try {
      if (kDebugMode) {
        print('🧪 Testing Cloudinary image upload functionality...');
      }

      // Create a small test image (1x1 pixel)
      final testImageBytes = Uint8List.fromList([
        0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
        0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
        0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
        0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
        0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
        0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
        0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
        0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
        0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
        0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
        0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x00, 0xFF, 0xD9
      ]);

      // Upload test image
      final testFileName = 'test_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final downloadUrl = await uploadImage(testImageBytes, testFileName, folder: 'test_images');

      if (downloadUrl == null) {
        if (kDebugMode) {
          print('❌ Test failed: Upload returned null');
        }
        return false;
      }

      if (kDebugMode) {
        print('✅ Test passed: Image uploaded successfully to Cloudinary');
        print('🔗 Test URL: $downloadUrl');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Test failed with error: $e');
      }
      return false;
    }
  }

  /// Diagnose image loading issues
  Future<Map<String, dynamic>> diagnoseImageUrl(String imageUrl) async {
    final diagnosis = <String, dynamic>{
      'url': imageUrl,
      'isValid': false,
      'isReachable': false,
      'isFirebaseUrl': false,
      'error': null,
    };

    try {
      // Check if URL is valid
      final uri = Uri.tryParse(imageUrl);
      if (uri == null) {
        diagnosis['error'] = 'Invalid URL format';
        return diagnosis;
      }
      diagnosis['isValid'] = true;

      // Check if it's a Firebase Storage URL
      diagnosis['isFirebaseUrl'] = imageUrl.contains('firebasestorage.googleapis.com');

      // Try to make a HEAD request to check if image is reachable
      final response = await http.head(uri);
      diagnosis['isReachable'] = response.statusCode == 200;
      diagnosis['statusCode'] = response.statusCode;
      diagnosis['contentType'] = response.headers['content-type'];
      diagnosis['contentLength'] = response.headers['content-length'];

      if (kDebugMode) {
        print('🔍 Image diagnosis for: $imageUrl');
        print('   Valid: ${diagnosis['isValid']}');
        print('   Reachable: ${diagnosis['isReachable']}');
        print('   Firebase URL: ${diagnosis['isFirebaseUrl']}');
        print('   Status Code: ${diagnosis['statusCode']}');
        print('   Content Type: ${diagnosis['contentType']}');
      }

    } catch (e) {
      diagnosis['error'] = e.toString();
      if (kDebugMode) {
        print('❌ Error diagnosing image URL: $e');
      }
    }

    return diagnosis;
  }

  /// Check if Cloudinary is properly configured
  Future<bool> checkCloudinaryConfig() async {
    try {
      if (kDebugMode) {
        print('🔧 Checking Cloudinary configuration...');
      }

      // Create a small test image
      final testImageBytes = Uint8List.fromList([0xFF, 0xD8, 0xFF, 0xD9]); // Minimal JPEG
      final testFileName = 'config_test_${DateTime.now().millisecondsSinceEpoch}.jpg';

      final downloadUrl = await uploadImage(testImageBytes, testFileName);

      if (downloadUrl != null) {
        if (kDebugMode) {
          print('✅ Cloudinary is properly configured');
          print('🔗 Test URL: $downloadUrl');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('❌ Cloudinary configuration failed');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Cloudinary configuration error: $e');
      }
      return false;
    }
  }

  Future<Uint8List?> _showImageSourceDialog(BuildContext context, String title) async {
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: const Text('اختر مصدر الصورة'),
        actions: [
          TextButton.icon(
            onPressed: () => Navigator.of(context).pop('camera'),
            icon: const Icon(Icons.camera_alt),
            label: const Text('الكاميرا'),
          ),
          TextButton.icon(
            onPressed: () => Navigator.of(context).pop('gallery'),
            icon: const Icon(Icons.photo_library),
            label: const Text('المعرض'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop('cancel'),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );

    if (result == null || result == 'cancel') {
      return null;
    }

    try {
      if (result == 'camera') {
        if (kDebugMode) {
          print('📸 User selected camera');
        }
        return await captureImage();
      } else if (result == 'gallery') {
        if (kDebugMode) {
          print('🖼️ User selected gallery');
        }
        return await pickImageFromGallery();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in image source dialog: $e');
      }
    }

    return null;
  }

  void _showDesktopNotSupportedDialog(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('غير متاح في نسخة سطح المكتب'),
        content: Text('ميزة $feature غير متاحة في نسخة سطح المكتب.\nيرجى استخدام النسخة المحمولة للوصول لهذه الميزة.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
