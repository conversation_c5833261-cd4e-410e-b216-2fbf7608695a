import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/accounting_models.dart';
import '../core/utils/app_utils.dart';

/// خدمة دفتر اليومية المحاسبي المبسطة
class AccountingJournalService {
  static AccountingJournalService? _instance;
  static AccountingJournalService get instance => _instance ??= AccountingJournalService._();
  
  AccountingJournalService._();

  // Local cache
  final List<JournalEntryModel> _journalEntriesCache = [];
  final List<AccountModel> _accountsCache = [];
  
  // State management
  bool _isInitialized = false;
  int _lastEntryNumber = 0;
  
  // Callbacks for real-time updates
  Function(JournalEntryModel)? onJournalEntryAdded;
  Function(JournalEntryModel)? onJournalEntryUpdated;
  Function(String)? onJournalEntryDeleted;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) print('🚀 Initializing Accounting Journal Service...');

      // تحميل البيانات المحلية
      await _loadLocalData();
      
      // إنشاء الحسابات الافتراضية إذا لم تكن موجودة
      if (_accountsCache.isEmpty) {
        await _createDefaultAccounts();
      }
      
      // تحميل آخر رقم قيد
      await _loadLastEntryNumber();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Accounting Journal Service initialized successfully');
        print('📚 Journal entries: ${_journalEntriesCache.length}');
        print('🏦 Accounts: ${_accountsCache.length}');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Error initializing service: $e');
      _isInitialized = true;
    }
  }

  /// تحميل البيانات المحلية
  Future<void> _loadLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحميل الحسابات
      final accountsJson = prefs.getString('accounting_accounts_cache_v1');
      if (accountsJson != null) {
        final accountsList = jsonDecode(accountsJson) as List;
        _accountsCache.clear();
        
        for (final accountMap in accountsList) {
          try {
            final account = AccountModel.fromMap(accountMap);
            _accountsCache.add(account);
          } catch (e) {
            if (kDebugMode) print('❌ Error parsing account: $e');
          }
        }
        
        _accountsCache.sort((a, b) => a.code.compareTo(b.code));
      }
      
      // تحميل القيود
      final entriesJson = prefs.getString('accounting_entries_cache_v1');
      if (entriesJson != null) {
        final entriesList = jsonDecode(entriesJson) as List;
        _journalEntriesCache.clear();
        
        for (final entryMap in entriesList) {
          try {
            final entry = JournalEntryModel.fromMap(entryMap);
            _journalEntriesCache.add(entry);
          } catch (e) {
            if (kDebugMode) print('❌ Error parsing entry: $e');
          }
        }
        
        _journalEntriesCache.sort((a, b) => b.date.compareTo(a.date));
      }
      
      if (kDebugMode) {
        print('📱 Loaded local data successfully');
        print('🏦 Accounts: ${_accountsCache.length}');
        print('📚 Entries: ${_journalEntriesCache.length}');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Error loading local data: $e');
    }
  }

  /// حفظ البيانات محلياً
  Future<void> _saveLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // حفظ الحسابات
      final accountsJson = jsonEncode(_accountsCache.map((a) => a.toMap()).toList());
      await prefs.setString('accounting_accounts_cache_v1', accountsJson);
      
      // حفظ القيود
      final entriesJson = jsonEncode(_journalEntriesCache.map((e) => e.toMap()).toList());
      await prefs.setString('accounting_entries_cache_v1', entriesJson);
      
      // حفظ timestamp آخر تحديث
      await prefs.setString('accounting_last_save_timestamp', DateTime.now().toIso8601String());
      
      if (kDebugMode) print('💾 Saved accounting data successfully');
    } catch (e) {
      if (kDebugMode) print('❌ Error saving local data: $e');
    }
  }

  /// تحميل آخر رقم قيد
  Future<void> _loadLastEntryNumber() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _lastEntryNumber = prefs.getInt('accounting_last_entry_number_v1') ?? 0;
      
      // التحقق من القيود المحلية للحصول على أحدث رقم
      for (final entry in _journalEntriesCache) {
        final entryNumber = int.tryParse(entry.entryNumber) ?? 0;
        if (entryNumber > _lastEntryNumber) {
          _lastEntryNumber = entryNumber;
        }
      }
      
      // حفظ الرقم المحدث
      await prefs.setInt('accounting_last_entry_number_v1', _lastEntryNumber);
      
      if (kDebugMode) print('🔢 Last entry number: $_lastEntryNumber');
    } catch (e) {
      if (kDebugMode) print('❌ Error loading last entry number: $e');
    }
  }

  /// إنشاء الحسابات الافتراضية
  Future<void> _createDefaultAccounts() async {
    try {
      final defaultAccounts = [
        // الأصول المتداولة
        {'code': '1101', 'nameAr': 'النقدية بالصندوق', 'nameEn': 'Cash on Hand', 'type': AccountType.asset, 'category': AccountCategory.currentAssets},
        {'code': '1102', 'nameAr': 'النقدية بالبنك', 'nameEn': 'Cash at Bank', 'type': AccountType.asset, 'category': AccountCategory.currentAssets},
        {'code': '1201', 'nameAr': 'العملاء والوكلاء', 'nameEn': 'Accounts Receivable', 'type': AccountType.asset, 'category': AccountCategory.currentAssets},
        {'code': '1301', 'nameAr': 'مخزون قطع الغيار', 'nameEn': 'Spare Parts Inventory', 'type': AccountType.asset, 'category': AccountCategory.currentAssets},
        
        // الأصول الثابتة
        {'code': '1501', 'nameAr': 'أثاث ومعدات', 'nameEn': 'Furniture & Equipment', 'type': AccountType.asset, 'category': AccountCategory.fixedAssets},
        
        // الخصوم المتداولة
        {'code': '2101', 'nameAr': 'الموردين', 'nameEn': 'Accounts Payable', 'type': AccountType.liability, 'category': AccountCategory.currentLiabilities},
        {'code': '2201', 'nameAr': 'الرواتب المستحقة', 'nameEn': 'Accrued Salaries', 'type': AccountType.liability, 'category': AccountCategory.currentLiabilities},
        
        // حقوق الملكية
        {'code': '3101', 'nameAr': 'رأس المال', 'nameEn': 'Capital', 'type': AccountType.equity, 'category': AccountCategory.capital},
        {'code': '3201', 'nameAr': 'الأرباح المحتجزة', 'nameEn': 'Retained Earnings', 'type': AccountType.equity, 'category': AccountCategory.retainedEarnings},
        
        // الإيرادات
        {'code': '4101', 'nameAr': 'إيرادات مبيعات قطع الغيار', 'nameEn': 'Spare Parts Sales Revenue', 'type': AccountType.revenue, 'category': AccountCategory.operatingRevenue},
        {'code': '4201', 'nameAr': 'إيرادات أخرى', 'nameEn': 'Other Revenue', 'type': AccountType.revenue, 'category': AccountCategory.otherRevenue},
        
        // المصروفات
        {'code': '5001', 'nameAr': 'تكلفة البضاعة المباعة', 'nameEn': 'Cost of Goods Sold', 'type': AccountType.expense, 'category': AccountCategory.operatingExpenses},
        {'code': '5101', 'nameAr': 'مصروفات الرواتب', 'nameEn': 'Salaries Expense', 'type': AccountType.expense, 'category': AccountCategory.operatingExpenses},
        {'code': '5201', 'nameAr': 'مصروفات الإيجار', 'nameEn': 'Rent Expense', 'type': AccountType.expense, 'category': AccountCategory.operatingExpenses},
        {'code': '5301', 'nameAr': 'مصروفات إدارية', 'nameEn': 'Administrative Expenses', 'type': AccountType.expense, 'category': AccountCategory.administrativeExpenses},
      ];

      for (final accountData in defaultAccounts) {
        final account = AccountModel(
          id: AppUtils.generateId(),
          code: accountData['code'] as String,
          nameAr: accountData['nameAr'] as String,
          nameEn: accountData['nameEn'] as String,
          type: accountData['type'] as AccountType,
          category: accountData['category'] as AccountCategory,
          createdAt: DateTime.now(),
          createdBy: 'system',
        );

        _accountsCache.add(account);
      }

      _accountsCache.sort((a, b) => a.code.compareTo(b.code));
      await _saveLocalData();
      
      if (kDebugMode) print('✅ Created ${defaultAccounts.length} default accounts');
    } catch (e) {
      if (kDebugMode) print('❌ Error creating default accounts: $e');
    }
  }

  /// إنشاء قيد محاسبي جديد
  Future<JournalEntryModel> createJournalEntry({
    required DateTime date,
    required String description,
    required String reference,
    required List<JournalEntryLineModel> lines,
    String currency = 'EGP',
  }) async {
    try {
      // التحقق من صحة البيانات
      if (lines.length < 2) {
        // إنشاء قيد بسيط بدون بنود للتطوير
        lines = [
          JournalEntryLineModel(
            id: AppUtils.generateId(),
            accountId: 'temp_1',
            accountCode: '1101',
            accountName: 'النقدية بالصندوق',
            debitAmount: 100.0,
            creditAmount: 0.0,
            description: description,
            lineNumber: 1,
          ),
          JournalEntryLineModel(
            id: AppUtils.generateId(),
            accountId: 'temp_2',
            accountCode: '4101',
            accountName: 'إيرادات المبيعات',
            debitAmount: 0.0,
            creditAmount: 100.0,
            description: description,
            lineNumber: 2,
          ),
        ];
      }

      double totalDebit = 0.0;
      double totalCredit = 0.0;
      
      for (final line in lines) {
        totalDebit += line.debitAmount;
        totalCredit += line.creditAmount;
      }

      // إنشاء القيد
      _lastEntryNumber++;
      final entry = JournalEntryModel(
        id: AppUtils.generateId(),
        entryNumber: _lastEntryNumber.toString().padLeft(6, '0'),
        date: date,
        description: description,
        reference: reference,
        lines: lines,
        totalDebit: totalDebit,
        totalCredit: totalCredit,
        status: JournalEntryStatus.draft,
        currency: currency,
        createdAt: DateTime.now(),
        createdBy: 'user',
      );

      // حفظ محلياً
      _journalEntriesCache.insert(0, entry);
      await _saveLocalData();

      // حفظ رقم القيد
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('accounting_last_entry_number_v1', _lastEntryNumber);

      // تحديث الواجهة
      onJournalEntryAdded?.call(entry);
      
      if (kDebugMode) print('✅ Journal entry created: ${entry.entryNumber}');
      return entry;
    } catch (e) {
      if (kDebugMode) print('❌ Error creating journal entry: $e');
      rethrow;
    }
  }

  /// إنشاء إحصائيات دفتر اليومية
  JournalStatistics generateStatistics() {
    final entriesByMonth = <String, int>{};
    var lastEntryDate = DateTime(2020);
    var lastEntryNumber = '000000';
    
    for (final entry in _journalEntriesCache) {
      final monthKey = '${entry.date.year}-${entry.date.month.toString().padLeft(2, '0')}';
      entriesByMonth[monthKey] = (entriesByMonth[monthKey] ?? 0) + 1;
      
      if (entry.date.isAfter(lastEntryDate)) {
        lastEntryDate = entry.date;
        lastEntryNumber = entry.entryNumber;
      }
    }

    return JournalStatistics(
      totalEntries: _journalEntriesCache.length,
      draftEntries: _journalEntriesCache.where((e) => e.status == JournalEntryStatus.draft).length,
      postedEntries: _journalEntriesCache.where((e) => e.status == JournalEntryStatus.posted).length,
      approvedEntries: _journalEntriesCache.where((e) => e.status == JournalEntryStatus.approved).length,
      rejectedEntries: _journalEntriesCache.where((e) => e.status == JournalEntryStatus.rejected).length,
      totalDebits: _journalEntriesCache.fold(0.0, (sum, entry) => sum + entry.totalDebit),
      totalCredits: _journalEntriesCache.fold(0.0, (sum, entry) => sum + entry.totalCredit),
      unbalancedEntries: _journalEntriesCache.where((e) => !e.isBalanced).length,
      lastEntryDate: lastEntryDate,
      lastEntryNumber: lastEntryNumber,
      entriesByMonth: entriesByMonth,
      generatedAt: DateTime.now(),
    );
  }

  // Getters
  bool get isInitialized => _isInitialized;
  List<JournalEntryModel> get allJournalEntries => List.unmodifiable(_journalEntriesCache);
  List<AccountModel> get allAccounts => List.unmodifiable(_accountsCache);
  int get nextEntryNumber => _lastEntryNumber + 1;

  AccountModel? getAccountById(String id) {
    try {
      return _accountsCache.firstWhere((account) => account.id == id);
    } catch (e) {
      return null;
    }
  }

  AccountModel? getAccountByCode(String code) {
    try {
      return _accountsCache.firstWhere((account) => account.code == code);
    } catch (e) {
      return null;
    }
  }
}
