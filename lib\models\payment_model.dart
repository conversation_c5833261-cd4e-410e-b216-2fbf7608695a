import 'package:cloud_firestore/cloud_firestore.dart';

class PaymentModel {
  final String id;
  final String receiptNumber; // رقم سند القبض الفريد
  final String agentId; // معرف الوكيل الذي قام بالدفع
  final double amount; // مبلغ الدفعة
  final String paymentMethod; // طريقة الدفع (نقدي، تحويل بنكي، شيك، إلخ)
  final DateTime paymentDate; // تاريخ الدفع
  final String status; // confirmed, pending, cancelled
  final String? notes; // ملاحظات إضافية
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy; // User ID who recorded this payment
  final String? confirmedBy; // User ID who confirmed this payment
  final Map<String, dynamic>? additionalData;

  PaymentModel({
    required this.id,
    required this.receiptNumber,
    required this.agentId,
    required this.amount,
    required this.paymentMethod,
    required this.paymentDate,
    this.status = 'pending',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.confirmedBy,
    this.additionalData,
  });

  // Convert from Firestore document
  factory PaymentModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return PaymentModel(
      id: doc.id,
      receiptNumber: data['receiptNumber'] ?? '',
      agentId: data['agentId'] ?? '',
      amount: (data['amount'] ?? 0).toDouble(),
      paymentMethod: data['paymentMethod'] ?? '',
      paymentDate: (data['paymentDate'] as Timestamp).toDate(),
      status: data['status'] ?? 'pending',
      notes: data['notes'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      confirmedBy: data['confirmedBy'],
      additionalData: data['additionalData'],
    );
  }

  // Convert from Map (for local database) - Enhanced with comprehensive error handling
  factory PaymentModel.fromMap(Map<String, dynamic> map) {
    try {
      return PaymentModel(
        id: _safeString(map['id']) ?? _generateId(),
        receiptNumber: _safeString(map['receiptNumber']) ?? _safeString(map['id']) ?? _generateId(),
        agentId: _safeString(map['agentId']) ?? '',
        amount: _safeDouble(map['amount']) ?? 0.0,
        paymentMethod: _safeString(map['paymentMethod']) ?? 'cash',
        paymentDate: _parseDateTime(map['paymentDate']) ?? DateTime.now(),
        status: _safeString(map['status']) ?? 'pending',
        notes: _safeString(map['notes']),
        createdAt: _parseDateTime(map['createdAt']) ?? DateTime.now(),
        updatedAt: _parseDateTime(map['updatedAt']) ?? DateTime.now(),
        createdBy: _safeString(map['createdBy']) ?? 'system',
        confirmedBy: _safeString(map['confirmedBy']),
        additionalData: _safeMap(map['additionalData']),
      );
    } catch (e) {
      // Return a safe default payment model if parsing fails completely
      return PaymentModel(
        id: _generateId(),
        receiptNumber: _generateId(),
        agentId: _safeString(map['agentId']) ?? '',
        amount: 0.0,
        paymentMethod: 'cash',
        paymentDate: DateTime.now(),
        status: 'pending',
        notes: 'خطأ في تحليل البيانات',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'system',
        confirmedBy: null,
        additionalData: null,
      );
    }
  }

  // Helper methods for safe data conversion
  static String? _safeString(dynamic value) {
    if (value == null) return null;
    return value.toString();
  }

  static double? _safeDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return null;
      }
    }
    if (value is num) return value.toDouble();
    return null;
  }

  static Map<String, dynamic>? _safeMap(dynamic value) {
    if (value == null) return null;
    if (value is Map<String, dynamic>) return value;
    if (value is Map) {
      try {
        return Map<String, dynamic>.from(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Helper method to safely parse DateTime
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Generate a unique ID
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Convert to Map (for Firestore and local database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'receiptNumber': receiptNumber,
      'agentId': agentId,
      'amount': amount,
      'paymentMethod': paymentMethod,
      'paymentDate': paymentDate.toIso8601String(),
      'status': status,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'confirmedBy': confirmedBy,
      'additionalData': additionalData,
    };
  }

  // Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    return {
      'receiptNumber': receiptNumber,
      'agentId': agentId,
      'amount': amount,
      'paymentMethod': paymentMethod,
      'paymentDate': Timestamp.fromDate(paymentDate),
      'status': status,
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'confirmedBy': confirmedBy,
      'additionalData': additionalData,
    };
  }

  // Copy with method for updates
  PaymentModel copyWith({
    String? id,
    String? receiptNumber,
    String? agentId,
    double? amount,
    String? paymentMethod,
    DateTime? paymentDate,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? confirmedBy,
    Map<String, dynamic>? additionalData,
  }) {
    return PaymentModel(
      id: id ?? this.id,
      receiptNumber: receiptNumber ?? this.receiptNumber,
      agentId: agentId ?? this.agentId,
      amount: amount ?? this.amount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentDate: paymentDate ?? this.paymentDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      confirmedBy: confirmedBy ?? this.confirmedBy,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  // Helper methods
  bool get isPending => status == 'pending';
  bool get isConfirmed => status == 'confirmed';
  bool get isCancelled => status == 'cancelled';

  @override
  String toString() {
    return 'PaymentModel(id: $id, receiptNumber: $receiptNumber, amount: $amount, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
