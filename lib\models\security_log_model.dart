import 'dart:convert';

class SecurityLogModel {
  final String id;
  final String userId;
  final String userName;
  final SecurityEventType eventType;
  final String description;
  final DateTime timestamp;
  final String? ipAddress;
  final String? deviceInfo;
  final SecurityLevel level;
  final Map<String, dynamic> metadata;
  final bool isSuccessful;

  SecurityLogModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.eventType,
    required this.description,
    required this.timestamp,
    this.ipAddress,
    this.deviceInfo,
    required this.level,
    this.metadata = const {},
    required this.isSuccessful,
  });

  factory SecurityLogModel.fromJson(Map<String, dynamic> json) {
    return SecurityLogModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      eventType: SecurityEventType.values.firstWhere(
        (e) => e.toString() == 'SecurityEventType.${json['eventType']}',
        orElse: () => SecurityEventType.other,
      ),
      description: json['description'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      ipAddress: json['ipAddress'] as String?,
      deviceInfo: json['deviceInfo'] as String?,
      level: SecurityLevel.values.firstWhere(
        (e) => e.toString() == 'SecurityLevel.${json['level']}',
        orElse: () => SecurityLevel.info,
      ),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      isSuccessful: json['isSuccessful'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'eventType': eventType.toString().split('.').last,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'ipAddress': ipAddress,
      'deviceInfo': deviceInfo,
      'level': level.toString().split('.').last,
      'metadata': metadata,
      'isSuccessful': isSuccessful,
    };
  }

  String toJsonString() => jsonEncode(toJson());

  factory SecurityLogModel.fromJsonString(String jsonString) =>
      SecurityLogModel.fromJson(jsonDecode(jsonString));

  @override
  String toString() {
    return 'SecurityLogModel(id: $id, eventType: $eventType, level: $level, isSuccessful: $isSuccessful)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SecurityLogModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// Get event type text in Arabic
  String get eventTypeText {
    switch (eventType) {
      case SecurityEventType.login:
        return 'تسجيل دخول';
      case SecurityEventType.logout:
        return 'تسجيل خروج';
      case SecurityEventType.passwordChange:
        return 'تغيير كلمة المرور';
      case SecurityEventType.dataAccess:
        return 'الوصول للبيانات';
      case SecurityEventType.dataModification:
        return 'تعديل البيانات';
      case SecurityEventType.backup:
        return 'نسخ احتياطي';
      case SecurityEventType.restore:
        return 'استعادة البيانات';
      case SecurityEventType.export:
        return 'تصدير البيانات';
      case SecurityEventType.import:
        return 'استيراد البيانات';
      case SecurityEventType.securityViolation:
        return 'انتهاك أمني';
      case SecurityEventType.systemAccess:
        return 'الوصول للنظام';
      case SecurityEventType.configurationChange:
        return 'تغيير الإعدادات';
      case SecurityEventType.other:
        return 'أخرى';
    }
  }

  /// Get level text in Arabic
  String get levelText {
    switch (level) {
      case SecurityLevel.info:
        return 'معلومات';
      case SecurityLevel.warning:
        return 'تحذير';
      case SecurityLevel.error:
        return 'خطأ';
      case SecurityLevel.critical:
        return 'حرج';
    }
  }

  /// Get status text in Arabic
  String get statusText {
    return isSuccessful ? 'نجح' : 'فشل';
  }
}

/// Security event types
enum SecurityEventType {
  login,
  logout,
  passwordChange,
  dataAccess,
  dataModification,
  backup,
  restore,
  export,
  import,
  securityViolation,
  systemAccess,
  configurationChange,
  other,
}

/// Security levels
enum SecurityLevel {
  info,
  warning,
  error,
  critical,
}

/// Security audit report
class SecurityAuditReport {
  final String id;
  final DateTime generatedAt;
  final DateTime fromDate;
  final DateTime toDate;
  final List<SecurityLogModel> logs;
  final Map<SecurityEventType, int> eventCounts;
  final Map<SecurityLevel, int> levelCounts;
  final Map<String, int> userActivityCounts;
  final List<SecurityViolation> violations;
  final SecurityScore score;

  SecurityAuditReport({
    required this.id,
    required this.generatedAt,
    required this.fromDate,
    required this.toDate,
    required this.logs,
    required this.eventCounts,
    required this.levelCounts,
    required this.userActivityCounts,
    required this.violations,
    required this.score,
  });

  factory SecurityAuditReport.fromLogs(
    List<SecurityLogModel> logs,
    DateTime fromDate,
    DateTime toDate,
  ) {
    final eventCounts = <SecurityEventType, int>{};
    final levelCounts = <SecurityLevel, int>{};
    final userActivityCounts = <String, int>{};
    final violations = <SecurityViolation>[];

    for (final log in logs) {
      eventCounts[log.eventType] = (eventCounts[log.eventType] ?? 0) + 1;
      levelCounts[log.level] = (levelCounts[log.level] ?? 0) + 1;
      userActivityCounts[log.userName] = (userActivityCounts[log.userName] ?? 0) + 1;

      if (log.eventType == SecurityEventType.securityViolation) {
        violations.add(SecurityViolation.fromLog(log));
      }
    }

    final score = SecurityScore.calculate(logs);

    return SecurityAuditReport(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      generatedAt: DateTime.now(),
      fromDate: fromDate,
      toDate: toDate,
      logs: logs,
      eventCounts: eventCounts,
      levelCounts: levelCounts,
      userActivityCounts: userActivityCounts,
      violations: violations,
      score: score,
    );
  }
}

/// Security violation
class SecurityViolation {
  final String id;
  final String userId;
  final String userName;
  final String description;
  final DateTime timestamp;
  final ViolationType type;
  final SecurityLevel severity;

  SecurityViolation({
    required this.id,
    required this.userId,
    required this.userName,
    required this.description,
    required this.timestamp,
    required this.type,
    required this.severity,
  });

  factory SecurityViolation.fromLog(SecurityLogModel log) {
    return SecurityViolation(
      id: log.id,
      userId: log.userId,
      userName: log.userName,
      description: log.description,
      timestamp: log.timestamp,
      type: ViolationType.other,
      severity: log.level,
    );
  }
}

/// Violation types
enum ViolationType {
  unauthorizedAccess,
  dataTheft,
  bruteForce,
  sqlInjection,
  xss,
  other,
}

/// Security score
class SecurityScore {
  final double overall;
  final double loginSecurity;
  final double dataSecurity;
  final double systemSecurity;
  final List<String> recommendations;

  SecurityScore({
    required this.overall,
    required this.loginSecurity,
    required this.dataSecurity,
    required this.systemSecurity,
    required this.recommendations,
  });

  factory SecurityScore.calculate(List<SecurityLogModel> logs) {
    double loginSecurity = 100.0;
    double dataSecurity = 100.0;
    double systemSecurity = 100.0;
    final recommendations = <String>[];

    // Calculate based on failed attempts, violations, etc.
    final failedLogins = logs.where((l) => 
        l.eventType == SecurityEventType.login && !l.isSuccessful).length;
    
    if (failedLogins > 10) {
      loginSecurity -= 20;
      recommendations.add('تحسين أمان تسجيل الدخول');
    }

    final violations = logs.where((l) => 
        l.eventType == SecurityEventType.securityViolation).length;
    
    if (violations > 0) {
      systemSecurity -= violations * 10;
      recommendations.add('معالجة الانتهاكات الأمنية');
    }

    final overall = (loginSecurity + dataSecurity + systemSecurity) / 3;

    return SecurityScore(
      overall: overall.clamp(0, 100),
      loginSecurity: loginSecurity.clamp(0, 100),
      dataSecurity: dataSecurity.clamp(0, 100),
      systemSecurity: systemSecurity.clamp(0, 100),
      recommendations: recommendations,
    );
  }

  String get overallGrade {
    if (overall >= 90) return 'ممتاز';
    if (overall >= 80) return 'جيد جداً';
    if (overall >= 70) return 'جيد';
    if (overall >= 60) return 'مقبول';
    return 'ضعيف';
  }
}
