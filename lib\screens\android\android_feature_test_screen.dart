import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:typed_data';
import '../../services/android/android_image_service.dart';
import '../../services/android/android_session_service.dart';
import '../../services/android/android_permission_service.dart';
import '../../services/android/android_auth_service.dart';
import '../../core/utils/app_utils.dart';

/// Comprehensive test screen for all Android-specific features
class AndroidFeatureTestScreen extends StatefulWidget {
  const AndroidFeatureTestScreen({super.key});

  @override
  State<AndroidFeatureTestScreen> createState() => _AndroidFeatureTestScreenState();
}

class _AndroidFeatureTestScreenState extends State<AndroidFeatureTestScreen> {
  final AndroidImageService _imageService = AndroidImageService.instance;
  final AndroidSessionService _sessionService = AndroidSessionService.instance;
  final AndroidPermissionService _permissionService = AndroidPermissionService.instance;
  final AndroidAuthService _authService = AndroidAuthService.instance;

  bool _isLoading = false;
  Map<String, bool> _testResults = {};
  Map<String, String> _testMessages = {};
  Uint8List? _capturedImage;
  Map<String, dynamic>? _ocrResult;

  @override
  void initState() {
    super.initState();
    _runInitialTests();
  }

  Future<void> _runInitialTests() async {
    await _testPermissions();
    await _testSessionService();
    await _testAuthService();
  }

  Future<void> _testPermissions() async {
    try {
      if (kDebugMode) {
        print('🧪 Testing Android permissions...');
      }

      final permissions = await _permissionService.checkAllPermissions();
      final allGranted = permissions.values.every((granted) => granted);

      setState(() {
        _testResults['permissions'] = allGranted;
        _testMessages['permissions'] = allGranted 
            ? 'جميع الصلاحيات ممنوحة'
            : 'بعض الصلاحيات مفقودة: ${permissions.entries.where((e) => !e.value).map((e) => e.key).join(', ')}';
      });

      if (kDebugMode) {
        print('✅ Permission test completed: $allGranted');
      }
    } catch (e) {
      setState(() {
        _testResults['permissions'] = false;
        _testMessages['permissions'] = 'خطأ في اختبار الصلاحيات: $e';
      });
      
      if (kDebugMode) {
        print('❌ Permission test failed: $e');
      }
    }
  }

  Future<void> _testSessionService() async {
    try {
      if (kDebugMode) {
        print('🧪 Testing Android session service...');
      }

      final isLoggedIn = await _sessionService.isLoggedIn();
      final sessionData = await _sessionService.loadUserSession();

      setState(() {
        _testResults['session'] = isLoggedIn && sessionData != null;
        _testMessages['session'] = isLoggedIn 
            ? 'الجلسة نشطة: ${sessionData?['userData']?['fullName'] ?? 'مستخدم غير معروف'}'
            : 'لا توجد جلسة نشطة';
      });

      if (kDebugMode) {
        print('✅ Session test completed: $isLoggedIn');
      }
    } catch (e) {
      setState(() {
        _testResults['session'] = false;
        _testMessages['session'] = 'خطأ في اختبار الجلسة: $e';
      });
      
      if (kDebugMode) {
        print('❌ Session test failed: $e');
      }
    }
  }

  Future<void> _testAuthService() async {
    try {
      if (kDebugMode) {
        print('🧪 Testing Android auth service...');
      }

      final isAuthenticated = _authService.isAuthenticated;
      final currentUser = _authService.currentUser;

      setState(() {
        _testResults['auth'] = isAuthenticated && currentUser != null;
        _testMessages['auth'] = isAuthenticated 
            ? 'مصادق: ${currentUser?['fullName'] ?? 'مستخدم غير معروف'}'
            : 'غير مصادق';
      });

      if (kDebugMode) {
        print('✅ Auth test completed: $isAuthenticated');
      }
    } catch (e) {
      setState(() {
        _testResults['auth'] = false;
        _testMessages['auth'] = 'خطأ في اختبار المصادقة: $e';
      });
      
      if (kDebugMode) {
        print('❌ Auth test failed: $e');
      }
    }
  }

  Future<void> _testCameraCapture() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing camera capture...');
      }

      final imageBytes = await _imageService.takePhoto();
      
      if (imageBytes != null) {
        setState(() {
          _capturedImage = imageBytes;
          _testResults['camera'] = true;
          _testMessages['camera'] = 'تم التقاط الصورة بنجاح (${imageBytes.length} بايت)';
        });

        if (kDebugMode) {
          print('✅ Camera test completed: ${imageBytes.length} bytes');
        }

        if (mounted) {
          AppUtils.showSnackBar(
            context,
            'تم التقاط الصورة بنجاح',
            isError: false,
          );
        }
      } else {
        setState(() {
          _testResults['camera'] = false;
          _testMessages['camera'] = 'فشل في التقاط الصورة';
        });

        if (mounted) {
          AppUtils.showSnackBar(
            context,
            'فشل في التقاط الصورة',
            isError: true,
          );
        }
      }
    } catch (e) {
      setState(() {
        _testResults['camera'] = false;
        _testMessages['camera'] = 'خطأ في اختبار الكاميرا: $e';
      });
      
      if (kDebugMode) {
        print('❌ Camera test failed: $e');
      }

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'خطأ في اختبار الكاميرا: $e',
          isError: true,
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testOCRExtraction() async {
    if (_capturedImage == null) {
      AppUtils.showSnackBar(
        context,
        'يجب التقاط صورة أولاً',
        isError: true,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing OCR extraction...');
      }

      final result = await _imageService.extractMotorFingerprintText(_capturedImage!);
      
      setState(() {
        _ocrResult = result;
        _testResults['ocr'] = result.isNotEmpty;
        _testMessages['ocr'] = result.isNotEmpty 
            ? 'تم استخراج النص: ${result['motorFingerprint']?.substring(0, 50) ?? 'نص فارغ'}...'
            : 'فشل في استخراج النص';
      });

      if (kDebugMode) {
        print('✅ OCR test completed: ${result.isNotEmpty}');
      }

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          result.isNotEmpty ? 'تم استخراج النص بنجاح' : 'فشل في استخراج النص',
          isError: result.isEmpty,
        );
      }
    } catch (e) {
      setState(() {
        _testResults['ocr'] = false;
        _testMessages['ocr'] = 'خطأ في اختبار OCR: $e';
      });
      
      if (kDebugMode) {
        print('❌ OCR test failed: $e');
      }

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'خطأ في اختبار OCR: $e',
          isError: true,
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testGalleryPicker() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing gallery picker...');
      }

      final imageBytes = await _imageService.pickFromGallery();
      
      if (imageBytes != null) {
        setState(() {
          _capturedImage = imageBytes;
          _testResults['gallery'] = true;
          _testMessages['gallery'] = 'تم اختيار الصورة بنجاح (${imageBytes.length} بايت)';
        });

        if (kDebugMode) {
          print('✅ Gallery test completed: ${imageBytes.length} bytes');
        }

        if (mounted) {
          AppUtils.showSnackBar(
            context,
            'تم اختيار الصورة بنجاح',
            isError: false,
          );
        }
      } else {
        setState(() {
          _testResults['gallery'] = false;
          _testMessages['gallery'] = 'لم يتم اختيار صورة';
        });
      }
    } catch (e) {
      setState(() {
        _testResults['gallery'] = false;
        _testMessages['gallery'] = 'خطأ في اختبار المعرض: $e';
      });
      
      if (kDebugMode) {
        print('❌ Gallery test failed: $e');
      }

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'خطأ في اختبار المعرض: $e',
          isError: true,
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _requestAllPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('🔐 Requesting all permissions...');
      }

      final granted = await _permissionService.requestAllPermissions();
      
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          granted ? 'تم منح جميع الصلاحيات' : 'بعض الصلاحيات لم يتم منحها',
          isError: !granted,
        );
      }

      // Re-test permissions
      await _testPermissions();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting permissions: $e');
      }

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'خطأ في طلب الصلاحيات: $e',
          isError: true,
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار ميزات Android'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _runInitialTests,
            tooltip: 'إعادة اختبار',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري الاختبار...'),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Test Results Summary
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'نتائج الاختبارات:',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ..._testResults.entries.map((entry) => _buildTestResultRow(
                            entry.key,
                            entry.value,
                            _testMessages[entry.key] ?? '',
                          )),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Action Buttons
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'اختبارات الميزات:',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          // Permission Test
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _requestAllPermissions,
                              icon: const Icon(Icons.security),
                              label: const Text('طلب جميع الصلاحيات'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 12),
                          
                          // Camera Test
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _testCameraCapture,
                              icon: const Icon(Icons.camera_alt),
                              label: const Text('اختبار الكاميرا'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 12),
                          
                          // Gallery Test
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _testGalleryPicker,
                              icon: const Icon(Icons.photo_library),
                              label: const Text('اختبار المعرض'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          
                          const SizedBox(height: 12),
                          
                          // OCR Test
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _capturedImage != null ? _testOCRExtraction : null,
                              icon: const Icon(Icons.text_fields),
                              label: const Text('اختبار استخراج النص (OCR)'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.purple,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Captured Image Preview
                  if (_capturedImage != null) ...[
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'الصورة المختارة:',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Center(
                              child: Container(
                                constraints: const BoxConstraints(maxHeight: 200),
                                child: Image.memory(_capturedImage!),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],

                  // OCR Results
                  if (_ocrResult != null) ...[
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'نتائج استخراج النص:',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text('النص المستخرج: ${_ocrResult!['motorFingerprint'] ?? 'لا يوجد'}'),
                            Text('الثقة: ${_ocrResult!['confidence'] ?? 'غير محدد'}'),
                            Text('الطريقة: ${_ocrResult!['method'] ?? 'غير محدد'}'),
                            if (_ocrResult!['isMotorFingerprint'] == true)
                              const Text(
                                '✅ تم التعرف على بصمة موتور',
                                style: TextStyle(color: Colors.green),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
    );
  }

  Widget _buildTestResultRow(String testName, bool result, String message) {
    final testNames = {
      'permissions': 'الصلاحيات',
      'session': 'الجلسة',
      'auth': 'المصادقة',
      'camera': 'الكاميرا',
      'gallery': 'المعرض',
      'ocr': 'استخراج النص',
    };

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            result ? Icons.check_circle : Icons.error,
            color: result ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  testNames[testName] ?? testName,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                if (message.isNotEmpty)
                  Text(
                    message,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
