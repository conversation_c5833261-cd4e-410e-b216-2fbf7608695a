import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/professional_agent_account.dart';
import '../models/user_model.dart';
import '../models/invoice_model.dart';
import '../models/warehouse_model.dart';
import '../core/utils/app_utils.dart';
import 'data_service.dart';
import 'local_database_service.dart';
import 'auth_service.dart';

/// الخدمة الاحترافية الموحدة لإدارة حسابات الوكلاء
/// تدعم جميع العمليات المحاسبية بدقة ووضوح مع التزامن مع Firebase
class ProfessionalAgentService {
  static final ProfessionalAgentService _instance = ProfessionalAgentService._internal();
  factory ProfessionalAgentService() => _instance;
  ProfessionalAgentService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final DataService _dataService = DataService.instance;
  final AuthService _authService = AuthService.instance;
  
  static const String _collection = 'professional_agent_accounts';
  static const String _transactionsCollection = 'agent_transactions';

  /// الحصول على جميع حسابات الوكلاء
  Future<List<ProfessionalAgentAccount>> getAllAgentAccounts() async {
    try {
      if (kDebugMode) {
        print('🔍 [Professional] Loading all agent accounts...');
      }

      // الحصول على جميع الوكلاء من قاعدة البيانات المحلية والـ Firebase
      final agents = await _dataService.getUsersByRole('agent');
      final List<ProfessionalAgentAccount> accounts = [];

      if (kDebugMode) {
        print('👥 [Professional] Found ${agents.length} agents in system');
      }

      // تحميل حسابات الوكلاء من قاعدة البيانات المحلية أيضاً
      final localAccounts = await _loadLocalAgentAccounts();

      // دمج البيانات من المصادر المختلفة
      final allAgentIds = <String>{};

      // إضافة معرفات الوكلاء من النظام
      for (final agent in agents) {
        allAgentIds.add(agent.id);
      }

      // إضافة معرفات الوكلاء من الحسابات المحلية
      for (final account in localAccounts) {
        allAgentIds.add(account.agentId);
      }

      if (kDebugMode) {
        print('🔍 [Professional] Processing ${allAgentIds.length} unique agent IDs');
      }

      for (final agentId in allAgentIds) {
        final account = await getOrCreateAgentAccount(agentId);
        if (account != null) {
          accounts.add(account);
        }
      }

      if (kDebugMode) {
        print('📊 [Professional] Loaded ${accounts.length} agent accounts');
        for (final account in accounts) {
          print('   - ${account.agentName}: ${account.currentBalance.toStringAsFixed(2)} ج.م (${account.transactionCount} معاملة)');
        }
      }

      return accounts;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Professional] Error loading agent accounts: $e');
      }
      return [];
    }
  }

  /// تحميل حسابات الوكلاء من قاعدة البيانات المحلية
  Future<List<ProfessionalAgentAccount>> _loadLocalAgentAccounts() async {
    try {
      final db = await LocalDatabaseService.instance.database;
      final List<Map<String, dynamic>> maps = await db.query('agent_accounts');

      final accounts = <ProfessionalAgentAccount>[];
      for (final map in maps) {
        try {
          final account = ProfessionalAgentAccount.fromMap(map);
          accounts.add(account);
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [Professional] Error parsing local account: $e');
          }
        }
      }

      if (kDebugMode) {
        print('💾 [Professional] Loaded ${accounts.length} accounts from local database');
      }

      return accounts;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Professional] Error loading local accounts: $e');
      }
      return [];
    }
  }

  /// الحصول على أو إنشاء حساب الوكيل
  Future<ProfessionalAgentAccount?> getOrCreateAgentAccount(String agentId) async {
    try {
      // البحث عن حساب موجود
      final existingAccount = await _getExistingAccount(agentId);
      if (existingAccount != null) {
        // التحقق من صحة الحساب وإعادة حسابه إذا لزم الأمر
        if (!existingAccount.validateAccount()) {
          if (kDebugMode) {
            print('⚠️ [Professional] Account validation failed, recalculating...');
          }
          return await _recalculateAccount(existingAccount);
        }
        return existingAccount;
      }

      // إنشاء حساب جديد
      return await _createNewAccount(agentId);
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Professional] Error getting/creating account for $agentId: $e');
      }
      return null;
    }
  }

  /// البحث عن حساب موجود
  Future<ProfessionalAgentAccount?> _getExistingAccount(String agentId) async {
    try {
      // البحث في Firebase أولاً
      if (await _dataService.isOnline()) {
        final querySnapshot = await _firestore
            .collection(_collection)
            .where('agentId', isEqualTo: agentId)
            .where('isActive', isEqualTo: true)
            .limit(1)
            .get();

        if (querySnapshot.docs.isNotEmpty) {
          return ProfessionalAgentAccount.fromFirestore(querySnapshot.docs.first);
        }
      }

      // البحث في قاعدة البيانات المحلية (مبسط)
      // سيتم تنفيذه لاحقاً

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Professional] Error searching for existing account: $e');
      }
      return null;
    }
  }

  /// إنشاء حساب جديد
  Future<ProfessionalAgentAccount> _createNewAccount(String agentId) async {
    try {
      // الحصول على بيانات الوكيل
      final agent = await _dataService.getUserById(agentId);
      if (agent == null) {
        throw Exception('Agent not found: $agentId');
      }

      final now = DateTime.now();
      final currentUser = _authService.currentUser;
      final accountId = 'prof_agent_${agentId}_${now.millisecondsSinceEpoch}';

      // حساب البيانات الأولية من المعاملات الموجودة
      final accountData = await _calculateAccountData(agentId);

      final account = ProfessionalAgentAccount(
        id: accountId,
        agentId: agentId,
        agentName: agent.fullName,
        agentPhone: agent.phone,
        agentEmail: agent.email,
        totalGoodsReceived: accountData['totalGoodsReceived'] ?? 0.0,
        totalGoodsWithdrawn: accountData['totalGoodsWithdrawn'] ?? 0.0,
        totalCustomerSales: accountData['totalCustomerSales'] ?? 0.0,
        totalAgentCommission: accountData['totalAgentCommission'] ?? 0.0,
        totalCompanyProfits: accountData['totalCompanyProfits'] ?? 0.0,
        totalPaymentsReceived: accountData['totalPaymentsReceived'] ?? 0.0,
        totalCreditsGiven: accountData['totalCreditsGiven'] ?? 0.0,
        currentBalance: accountData['currentBalance'] ?? 0.0,
        totalDebt: accountData['totalDebt'] ?? 0.0,
        availableCredit: accountData['availableCredit'] ?? 0.0,
        transactions: accountData['transactions'] ?? [],
        transactionCount: accountData['transactionCount'] ?? 0,
        lastTransactionDate: accountData['lastTransactionDate'],
        createdAt: now,
        updatedAt: now,
        createdBy: currentUser?.id ?? 'system',
        lastUpdatedBy: currentUser?.id ?? 'system',
        isActive: true,
        isVerified: true,
      );

      // حفظ في قاعدة البيانات المحلية (سيتم تنفيذه لاحقاً)

      // حفظ في Firebase إذا كان متصل
      if (await _dataService.isOnline()) {
        await _firestore.collection(_collection).doc(accountId).set(account.toFirestore());
      }

      if (kDebugMode) {
        print('✅ [Professional] Created new account for ${agent.fullName}');
        print('   Balance: ${account.currentBalance.toStringAsFixed(2)} ج.م');
        print('   Transactions: ${account.transactionCount}');
      }

      return account;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Professional] Error creating new account: $e');
      }
      rethrow;
    }
  }

  /// حساب بيانات الحساب من المعاملات الموجودة (مبسط)
  Future<Map<String, dynamic>> _calculateAccountData(String agentId) async {
    try {
      // حساب بسيط للبداية
      final List<ProfessionalAgentTransaction> transactions = [];

      // حساب المبيعات من الفواتير الموجودة
      final customerInvoices = await _dataService.getInvoicesByAgent(agentId);
      double totalCustomerSales = 0.0;
      double totalAgentCommission = 0.0;

      for (final invoice in customerInvoices) {
        if (invoice.status == 'completed') {
          final saleAmount = invoice.sellingPrice;
          totalCustomerSales += saleAmount;

          // حساب العمولة (افتراضياً 10%)
          final commission = saleAmount * 0.1;
          totalAgentCommission += commission;

          transactions.add(ProfessionalAgentTransaction(
            id: 'customer_sale_${invoice.id}',
            type: 'customer_sale',
            amount: saleAmount,
            description: 'مبيعات عملاء - فاتورة ${invoice.invoiceNumber}',
            timestamp: invoice.createdAt,
            invoiceId: invoice.id,
            customerId: invoice.customerId,
            createdBy: invoice.createdBy,
            metadata: {
              'invoiceNumber': invoice.invoiceNumber,
              'commission': commission,
            },
          ));
        }
      }

      // حساب المدفوعات
      final payments = await _dataService.getPaymentsByAgent(agentId);
      double totalPaymentsReceived = 0.0;

      for (final payment in payments) {
        totalPaymentsReceived += payment['amount'] ?? 0.0;

        transactions.add(ProfessionalAgentTransaction(
          id: 'payment_${payment['id']}',
          type: 'payment_received',
          amount: payment['amount'] ?? 0.0,
          description: 'دفعة مستلمة - ${payment['notes'] ?? ''}',
          timestamp: DateTime.parse(payment['createdAt']),
          createdBy: payment['createdBy'] ?? 'system',
          metadata: {
            'paymentMethod': payment['paymentMethod'],
            'receiptNumber': payment['receiptNumber'],
          },
        ));
      }

      // ترتيب المعاملات حسب التاريخ
      transactions.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      // حساب الرصيد الحالي (مبسط)
      final currentBalance = totalCustomerSales - totalPaymentsReceived;
      final totalDebt = currentBalance > 0 ? currentBalance : 0.0;
      final availableCredit = currentBalance < 0 ? currentBalance.abs() : 0.0;

      return {
        'totalGoodsReceived': 0.0,
        'totalGoodsWithdrawn': 0.0,
        'totalCustomerSales': totalCustomerSales,
        'totalAgentCommission': totalAgentCommission,
        'totalCompanyProfits': totalCustomerSales - totalAgentCommission,
        'totalPaymentsReceived': totalPaymentsReceived,
        'totalCreditsGiven': 0.0,
        'currentBalance': currentBalance,
        'totalDebt': totalDebt,
        'availableCredit': availableCredit,
        'transactions': transactions,
        'transactionCount': transactions.length,
        'lastTransactionDate': transactions.isNotEmpty ? transactions.last.timestamp : null,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Professional] Error calculating account data: $e');
      }
      return {};
    }
  }

  /// إعادة حساب الحساب
  Future<ProfessionalAgentAccount> _recalculateAccount(ProfessionalAgentAccount existingAccount) async {
    try {
      final accountData = await _calculateAccountData(existingAccount.agentId);
      final now = DateTime.now();
      final currentUser = _authService.currentUser;

      final updatedAccount = ProfessionalAgentAccount(
        id: existingAccount.id,
        agentId: existingAccount.agentId,
        agentName: existingAccount.agentName,
        agentPhone: existingAccount.agentPhone,
        agentEmail: existingAccount.agentEmail,
        totalGoodsReceived: accountData['totalGoodsReceived'] ?? 0.0,
        totalGoodsWithdrawn: accountData['totalGoodsWithdrawn'] ?? 0.0,
        totalCustomerSales: accountData['totalCustomerSales'] ?? 0.0,
        totalAgentCommission: accountData['totalAgentCommission'] ?? 0.0,
        totalCompanyProfits: accountData['totalCompanyProfits'] ?? 0.0,
        totalPaymentsReceived: accountData['totalPaymentsReceived'] ?? 0.0,
        totalCreditsGiven: accountData['totalCreditsGiven'] ?? 0.0,
        currentBalance: accountData['currentBalance'] ?? 0.0,
        totalDebt: accountData['totalDebt'] ?? 0.0,
        availableCredit: accountData['availableCredit'] ?? 0.0,
        transactions: accountData['transactions'] ?? [],
        transactionCount: accountData['transactionCount'] ?? 0,
        lastTransactionDate: accountData['lastTransactionDate'],
        createdAt: existingAccount.createdAt,
        updatedAt: now,
        createdBy: existingAccount.createdBy,
        lastUpdatedBy: currentUser?.id ?? 'system',
        isActive: existingAccount.isActive,
        isVerified: true,
        metadata: existingAccount.metadata,
        notes: existingAccount.notes,
      );

      // تحديث في قاعدة البيانات المحلية (سيتم تنفيذه لاحقاً)

      // تحديث في Firebase إذا كان متصل
      if (await _dataService.isOnline()) {
        await _firestore.collection(_collection).doc(existingAccount.id).update(updatedAccount.toFirestore());
      }

      if (kDebugMode) {
        print('✅ [Professional] Recalculated account for ${existingAccount.agentName}');
        print('   Old balance: ${existingAccount.currentBalance.toStringAsFixed(2)} ج.م');
        print('   New balance: ${updatedAccount.currentBalance.toStringAsFixed(2)} ج.م');
      }

      return updatedAccount;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Professional] Error recalculating account: $e');
      }
      rethrow;
    }
  }

  /// تسجيل دفعة للوكيل
  Future<void> addPayment({
    required String agentId,
    required double amount,
    required String paymentMethod,
    String? notes,
  }) async {
    try {
      final paymentId = 'payment_${DateTime.now().millisecondsSinceEpoch}';
      final now = DateTime.now();
      final currentUser = _authService.currentUser;

      // إنشاء سجل الدفعة
      final paymentData = {
        'id': paymentId,
        'agentId': agentId,
        'amount': amount,
        'paymentMethod': paymentMethod,
        'notes': notes ?? '',
        'createdAt': now.toIso8601String(),
        'createdBy': currentUser?.id ?? 'admin',
      };

      // حفظ الدفعة في Firebase إذا كان متصل
      if (await _dataService.isOnline()) {
        await _firestore.collection('agent_payments').doc(paymentId).set(paymentData);
      }

      // حفظ الدفعة في قاعدة البيانات المحلية (سيتم تنفيذه لاحقاً)
      // await _dataService.insertOrUpdate('agent_payments', paymentData);

      if (kDebugMode) {
        print('💰 Payment registered: $paymentData');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error adding payment: $e');
      }
      throw 'خطأ في تسجيل الدفعة: $e';
    }
  }
}
