import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/item_model.dart';
import '../../services/data_service.dart';

class ItemSelectionScreen extends StatefulWidget {
  final String? warehouseId;
  final String title;
  final bool multiSelect;
  final List<String>? excludeStatuses;

  const ItemSelectionScreen({
    super.key,
    this.warehouseId,
    this.title = 'اختيار الأصناف',
    this.multiSelect = false,
    this.excludeStatuses,
  });

  @override
  State<ItemSelectionScreen> createState() => _ItemSelectionScreenState();
}

class _ItemSelectionScreenState extends State<ItemSelectionScreen> {
  final DataService _dataService = DataService.instance;
  final TextEditingController _searchController = TextEditingController();
  
  List<ItemModel> _items = [];
  List<ItemModel> _filteredItems = [];
  List<ItemModel> _selectedItems = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadItems();
    _searchController.addListener(_filterItems);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadItems() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ItemModel> items;
      
      if (widget.warehouseId != null) {
        items = await _dataService.getWarehouseItems(widget.warehouseId!);
      } else {
        items = await _dataService.getItems();
      }

      // Filter out excluded statuses
      if (widget.excludeStatuses != null) {
        items = items.where((item) => !widget.excludeStatuses!.contains(item.status)).toList();
      }

      setState(() {
        _items = items;
        _filterItems();
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل الأصناف: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterItems() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredItems = _items.where((item) {
        return item.brand.toLowerCase().contains(query) ||
               item.model.toLowerCase().contains(query) ||
               item.motorFingerprintText.toLowerCase().contains(query) ||
               item.id.toLowerCase().contains(query);
      }).toList();
    });
  }

  void _toggleItemSelection(ItemModel item) {
    setState(() {
      if (_selectedItems.contains(item)) {
        _selectedItems.remove(item);
      } else {
        if (widget.multiSelect) {
          _selectedItems.add(item);
        } else {
          _selectedItems = [item];
        }
      }
    });
  }

  void _confirmSelection() {
    if (_selectedItems.isEmpty) {
      AppUtils.showSnackBar(context, 'يرجى اختيار صنف واحد على الأقل', isError: true);
      return;
    }

    Navigator.of(context).pop(widget.multiSelect ? _selectedItems : _selectedItems.first);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          if (_selectedItems.isNotEmpty)
            TextButton(
              onPressed: _confirmSelection,
              child: Text(
                'تأكيد (${_selectedItems.length})',
                style: const TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'البحث بالاسم أو البصمة أو رقم الشاسيه...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
          ),
          
          // Items list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredItems.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
                        itemCount: _filteredItems.length,
                        itemBuilder: (context, index) {
                          final item = _filteredItems[index];
                          final isSelected = _selectedItems.contains(item);
                          
                          return Card(
                            margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: isSelected 
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(context).colorScheme.surfaceContainerHighest,
                                child: Icon(
                                  _getItemIcon(item.type),
                                  color: isSelected 
                                      ? Colors.white
                                      : Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                              title: Text(
                                '${item.brand} ${item.model}',
                                style: TextStyle(
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                ),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('البصمة: ${item.motorFingerprintText}'),
                                  Text('النوع: ${item.type}'),
                                  Text('الحالة: ${item.status}'),
                                ],
                              ),
                              trailing: isSelected
                                  ? Icon(
                                      Icons.check_circle,
                                      color: Theme.of(context).colorScheme.primary,
                                    )
                                  : const Icon(Icons.radio_button_unchecked),
                              onTap: () => _toggleItemSelection(item),
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'لا توجد أصناف متاحة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getItemIcon(String type) {
    switch (type.toLowerCase()) {
      case 'موتوسيكل':
        return Icons.motorcycle;
      case 'تروسيكل':
        return Icons.electric_rickshaw;
      case 'سكوتر كهرباء':
        return Icons.electric_scooter;
      case 'توكتوك':
        return Icons.directions_car;
      default:
        return Icons.two_wheeler;
    }
  }
}
