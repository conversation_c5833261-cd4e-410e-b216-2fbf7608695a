import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/theme/app_colors.dart';
import '../../core/utils/app_utils.dart';
import '../../models/professional_agent_account.dart';
import '../../providers/auth_provider.dart';
import '../../services/professional_agent_service.dart';
import 'agent_account_details_screen.dart';
import 'agent_payment_screen.dart';

/// شاشة إدارة الوكلاء الاحترافية المحسنة
/// تصميم احترافي مع تحليل البيانات وإدارة شاملة
class EnhancedAgentManagementScreen extends StatefulWidget {
  const EnhancedAgentManagementScreen({super.key});

  @override
  State<EnhancedAgentManagementScreen> createState() => _EnhancedAgentManagementScreenState();
}

class _EnhancedAgentManagementScreenState extends State<EnhancedAgentManagementScreen> {
  final ProfessionalAgentService _agentService = ProfessionalAgentService();
  
  List<ProfessionalAgentAccount> _agentAccounts = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String _sortBy = 'name';
  bool _sortAscending = true;
  String _filterStatus = 'all'; // all, active, inactive, debt

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accounts = await _agentService.getAllAgentAccounts();
      setState(() {
        _agentAccounts = accounts;
        _isLoading = false;
      });
      _sortAccounts();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    }
  }

  void _sortAccounts() {
    _agentAccounts.sort((a, b) {
      int comparison = 0;
      
      switch (_sortBy) {
        case 'name':
          comparison = a.agentName.compareTo(b.agentName);
          break;
        case 'balance':
          comparison = a.currentBalance.compareTo(b.currentBalance);
          break;
        case 'sales':
          comparison = a.totalCustomerSales.compareTo(b.totalCustomerSales);
          break;
        case 'transactions':
          comparison = a.transactionCount.compareTo(b.transactionCount);
          break;
        case 'commission':
          comparison = a.totalAgentCommission.compareTo(b.totalAgentCommission);
          break;
      }
      
      return _sortAscending ? comparison : -comparison;
    });
  }

  List<ProfessionalAgentAccount> get _filteredAccounts {
    var filtered = _agentAccounts.where((account) {
      // تطبيق فلتر البحث
      if (_searchQuery.isNotEmpty) {
        final searchLower = _searchQuery.toLowerCase();
        if (!account.agentName.toLowerCase().contains(searchLower) &&
            !(account.agentPhone?.toLowerCase().contains(searchLower) ?? false)) {
          return false;
        }
      }
      
      // تطبيق فلتر الحالة
      switch (_filterStatus) {
        case 'active':
          return account.isActive && account.currentBalance >= 0;
        case 'inactive':
          return !account.isActive;
        case 'debt':
          return account.currentBalance < 0;
        default:
          return true;
      }
    }).toList();
    
    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildAnalyticsSection(),
          _buildControlsSection(),
          Expanded(child: _buildAgentsList()),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'إدارة الوكلاء الاحترافية',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.analytics_outlined),
          onPressed: () {
            // TODO: فتح شاشة التحليلات المتقدمة
            AppUtils.showSnackBar(context, 'التحليلات المتقدمة قيد التطوير');
          },
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadData,
        ),
      ],
    );
  }

  Widget _buildAnalyticsSection() {
    if (_agentAccounts.isEmpty) return const SizedBox.shrink();
    
    final totalBalance = _agentAccounts.fold(0.0, (sum, account) => sum + account.currentBalance);
    final totalSales = _agentAccounts.fold(0.0, (sum, account) => sum + account.totalCustomerSales);
    final totalCommissions = _agentAccounts.fold(0.0, (sum, account) => sum + account.totalAgentCommission);
    final activeAgents = _agentAccounts.where((account) => account.isActive).length;
    final debtAgents = _agentAccounts.where((account) => account.currentBalance < 0).length;
    final avgSalesPerAgent = _agentAccounts.isNotEmpty ? totalSales / _agentAccounts.length : 0.0;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.analytics, color: Colors.white, size: 24),
              SizedBox(width: 8),
              Text(
                'تحليل البيانات',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 3,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            childAspectRatio: 1.2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildAnalyticsCard(
                title: 'إجمالي الوكلاء',
                value: '${_agentAccounts.length}',
                subtitle: '$activeAgents نشط',
                icon: Icons.people,
                color: Colors.white,
              ),
              _buildAnalyticsCard(
                title: 'إجمالي الأرصدة',
                value: '${totalBalance.toStringAsFixed(0)}',
                subtitle: 'ج.م',
                icon: Icons.account_balance_wallet,
                color: totalBalance >= 0 ? Colors.lightGreen : Colors.orange,
              ),
              _buildAnalyticsCard(
                title: 'إجمالي المبيعات',
                value: '${(totalSales / 1000).toStringAsFixed(0)}K',
                subtitle: 'ج.م',
                icon: Icons.trending_up,
                color: Colors.lightBlue,
              ),
              _buildAnalyticsCard(
                title: 'إجمالي العمولات',
                value: '${(totalCommissions / 1000).toStringAsFixed(0)}K',
                subtitle: 'ج.م',
                icon: Icons.monetization_on,
                color: Colors.amber,
              ),
              _buildAnalyticsCard(
                title: 'متوسط المبيعات',
                value: '${(avgSalesPerAgent / 1000).toStringAsFixed(1)}K',
                subtitle: 'ج.م/وكيل',
                icon: Icons.bar_chart,
                color: Colors.purple[200]!,
              ),
              _buildAnalyticsCard(
                title: 'وكلاء مديونين',
                value: '$debtAgents',
                subtitle: 'وكيل',
                icon: Icons.warning,
                color: debtAgents > 0 ? Colors.red[300]! : Colors.green[300]!,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 8,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildControlsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            decoration: InputDecoration(
              hintText: 'البحث عن وكيل (الاسم أو الهاتف)...',
              prefixIcon: const Icon(Icons.search, color: AppColors.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: const BorderSide(color: AppColors.primary),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          
          const SizedBox(height: 12),
          
          // أزرار الفلترة والترتيب
          Row(
            children: [
              Expanded(
                child: _buildFilterChip('الكل', 'all'),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildFilterChip('نشط', 'active'),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildFilterChip('مديون', 'debt'),
              ),
              const SizedBox(width: 8),
              PopupMenuButton<String>(
                icon: Icon(Icons.sort, color: AppColors.primary),
                onSelected: (value) {
                  setState(() {
                    if (_sortBy == value) {
                      _sortAscending = !_sortAscending;
                    } else {
                      _sortBy = value;
                      _sortAscending = true;
                    }
                    _sortAccounts();
                  });
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'name', child: Text('ترتيب بالاسم')),
                  const PopupMenuItem(value: 'balance', child: Text('ترتيب بالرصيد')),
                  const PopupMenuItem(value: 'sales', child: Text('ترتيب بالمبيعات')),
                  const PopupMenuItem(value: 'commission', child: Text('ترتيب بالعمولة')),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _filterStatus == value;
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : AppColors.primary,
          fontSize: 12,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _filterStatus = value;
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: AppColors.primary,
      checkmarkColor: Colors.white,
    );
  }

  Widget _buildAgentsList() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل بيانات الوكلاء...'),
          ],
        ),
      );
    }

    final filteredAccounts = _filteredAccounts;

    if (filteredAccounts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty ? 'لا توجد نتائج للبحث' : 'لا توجد حسابات وكلاء',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            if (_searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'جرب البحث بكلمات مختلفة',
                style: TextStyle(color: Colors.grey[500]),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredAccounts.length,
      itemBuilder: (context, index) {
        final account = filteredAccounts[index];
        return _buildEnhancedAgentCard(account);
      },
    );
  }

  Widget _buildEnhancedAgentCard(ProfessionalAgentAccount account) {
    final balanceColor = account.currentBalance >= 0 ? Colors.green : Colors.red;
    final isValid = account.validateAccount();

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: isValid ? Colors.transparent : Colors.red.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => _openAccountDetails(account),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // الصف الأول: معلومات الوكيل الأساسية
              Row(
                children: [
                  // صورة الوكيل
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.7)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Center(
                      child: Text(
                        account.agentName.isNotEmpty ? account.agentName[0] : 'و',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // معلومات الوكيل
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                account.agentName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            if (!isValid)
                              Icon(Icons.warning, color: Colors.red, size: 16),
                            if (account.isVerified)
                              Icon(Icons.verified, color: Colors.green, size: 16),
                          ],
                        ),
                        if (account.agentPhone != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            account.agentPhone!,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // الرصيد
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: balanceColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: balanceColor.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      '${account.currentBalance.toStringAsFixed(0)} ج.م',
                      style: TextStyle(
                        color: balanceColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // الصف الثاني: الإحصائيات
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'المبيعات',
                      '${(account.totalCustomerSales / 1000).toStringAsFixed(1)}K',
                      Icons.trending_up,
                      Colors.blue,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'العمولة',
                      '${account.totalAgentCommission.toStringAsFixed(0)}',
                      Icons.monetization_on,
                      Colors.orange,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'المعاملات',
                      '${account.transactionCount}',
                      Icons.receipt,
                      Colors.purple,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'الحالة',
                      account.getAccountStatus(),
                      Icons.info,
                      balanceColor,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // الصف الثالث: أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: _buildActionButton(
                      'كشف الحساب',
                      Icons.receipt_long,
                      AppColors.primary,
                      () => _openAccountDetails(account),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildActionButton(
                      'تسجيل دفعة',
                      Icons.payment,
                      Colors.green,
                      () => _addPayment(account),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildActionButton(
                      'PDF',
                      Icons.picture_as_pdf,
                      Colors.red,
                      () => _exportToPDF(account),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 14),
      label: Text(
        label,
        style: const TextStyle(fontSize: 10),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _openAccountDetails(ProfessionalAgentAccount account) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AgentAccountDetailsScreen(account: account),
      ),
    );
  }

  void _addPayment(ProfessionalAgentAccount account) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AgentPaymentScreen(account: account),
      ),
    );

    // إذا تم تسجيل الدفعة بنجاح، أعد تحميل البيانات
    if (result == true) {
      _loadData();
    }
  }

  void _exportToPDF(ProfessionalAgentAccount account) {
    // TODO: تنفيذ تصدير PDF
    AppUtils.showSnackBar(context, 'تصدير PDF قيد التطوير');
  }
}
