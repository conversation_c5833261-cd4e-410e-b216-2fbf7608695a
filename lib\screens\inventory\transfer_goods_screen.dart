import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/warehouse_model.dart';
import '../../models/item_model.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';
import '../../services/enhanced_notification_service.dart';
import '../items/item_selection_screen.dart';
import '../../services/permissions_service.dart';

class TransferGoodsScreen extends StatefulWidget {
  const TransferGoodsScreen({super.key});

  @override
  State<TransferGoodsScreen> createState() => _TransferGoodsScreenState();
}

class _TransferGoodsScreenState extends State<TransferGoodsScreen> {
  final DataService _dataService = DataService.instance;
  final _formKey = GlobalKey<FormState>();
  
  List<WarehouseModel> _warehouses = [];
  List<ItemModel> _selectedItems = [];

  WarehouseModel? _sourceWarehouse;
  WarehouseModel? _targetWarehouse;

  final _notesController = TextEditingController();
  
  bool _isLoading = false;
  bool _isTransferring = false;

  @override
  void initState() {
    super.initState();
    _loadWarehouses();
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  /// Get valid target warehouses based on transfer rules
  List<WarehouseModel> _getValidTargetWarehouses() {
    if (_sourceWarehouse == null) return [];

    return _warehouses.where((warehouse) {
      // Cannot transfer to the same warehouse
      if (warehouse.id == _sourceWarehouse!.id) return false;

      // Can transfer to any other active warehouse
      return warehouse.isActive;
    }).toList();
  }

  Future<void> _loadWarehouses() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load all warehouses for transfer operations (not just user accessible)
      _warehouses = await _dataService.getAllWarehouses();

      if (kDebugMode) {
        print('Loaded ${_warehouses.length} warehouses for transfer:');
        for (final warehouse in _warehouses) {
          print('- ${warehouse.name} (${warehouse.typeNameArabic}) - Active: ${warehouse.isActive}');
          if (warehouse.isAgentWarehouse) {
            print('  Owner: ${warehouse.ownerId}');
          }
        }
      }

      // If no warehouses found, try to sync from Firebase
      if (_warehouses.isEmpty) {
        if (kDebugMode) {
          print('No warehouses found locally, syncing from Firebase...');
        }
        await _dataService.syncWarehousesFromFirebase();
        _warehouses = await _dataService.getAllWarehouses();

        if (kDebugMode) {
          print('After sync: ${_warehouses.length} warehouses loaded');
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('Error loading warehouses: $e');
      }
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل المخازن: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectItems() async {
    if (_sourceWarehouse == null) {
      AppUtils.showSnackBar(context, 'يرجى اختيار المخزن المصدر أولاً', isError: true);
      return;
    }

    final result = await Navigator.of(context).push<List<ItemModel>>(
      MaterialPageRoute(
        builder: (context) => ItemSelectionScreen(
          warehouseId: _sourceWarehouse!.id,
          title: 'اختيار الأصناف للتحويل',
          multiSelect: true,
          excludeStatuses: const ['مباع', 'محول'],
        ),
      ),
    );

    if (result != null && result.isNotEmpty) {
      setState(() {
        _selectedItems = result;
      });
    }
  }

  Future<void> _performTransfer() async {
    if (!_formKey.currentState!.validate()) return;
    if (_sourceWarehouse == null || _targetWarehouse == null || _selectedItems.isEmpty) {
      AppUtils.showSnackBar(context, 'يرجى اختيار جميع البيانات المطلوبة', isError: true);
      return;
    }

    // التحقق من وجود الإنترنت قبل البدء
    final isOnline = await _dataService.isOnline();
    if (!isOnline) {
      AppUtils.showSnackBar(
        context,
        'لا يوجد اتصال بالإنترنت. يرجى التأكد من الاتصال قبل تحويل البضاعة لضمان عدم فقدان البيانات.',
        isError: true,
      );
      return;
    }

    setState(() {
      _isTransferring = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser!;

      // Transfer each selected item individually
      for (final item in _selectedItems) {
        await _dataService.transferItemBetweenWarehouses(
          sourceWarehouseId: _sourceWarehouse!.id,
          targetWarehouseId: _targetWarehouse!.id,
          itemId: item.id,
          createdBy: currentUser.id,
          notes: _notesController.text.trim().isNotEmpty ? _notesController.text.trim() : null,
        );
      }

      // Send notifications to warehouse owners if they are agents
      await _sendTransferNotifications(currentUser);

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تحويل ${_selectedItems.length} صنف بنجاح');
        _resetForm();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحويل البضاعة: $e', isError: true);
      }
    } finally {
      setState(() {
        _isTransferring = false;
      });
    }
  }

  Future<void> _sendTransferNotifications(UserModel currentUser) async {
    // Smart notifications are already sent by DataService
    // No need for duplicate notifications here
    if (kDebugMode) {
      print('✅ Transfer completed - smart notifications handled by DataService');
    }
  }

  Future<UserModel?> _getWarehouseOwner(WarehouseModel warehouse) async {
    try {
      if (warehouse.isAgentWarehouse && warehouse.ownerId != null) {
        return await _dataService.getUserById(warehouse.ownerId!);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting warehouse owner: $e');
      }
      return null;
    }
  }

  void _resetForm() {
    setState(() {
      _sourceWarehouse = null;
      _targetWarehouse = null;
      _selectedItems.clear();
    });
    _notesController.clear();
  }

  IconData _getItemIcon(String type) {
    switch (type.toLowerCase()) {
      case 'موتوسيكل':
        return Icons.motorcycle;
      case 'تروسيكل':
        return Icons.electric_rickshaw;
      case 'سكوتر كهرباء':
        return Icons.electric_scooter;
      case 'توكتوك':
        return Icons.directions_car;
      default:
        return Icons.two_wheeler;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Check permissions
        if (!PermissionsService.canAccess(authProvider.currentUser!, 'transfer_goods')) {
          return Scaffold(
            appBar: AppBar(title: const Text('تحويل البضاعة')),
            body: const Center(
              child: Text('ليس لديك صلاحية للوصول إلى هذه الشاشة'),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: const Text('تحويل البضاعة بين المخازن'),
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: _isLoading ? null : _loadWarehouses,
              ),
            ],
          ),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        _buildWarehouseSelection(),
                        const SizedBox(height: AppConstants.defaultPadding),
                        _buildItemSelection(),
                        const SizedBox(height: AppConstants.defaultPadding),
                        _buildNotesInput(),
                        const SizedBox(height: AppConstants.largePadding),
                        _buildTransferButton(),
                      ],
                    ),
                  ),
                ),
        );
      },
    );
  }

  Widget _buildWarehouseSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار المخازن',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // Transfer rules info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue.shade700),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'قواعد التحويل: يمكن تحويل البضاعة من أي مخزن إلى أي مخزن آخر نشط',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Source warehouse
            DropdownButtonFormField<WarehouseModel>(
              value: _sourceWarehouse,
              decoration: const InputDecoration(
                labelText: 'المخزن المصدر',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.warehouse_outlined),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              isExpanded: true,
              items: _warehouses.map((warehouse) {
                return DropdownMenuItem(
                  value: warehouse,
                  child: SizedBox(
                    width: double.infinity,
                    child: Text(
                      '${warehouse.name} (${warehouse.typeNameArabic})',
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: const TextStyle(fontSize: 13),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (warehouse) {
                setState(() {
                  _sourceWarehouse = warehouse;
                  _selectedItems.clear();
                });
              },
              validator: (value) => value == null ? 'يرجى اختيار المخزن المصدر' : null,
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Target warehouse
            DropdownButtonFormField<WarehouseModel>(
              value: _targetWarehouse,
              decoration: const InputDecoration(
                labelText: 'المخزن المستهدف',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.store_outlined),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              isExpanded: true,
              items: _getValidTargetWarehouses().map((warehouse) {
                return DropdownMenuItem(
                  value: warehouse,
                  child: SizedBox(
                    width: double.infinity,
                    child: Text(
                      '${warehouse.name} (${warehouse.typeNameArabic})',
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: const TextStyle(fontSize: 13),
                    ),
                  ),
                );
              }).toList(),
              onChanged: (warehouse) {
                setState(() {
                  _targetWarehouse = warehouse;
                });
              },
              validator: (value) => value == null ? 'يرجى اختيار المخزن المستهدف' : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار الصنف',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            if (_sourceWarehouse == null)
              Container(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Text(
                      'يرجى اختيار المخزن المصدر أولاً',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              )
            else
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ElevatedButton.icon(
                    onPressed: _selectItems,
                    icon: const Icon(Icons.add_shopping_cart),
                    label: Text('اختيار الأصناف (${_selectedItems.length})'),
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 48),
                    ),
                  ),

                  if (_selectedItems.isNotEmpty) ...[
                    const SizedBox(height: AppConstants.defaultPadding),
                    Text(
                      'الأصناف المختارة:',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: AppConstants.smallPadding),
                    Container(
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: _selectedItems.length,
                        itemBuilder: (context, index) {
                          final item = _selectedItems[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
                            child: ListTile(
                              leading: CircleAvatar(
                                child: Icon(_getItemIcon(item.type)),
                              ),
                              title: Text('${item.brand} ${item.model}'),
                              subtitle: Text('البصمة: ${item.motorFingerprintText}'),
                              trailing: IconButton(
                                icon: const Icon(Icons.remove_circle_outline),
                                onPressed: () {
                                  setState(() {
                                    _selectedItems.removeAt(index);
                                  });
                                },
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ],
              ),
          ],
        ),
      ),
    );
  }



  Widget _buildNotesInput() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات (اختياري)',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.note_outlined),
          ),
          maxLines: 3,
        ),
      ),
    );
  }

  Widget _buildTransferButton() {
    return ElevatedButton.icon(
      onPressed: _isTransferring ? null : _performTransfer,
      icon: _isTransferring 
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(Icons.swap_horiz),
      label: Text(_isTransferring ? 'جاري التحويل...' : 'تحويل البضاعة'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
    );
  }
}
