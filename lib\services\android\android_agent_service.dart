import 'package:flutter/foundation.dart';
import '../../models/agent_account_model.dart';
import '../../models/invoice_model.dart';
import '../../models/user_model.dart';
import '../data_service.dart';

/// Android-specific agent service with accurate accounting calculations
class AndroidAgentService {
  static AndroidAgentService? _instance;
  static AndroidAgentService get instance => _instance ??= AndroidAgentService._();
  
  AndroidAgentService._();

  final DataService _dataService = DataService.instance;

  /// Calculate accurate agent account data with proper transaction handling
  Future<Map<String, dynamic>> calculateAgentAccountData(String agentId) async {
    try {
      if (kDebugMode) {
        print('🔍 [Android] Calculating accurate account data for agent: $agentId');
      }

      double totalDebt = 0.0;
      double totalPaid = 0.0;
      double totalCredits = 0.0;
      double totalSales = 0.0;
      double totalAgentProfits = 0.0;
      double totalCompanyProfits = 0.0;
      List<AgentTransaction> transactions = [];

      // Get agent information
      final agent = await _dataService.getUserById(agentId);
      if (agent == null) {
        throw Exception('Agent not found: $agentId');
      }

      // 1. Calculate debt from transfer invoices (goods sent to agent)
      final transferInvoices = await _getAgentTransferInvoices(agentId);
      for (final invoice in transferInvoices) {
        final debtAmount = invoice.sellingPrice;
        totalDebt += debtAmount;

        transactions.add(AgentTransaction(
          id: 'debt_${invoice.id}',
          type: 'debt',
          amount: debtAmount,
          description: 'تحويل بضاعة - فاتورة ${invoice.invoiceNumber}',
          invoiceId: invoice.id,
          timestamp: invoice.createdAt,
          createdBy: invoice.createdBy,
          metadata: {
            'invoice_type': 'transfer',
            'items_count': invoice.items.length,
          },
        ));

        if (kDebugMode) {
          print('   📦 Transfer Invoice ${invoice.invoiceNumber}: +${debtAmount} ج.م debt');
        }
      }

      // 2. Calculate credits from customer sales (agent's sales to customers)
      final customerInvoices = await _getAgentCustomerInvoices(agentId);
      for (final invoice in customerInvoices) {
        final saleAmount = invoice.sellingPrice;
        final agentProfit = invoice.agentProfitShare;
        final companyProfit = saleAmount - agentProfit;

        totalSales += saleAmount;
        totalAgentProfits += agentProfit;
        totalCompanyProfits += companyProfit;

        // Agent gets credit for the sale amount (reduces debt)
        totalCredits += saleAmount;

        transactions.add(AgentTransaction(
          id: 'sale_${invoice.id}',
          type: 'credit',
          amount: saleAmount,
          description: 'بيع للعميل ${invoice.customerName} - فاتورة ${invoice.invoiceNumber}',
          invoiceId: invoice.id,
          timestamp: invoice.createdAt,
          createdBy: invoice.createdBy,
          metadata: {
            'invoice_type': 'customer_sale',
            'customer_name': invoice.customerName,
            'agent_profit': agentProfit,
            'company_profit': companyProfit,
          },
        ));

        if (kDebugMode) {
          print('   💰 Customer Sale ${invoice.invoiceNumber}: -${saleAmount} ج.م credit (Agent profit: ${agentProfit})');
        }
      }

      // 3. Calculate payments made by agent
      final payments = await _getAgentPayments(agentId);
      for (final payment in payments) {
        final amount = payment['amount']?.toDouble() ?? 0.0;
        if (amount > 0) {
          totalPaid += amount;

          transactions.add(AgentTransaction(
            id: 'payment_${payment['id']}',
            type: 'payment',
            amount: amount,
            description: payment['description'] ?? 'دفعة من الوكيل',
            timestamp: payment['createdAt'] != null 
                ? DateTime.parse(payment['createdAt']) 
                : DateTime.now(),
            createdBy: payment['createdBy'] ?? 'system',
            metadata: {
              'payment_method': payment['paymentMethod'] ?? 'cash',
              'notes': payment['notes'],
            },
          ));

          if (kDebugMode) {
            print('   💸 Payment: -${amount} ج.م');
          }
        }
      }

      // Sort transactions by timestamp
      transactions.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      // Calculate final balance using proper accounting logic
      // Balance = Total Debt - Total Credits - Total Payments
      final currentBalance = totalDebt - totalCredits - totalPaid;

      if (kDebugMode) {
        print('📊 [Android] Account calculation summary for ${agent.fullName}:');
        print('   📦 Total Debt (transfers): ${totalDebt} ج.م');
        print('   💰 Total Credits (sales): ${totalCredits} ج.م');
        print('   💸 Total Payments: ${totalPaid} ج.م');
        print('   🏦 Current Balance: ${currentBalance} ج.م');
        print('   📈 Total Sales: ${totalSales} ج.م');
        print('   💵 Agent Profits: ${totalAgentProfits} ج.م');
        print('   🏢 Company Profits: ${totalCompanyProfits} ج.م');
        print('   📝 Transactions: ${transactions.length}');
      }

      return {
        'totalDebt': totalDebt,
        'totalPaid': totalPaid,
        'totalCredits': totalCredits,
        'totalSales': totalSales,
        'totalAgentProfits': totalAgentProfits,
        'totalCompanyProfits': totalCompanyProfits,
        'currentBalance': currentBalance,
        'transactions': transactions,
        'calculationMethod': 'android_accurate',
        'calculatedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Android] Error calculating agent account data: $e');
      }
      
      return {
        'totalDebt': 0.0,
        'totalPaid': 0.0,
        'totalCredits': 0.0,
        'totalSales': 0.0,
        'totalAgentProfits': 0.0,
        'totalCompanyProfits': 0.0,
        'currentBalance': 0.0,
        'transactions': <AgentTransaction>[],
        'error': e.toString(),
      };
    }
  }

  /// Create or update agent account with accurate calculations
  Future<AgentAccountModel> createOrUpdateAgentAccount(String agentId) async {
    try {
      if (kDebugMode) {
        print('🔄 [Android] Creating/updating agent account: $agentId');
      }

      final agent = await _dataService.getUserById(agentId);
      if (agent == null) {
        throw Exception('Agent not found: $agentId');
      }

      // Calculate accurate account data
      final accountData = await calculateAgentAccountData(agentId);

      // Check if account already exists
      final existingAccount = await _dataService.getAgentAccount(agentId);

      final account = AgentAccountModel(
        id: existingAccount?.id ?? 'agent_${agentId}_${DateTime.now().millisecondsSinceEpoch}',
        agentId: agentId,
        agentName: agent.fullName,
        agentPhone: agent.phone,
        totalDebt: accountData['totalDebt'],
        totalPaid: accountData['totalPaid'],
        currentBalance: accountData['currentBalance'],
        transactions: accountData['transactions'],
        createdAt: existingAccount?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: existingAccount?.createdBy ?? 'android_system',
        additionalData: {
          'totalCredits': accountData['totalCredits'],
          'totalSales': accountData['totalSales'],
          'totalAgentProfits': accountData['totalAgentProfits'],
          'totalCompanyProfits': accountData['totalCompanyProfits'],
          'calculationMethod': accountData['calculationMethod'],
          'calculatedAt': accountData['calculatedAt'],
        },
      );

      // Save the account
      await _dataService.createOrUpdateAgentAccount(account);

      if (kDebugMode) {
        print('✅ [Android] Agent account updated: ${agent.fullName}');
        print('   Balance: ${account.currentBalance} ج.م');
      }

      return account;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Android] Error creating/updating agent account: $e');
      }
      rethrow;
    }
  }

  /// Recalculate all agent accounts with accurate Android logic
  Future<void> recalculateAllAgentAccounts() async {
    try {
      if (kDebugMode) {
        print('🔄 [Android] Recalculating all agent accounts...');
      }

      final agents = await _dataService.getUsersByRole('agent');
      
      for (final agent in agents) {
        try {
          await createOrUpdateAgentAccount(agent.id);
          
          if (kDebugMode) {
            print('✅ [Android] Recalculated account for: ${agent.fullName}');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ [Android] Error recalculating account for ${agent.fullName}: $e');
          }
        }
      }

      if (kDebugMode) {
        print('✅ [Android] All agent accounts recalculated');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Android] Error recalculating all agent accounts: $e');
      }
    }
  }

  /// Get transfer invoices for agent (goods sent to agent)
  Future<List<InvoiceModel>> _getAgentTransferInvoices(String agentId) async {
    try {
      final allInvoices = await _dataService.getInvoicesByAgent(agentId);
      return allInvoices.where((invoice) => 
        invoice.type == 'agent' || 
        invoice.type == 'goods' ||
        invoice.type == 'transfer'
      ).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting transfer invoices for agent $agentId: $e');
      }
      return [];
    }
  }

  /// Get customer invoices for agent (agent's sales to customers)
  Future<List<InvoiceModel>> _getAgentCustomerInvoices(String agentId) async {
    try {
      final allInvoices = await _dataService.getInvoicesByAgent(agentId);
      return allInvoices.where((invoice) => 
        invoice.type == 'customer' || 
        invoice.type == 'sale'
      ).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting customer invoices for agent $agentId: $e');
      }
      return [];
    }
  }

  /// Get payments made by agent
  Future<List<Map<String, dynamic>>> _getAgentPayments(String agentId) async {
    try {
      return await _dataService.getPaymentsByAgent(agentId);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting payments for agent $agentId: $e');
      }
      return [];
    }
  }

  /// Validate agent account calculations
  Future<Map<String, dynamic>> validateAgentAccount(String agentId) async {
    try {
      if (kDebugMode) {
        print('🔍 [Android] Validating agent account: $agentId');
      }

      final existingAccount = await _dataService.getAgentAccount(agentId);
      final calculatedData = await calculateAgentAccountData(agentId);

      if (existingAccount == null) {
        return {
          'isValid': false,
          'reason': 'Account does not exist',
          'recommendation': 'Create new account',
        };
      }

      final balanceDifference = (existingAccount.currentBalance - calculatedData['currentBalance']).abs();
      const tolerance = 0.01; // 1 piaster tolerance

      final isValid = balanceDifference <= tolerance;

      if (kDebugMode) {
        print('📊 [Android] Validation result for ${existingAccount.agentName}:');
        print('   Stored balance: ${existingAccount.currentBalance} ج.م');
        print('   Calculated balance: ${calculatedData['currentBalance']} ج.م');
        print('   Difference: ${balanceDifference} ج.م');
        print('   Is valid: $isValid');
      }

      return {
        'isValid': isValid,
        'storedBalance': existingAccount.currentBalance,
        'calculatedBalance': calculatedData['currentBalance'],
        'difference': balanceDifference,
        'tolerance': tolerance,
        'recommendation': isValid ? 'Account is accurate' : 'Recalculation needed',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ [Android] Error validating agent account: $e');
      }
      
      return {
        'isValid': false,
        'reason': 'Validation error: $e',
        'recommendation': 'Check account data',
      };
    }
  }
}
