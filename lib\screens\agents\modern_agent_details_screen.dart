import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/utils/app_utils.dart';
import '../../models/professional_agent_account.dart';
import 'agent_payment_screen.dart';

/// شاشة تفاصيل الوكيل العصرية
/// تصميم حديث مع تبويبات منظمة ومفصلة
class ModernAgentDetailsScreen extends StatefulWidget {
  final ProfessionalAgentAccount account;

  const ModernAgentDetailsScreen({
    super.key,
    required this.account,
  });

  @override
  State<ModernAgentDetailsScreen> createState() => _ModernAgentDetailsScreenState();
}

class _ModernAgentDetailsScreenState extends State<ModernAgentDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            _buildSliverAppBar(),
            _buildSliverTabBar(),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildAccountSummaryTab(),
            _buildTransactionsTab(),
            _buildWarehouseTab(),
            _buildAnalyticsTab(),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  Widget _buildSliverAppBar() {
    final account = widget.account;
    final balanceColor = account.currentBalance >= 0 ? Colors.green : Colors.red;
    
    return SliverAppBar(
      expandedHeight: 280,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  const SizedBox(height: 60), // مساحة للـ AppBar
                  
                  // صورة الوكيل
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(40),
                      border: Border.all(color: Colors.white.withValues(alpha: 0.3), width: 3),
                    ),
                    child: Center(
                      child: Text(
                        account.agentName.isNotEmpty ? account.agentName[0] : 'و',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 32,
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // اسم الوكيل
                  Text(
                    account.agentName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  if (account.agentPhone != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      account.agentPhone!,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 16,
                      ),
                    ),
                  ],
                  
                  const SizedBox(height: 20),
                  
                  // الرصيد الحالي
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(25),
                      border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          account.currentBalance >= 0 ? Icons.trending_up : Icons.trending_down,
                          color: account.currentBalance >= 0 ? Colors.lightGreen : Colors.orange,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${account.currentBalance.toStringAsFixed(2)} ج.م',
                          style: TextStyle(
                            color: account.currentBalance >= 0 ? Colors.lightGreen : Colors.orange,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliverTabBar() {
    return SliverPersistentHeader(
      delegate: _SliverTabBarDelegate(
        TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: Colors.grey,
          indicatorColor: AppColors.primary,
          indicatorWeight: 3,
          labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          unselectedLabelStyle: const TextStyle(fontSize: 12),
          tabs: const [
            Tab(
              icon: Icon(Icons.account_balance_wallet, size: 20),
              text: 'ملخص الحساب',
            ),
            Tab(
              icon: Icon(Icons.receipt_long, size: 20),
              text: 'المعاملات',
            ),
            Tab(
              icon: Icon(Icons.warehouse, size: 20),
              text: 'المخزن',
            ),
            Tab(
              icon: Icon(Icons.analytics, size: 20),
              text: 'التحليلات',
            ),
          ],
        ),
      ),
      pinned: true,
    );
  }

  Widget _buildAccountSummaryTab() {
    final account = widget.account;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // بطاقات الإحصائيات السريعة
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المبيعات',
                  '${account.totalCustomerSales.toStringAsFixed(0)} ج.م',
                  Icons.trending_up,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'إجمالي العمولات',
                  '${account.totalAgentCommission.toStringAsFixed(0)} ج.م',
                  Icons.monetization_on,
                  Colors.orange,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'المدفوعات المستلمة',
                  '${account.totalPaymentsReceived.toStringAsFixed(0)} ج.م',
                  Icons.payment,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'عدد المعاملات',
                  '${account.transactionCount}',
                  Icons.receipt,
                  Colors.purple,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // تفاصيل الحساب
          _buildDetailsCard(
            'تفاصيل الحساب',
            Icons.account_circle,
            [
              _buildDetailRow('رقم الحساب', account.agentId),
              _buildDetailRow('اسم الوكيل', account.agentName),
              if (account.agentPhone != null)
                _buildDetailRow('رقم الهاتف', account.agentPhone!),
              if (account.agentEmail != null)
                _buildDetailRow('البريد الإلكتروني', account.agentEmail!),
              _buildDetailRow('تاريخ الإنشاء', AppUtils.formatDate(account.createdAt)),
              _buildDetailRow('آخر تحديث', AppUtils.formatDate(account.updatedAt)),
              _buildDetailRow('الحالة', account.isActive ? 'نشط' : 'غير نشط'),
              _buildDetailRow('التحقق', account.isVerified ? 'محقق' : 'غير محقق'),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // الملخص المالي
          _buildDetailsCard(
            'الملخص المالي',
            Icons.account_balance,
            [
              _buildDetailRow('الرصيد الحالي', '${account.currentBalance.toStringAsFixed(2)} ج.م',
                  valueColor: account.currentBalance >= 0 ? Colors.green : Colors.red),
              _buildDetailRow('إجمالي المبيعات', '${account.totalCustomerSales.toStringAsFixed(2)} ج.م'),
              _buildDetailRow('إجمالي العمولات', '${account.totalAgentCommission.toStringAsFixed(2)} ج.م'),
              _buildDetailRow('أرباح الشركة', '${account.totalCompanyProfits.toStringAsFixed(2)} ج.م'),
              _buildDetailRow('المدفوعات المستلمة', '${account.totalPaymentsReceived.toStringAsFixed(2)} ج.م'),
              if (account.totalDebt > 0)
                _buildDetailRow('المديونية', '${account.totalDebt.toStringAsFixed(2)} ج.م', 
                    valueColor: Colors.red),
              if (account.availableCredit > 0)
                _buildDetailRow('الرصيد الدائن', '${account.availableCredit.toStringAsFixed(2)} ج.م', 
                    valueColor: Colors.green),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'جدول المعاملات',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          Text(
            'سيتم تطوير هذا القسم قريباً',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.warehouse_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'إدارة المخزن',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          Text(
            'سيتم تطوير هذا القسم قريباً',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.analytics_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'التحليلات والتقارير',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          Text(
            'سيتم تطوير هذا القسم قريباً',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsCard(String title, IconData icon, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: valueColor ?? Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "payment",
          onPressed: _addPayment,
          backgroundColor: Colors.green,
          child: const Icon(Icons.payment, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "pdf",
          onPressed: _exportToPDF,
          backgroundColor: Colors.red,
          child: const Icon(Icons.picture_as_pdf, color: Colors.white),
        ),
      ],
    );
  }

  void _addPayment() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AgentPaymentScreen(account: widget.account),
      ),
    );

    if (result == true) {
      // إعادة تحميل البيانات إذا لزم الأمر
      setState(() {});
    }
  }

  void _exportToPDF() {
    AppUtils.showSnackBar(context, 'تصدير PDF قيد التطوير');
  }
}

// مساعد لـ SliverTabBar
class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverTabBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.white,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}
