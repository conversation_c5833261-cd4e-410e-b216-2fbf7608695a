import 'package:flutter/foundation.dart';

/// Utility class to fix image URLs for Windows compatibility
class ImageUrlFixer {
  /// Fix Cloudinary URLs by converting HTTPS to HTTP to bypass SSL issues on Windows
  static String fixCloudinaryUrl(String originalUrl) {
    if (originalUrl.isEmpty || originalUrl.trim().isEmpty) {
      if (kDebugMode) {
        print('🔧 ImageUrlFixer: Empty URL provided');
      }
      return '';
    }

    // Convert HTTPS Cloudinary URLs to HTTP to bypass SSL issues
    if (originalUrl.contains('cloudinary.com') && originalUrl.startsWith('https://')) {
      final fixedUrl = originalUrl.replaceFirst('https://', 'http://');
      if (kDebugMode) {
        print('🔧 ImageUrlFixer: Converting HTTPS to HTTP');
        print('   Original: $originalUrl');
        print('   Fixed: $fixedUrl');
      }
      return fixedUrl;
    }

    if (kDebugMode) {
      print('🔧 ImageUrlFixer: URL unchanged: $originalUrl');
    }
    return originalUrl;
  }

  /// Fix multiple URLs at once
  static List<String> fixMultipleUrls(List<String> urls) {
    return urls.map((url) => fixCloudinaryUrl(url)).toList();
  }

  /// Check if URL is valid
  static bool isValidUrl(String url) {
    if (url.isEmpty || url.trim().isEmpty) {
      return false;
    }

    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      if (kDebugMode) {
        print('🔧 ImageUrlFixer: Invalid URL: $url - $e');
      }
      return false;
    }
  }

  /// Get a safe URL (returns empty string if invalid)
  static String getSafeUrl(String url) {
    if (!isValidUrl(url)) {
      return '';
    }
    return fixCloudinaryUrl(url);
  }

  /// Test if URL is a Cloudinary URL
  static bool isCloudinaryUrl(String url) {
    return url.contains('cloudinary.com');
  }

  /// Get debug info about URL
  static Map<String, dynamic> getUrlDebugInfo(String url) {
    return {
      'original': url,
      'isEmpty': url.isEmpty,
      'isValid': isValidUrl(url),
      'isCloudinary': isCloudinaryUrl(url),
      'fixed': fixCloudinaryUrl(url),
      'needsFixing': isCloudinaryUrl(url) && url.startsWith('https://'),
    };
  }

  /// Print debug info for multiple URLs
  static void debugUrls(List<String> urls, String context) {
    if (!kDebugMode) return;

    print('🔧 ImageUrlFixer Debug - $context:');
    for (int i = 0; i < urls.length; i++) {
      final info = getUrlDebugInfo(urls[i]);
      print('   [$i] ${info['original']}');
      print('       Valid: ${info['isValid']}, Cloudinary: ${info['isCloudinary']}, Needs fixing: ${info['needsFixing']}');
      if (info['needsFixing']) {
        print('       Fixed: ${info['fixed']}');
      }
    }
  }
}
