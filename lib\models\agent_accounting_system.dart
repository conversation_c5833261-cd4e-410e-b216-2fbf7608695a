import 'package:flutter/foundation.dart';

/// نظام محاسبي شامل لحسابات الوكلاء مع المؤسسة
/// يطبق مبادئ المحاسبة المزدوجة ويضمن دقة الحسابات

/// نموذج حساب الوكيل الشامل
class AgentAccount {
  final String id;
  final String agentId;
  final String agentName;
  final String agentPhone;
  
  // === الحسابات الأساسية ===
  final double totalGoodsReceived;      // إجمالي البضاعة المستلمة (قيمة الشراء)
  final double totalGoodsWithdrawn;     // إجمالي البضاعة المسحوبة (قيمة الشراء)
  final double totalCustomerSales;      // إجمالي مبيعات العملاء (قيمة البيع)
  final double totalAgentCommission;    // إجمالي عمولة الوكيل
  final double totalPaymentsReceived;   // إجمالي المدفوعات المستلمة
  
  // === الحسابات المحسوبة ===
  final double currentBalance;          // الرصيد الحالي (موجب = مدين، سالب = دائن)
  final double availableCredit;         // الرصيد الدائن المتاح
  
  // === المعاملات ===
  final List<AgentTransaction> transactions;
  
  // === معلومات التتبع ===
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final String lastUpdatedBy;

  const AgentAccount({
    required this.id,
    required this.agentId,
    required this.agentName,
    required this.agentPhone,
    required this.totalGoodsReceived,
    required this.totalGoodsWithdrawn,
    required this.totalCustomerSales,
    required this.totalAgentCommission,
    required this.totalPaymentsReceived,
    required this.currentBalance,
    required this.availableCredit,
    required this.transactions,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    required this.lastUpdatedBy,
  });

  /// حساب الرصيد الحالي بناءً على المعادلة المحاسبية
  /// الرصيد = (البضاعة المستلمة - البضاعة المسحوبة) - (المبيعات + العمولة) + المدفوعات
  double calculateCurrentBalance() {
    final netGoodsValue = totalGoodsReceived - totalGoodsWithdrawn;
    final totalEarnings = totalCustomerSales + totalAgentCommission;
    return netGoodsValue - totalEarnings + totalPaymentsReceived;
  }

  /// حساب المديونية الحالية (إذا كان الرصيد موجب)
  double getCurrentDebt() {
    final balance = calculateCurrentBalance();
    return balance > 0 ? balance : 0.0;
  }

  /// حساب الرصيد الدائن (إذا كان الرصيد سالب)
  double getCreditBalance() {
    final balance = calculateCurrentBalance();
    return balance < 0 ? balance.abs() : 0.0;
  }

  /// التحقق من صحة الحسابات
  bool validateAccounts() {
    final calculatedBalance = calculateCurrentBalance();
    const tolerance = 0.01; // تسامح في الحسابات للأرقام العشرية
    return (calculatedBalance - currentBalance).abs() < tolerance;
  }

  /// حساب نسبة العمولة
  double getCommissionRate() {
    if (totalCustomerSales == 0) return 0.0;
    return (totalAgentCommission / totalCustomerSales) * 100;
  }

  /// حساب صافي الربح للوكيل
  double getNetProfit() {
    return totalAgentCommission - getCurrentDebt();
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'agentId': agentId,
      'agentName': agentName,
      'agentPhone': agentPhone,
      'totalGoodsReceived': totalGoodsReceived,
      'totalGoodsWithdrawn': totalGoodsWithdrawn,
      'totalCustomerSales': totalCustomerSales,
      'totalAgentCommission': totalAgentCommission,
      'totalPaymentsReceived': totalPaymentsReceived,
      'currentBalance': currentBalance,
      'availableCredit': availableCredit,
      'transactions': transactions.map((t) => t.toMap()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'lastUpdatedBy': lastUpdatedBy,
    };
  }

  /// إنشاء من Map
  factory AgentAccount.fromMap(Map<String, dynamic> map) {
    return AgentAccount(
      id: map['id'] ?? '',
      agentId: map['agentId'] ?? '',
      agentName: map['agentName'] ?? '',
      agentPhone: map['agentPhone'] ?? '',
      totalGoodsReceived: (map['totalGoodsReceived'] ?? 0.0).toDouble(),
      totalGoodsWithdrawn: (map['totalGoodsWithdrawn'] ?? 0.0).toDouble(),
      totalCustomerSales: (map['totalCustomerSales'] ?? 0.0).toDouble(),
      totalAgentCommission: (map['totalAgentCommission'] ?? 0.0).toDouble(),
      totalPaymentsReceived: (map['totalPaymentsReceived'] ?? 0.0).toDouble(),
      currentBalance: (map['currentBalance'] ?? 0.0).toDouble(),
      availableCredit: (map['availableCredit'] ?? 0.0).toDouble(),
      transactions: (map['transactions'] as List<dynamic>?)
          ?.map((t) => AgentTransaction.fromMap(t as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updatedAt'] ?? DateTime.now().toIso8601String()),
      createdBy: map['createdBy'] ?? '',
      lastUpdatedBy: map['lastUpdatedBy'] ?? '',
    );
  }

  /// نسخ مع تحديث
  AgentAccount copyWith({
    String? id,
    String? agentId,
    String? agentName,
    String? agentPhone,
    double? totalGoodsReceived,
    double? totalGoodsWithdrawn,
    double? totalCustomerSales,
    double? totalAgentCommission,
    double? totalPaymentsReceived,
    double? currentBalance,
    double? availableCredit,
    List<AgentTransaction>? transactions,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? lastUpdatedBy,
  }) {
    return AgentAccount(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      agentPhone: agentPhone ?? this.agentPhone,
      totalGoodsReceived: totalGoodsReceived ?? this.totalGoodsReceived,
      totalGoodsWithdrawn: totalGoodsWithdrawn ?? this.totalGoodsWithdrawn,
      totalCustomerSales: totalCustomerSales ?? this.totalCustomerSales,
      totalAgentCommission: totalAgentCommission ?? this.totalAgentCommission,
      totalPaymentsReceived: totalPaymentsReceived ?? this.totalPaymentsReceived,
      currentBalance: currentBalance ?? this.currentBalance,
      availableCredit: availableCredit ?? this.availableCredit,
      transactions: transactions ?? this.transactions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      lastUpdatedBy: lastUpdatedBy ?? this.lastUpdatedBy,
    );
  }

  @override
  String toString() {
    return 'AgentAccount(id: $id, agentName: $agentName, currentBalance: $currentBalance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AgentAccount && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج معاملة الوكيل
class AgentTransaction {
  final String id;
  final String agentId;
  final AgentTransactionType type;
  final String description;
  final double amount;                  // المبلغ (موجب أو سالب حسب النوع)
  final double balanceAfter;           // الرصيد بعد المعاملة
  final String? relatedInvoiceId;      // معرف الفاتورة المرتبطة
  final String? relatedTransferId;     // معرف التحويل المرتبط
  final String? relatedPaymentId;      // معرف الدفعة المرتبطة
  final Map<String, dynamic>? metadata; // بيانات إضافية
  final DateTime createdAt;
  final String createdBy;

  const AgentTransaction({
    required this.id,
    required this.agentId,
    required this.type,
    required this.description,
    required this.amount,
    required this.balanceAfter,
    this.relatedInvoiceId,
    this.relatedTransferId,
    this.relatedPaymentId,
    this.metadata,
    required this.createdAt,
    required this.createdBy,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'agentId': agentId,
      'type': type.name,
      'description': description,
      'amount': amount,
      'balanceAfter': balanceAfter,
      'relatedInvoiceId': relatedInvoiceId,
      'relatedTransferId': relatedTransferId,
      'relatedPaymentId': relatedPaymentId,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
    };
  }

  /// إنشاء من Map
  factory AgentTransaction.fromMap(Map<String, dynamic> map) {
    return AgentTransaction(
      id: map['id'] ?? '',
      agentId: map['agentId'] ?? '',
      type: AgentTransactionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => AgentTransactionType.other,
      ),
      description: map['description'] ?? '',
      amount: (map['amount'] ?? 0.0).toDouble(),
      balanceAfter: (map['balanceAfter'] ?? 0.0).toDouble(),
      relatedInvoiceId: map['relatedInvoiceId'],
      relatedTransferId: map['relatedTransferId'],
      relatedPaymentId: map['relatedPaymentId'],
      metadata: map['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      createdBy: map['createdBy'] ?? '',
    );
  }

  @override
  String toString() {
    return 'AgentTransaction(id: $id, type: $type, amount: $amount)';
  }
}

/// أنواع معاملات الوكيل
enum AgentTransactionType {
  goodsReceived,    // استلام بضاعة
  goodsWithdrawn,   // سحب بضاعة
  customerSale,     // مبيعات عملاء
  commission,       // عمولة
  payment,          // دفعة
  adjustment,       // تسوية
  other,           // أخرى
}

/// امتداد لأنواع المعاملات
extension AgentTransactionTypeExtension on AgentTransactionType {
  String get displayName {
    switch (this) {
      case AgentTransactionType.goodsReceived:
        return 'استلام بضاعة';
      case AgentTransactionType.goodsWithdrawn:
        return 'سحب بضاعة';
      case AgentTransactionType.customerSale:
        return 'مبيعات عملاء';
      case AgentTransactionType.commission:
        return 'عمولة';
      case AgentTransactionType.payment:
        return 'دفعة';
      case AgentTransactionType.adjustment:
        return 'تسوية';
      case AgentTransactionType.other:
        return 'أخرى';
    }
  }

  bool get isDebit {
    switch (this) {
      case AgentTransactionType.goodsReceived:
      case AgentTransactionType.payment:
        return true;
      case AgentTransactionType.goodsWithdrawn:
      case AgentTransactionType.customerSale:
      case AgentTransactionType.commission:
        return false;
      case AgentTransactionType.adjustment:
      case AgentTransactionType.other:
        return false; // يعتمد على المبلغ
    }
  }
}
