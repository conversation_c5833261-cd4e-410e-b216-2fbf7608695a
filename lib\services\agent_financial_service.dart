import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/agent_financial_account_model.dart';
import '../models/invoice_model.dart';
import '../models/payment_model.dart';
import '../core/constants/app_constants.dart';

/// خدمة إدارة الحسابات المالية للوكلاء
/// تضمن دقة الحسابات وتطبيق المبادئ المحاسبية الصحيحة
class AgentFinancialService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  static const String _collection = 'agent_financial_accounts';

  /// إنشاء أو تحديث حساب وكيل
  Future<AgentFinancialAccount> createOrUpdateAgentAccount({
    required String agentId,
    required String agentName,
    required String agentPhone,
    required String userId,
  }) async {
    try {
      // البحث عن حساب موجود
      final existingAccount = await getAgentAccount(agentId);
      
      if (existingAccount != null) {
        // إعادة حساب الحساب الموجود
        return await recalculateAgentAccount(agentId, userId);
      }

      // إنشاء حساب جديد
      final accountId = 'agent_financial_${agentId}';
      final now = DateTime.now();

      final newAccount = AgentFinancialAccount(
        id: accountId,
        agentId: agentId,
        agentName: agentName,
        agentPhone: agentPhone,
        totalGoodsReceived: 0.0,
        totalGoodsReturned: 0.0,
        totalCustomerSales: 0.0,
        totalAgentProfits: 0.0,
        totalCompanyProfits: 0.0,
        totalPaymentsReceived: 0.0,
        currentDebt: 0.0,
        currentBalance: 0.0,
        transactions: [],
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        lastUpdatedBy: userId,
      );

      await _firestore.collection(_collection).doc(accountId).set(newAccount.toMap());
      
      if (kDebugMode) {
        print('✅ Created new agent financial account: $agentName');
      }

      return newAccount;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating agent account: $e');
      }
      rethrow;
    }
  }

  /// الحصول على حساب وكيل
  Future<AgentFinancialAccount?> getAgentAccount(String agentId) async {
    try {
      final accountId = 'agent_financial_$agentId';
      final doc = await _firestore.collection(_collection).doc(accountId).get();
      
      if (doc.exists && doc.data() != null) {
        return AgentFinancialAccount.fromMap(doc.data()!);
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting agent account: $e');
      }
      return null;
    }
  }

  /// الحصول على جميع حسابات الوكلاء
  Future<List<AgentFinancialAccount>> getAllAgentAccounts() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();
      
      return snapshot.docs
          .map((doc) => AgentFinancialAccount.fromMap(doc.data()))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all agent accounts: $e');
      }
      return [];
    }
  }

  /// إعادة حساب حساب وكيل من الصفر
  Future<AgentFinancialAccount> recalculateAgentAccount(String agentId, String userId) async {
    try {
      if (kDebugMode) {
        print('🔄 Recalculating agent account: $agentId');
      }

      // جمع جميع الفواتير المتعلقة بالوكيل
      final invoicesSnapshot = await _firestore
          .collection('invoices')
          .where('agentId', isEqualTo: agentId)
          .get();

      // جمع جميع المدفوعات المتعلقة بالوكيل
      final paymentsSnapshot = await _firestore
          .collection('payments')
          .where('agentId', isEqualTo: agentId)
          .get();

      // تحويل البيانات إلى نماذج
      final invoices = invoicesSnapshot.docs
          .map((doc) => InvoiceModel.fromMap(doc.data()))
          .toList();

      final payments = paymentsSnapshot.docs
          .map((doc) => PaymentModel.fromMap(doc.data()))
          .toList();

      // حساب المجاميع
      final calculations = _calculateAccountTotals(invoices, payments);

      // إنشاء قائمة المعاملات
      final transactions = _generateTransactionHistory(invoices, payments);

      // الحصول على بيانات الوكيل
      final agentDoc = await _firestore.collection('users').doc(agentId).get();
      final agentData = agentDoc.data() ?? {};

      final accountId = 'agent_financial_$agentId';
      final now = DateTime.now();

      // إنشاء الحساب المحدث
      final updatedAccount = AgentFinancialAccount(
        id: accountId,
        agentId: agentId,
        agentName: agentData['fullName'] ?? 'Unknown Agent',
        agentPhone: agentData['phone'] ?? '',
        totalGoodsReceived: calculations['totalGoodsReceived']!,
        totalGoodsReturned: calculations['totalGoodsReturned']!,
        totalCustomerSales: calculations['totalCustomerSales']!,
        totalAgentProfits: calculations['totalAgentProfits']!,
        totalCompanyProfits: calculations['totalCompanyProfits']!,
        totalPaymentsReceived: calculations['totalPaymentsReceived']!,
        currentDebt: calculations['currentDebt']!,
        currentBalance: calculations['currentBalance']!,
        transactions: transactions,
        createdAt: DateTime.now(),
        updatedAt: now,
        createdBy: userId,
        lastUpdatedBy: userId,
      );

      // التحقق من صحة الحسابات
      if (!updatedAccount.validateAccounts()) {
        if (kDebugMode) {
          print('⚠️ Account validation failed for agent: $agentId');
        }
      }

      // حفظ الحساب المحدث
      await _firestore.collection(_collection).doc(accountId).set(updatedAccount.toMap());

      if (kDebugMode) {
        print('✅ Agent account recalculated successfully: ${updatedAccount.agentName}');
        print('   Balance: ${updatedAccount.currentBalance}');
        print('   Debt: ${updatedAccount.currentDebt}');
        print('   Transactions: ${updatedAccount.transactions.length}');
      }

      return updatedAccount;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error recalculating agent account: $e');
      }
      rethrow;
    }
  }

  /// حساب المجاميع من الفواتير والمدفوعات
  Map<String, double> _calculateAccountTotals(
    List<InvoiceModel> invoices,
    List<PaymentModel> payments,
  ) {
    double totalGoodsReceived = 0.0;
    double totalGoodsReturned = 0.0;
    double totalCustomerSales = 0.0;
    double totalAgentProfits = 0.0;
    double totalCompanyProfits = 0.0;
    double totalPaymentsReceived = 0.0;

    // حساب من الفواتير
    for (final invoice in invoices) {
      switch (invoice.type) {
        case 'goods':
        case 'agent':
        case 'transfer':
          // بضاعة مستلمة من المؤسسة أو محولة للوكيل
          if (invoice.status != 'cancelled') {
            // إذا كانت فاتورة تحويل للوكيل (الوكيل يستلم بضاعة)
            if (invoice.agentId != null &&
                (invoice.additionalData?['transferType'] == 'to_agent' ||
                 invoice.additionalData?['targetWarehouseType'] == 'agent')) {
              totalGoodsReceived += invoice.sellingPrice;
            }
            // إذا كانت فاتورة تحويل من الوكيل (الوكيل يرجع بضاعة)
            else if (invoice.agentId != null &&
                     (invoice.additionalData?['transferType'] == 'from_agent' ||
                      invoice.additionalData?['sourceWarehouseType'] == 'agent')) {
              totalGoodsReturned += invoice.sellingPrice;
            }
            // فاتورة بضاعة عادية للوكيل
            else if (invoice.agentId != null) {
              totalGoodsReceived += invoice.sellingPrice;
            }
          }
          break;

        case 'customer':
          // مبيعات للعملاء
          if (invoice.status != 'cancelled') {
            totalCustomerSales += invoice.sellingPrice;
            totalAgentProfits += invoice.agentProfitShare;
            totalCompanyProfits += invoice.companyProfitShare;
          }
          break;

        case 'return':
          // بضاعة مرتجعة
          if (invoice.status != 'cancelled') {
            totalGoodsReturned += invoice.sellingPrice;
          }
          break;
      }
    }

    // حساب من المدفوعات
    for (final payment in payments) {
      if (payment.status == 'completed') {
        totalPaymentsReceived += payment.amount;
      }
    }

    // حساب الرصيد والمديونية
    final currentBalance = (totalGoodsReceived - totalGoodsReturned) + 
                          totalCompanyProfits - totalPaymentsReceived;
    final currentDebt = currentBalance > 0 ? currentBalance : 0.0;

    return {
      'totalGoodsReceived': totalGoodsReceived,
      'totalGoodsReturned': totalGoodsReturned,
      'totalCustomerSales': totalCustomerSales,
      'totalAgentProfits': totalAgentProfits,
      'totalCompanyProfits': totalCompanyProfits,
      'totalPaymentsReceived': totalPaymentsReceived,
      'currentBalance': currentBalance,
      'currentDebt': currentDebt,
    };
  }

  /// إنشاء تاريخ المعاملات
  List<AgentTransaction> _generateTransactionHistory(
    List<InvoiceModel> invoices,
    List<PaymentModel> payments,
  ) {
    final List<AgentTransaction> transactions = [];
    
    // إضافة معاملات الفواتير
    for (final invoice in invoices) {
      String type;
      String description;
      double amount;

      switch (invoice.type) {
        case 'goods':
        case 'agent':
          type = 'goods_received';
          description = 'استلام بضاعة - فاتورة ${invoice.invoiceNumber}';
          amount = invoice.sellingPrice;
          break;

        case 'transfer':
          // تحديد نوع التحويل
          if (invoice.agentId != null &&
              (invoice.additionalData?['transferType'] == 'to_agent' ||
               invoice.additionalData?['targetWarehouseType'] == 'agent')) {
            type = 'goods_received';
            description = 'تحويل بضاعة للوكيل - ${invoice.additionalData?['itemDescription'] ?? 'صنف غير محدد'}';
            amount = invoice.sellingPrice;
          } else if (invoice.agentId != null &&
                     (invoice.additionalData?['transferType'] == 'from_agent' ||
                      invoice.additionalData?['sourceWarehouseType'] == 'agent')) {
            type = 'goods_returned';
            description = 'تحويل بضاعة من الوكيل - ${invoice.additionalData?['itemDescription'] ?? 'صنف غير محدد'}';
            amount = -invoice.sellingPrice;
          } else {
            continue; // تجاهل التحويلات التي لا تخص هذا الوكيل
          }
          break;

        case 'customer':
          type = 'customer_sale';
          description = 'بيع للعميل ${invoice.customerData?['name'] ?? 'غير محدد'} - فاتورة ${invoice.invoiceNumber}';
          amount = invoice.companyProfitShare;
          break;

        case 'return':
          type = 'goods_returned';
          description = 'إرجاع بضاعة - فاتورة ${invoice.invoiceNumber}';
          amount = -invoice.sellingPrice;
          break;

        default:
          continue;
      }

      transactions.add(AgentTransaction(
        id: 'txn_${invoice.id}',
        type: type,
        description: description,
        amount: amount,
        balanceAfter: 0.0, // سيتم حسابه لاحقاً
        relatedInvoiceId: invoice.id,
        createdAt: invoice.createdAt,
        createdBy: invoice.createdBy,
        metadata: {
          'invoiceType': invoice.type,
          'invoiceNumber': invoice.invoiceNumber,
          'transferType': invoice.additionalData?['transferType'],
          'itemDescription': invoice.additionalData?['itemDescription'],
        },
      ));
    }

    // إضافة معاملات المدفوعات
    for (final payment in payments) {
      if (payment.status == 'completed') {
        transactions.add(AgentTransaction(
          id: 'txn_${payment.id}',
          type: 'payment_received',
          description: 'دفعة مستلمة - ${payment.paymentMethod}',
          amount: -payment.amount,
          balanceAfter: 0.0, // سيتم حسابه لاحقاً
          relatedPaymentId: payment.id,
          createdAt: payment.createdAt,
          createdBy: payment.createdBy,
        ));
      }
    }

    // ترتيب المعاملات حسب التاريخ
    transactions.sort((a, b) => a.createdAt.compareTo(b.createdAt));

    // حساب الرصيد بعد كل معاملة
    double runningBalance = 0.0;
    for (int i = 0; i < transactions.length; i++) {
      runningBalance += transactions[i].amount;
      transactions[i] = AgentTransaction(
        id: transactions[i].id,
        type: transactions[i].type,
        description: transactions[i].description,
        amount: transactions[i].amount,
        balanceAfter: runningBalance,
        relatedInvoiceId: transactions[i].relatedInvoiceId,
        relatedPaymentId: transactions[i].relatedPaymentId,
        metadata: transactions[i].metadata,
        createdAt: transactions[i].createdAt,
        createdBy: transactions[i].createdBy,
      );
    }

    return transactions;
  }

  /// تسجيل دفعة جديدة من الوكيل
  Future<AgentFinancialAccount> recordPayment({
    required String agentId,
    required double amount,
    required String paymentMethod,
    required String userId,
    String? notes,
  }) async {
    try {
      if (kDebugMode) {
        print('💰 Recording payment for agent: $agentId, Amount: $amount');
      }

      // إنشاء سند القبض
      final paymentId = 'payment_${DateTime.now().millisecondsSinceEpoch}';
      final receiptNumber = 'RCPT-${DateTime.now().millisecondsSinceEpoch}';
      final now = DateTime.now();

      final payment = PaymentModel(
        id: paymentId,
        receiptNumber: receiptNumber,
        agentId: agentId,
        amount: amount,
        paymentMethod: paymentMethod,
        paymentDate: now,
        status: 'completed',
        notes: notes ?? '',
        createdAt: now,
        createdBy: userId,
        updatedAt: now,
      );

      // حفظ سند القبض
      await _firestore.collection('payments').doc(paymentId).set(payment.toMap());

      // إعادة حساب حساب الوكيل
      final updatedAccount = await recalculateAgentAccount(agentId, userId);

      if (kDebugMode) {
        print('✅ Payment recorded successfully');
        print('   New balance: ${updatedAccount.currentBalance}');
      }

      return updatedAccount;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error recording payment: $e');
      }
      rethrow;
    }
  }

  /// سحب بضاعة من الوكيل (إرجاع للمؤسسة)
  Future<AgentFinancialAccount> withdrawGoods({
    required String agentId,
    required String itemId,
    required double originalValue,
    required String userId,
    String? reason,
  }) async {
    try {
      if (kDebugMode) {
        print('📦 Withdrawing goods from agent: $agentId, Value: $originalValue');
      }

      // إنشاء فاتورة إرجاع
      final returnInvoiceId = 'return_${DateTime.now().millisecondsSinceEpoch}';
      final now = DateTime.now();

      final returnInvoice = InvoiceModel(
        id: returnInvoiceId,
        invoiceNumber: 'RET-${DateTime.now().millisecondsSinceEpoch}',
        type: 'return',
        agentId: agentId,
        warehouseId: 'agent_warehouse_$agentId',
        itemId: itemId,
        itemCost: originalValue,
        sellingPrice: originalValue,
        profitAmount: 0.0,
        agentProfitShare: 0.0,
        companyProfitShare: 0.0,
        status: 'completed',
        createdAt: now,
        createdBy: userId,
        updatedAt: now,
        additionalData: {
          'reason': reason ?? 'سحب بضاعة من الوكيل',
          'withdrawalType': 'goods_withdrawal',
        },
      );

      // حفظ فاتورة الإرجاع
      await _firestore.collection('invoices').doc(returnInvoiceId).set(returnInvoice.toMap());

      // تحديث حالة الصنف إلى مرتجع
      await _firestore.collection('items').doc(itemId).update({
        'status': 'returned',
        'returnedAt': Timestamp.fromDate(DateTime.now()),
        'returnedBy': userId,
        'returnReason': reason ?? 'سحب بضاعة من الوكيل',
      });

      // إعادة حساب حساب الوكيل
      final updatedAccount = await recalculateAgentAccount(agentId, userId);

      if (kDebugMode) {
        print('✅ Goods withdrawn successfully');
        print('   New balance: ${updatedAccount.currentBalance}');
      }

      return updatedAccount;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error withdrawing goods: $e');
      }
      rethrow;
    }
  }

  /// الحصول على إحصائيات شاملة لجميع الوكلاء
  Future<Map<String, dynamic>> getAgentsStatistics() async {
    try {
      final accounts = await getAllAgentAccounts();

      double totalDebt = 0.0;
      double totalCredit = 0.0;
      double totalSales = 0.0;
      double totalProfits = 0.0;
      int agentsWithDebt = 0;
      int agentsWithCredit = 0;
      int activeAgents = 0;

      for (final account in accounts) {
        totalSales += account.totalCustomerSales;
        totalProfits += account.totalAgentProfits + account.totalCompanyProfits;

        if (account.currentBalance > 0) {
          totalDebt += account.currentBalance;
          agentsWithDebt++;
        } else if (account.currentBalance < 0) {
          totalCredit += account.currentBalance.abs();
          agentsWithCredit++;
        }

        // اعتبار الوكيل نشط إذا كان لديه معاملات
        if (account.transactions.isNotEmpty) {
          activeAgents++;
        }
      }

      return {
        'totalAgents': accounts.length,
        'activeAgents': activeAgents,
        'agentsWithDebt': agentsWithDebt,
        'agentsWithCredit': agentsWithCredit,
        'totalDebt': totalDebt,
        'totalCredit': totalCredit,
        'totalSales': totalSales,
        'totalProfits': totalProfits,
        'averageBalance': accounts.isNotEmpty ?
            accounts.map((a) => a.currentBalance).reduce((a, b) => a + b) / accounts.length : 0.0,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting agents statistics: $e');
      }
      return {};
    }
  }

  /// إعادة حساب جميع حسابات الوكلاء
  Future<void> recalculateAllAgentAccounts(String userId) async {
    try {
      if (kDebugMode) {
        print('🔄 Recalculating all agent accounts...');
      }

      // الحصول على جميع الوكلاء
      final agentsSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'agent')
          .get();

      int processed = 0;
      for (final agentDoc in agentsSnapshot.docs) {
        try {
          await recalculateAgentAccount(agentDoc.id, userId);
          processed++;
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error recalculating account for agent ${agentDoc.id}: $e');
          }
        }
      }

      if (kDebugMode) {
        print('✅ Recalculated $processed agent accounts');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error recalculating all agent accounts: $e');
      }
      rethrow;
    }
  }
}
