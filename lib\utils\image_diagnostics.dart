import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:io';

/// Utility class for diagnosing image loading issues
class ImageDiagnostics {
  static final ImageDiagnostics _instance = ImageDiagnostics._internal();
  factory ImageDiagnostics() => _instance;
  ImageDiagnostics._internal();

  /// Test image URL accessibility
  Future<Map<String, dynamic>> testImageUrl(String imageUrl) async {
    if (kDebugMode) {
      print('🔍 Testing image URL: $imageUrl');
    }

    final result = <String, dynamic>{
      'url': imageUrl,
      'isValid': false,
      'isAccessible': false,
      'statusCode': null,
      'contentType': null,
      'contentLength': null,
      'error': null,
      'suggestions': <String>[],
    };

    try {
      // Validate URL format
      final uri = Uri.parse(imageUrl);
      if (!uri.hasScheme || (uri.scheme != 'http' && uri.scheme != 'https')) {
        result['error'] = 'Invalid URL scheme. Must be http or https.';
        result['suggestions'].add('Check if the URL starts with http:// or https://');
        return result;
      }
      result['isValid'] = true;

      // Test HTTP accessibility
      final response = await http.head(
        uri,
        headers: {
          'User-Agent': 'El-Farhan-Desktop/1.0',
          'Accept': 'image/*',
        },
      ).timeout(const Duration(seconds: 10));

      result['statusCode'] = response.statusCode;
      result['contentType'] = response.headers['content-type'];
      result['contentLength'] = response.headers['content-length'];

      if (response.statusCode == 200) {
        result['isAccessible'] = true;
        
        // Check if it's actually an image
        final contentType = response.headers['content-type'] ?? '';
        if (!contentType.startsWith('image/')) {
          result['error'] = 'URL does not point to an image file';
          result['suggestions'].add('Verify that the URL points to an image file');
        }
      } else {
        result['error'] = 'HTTP ${response.statusCode}: ${response.reasonPhrase}';
        result['suggestions'].add('Check if the image URL is correct and accessible');
        
        if (response.statusCode == 404) {
          result['suggestions'].add('The image file was not found on the server');
        } else if (response.statusCode == 403) {
          result['suggestions'].add('Access to the image is forbidden');
        } else if (response.statusCode >= 500) {
          result['suggestions'].add('Server error - try again later');
        }
      }

    } catch (e) {
      result['error'] = e.toString();
      
      if (e is SocketException) {
        result['suggestions'].add('Check your internet connection');
        result['suggestions'].add('Verify that the domain is accessible');
      } else if (e is HttpException) {
        result['suggestions'].add('HTTP error occurred - check the URL');
      } else if (e.toString().contains('CERTIFICATE_VERIFY_FAILED')) {
        result['suggestions'].add('SSL certificate verification failed');
        result['suggestions'].add('This might be a Cloudinary SSL issue');
      } else {
        result['suggestions'].add('Unknown error - check the URL format');
      }
    }

    if (kDebugMode) {
      print('🔍 Image test result: $result');
    }

    return result;
  }

  /// Test multiple image URLs
  Future<List<Map<String, dynamic>>> testMultipleImageUrls(List<String> imageUrls) async {
    final results = <Map<String, dynamic>>[];
    
    for (final url in imageUrls) {
      if (url.isNotEmpty) {
        final result = await testImageUrl(url);
        results.add(result);
      }
    }
    
    return results;
  }

  /// Get diagnostic report for an item's images
  Future<Map<String, dynamic>> getItemImageDiagnostics(String motorImageUrl, String chassisImageUrl) async {
    final urls = <String>[];
    if (motorImageUrl.isNotEmpty) urls.add(motorImageUrl);
    if (chassisImageUrl.isNotEmpty) urls.add(chassisImageUrl);

    final results = await testMultipleImageUrls(urls);
    
    final report = <String, dynamic>{
      'totalImages': urls.length,
      'accessibleImages': results.where((r) => r['isAccessible'] == true).length,
      'failedImages': results.where((r) => r['isAccessible'] != true).length,
      'results': results,
      'overallStatus': 'unknown',
      'recommendations': <String>[],
    };

    // Determine overall status
    if (report['failedImages'] == 0 && report['totalImages'] > 0) {
      report['overallStatus'] = 'good';
    } else if (report['accessibleImages'] > 0) {
      report['overallStatus'] = 'partial';
      report['recommendations'].add('Some images are not accessible');
    } else if (report['totalImages'] > 0) {
      report['overallStatus'] = 'bad';
      report['recommendations'].add('No images are accessible');
    } else {
      report['overallStatus'] = 'no_images';
      report['recommendations'].add('No images found for this item');
    }

    // Add general recommendations
    if (report['failedImages'] > 0) {
      report['recommendations'].add('Check your internet connection');
      report['recommendations'].add('Verify Cloudinary configuration');
      report['recommendations'].add('Try uploading images again');
    }

    return report;
  }

  /// Print diagnostic report to console
  void printDiagnosticReport(Map<String, dynamic> report) {
    if (!kDebugMode) return;

    print('\n🔍 === IMAGE DIAGNOSTICS REPORT ===');
    print('📊 Total Images: ${report['totalImages']}');
    print('✅ Accessible: ${report['accessibleImages']}');
    print('❌ Failed: ${report['failedImages']}');
    print('📈 Overall Status: ${report['overallStatus']}');
    
    if (report['recommendations'] != null && report['recommendations'].isNotEmpty) {
      print('\n💡 Recommendations:');
      for (final rec in report['recommendations']) {
        print('   • $rec');
      }
    }

    if (report['results'] != null) {
      print('\n📋 Detailed Results:');
      for (int i = 0; i < report['results'].length; i++) {
        final result = report['results'][i];
        print('   ${i + 1}. ${result['url']}');
        print('      Status: ${result['isAccessible'] ? '✅ Accessible' : '❌ Failed'}');
        if (result['error'] != null) {
          print('      Error: ${result['error']}');
        }
        if (result['statusCode'] != null) {
          print('      HTTP: ${result['statusCode']}');
        }
        if (result['contentType'] != null) {
          print('      Type: ${result['contentType']}');
        }
      }
    }
    
    print('🔍 === END REPORT ===\n');
  }

  /// Test Cloudinary connectivity
  Future<bool> testCloudinaryConnectivity() async {
    try {
      // Test with a known Cloudinary URL
      const testUrl = 'https://res.cloudinary.com/demo/image/upload/sample.jpg';
      final result = await testImageUrl(testUrl);
      return result['isAccessible'] == true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Cloudinary connectivity test failed: $e');
      }
      return false;
    }
  }

  /// Get network status
  Future<Map<String, dynamic>> getNetworkStatus() async {
    final status = <String, dynamic>{
      'isConnected': false,
      'canReachCloudinary': false,
      'canReachGoogle': false,
      'error': null,
    };

    try {
      // Test basic connectivity
      final googleResponse = await http.head(Uri.parse('https://www.google.com')).timeout(const Duration(seconds: 5));
      status['canReachGoogle'] = googleResponse.statusCode == 200;
      status['isConnected'] = status['canReachGoogle'];

      // Test Cloudinary connectivity
      status['canReachCloudinary'] = await testCloudinaryConnectivity();

    } catch (e) {
      status['error'] = e.toString();
    }

    return status;
  }
}
