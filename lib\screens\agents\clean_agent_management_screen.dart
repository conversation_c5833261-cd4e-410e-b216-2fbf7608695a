import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/theme/app_colors.dart';
import '../../core/utils/app_utils.dart';
import '../../models/simple_agent_account.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/simple_agent_service.dart';
import '../../services/data_service.dart';

/// شاشة إدارة الوكلاء الجديدة والمبسطة
class CleanAgentManagementScreen extends StatefulWidget {
  const CleanAgentManagementScreen({super.key});

  @override
  State<CleanAgentManagementScreen> createState() => _CleanAgentManagementScreenState();
}

class _CleanAgentManagementScreenState extends State<CleanAgentManagementScreen> {
  final SimpleAgentService _agentService = SimpleAgentService();
  final DataService _dataService = DataService.instance;
  
  List<SimpleAgentAccount> _agentAccounts = [];
  List<UserModel> _agents = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحديث جميع الحسابات أولاً
      await _agentService.updateAllAgentAccounts();
      
      // تحميل البيانات
      final accounts = await _agentService.getAllAgentAccounts();
      final agents = await _dataService.getUsersByRole('agent');

      setState(() {
        _agentAccounts = accounts;
        _agents = agents;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    }
  }

  List<SimpleAgentAccount> get _filteredAccounts {
    if (_searchQuery.isEmpty) return _agentAccounts;
    
    return _agentAccounts.where((account) {
      return account.agentName.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الوكلاء'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والإحصائيات
          _buildHeaderSection(),
          
          // قائمة الوكلاء
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildAgentsList(),
          ),
        ],
      ),
      floatingActionButton: authProvider.canManageUsers
          ? FloatingActionButton(
              onPressed: () {
                // الانتقال لشاشة إضافة وكيل جديد
                Navigator.pushNamed(context, '/users/add');
              },
              backgroundColor: AppColors.primary,
              child: const Icon(Icons.person_add, color: Colors.white),
            )
          : null,
    );
  }

  Widget _buildHeaderSection() {
    final totalBalance = _agentAccounts.fold(0.0, (sum, account) => sum + account.currentBalance);
    final activeAgents = _agentAccounts.where((account) => account.isActive).length;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            decoration: InputDecoration(
              hintText: 'البحث عن وكيل...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          
          const SizedBox(height: 16),
          
          // الإحصائيات
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: 'إجمالي الوكلاء',
                  value: '$activeAgents',
                  icon: Icons.people,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  title: 'إجمالي الأرصدة',
                  value: '${totalBalance.toStringAsFixed(2)} ج.م',
                  icon: Icons.account_balance_wallet,
                  color: totalBalance >= 0 ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAgentsList() {
    final filteredAccounts = _filteredAccounts;
    
    if (filteredAccounts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد حسابات وكلاء',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredAccounts.length,
      itemBuilder: (context, index) {
        final account = filteredAccounts[index];
        return _buildAgentCard(account);
      },
    );
  }

  Widget _buildAgentCard(SimpleAgentAccount account) {
    final balanceColor = account.currentBalance >= 0 ? Colors.green : Colors.red;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: AppColors.primary,
          child: Text(
            account.agentName.isNotEmpty ? account.agentName[0] : 'و',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(
          account.agentName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text('الرصيد: ${account.currentBalance.toStringAsFixed(2)} ج.م'),
            Text('المعاملات: ${account.transactionCount}'),
            Text('الحالة: ${account.balanceStatus}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: balanceColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                account.balanceStatus,
                style: TextStyle(
                  color: balanceColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
            const SizedBox(height: 4),
            Icon(
              account.hasActivity ? Icons.trending_up : Icons.trending_flat,
              color: account.hasActivity ? Colors.green : Colors.grey,
              size: 16,
            ),
          ],
        ),
        onTap: () {
          _showAgentDetails(account);
        },
      ),
    );
  }

  void _showAgentDetails(SimpleAgentAccount account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل حساب ${account.agentName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('الرصيد الحالي', '${account.currentBalance.toStringAsFixed(2)} ج.م'),
            _buildDetailRow('إجمالي المبيعات', '${account.totalSales.toStringAsFixed(2)} ج.م'),
            _buildDetailRow('إجمالي المدفوعات', '${account.totalPayments.toStringAsFixed(2)} ج.م'),
            _buildDetailRow('إجمالي العمولات', '${account.totalCommission.toStringAsFixed(2)} ج.م'),
            _buildDetailRow('عدد المعاملات', '${account.transactionCount}'),
            _buildDetailRow('آخر معاملة', AppUtils.formatDate(account.lastTransactionDate)),
            _buildDetailRow('الحالة', account.balanceStatus),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _updateSingleAccount(account.agentId);
            },
            child: const Text('تحديث الحساب'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value),
        ],
      ),
    );
  }

  Future<void> _updateSingleAccount(String agentId) async {
    try {
      await _agentService.updateAgentAccount(agentId);
      await _loadData();
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تحديث الحساب بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحديث الحساب: $e', isError: true);
      }
    }
  }
}
