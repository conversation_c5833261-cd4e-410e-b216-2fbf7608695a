import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../core/theme/desktop_theme.dart';
import '../../widgets/desktop/desktop_dashboard_cards.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';
import '../../models/item_model.dart';
import '../../models/warehouse_model.dart';
import '../../models/invoice_model.dart';

/// Professional desktop dashboard with comprehensive business overview
class DesktopDashboardScreen extends StatefulWidget {
  const DesktopDashboardScreen({super.key});

  @override
  State<DesktopDashboardScreen> createState() => _DesktopDashboardScreenState();
}

class _DesktopDashboardScreenState extends State<DesktopDashboardScreen> {
  bool _isLoading = true;
  List<ItemModel> _items = [];
  List<WarehouseModel> _warehouses = [];
  List<InvoiceModel> _invoices = [];
  Map<String, dynamic> _statistics = {};

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);
      
      final items = await dataService.getItems();
      final warehouses = await dataService.getWarehouses();
      final invoices = await dataService.getInvoices();
      
      if (mounted) {
        setState(() {
          _items = items;
          _warehouses = warehouses;
          _invoices = invoices;
          _statistics = _calculateStatistics();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Map<String, dynamic> _calculateStatistics() {
    try {
      final availableItems = _items.where((item) => item.status == 'متاح').length;
      final soldItems = _items.where((item) => item.status == 'مباع').length;
      final totalValue = _items.fold<double>(0, (sum, item) => sum + item.purchasePrice);

      final todayInvoices = _invoices.where((invoice) {
        final today = DateTime.now();
        final invoiceDate = invoice.createdAt;
        return invoiceDate.year == today.year &&
               invoiceDate.month == today.month &&
               invoiceDate.day == today.day;
      }).length;

      final monthlyRevenue = _invoices.where((invoice) {
        final now = DateTime.now();
        final invoiceDate = invoice.createdAt;
        return invoiceDate.year == now.year && invoiceDate.month == now.month;
      }).fold<double>(0, (sum, invoice) => sum + invoice.sellingPrice);

      final statistics = {
        'totalItems': _items.length,
        'availableItems': availableItems,
        'soldItems': soldItems,
        'totalValue': totalValue,
        'totalWarehouses': _warehouses.length,
        'totalInvoices': _invoices.length,
        'todayInvoices': todayInvoices,
        'monthlyRevenue': monthlyRevenue,
      };

      if (kDebugMode) {
        print('📊 Dashboard statistics calculated: $statistics');
      }

      return statistics;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error calculating statistics: $e');
      }
      return {
        'totalItems': 0,
        'availableItems': 0,
        'soldItems': 0,
        'totalValue': 0.0,
        'totalWarehouses': 0,
        'totalInvoices': 0,
        'todayInvoices': 0,
        'monthlyRevenue': 0.0,
      };
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Section
          _buildWelcomeSection(),
          
          const SizedBox(height: DesktopTheme.spacingXLarge),
          
          // Key Metrics
          _buildKeyMetrics(),
          
          const SizedBox(height: DesktopTheme.spacingXLarge),
          
          // Quick Actions
          _buildQuickActions(),
          
          const SizedBox(height: DesktopTheme.spacingXLarge),
          
          // Charts and Analytics
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Sales Chart
              Expanded(
                flex: 2,
                child: _buildSalesChart(),
              ),
              
              const SizedBox(width: DesktopTheme.spacingLarge),
              
              // Recent Activities
              Expanded(
                child: _buildRecentActivities(),
              ),
            ],
          ),
          
          const SizedBox(height: DesktopTheme.spacingXLarge),
          
          // System Status
          _buildSystemStatus(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final authProvider = Provider.of<AuthProvider>(context);
    final now = DateTime.now();
    final timeOfDay = now.hour < 12 ? 'صباح الخير' : 
                     now.hour < 17 ? 'مساء الخير' : 'مساء الخير';

    return Container(
      padding: const EdgeInsets.all(DesktopTheme.spacingXLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DesktopTheme.primaryBlue,
            DesktopTheme.primaryBlueDark,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(DesktopTheme.radiusXLarge),
        boxShadow: DesktopTheme.elevatedShadow,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$timeOfDay، ${authProvider.currentUser?.fullName}',
                  style: DesktopTheme.headingLarge.copyWith(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: DesktopTheme.spacingSmall),
                Text(
                  'مرحباً بك في نظام إدارة النقل - الفرحان',
                  style: DesktopTheme.titleMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: DesktopTheme.spacingMedium),
                Text(
                  'تاريخ اليوم: ${now.day}/${now.month}/${now.year}',
                  style: DesktopTheme.bodyMedium.copyWith(
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesktopTheme.radiusLarge),
            ),
            child: Icon(
              Icons.dashboard_outlined,
              size: 64,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeyMetrics() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المؤشرات الرئيسية',
          style: DesktopTheme.headingMedium,
        ),
        const SizedBox(height: DesktopTheme.spacingLarge),
        DesktopDashboardGrid(
          crossAxisCount: 4,
          childAspectRatio: 1.3,
          children: [
            DesktopDashboardCard(
              title: 'إجمالي الأصناف',
              value: (_statistics['totalItems'] ?? 0).toString(),
              subtitle: '${_statistics['availableItems'] ?? 0} متاح',
              icon: Icons.inventory_2_outlined,
              iconColor: DesktopTheme.primaryBlue,
              trend: '+5%',
              isPositiveTrend: true,
              onTap: () => _navigateToInventory(),
            ),
            DesktopDashboardCard(
              title: 'المبيعات اليوم',
              value: (_statistics['todayInvoices'] ?? 0).toString(),
              subtitle: 'فاتورة',
              icon: Icons.trending_up_outlined,
              iconColor: DesktopTheme.accentGreen,
              trend: '+12%',
              isPositiveTrend: true,
              onTap: () => _navigateToSales(),
            ),
            DesktopDashboardCard(
              title: 'إيرادات الشهر',
              value: '${((_statistics['monthlyRevenue'] ?? 0) / 1000).toStringAsFixed(1)}ك',
              subtitle: 'جنيه مصري',
              icon: Icons.account_balance_wallet_outlined,
              iconColor: DesktopTheme.accentOrange,
              trend: '+8%',
              isPositiveTrend: true,
              onTap: () => _navigateToReports(),
            ),
            DesktopDashboardCard(
              title: 'المخازن النشطة',
              value: (_statistics['totalWarehouses'] ?? 0).toString(),
              subtitle: 'مخزن',
              icon: Icons.warehouse_outlined,
              iconColor: DesktopTheme.accentPurple,
              onTap: () => _navigateToWarehouses(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: DesktopTheme.headingMedium,
        ),
        const SizedBox(height: DesktopTheme.spacingLarge),
        DesktopDashboardGrid(
          crossAxisCount: 6,
          childAspectRatio: 1.0,
          children: [
            DesktopQuickActionCard(
              title: 'إضافة عنصر',
              description: 'إضافة عنصر جديد للمخزون',
              icon: Icons.add_box_outlined,
              iconColor: DesktopTheme.primaryBlue,
              onTap: () => _addNewItem(),
            ),
            DesktopQuickActionCard(
              title: 'إنشاء فاتورة',
              description: 'إنشاء فاتورة مبيعات جديدة',
              icon: Icons.receipt_long_outlined,
              iconColor: DesktopTheme.accentGreen,
              onTap: () => _createInvoice(),
            ),
            DesktopQuickActionCard(
              title: 'نقل بضائع',
              description: 'نقل بضائع بين المخازن',
              icon: Icons.swap_horiz_outlined,
              iconColor: DesktopTheme.accentOrange,
              onTap: () => _transferItems(),
            ),
            DesktopQuickActionCard(
              title: 'تقرير مبيعات',
              description: 'عرض تقارير المبيعات',
              icon: Icons.analytics_outlined,
              iconColor: DesktopTheme.accentPurple,
              onTap: () => _viewSalesReports(),
            ),
            DesktopQuickActionCard(
              title: 'إدارة الوكلاء',
              description: 'إدارة حسابات الوكلاء',
              icon: Icons.people_outlined,
              iconColor: DesktopTheme.primaryBlueDark,
              onTap: () => _manageAgents(),
            ),
            DesktopQuickActionCard(
              title: 'الإعدادات',
              description: 'إعدادات النظام',
              icon: Icons.settings_outlined,
              iconColor: DesktopTheme.textSecondary,
              onTap: () => _openSettings(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSalesChart() {
    return DesktopChartCard(
      title: 'مبيعات الأسبوع',
      subtitle: 'إجمالي المبيعات لآخر 7 أيام',
      height: 300,
      chart: Container(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.bar_chart,
                size: 64,
                color: DesktopTheme.textTertiary,
              ),
              const SizedBox(height: DesktopTheme.spacingMedium),
              Text(
                'الرسم البياني قيد التطوير',
                style: DesktopTheme.titleMedium.copyWith(
                  color: DesktopTheme.textTertiary,
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildRecentActivities() {
    return Container(
      height: 300,
      decoration: DesktopTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: BoxDecoration(
              color: DesktopTheme.backgroundTertiary,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(DesktopTheme.radiusLarge),
                topRight: Radius.circular(DesktopTheme.radiusLarge),
              ),
              border: Border(
                bottom: BorderSide(
                  color: DesktopTheme.borderLight,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'الأنشطة الأخيرة',
                  style: DesktopTheme.titleLarge,
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {},
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
              itemCount: 5,
              itemBuilder: (context, index) {
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: DesktopTheme.primaryBlue.withOpacity(0.1),
                    child: Icon(
                      Icons.receipt_long_outlined,
                      color: DesktopTheme.primaryBlue,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    'فاتورة مبيعات جديدة',
                    style: DesktopTheme.titleSmall,
                  ),
                  subtitle: Text(
                    'منذ ${index + 1} ساعة',
                    style: DesktopTheme.bodySmall,
                  ),
                  trailing: Icon(
                    Icons.chevron_right,
                    color: DesktopTheme.textTertiary,
                  ),
                  onTap: () {},
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemStatus() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'حالة النظام',
          style: DesktopTheme.headingMedium,
        ),
        const SizedBox(height: DesktopTheme.spacingLarge),
        Row(
          children: [
            Expanded(
              child: DesktopStatusCard(
                title: 'قاعدة البيانات',
                status: 'متصلة',
                statusColor: DesktopTheme.statusSuccess,
                statusIcon: Icons.check_circle_outline,
                lastUpdated: 'منذ دقيقة',
              ),
            ),
            const SizedBox(width: DesktopTheme.spacingMedium),
            Expanded(
              child: DesktopStatusCard(
                title: 'التزامن',
                status: 'نشط',
                statusColor: DesktopTheme.statusSuccess,
                statusIcon: Icons.sync,
                lastUpdated: 'منذ 5 دقائق',
              ),
            ),
            const SizedBox(width: DesktopTheme.spacingMedium),
            Expanded(
              child: DesktopStatusCard(
                title: 'النسخ الاحتياطي',
                status: 'مكتمل',
                statusColor: DesktopTheme.statusInfo,
                statusIcon: Icons.backup,
                lastUpdated: 'اليوم 3:00 ص',
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Navigation methods
  void _navigateToInventory() {
    // TODO: Navigate to inventory
  }

  void _navigateToSales() {
    // TODO: Navigate to sales
  }

  void _navigateToReports() {
    // TODO: Navigate to reports
  }

  void _navigateToWarehouses() {
    // TODO: Navigate to warehouses
  }

  // Quick action methods
  void _addNewItem() {
    // TODO: Open add item dialog
  }

  void _createInvoice() {
    // TODO: Open create invoice dialog
  }

  void _transferItems() {
    // TODO: Open transfer items dialog
  }

  void _viewSalesReports() {
    // TODO: Navigate to sales reports
  }

  void _manageAgents() {
    // TODO: Navigate to agent management
  }

  void _openSettings() {
    // TODO: Open settings
  }
}
