import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/data_service.dart';
import '../services/auth_service.dart';
import '../services/permissions_service.dart';
import '../core/constants/app_constants.dart';

/// نظام اختبار عملي للسيناريوهات الأساسية في تطبيق آل فرحان للنقل الخفيف
class PracticalScenariosTest {
  static final DataService _dataService = DataService.instance;
  static final AuthService _authService = AuthService.instance;
  
  /// تشغيل الاختبارات العملية الأساسية
  static Future<void> runBasicTests() async {
    if (kDebugMode) {
      print('🚀 بدء الاختبارات العملية لتطبيق آل فرحان للنقل الخفيف');
      print('=' * 60);
      
      try {
        // اختبار 1: التحقق من المستخدم الأعلى
        await _testSuperAdminLogin();
        
        // اختبار 2: اختبار إنشاء المستخدمين
        await _testUserCreation();
        
        // اختبار 3: اختبار الصلاحيات
        await _testPermissions();
        
        // اختبار 4: اختبار المخازن
        await _testWarehouses();
        
        // اختبار 5: اختبار البيانات الأساسية
        await _testBasicData();
        
        print('✅ تم إكمال جميع الاختبارات العملية بنجاح');
        print('=' * 60);
        
      } catch (e) {
        print('❌ خطأ في الاختبارات: $e');
        rethrow;
      }
    }
  }
  
  /// اختبار تسجيل دخول المدير الأعلى
  static Future<void> _testSuperAdminLogin() async {
    print('\n👤 اختبار 1: التحقق من المستخدم الأعلى');
    
    try {
      // محاولة تسجيل الدخول
      final superAdmin = await _authService.signInWithUsername('ahmed', 'admin123');
      
      if (superAdmin != null) {
        print('    ✅ تم تسجيل دخول المدير الأعلى بنجاح');
        print('    ✅ الاسم: ${superAdmin.fullName}');
        print('    ✅ الدور: ${superAdmin.role}');
        print('    ✅ الصلاحيات: ${superAdmin.canManageUsers ? "إدارة المستخدمين" : "محدودة"}');
      } else {
        throw 'فشل في تسجيل دخول المدير الأعلى';
      }
    } catch (e) {
      print('    ❌ خطأ في تسجيل دخول المدير الأعلى: $e');
      rethrow;
    }
  }
  
  /// اختبار إنشاء المستخدمين
  static Future<void> _testUserCreation() async {
    print('\n👥 اختبار 2: إنشاء المستخدمين');
    
    try {
      final now = DateTime.now();
      
      // إنشاء مستخدم إداري للاختبار
      final testAdmin = UserModel(
        id: 'test_admin_${now.millisecondsSinceEpoch}',
        username: 'test.admin',
        email: '<EMAIL>',
        fullName: 'مدير اختبار',
        phone: '01000000001',
        role: AppConstants.adminRole,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      );
      
      await _dataService.createUserWithPassword(testAdmin, 'Test@123');
      print('    ✅ تم إنشاء مستخدم إداري للاختبار');
      
      // إنشاء وكيل للاختبار
      final testAgent = UserModel(
        id: 'test_agent_${now.millisecondsSinceEpoch}',
        username: 'test.agent',
        email: '<EMAIL>',
        fullName: 'وكيل اختبار',
        phone: '01000000002',
        role: AppConstants.agentRole,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      );
      
      await _dataService.createUserWithPassword(testAgent, 'Test@123');
      print('    ✅ تم إنشاء وكيل للاختبار');
      
      // إنشاء مستخدم معرض للاختبار
      final testShowroom = UserModel(
        id: 'test_showroom_${now.millisecondsSinceEpoch}',
        username: 'test.showroom',
        email: '<EMAIL>',
        fullName: 'مستخدم معرض اختبار',
        phone: '01000000003',
        role: AppConstants.showroomRole,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      );
      
      await _dataService.createUserWithPassword(testShowroom, 'Test@123');
      print('    ✅ تم إنشاء مستخدم معرض للاختبار');
      
    } catch (e) {
      print('    ❌ خطأ في إنشاء المستخدمين: $e');
      rethrow;
    }
  }
  
  /// اختبار الصلاحيات
  static Future<void> _testPermissions() async {
    print('\n🔐 اختبار 3: الصلاحيات');
    
    try {
      // الحصول على المستخدمين للاختبار
      final users = await _dataService.getUsers();
      
      UserModel? superAdmin;
      UserModel? admin;
      UserModel? agent;
      UserModel? showroom;
      
      for (final user in users) {
        switch (user.role) {
          case AppConstants.superAdminRole:
            superAdmin = user;
            break;
          case AppConstants.adminRole:
            admin = user;
            break;
          case AppConstants.agentRole:
            agent = user;
            break;
          case AppConstants.showroomRole:
            showroom = user;
            break;
        }
      }
      
      // اختبار صلاحيات المدير الأعلى
      if (superAdmin != null) {
        assert(PermissionsService.canAccess(superAdmin, 'user_management'), 
               'المدير الأعلى يجب أن يتمكن من إدارة المستخدمين');
        assert(PermissionsService.canAccess(superAdmin, 'all_reports'), 
               'المدير الأعلى يجب أن يتمكن من رؤية جميع التقارير');
        print('    ✅ صلاحيات المدير الأعلى صحيحة');
      }
      
      // اختبار صلاحيات المدير الإداري
      if (admin != null) {
        assert(PermissionsService.canAccess(admin, 'inventory_management'), 
               'المدير الإداري يجب أن يتمكن من إدارة المخزون');
        assert(!PermissionsService.canAccess(admin, 'user_management'), 
               'المدير الإداري لا يجب أن يتمكن من إدارة المستخدمين');
        print('    ✅ صلاحيات المدير الإداري صحيحة');
      }
      
      // اختبار صلاحيات الوكيل
      if (agent != null) {
        assert(PermissionsService.canAccess(agent, 'agent_sales'), 
               'الوكيل يجب أن يتمكن من المبيعات');
        assert(!PermissionsService.canAccess(agent, 'inventory_management'), 
               'الوكيل لا يجب أن يتمكن من إدارة المخزون العام');
        print('    ✅ صلاحيات الوكيل صحيحة');
      }
      
      // اختبار صلاحيات مستخدم المعرض
      if (showroom != null) {
        assert(PermissionsService.canAccess(showroom, 'showroom_sales'), 
               'مستخدم المعرض يجب أن يتمكن من مبيعات المعرض');
        assert(!PermissionsService.canAccess(showroom, 'agent_payments'), 
               'مستخدم المعرض لا يجب أن يتمكن من إدارة مدفوعات الوكلاء');
        print('    ✅ صلاحيات مستخدم المعرض صحيحة');
      }
      
    } catch (e) {
      print('    ❌ خطأ في اختبار الصلاحيات: $e');
      rethrow;
    }
  }
  
  /// اختبار المخازن
  static Future<void> _testWarehouses() async {
    print('\n🏪 اختبار 4: المخازن');
    
    try {
      // الحصول على المخازن الموجودة
      final warehouses = await _dataService.getWarehouses();
      
      print('    📋 المخازن الموجودة:');
      for (final warehouse in warehouses) {
        print('      - ${warehouse.name} (${warehouse.type})');
      }
      
      // التحقق من وجود المخازن الأساسية
      final mainWarehouses = warehouses.where((w) => w.type == AppConstants.mainWarehouse).toList();
      final showroomWarehouses = warehouses.where((w) => w.type == AppConstants.showroomWarehouse).toList();
      
      if (mainWarehouses.isNotEmpty) {
        print('    ✅ يوجد مخزن رئيسي');
      } else {
        print('    ⚠️ لا يوجد مخزن رئيسي');
      }
      
      if (showroomWarehouses.isNotEmpty) {
        print('    ✅ يوجد مخزن معرض');
      } else {
        print('    ⚠️ لا يوجد مخزن معرض');
      }
      
    } catch (e) {
      print('    ❌ خطأ في اختبار المخازن: $e');
      rethrow;
    }
  }
  
  /// اختبار البيانات الأساسية
  static Future<void> _testBasicData() async {
    print('\n📊 اختبار 5: البيانات الأساسية');
    
    try {
      // اختبار الأصناف
      final items = await _dataService.getItems();
      print('    📦 عدد الأصناف: ${items.length}');
      
      // اختبار الفواتير
      final invoices = await _dataService.getInvoices();
      print('    🧾 عدد الفواتير: ${invoices.length}');
      
      // اختبار حسابات الوكلاء
      final agents = await _dataService.getUsers(role: AppConstants.agentRole);
      print('    👥 عدد الوكلاء: ${agents.length}');
      
      for (final agent in agents) {
        final account = await _dataService.getAgentAccount(agent.id);
        if (account != null) {
          print('      - ${agent.fullName}: رصيد ${account.currentBalance.toStringAsFixed(2)} جنيه');
        }
      }
      
      print('    ✅ تم فحص البيانات الأساسية');
      
    } catch (e) {
      print('    ❌ خطأ في اختبار البيانات الأساسية: $e');
      rethrow;
    }
  }
  
  /// تشغيل اختبار سريع للوظائف الأساسية
  static Future<void> runQuickTest() async {
    if (kDebugMode) {
      print('⚡ اختبار سريع للوظائف الأساسية');
      print('-' * 40);
      
      try {
        // اختبار تسجيل الدخول
        final user = await _authService.signInWithUsername('ahmed', 'admin123');
        if (user != null) {
          print('✅ تسجيل الدخول يعمل');
        }
        
        // اختبار قاعدة البيانات
        final users = await _dataService.getUsers();
        print('✅ قاعدة البيانات تعمل (${users.length} مستخدم)');
        
        // اختبار الصلاحيات
        if (user != null && PermissionsService.canAccess(user, 'user_management')) {
          print('✅ نظام الصلاحيات يعمل');
        }
        
        print('✅ جميع الوظائف الأساسية تعمل بشكل صحيح');
        
      } catch (e) {
        print('❌ خطأ في الاختبار السريع: $e');
      }
    }
  }
}
