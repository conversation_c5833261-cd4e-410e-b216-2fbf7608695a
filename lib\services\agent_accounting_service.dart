import 'package:flutter/foundation.dart';
import '../models/agent_accounting_system.dart';
import '../models/user_model.dart';
import '../models/invoice_model.dart';
import '../models/transfer_model.dart';
import 'data_service.dart';
import 'firebase_service.dart';

/// خدمة محاسبية شاملة لحسابات الوكلاء
/// تطبق مبادئ المحاسبة المزدوجة وتضمن دقة الحسابات
class AgentAccountingService {
  static final AgentAccountingService _instance = AgentAccountingService._internal();
  factory AgentAccountingService() => _instance;
  AgentAccountingService._internal();

  final DataService _dataService = DataService.instance;
  final FirebaseService _firebaseService = FirebaseService.instance;

  /// الحصول على حساب وكيل محدد
  Future<AgentAccount?> getAgentAccount(String agentId) async {
    try {
      if (kDebugMode) {
        print('🔍 Getting agent account for: $agentId');
      }

      // البحث عن الحساب في قاعدة البيانات
      final accountData = await _dataService.getAgentAccount(agentId);
      if (accountData != null) {
        return AgentAccount.fromMap(accountData);
      }

      // إذا لم يوجد الحساب، إنشاء حساب جديد
      return await _createNewAgentAccount(agentId);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting agent account: $e');
      }
      return null;
    }
  }

  /// إنشاء حساب جديد للوكيل
  Future<AgentAccount> _createNewAgentAccount(String agentId) async {
    try {
      // الحصول على بيانات الوكيل
      final agent = await _dataService.getUserById(agentId);
      if (agent == null) {
        throw 'الوكيل غير موجود';
      }

      final newAccount = AgentAccount(
        id: 'acc_${agentId}_${DateTime.now().millisecondsSinceEpoch}',
        agentId: agentId,
        agentName: agent.fullName,
        agentPhone: agent.phone,
        totalGoodsReceived: 0.0,
        totalGoodsWithdrawn: 0.0,
        totalCustomerSales: 0.0,
        totalAgentCommission: 0.0,
        totalPaymentsReceived: 0.0,
        currentBalance: 0.0,
        availableCredit: 0.0,
        transactions: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'system',
        lastUpdatedBy: 'system',
      );

      // حفظ الحساب الجديد
      await _dataService.saveAgentAccount(newAccount.toMap());
      
      if (kDebugMode) {
        print('✅ Created new agent account for: ${agent.fullName}');
      }

      return newAccount;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating agent account: $e');
      }
      rethrow;
    }
  }

  /// إعادة حساب حساب الوكيل من جميع المعاملات
  Future<AgentAccount> recalculateAgentAccount(String agentId) async {
    try {
      if (kDebugMode) {
        print('🔄 Recalculating agent account for: $agentId');
      }

      // الحصول على بيانات الوكيل
      final agent = await _dataService.getUserById(agentId);
      if (agent == null) {
        throw 'الوكيل غير موجود';
      }

      // الحصول على جميع الفواتير المرتبطة بالوكيل
      final invoices = await _dataService.getInvoicesByAgent(agentId);
      
      // الحصول على جميع التحويلات المرتبطة بالوكيل
      final transfers = await _dataService.getTransfersByAgent(agentId);
      
      // الحصول على جميع الدفعات المرتبطة بالوكيل
      final payments = await _dataService.getPaymentsByAgent(agentId);

      // حساب المجاميع
      double totalGoodsReceived = 0.0;
      double totalGoodsWithdrawn = 0.0;
      double totalCustomerSales = 0.0;
      double totalAgentCommission = 0.0;
      double totalPaymentsReceived = 0.0;

      List<AgentTransaction> transactions = [];

      // معالجة الفواتير
      for (final invoice in invoices) {
        if (invoice.type == 'transfer' && invoice.toWarehouse?.contains('agent_$agentId') == true) {
          // استلام بضاعة
          totalGoodsReceived += invoice.totalAmount;
          transactions.add(AgentTransaction(
            id: 'txn_${invoice.id}_received',
            agentId: agentId,
            type: AgentTransactionType.goodsReceived,
            description: 'استلام بضاعة - فاتورة ${invoice.invoiceNumber}',
            amount: invoice.totalAmount,
            balanceAfter: 0.0, // سيتم حسابه لاحقاً
            relatedInvoiceId: invoice.id,
            createdAt: invoice.createdAt,
            createdBy: invoice.createdBy,
          ));
        } else if (invoice.type == 'sale' && invoice.customerId?.startsWith('agent_') != true) {
          // مبيعات للعملاء
          totalCustomerSales += invoice.totalAmount;
          final commission = invoice.totalAmount * 0.1; // 10% عمولة افتراضية
          totalAgentCommission += commission;
          
          transactions.add(AgentTransaction(
            id: 'txn_${invoice.id}_sale',
            agentId: agentId,
            type: AgentTransactionType.customerSale,
            description: 'مبيعات عملاء - فاتورة ${invoice.invoiceNumber}',
            amount: -invoice.totalAmount,
            balanceAfter: 0.0,
            relatedInvoiceId: invoice.id,
            createdAt: invoice.createdAt,
            createdBy: invoice.createdBy,
          ));

          transactions.add(AgentTransaction(
            id: 'txn_${invoice.id}_commission',
            agentId: agentId,
            type: AgentTransactionType.commission,
            description: 'عمولة مبيعات - فاتورة ${invoice.invoiceNumber}',
            amount: -commission,
            balanceAfter: 0.0,
            relatedInvoiceId: invoice.id,
            createdAt: invoice.createdAt,
            createdBy: invoice.createdBy,
          ));
        }
      }

      // معالجة التحويلات (سحب البضاعة)
      for (final transfer in transfers) {
        if (transfer.fromWarehouse.contains('agent_$agentId')) {
          // سحب بضاعة من الوكيل
          final withdrawnValue = _calculateTransferValue(transfer);
          totalGoodsWithdrawn += withdrawnValue;
          
          transactions.add(AgentTransaction(
            id: 'txn_${transfer.id}_withdrawn',
            agentId: agentId,
            type: AgentTransactionType.goodsWithdrawn,
            description: 'سحب بضاعة - تحويل ${transfer.transferNumber}',
            amount: -withdrawnValue,
            balanceAfter: 0.0,
            relatedTransferId: transfer.id,
            createdAt: transfer.createdAt,
            createdBy: transfer.createdBy,
          ));
        }
      }

      // معالجة الدفعات
      for (final payment in payments) {
        totalPaymentsReceived += payment.amount;
        transactions.add(AgentTransaction(
          id: 'txn_${payment.id}_payment',
          agentId: agentId,
          type: AgentTransactionType.payment,
          description: 'دفعة ${payment.paymentMethod} - ${payment.notes ?? ''}',
          amount: -payment.amount,
          balanceAfter: 0.0,
          relatedPaymentId: payment.id,
          createdAt: payment.createdAt,
          createdBy: payment.createdBy,
        ));
      }

      // ترتيب المعاملات حسب التاريخ
      transactions.sort((a, b) => a.createdAt.compareTo(b.createdAt));

      // حساب الرصيد بعد كل معاملة
      double runningBalance = 0.0;
      for (int i = 0; i < transactions.length; i++) {
        runningBalance += transactions[i].amount;
        transactions[i] = AgentTransaction(
          id: transactions[i].id,
          agentId: transactions[i].agentId,
          type: transactions[i].type,
          description: transactions[i].description,
          amount: transactions[i].amount,
          balanceAfter: runningBalance,
          relatedInvoiceId: transactions[i].relatedInvoiceId,
          relatedTransferId: transactions[i].relatedTransferId,
          relatedPaymentId: transactions[i].relatedPaymentId,
          metadata: transactions[i].metadata,
          createdAt: transactions[i].createdAt,
          createdBy: transactions[i].createdBy,
        );
      }

      // حساب الرصيد الحالي
      final currentBalance = totalGoodsReceived - totalGoodsWithdrawn - totalCustomerSales - totalAgentCommission + totalPaymentsReceived;
      final availableCredit = currentBalance < 0 ? currentBalance.abs() : 0.0;

      // إنشاء الحساب المحدث
      final updatedAccount = AgentAccount(
        id: 'acc_$agentId',
        agentId: agentId,
        agentName: agent.fullName,
        agentPhone: agent.phone,
        totalGoodsReceived: totalGoodsReceived,
        totalGoodsWithdrawn: totalGoodsWithdrawn,
        totalCustomerSales: totalCustomerSales,
        totalAgentCommission: totalAgentCommission,
        totalPaymentsReceived: totalPaymentsReceived,
        currentBalance: currentBalance,
        availableCredit: availableCredit,
        transactions: transactions,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'system',
        lastUpdatedBy: 'system',
      );

      // حفظ الحساب المحدث
      await _dataService.saveAgentAccount(updatedAccount.toMap());

      if (kDebugMode) {
        print('✅ Recalculated agent account: ${agent.fullName}');
        print('   Balance: $currentBalance');
        print('   Transactions: ${transactions.length}');
      }

      return updatedAccount;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error recalculating agent account: $e');
      }
      rethrow;
    }
  }

  /// حساب قيمة التحويل
  double _calculateTransferValue(TransferModel transfer) {
    double totalValue = 0.0;
    for (final item in transfer.items) {
      totalValue += item.quantity * (item.unitPrice ?? 0.0);
    }
    return totalValue;
  }

  /// الحصول على جميع حسابات الوكلاء
  Future<List<AgentAccount>> getAllAgentAccounts() async {
    try {
      final agents = await _dataService.getUsersByRole('agent');
      List<AgentAccount> accounts = [];

      for (final agent in agents) {
        final account = await getAgentAccount(agent.id);
        if (account != null) {
          accounts.add(account);
        }
      }

      return accounts;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all agent accounts: $e');
      }
      return [];
    }
  }

  /// تسجيل دفعة من الوكيل
  Future<void> recordPayment({
    required String agentId,
    required double amount,
    required String paymentMethod,
    required String userId,
    String? notes,
  }) async {
    try {
      if (kDebugMode) {
        print('💰 Recording payment: $amount from agent: $agentId');
      }

      // إنشاء سجل الدفعة
      final payment = {
        'id': 'pay_${DateTime.now().millisecondsSinceEpoch}',
        'agentId': agentId,
        'amount': amount,
        'paymentMethod': paymentMethod,
        'notes': notes,
        'createdAt': DateTime.now().toIso8601String(),
        'createdBy': userId,
      };

      // حفظ الدفعة
      await _dataService.savePayment(payment);

      // إعادة حساب حساب الوكيل
      await recalculateAgentAccount(agentId);

      if (kDebugMode) {
        print('✅ Payment recorded successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error recording payment: $e');
      }
      rethrow;
    }
  }

  /// معالجة سحب البضاعة من الوكيل
  Future<void> processGoodsWithdrawal({
    required String agentId,
    required String transferId,
    required double withdrawnValue,
    required String userId,
  }) async {
    try {
      if (kDebugMode) {
        print('📦 Processing goods withdrawal: $withdrawnValue from agent: $agentId');
      }

      // إعادة حساب حساب الوكيل لتضمين السحب الجديد
      await recalculateAgentAccount(agentId);

      if (kDebugMode) {
        print('✅ Goods withdrawal processed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error processing goods withdrawal: $e');
      }
      rethrow;
    }
  }

  /// الحصول على إحصائيات الوكلاء
  Future<Map<String, dynamic>> getAgentsStatistics() async {
    try {
      final accounts = await getAllAgentAccounts();

      int totalAgents = accounts.length;
      int activeAgents = accounts.where((a) => a.transactions.isNotEmpty).length;
      double totalDebt = accounts.fold(0.0, (sum, a) => sum + a.getCurrentDebt());
      double totalCredit = accounts.fold(0.0, (sum, a) => sum + a.getCreditBalance());
      double totalSales = accounts.fold(0.0, (sum, a) => sum + a.totalCustomerSales);
      double totalCommissions = accounts.fold(0.0, (sum, a) => sum + a.totalAgentCommission);

      return {
        'totalAgents': totalAgents,
        'activeAgents': activeAgents,
        'totalDebt': totalDebt,
        'totalCredit': totalCredit,
        'totalSales': totalSales,
        'totalCommissions': totalCommissions,
        'averageDebtPerAgent': totalAgents > 0 ? totalDebt / totalAgents : 0.0,
        'averageSalesPerAgent': activeAgents > 0 ? totalSales / activeAgents : 0.0,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting agents statistics: $e');
      }
      return {};
    }
  }

  /// التحقق من صحة جميع الحسابات
  Future<Map<String, bool>> validateAllAccounts() async {
    try {
      final accounts = await getAllAgentAccounts();
      Map<String, bool> validationResults = {};

      for (final account in accounts) {
        validationResults[account.agentId] = account.validateAccounts();
      }

      return validationResults;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating accounts: $e');
      }
      return {};
    }
  }

  /// إعادة حساب جميع حسابات الوكلاء
  Future<void> recalculateAllAgentAccounts() async {
    try {
      if (kDebugMode) {
        print('🔄 Recalculating all agent accounts...');
      }

      final agents = await _dataService.getUsersByRole('agent');

      for (final agent in agents) {
        await recalculateAgentAccount(agent.id);
      }

      if (kDebugMode) {
        print('✅ All agent accounts recalculated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error recalculating all accounts: $e');
      }
      rethrow;
    }
  }

  /// البحث في حسابات الوكلاء
  Future<List<AgentAccount>> searchAgentAccounts(String query) async {
    try {
      final allAccounts = await getAllAgentAccounts();

      if (query.isEmpty) return allAccounts;

      return allAccounts.where((account) {
        return account.agentName.toLowerCase().contains(query.toLowerCase()) ||
               account.agentPhone.contains(query) ||
               account.agentId.contains(query);
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error searching agent accounts: $e');
      }
      return [];
    }
  }

  /// الحصول على تقرير مفصل لحساب الوكيل
  Future<Map<String, dynamic>> getAgentAccountReport(String agentId) async {
    try {
      final account = await getAgentAccount(agentId);
      if (account == null) return {};

      // تجميع المعاملات حسب النوع
      Map<AgentTransactionType, List<AgentTransaction>> transactionsByType = {};
      for (final transaction in account.transactions) {
        transactionsByType.putIfAbsent(transaction.type, () => []).add(transaction);
      }

      // حساب المجاميع لكل نوع
      Map<String, double> totals = {};
      for (final type in AgentTransactionType.values) {
        final transactions = transactionsByType[type] ?? [];
        totals[type.name] = transactions.fold(0.0, (sum, t) => sum + t.amount.abs());
      }

      return {
        'account': account.toMap(),
        'transactionsByType': transactionsByType.map(
          (key, value) => MapEntry(key.name, value.map((t) => t.toMap()).toList()),
        ),
        'totals': totals,
        'summary': {
          'totalTransactions': account.transactions.length,
          'firstTransactionDate': account.transactions.isNotEmpty
              ? account.transactions.first.createdAt.toIso8601String()
              : null,
          'lastTransactionDate': account.transactions.isNotEmpty
              ? account.transactions.last.createdAt.toIso8601String()
              : null,
          'isValid': account.validateAccounts(),
        },
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting agent account report: $e');
      }
      return {};
    }
  }
}
