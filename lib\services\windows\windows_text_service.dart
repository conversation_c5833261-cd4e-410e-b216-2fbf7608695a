import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'dart:convert';

/// خدمة النصوص المحسنة للويندوز - بديل لـ OCR
class WindowsTextService {
  static final WindowsTextService _instance = WindowsTextService._internal();
  factory WindowsTextService() => _instance;
  WindowsTextService._internal();

  static WindowsTextService get instance => _instance;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (kDebugMode) {
      print('🔄 Initializing WindowsTextService...');
    }
    
    try {
      if (kDebugMode) {
        print('✅ WindowsTextService initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize WindowsTextService: $e');
      }
    }
  }

  /// قراءة النص من ملف نصي
  Future<String?> readTextFromFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['txt', 'csv', 'json'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final content = await file.readAsString(encoding: utf8);
        return content;
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error reading text from file: $e');
      }
      return null;
    }
  }

  /// استخراج النص من صورة (محاكاة بسيطة)
  Future<String?> extractTextFromImage(File imageFile) async {
    try {
      // في التطبيق الحقيقي، يمكن استخدام Windows OCR API
      // هنا نقدم محاكاة بسيطة
      
      if (kDebugMode) {
        print('🔄 Simulating text extraction from image...');
      }

      // محاكاة معالجة الصورة
      await Future.delayed(const Duration(seconds: 2));

      // إرجاع نص تجريبي
      return 'نص تجريبي مستخرج من الصورة\n'
             'يمكن تطوير هذه الوظيفة لاحقاً\n'
             'باستخدام Windows OCR API';
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error extracting text from image: $e');
      }
      return null;
    }
  }

  /// تحليل النص وإستخراج المعلومات
  Map<String, dynamic> analyzeText(String text) {
    try {
      final lines = text.split('\n');
      final words = text.split(' ');
      final characters = text.length;

      // البحث عن أرقام
      final numberRegex = RegExp(r'\d+');
      final numbers = numberRegex.allMatches(text).map((m) => m.group(0)).toList();

      // البحث عن تواريخ
      final dateRegex = RegExp(r'\d{1,2}/\d{1,2}/\d{4}');
      final dates = dateRegex.allMatches(text).map((m) => m.group(0)).toList();

      // البحث عن أرقام هواتف
      final phoneRegex = RegExp(r'\d{11}');
      final phones = phoneRegex.allMatches(text).map((m) => m.group(0)).toList();

      return {
        'totalLines': lines.length,
        'totalWords': words.length,
        'totalCharacters': characters,
        'numbers': numbers,
        'dates': dates,
        'phones': phones,
        'isEmpty': text.trim().isEmpty,
        'hasArabic': _containsArabic(text),
        'hasEnglish': _containsEnglish(text),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error analyzing text: $e');
      }
      return {};
    }
  }

  /// تنظيف النص
  String cleanText(String text) {
    try {
      // إزالة المسافات الزائدة
      text = text.replaceAll(RegExp(r'\s+'), ' ');
      
      // إزالة الأسطر الفارغة
      text = text.replaceAll(RegExp(r'\n\s*\n'), '\n');
      
      // تنظيف البداية والنهاية
      text = text.trim();
      
      return text;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error cleaning text: $e');
      }
      return text;
    }
  }

  /// تحويل النص إلى JSON
  Map<String, dynamic>? textToJson(String text) {
    try {
      return json.decode(text);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting text to JSON: $e');
      }
      return null;
    }
  }

  /// تحويل JSON إلى نص
  String jsonToText(Map<String, dynamic> jsonData) {
    try {
      return json.encode(jsonData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting JSON to text: $e');
      }
      return '';
    }
  }

  /// البحث في النص
  List<Map<String, dynamic>> searchInText(String text, String searchTerm) {
    try {
      final results = <Map<String, dynamic>>[];
      final lines = text.split('\n');

      for (int i = 0; i < lines.length; i++) {
        final line = lines[i];
        if (line.toLowerCase().contains(searchTerm.toLowerCase())) {
          results.add({
            'lineNumber': i + 1,
            'line': line,
            'position': line.toLowerCase().indexOf(searchTerm.toLowerCase()),
          });
        }
      }

      return results;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error searching in text: $e');
      }
      return [];
    }
  }

  /// استبدال النص
  String replaceInText(String text, String oldText, String newText) {
    try {
      return text.replaceAll(oldText, newText);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error replacing text: $e');
      }
      return text;
    }
  }

  /// تقسيم النص إلى فقرات
  List<String> splitIntoParagraphs(String text) {
    try {
      return text.split('\n\n').where((p) => p.trim().isNotEmpty).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error splitting text into paragraphs: $e');
      }
      return [text];
    }
  }

  /// تقسيم النص إلى جمل
  List<String> splitIntoSentences(String text) {
    try {
      return text.split(RegExp(r'[.!?]')).where((s) => s.trim().isNotEmpty).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error splitting text into sentences: $e');
      }
      return [text];
    }
  }

  /// حفظ النص في ملف
  Future<File?> saveTextToFile(String text, String fileName) async {
    try {
      final directory = Directory.current;
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(text, encoding: utf8);
      return file;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving text to file: $e');
      }
      return null;
    }
  }

  /// تحويل النص إلى قائمة
  List<String> textToList(String text, {String separator = '\n'}) {
    try {
      return text.split(separator).where((item) => item.trim().isNotEmpty).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting text to list: $e');
      }
      return [];
    }
  }

  /// تحويل القائمة إلى نص
  String listToText(List<String> list, {String separator = '\n'}) {
    try {
      return list.join(separator);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error converting list to text: $e');
      }
      return '';
    }
  }

  /// فلترة النص
  String filterText(String text, {
    bool removeNumbers = false,
    bool removeSpecialChars = false,
    bool removeExtraSpaces = true,
  }) {
    try {
      String result = text;

      if (removeNumbers) {
        result = result.replaceAll(RegExp(r'\d'), '');
      }

      if (removeSpecialChars) {
        result = result.replaceAll(RegExp(r'[^\w\s\u0600-\u06FF]'), '');
      }

      if (removeExtraSpaces) {
        result = result.replaceAll(RegExp(r'\s+'), ' ').trim();
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error filtering text: $e');
      }
      return text;
    }
  }

  /// التحقق من وجود نص عربي
  bool _containsArabic(String text) {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(text);
  }

  /// التحقق من وجود نص إنجليزي
  bool _containsEnglish(String text) {
    return RegExp(r'[a-zA-Z]').hasMatch(text);
  }

  /// إحصائيات النص
  Map<String, dynamic> getTextStatistics(String text) {
    try {
      final analysis = analyzeText(text);
      final paragraphs = splitIntoParagraphs(text);
      final sentences = splitIntoSentences(text);

      return {
        ...analysis,
        'totalParagraphs': paragraphs.length,
        'totalSentences': sentences.length,
        'averageWordsPerSentence': sentences.isNotEmpty 
            ? (analysis['totalWords'] / sentences.length).toStringAsFixed(1)
            : '0',
        'averageSentencesPerParagraph': paragraphs.isNotEmpty
            ? (sentences.length / paragraphs.length).toStringAsFixed(1)
            : '0',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting text statistics: $e');
      }
      return {};
    }
  }
}
