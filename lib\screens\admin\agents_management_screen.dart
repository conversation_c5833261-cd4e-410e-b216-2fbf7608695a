import 'package:flutter/material.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/user_model.dart';
import '../../models/warehouse_model.dart';

import '../../services/data_service.dart';
import 'add_agent_screen.dart';

class AgentsManagementScreen extends StatefulWidget {
  const AgentsManagementScreen({super.key});

  @override
  State<AgentsManagementScreen> createState() => _AgentsManagementScreenState();
}

class _AgentsManagementScreenState extends State<AgentsManagementScreen> {
  final DataService _dataService = DataService.instance;
  
  List<UserModel> _agents = [];
  List<WarehouseModel> _warehouses = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final agents = await _dataService.getAllUsers();
      final warehouses = await _dataService.getAllWarehouses();
      
      setState(() {
        _agents = agents.where((user) => user.role == 'agent').toList();
        _warehouses = warehouses;
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteAgent(UserModel agent) async {
    final confirmed = await AppUtils.showConfirmDialog(
      context,
      title: 'حذف الوكيل',
      message: 'هل أنت متأكد من حذف الوكيل "${agent.fullName}"؟\nسيتم حذف جميع بياناته.',
    );

    if (confirmed) {
      try {
        await _dataService.deleteUser(agent.id);
        if (mounted) {
          AppUtils.showSnackBar(context, 'تم حذف الوكيل بنجاح');
          _loadData();
        }
      } catch (e) {
        if (mounted) {
          AppUtils.showSnackBar(context, 'خطأ في حذف الوكيل: $e', isError: true);
        }
      }
    }
  }

  String _getWarehouseName(String warehouseId) {
    final warehouse = _warehouses.firstWhere(
      (w) => w.id == warehouseId,
      orElse: () => WarehouseModel(
        id: '',
        name: 'غير محدد',
        type: 'agent',
        address: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );
    return warehouse.name;
  }

  @override
  Widget build(BuildContext context) {
    // final authProvider = Provider.of<AuthProvider>(context); // Not used currently
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الوكلاء'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _agents.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  itemCount: _agents.length,
                  itemBuilder: (context, index) {
                    final agent = _agents[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          child: Text(
                            agent.fullName.substring(0, 1),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        title: Text(
                          agent.fullName,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('اسم المستخدم: ${agent.username}'),
                            Text('الهاتف: ${agent.phone}'),
                            if (agent.warehouseId != null)
                              Text('المخزن: ${_getWarehouseName(agent.warehouseId!)}'),
                            Text(
                              'تاريخ الإنشاء: ${AppUtils.formatDate(agent.createdAt)}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                        trailing: PopupMenuButton<String>(
                          onSelected: (value) {
                            switch (value) {
                              case 'edit':
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => AddAgentScreen(agent: agent),
                                  ),
                                ).then((_) => _loadData());
                                break;
                              case 'delete':
                                _deleteAgent(agent);
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  Icon(Icons.edit),
                                  SizedBox(width: 8),
                                  Text('تعديل'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('حذف', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const AddAgentScreen(),
            ),
          ).then((_) => _loadData());
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'لا يوجد وكلاء',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'اضغط على زر + لإضافة وكيل جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }
}
