import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/item_model.dart';
import '../../models/warehouse_model.dart';
import '../../services/image_service.dart';
import '../../services/data_service.dart';
import '../../services/motor_data_extraction_service.dart';
import '../../services/windows/enhanced_camera_service.dart';

import 'dart:io';
import '../../providers/auth_provider.dart';

class AddItemScreen extends StatefulWidget {
  final ItemModel? editItem;

  const AddItemScreen({
    super.key,
    this.editItem,
  });

  @override
  State<AddItemScreen> createState() => _AddItemScreenState();
}

class _AddItemScreenState extends State<AddItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _typeController = TextEditingController();
  final _modelController = TextEditingController();
  final _colorController = TextEditingController();
  final _brandController = TextEditingController();
  final _countryController = TextEditingController();
  final _yearController = TextEditingController();
  final _purchasePriceController = TextEditingController();
  final _sellingPriceController = TextEditingController();
  final _motorFingerprintController = TextEditingController();
  final _chassisNumberController = TextEditingController();

  final ImageService _imageService = ImageService.instance;
  final DataService _dataService = DataService.instance;
  final MotorDataExtractionService _extractionService = MotorDataExtractionService.instance;

  // خدمات الكاميرا المحسنة حسب المنصة
  late final dynamic _cameraService;

  Uint8List? _motorFingerprintImage;
  Uint8List? _chassisImage;
  String? _selectedWarehouseId;
  List<WarehouseModel> _warehouses = [];
  bool _isLoading = false;
  bool _isProcessingImage = false;
  bool _isProcessingChassisImage = false;

  final List<String> _vehicleTypes = [
    'موتوسيكل',
    'تروسيكل',
    'سكوتر كهرباء',
    'توكتوك',
  ];

  String _selectedVehicleType = 'موتوسيكل';

  @override
  void initState() {
    super.initState();
    _loadWarehouses();
    _initializeForEdit();
  }

  /// Initialize form for editing if editItem is provided
  void _initializeForEdit() {
    if (widget.editItem != null) {
      final item = widget.editItem!;
      _typeController.text = item.type;
      _modelController.text = item.model;
      _colorController.text = item.color;
      _brandController.text = item.brand;
      _countryController.text = item.countryOfOrigin;
      _yearController.text = item.yearOfManufacture.toString();
      _purchasePriceController.text = item.purchasePrice.toString();
      _sellingPriceController.text = item.suggestedSellingPrice.toString();
      _motorFingerprintController.text = item.motorFingerprintText;
      _chassisNumberController.text = item.chassisNumber;
      _selectedWarehouseId = item.currentWarehouseId;
    }
  }

  @override
  void dispose() {
    _typeController.dispose();
    _modelController.dispose();
    _colorController.dispose();
    _brandController.dispose();
    _countryController.dispose();
    _yearController.dispose();
    _purchasePriceController.dispose();
    _sellingPriceController.dispose();
    _motorFingerprintController.dispose();
    _chassisNumberController.dispose();
    super.dispose();
  }

  Future<void> _loadWarehouses() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (authProvider.canManageInventory) {
        final warehouses = await _dataService.getWarehouses(isActive: true);
        setState(() {
          _warehouses = warehouses;
          // البحث عن المخزن الرئيسي للمؤسسة
          final mainWarehouse = _warehouses.firstWhere(
            (warehouse) => warehouse.type == 'main' && warehouse.name.contains('الرئيسي'),
            orElse: () => _warehouses.isNotEmpty ? _warehouses.first : WarehouseModel(
              id: '', name: '', address: '', type: 'main',
              isActive: true, createdAt: DateTime.now(), updatedAt: DateTime.now()
            ),
          );
          _selectedWarehouseId = mainWarehouse.id;
        });
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل المخازن: $e', isError: true);
      }
    }
  }



  Future<void> _captureMotorFingerprint() async {
    try {
      setState(() {
        _isProcessingImage = true;
      });

      if (!mounted) return;

      // استخدام الخدمة المحسنة لالتقاط الصورة
      final capturedImage = await _cameraService.captureMotorFingerprintImage();

      if (capturedImage != null && mounted) {
        final image = capturedImage.bytes;

        // Validate image quality
        final isValidQuality = await _imageService.validateImageQuality(image);
        if (!isValidQuality) {
          throw 'جودة الصورة غير مناسبة. يرجى التأكد من الإضاءة الجيدة ووضوح الصورة';
        }

        // استخراج البيانات تلقائياً من الصورة
        if (mounted) {
          AppUtils.showSnackBar(
            context,
            'جاري استخراج البيانات من الصورة...',
          );
        }

        // استخراج البيانات باستخدام الذكاء الاصطناعي
        final extractedData = await _extractionService.extractMotorData(image);

        if (mounted) {
          final shouldUseExtracted = await _showDataExtractionDialog(extractedData);

          if (shouldUseExtracted) {
            // استخدام البيانات المستخرجة
            setState(() {
              _motorFingerprintImage = image;
              _motorFingerprintController.text = extractedData['motorFingerprint'] ?? '';
              _brandController.text = extractedData['brand'] ?? '';
              _modelController.text = extractedData['model'] ?? '';
              _selectedVehicleType = extractedData['vehicleType'] ?? _vehicleTypes.first;
              _colorController.text = extractedData['color'] ?? '';
              _yearController.text = extractedData['year']?.toString() ?? '';
            });

            if (mounted) {
              AppUtils.showSnackBar(
                context,
                'تم استخراج البيانات بنجاح! مستوى الثقة: ${(extractedData['confidence'] * 100).toStringAsFixed(0)}%',
              );
            }
          } else {
            // الإدخال اليدوي
            final motorFingerprint = await _showMotorFingerprintDialog();
            if (motorFingerprint != null && motorFingerprint.isNotEmpty) {
              setState(() {
                _motorFingerprintImage = image;
                _motorFingerprintController.text = motorFingerprint;
              });

              if (mounted) {
                AppUtils.showSnackBar(
                  context,
                  'تم حفظ صورة بصمة الموتور مع الإدخال اليدوي',
                );
              }
            }
          }
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, e.toString(), isError: true);
      }
    } finally {
      setState(() {
        _isProcessingImage = false;
      });
    }
  }

  Future<void> _captureChassisNumber() async {
    try {
      setState(() {
        _isProcessingChassisImage = true;
      });

      if (!mounted) return;

      // استخدام الخدمة المحسنة لالتقاط صورة الشاسيه
      final capturedImage = await _cameraService.captureChassisImage();

      if (capturedImage != null && mounted) {
        final image = capturedImage.bytes;

        // Validate image quality
        final isValidQuality = await _imageService.validateImageQuality(image);
        if (!isValidQuality) {
          throw 'جودة الصورة غير مناسبة. يرجى التأكد من الإضاءة الجيدة ووضوح الصورة';
        }

        // For Windows compatibility, ask user to enter chassis number manually
        // since OCR is not available
        String? chassisNumber;
        if (mounted) {
          chassisNumber = await _showChassisNumberDialog();
        }

        if (chassisNumber != null && chassisNumber.isNotEmpty) {
          setState(() {
            _chassisImage = image;
            _chassisNumberController.text = chassisNumber!;
          });

          if (mounted) {
            AppUtils.showSnackBar(
              context,
              'تم حفظ صورة الشاسيه ورقم الشاسيه بنجاح'
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, e.toString(), isError: true);
      }
    } finally {
      setState(() {
        _isProcessingChassisImage = false;
      });
    }
  }

  Future<String?> _showChassisNumberDialog() async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدخال رقم الشاسيه'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('يرجى إدخال رقم الشاسيه من الصورة:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'رقم الشاسيه',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, controller.text.trim()),
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// عرض نتائج استخراج البيانات للمستخدم
  Future<bool> _showDataExtractionDialog(Map<String, dynamic> extractedData) async {
    final confidence = (extractedData['confidence'] as double? ?? 0.0) * 100;

    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نتائج استخراج البيانات'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'تم استخراج البيانات التالية من الصورة:',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 16),

              _buildDataRow('الماركة:', extractedData['brand'] ?? 'غير محدد'),
              _buildDataRow('الموديل:', extractedData['model'] ?? 'غير محدد'),
              _buildDataRow('نوع المركبة:', extractedData['vehicleType'] ?? 'غير محدد'),
              _buildDataRow('بصمة الموتور:', extractedData['motorFingerprint'] ?? 'غير محدد'),
              _buildDataRow('رقم الشاسيه:', extractedData['chassisNumber'] ?? 'غير محدد'),
              _buildDataRow('سنة الصنع:', extractedData['year']?.toString() ?? 'غير محدد'),
              _buildDataRow('اللون:', extractedData['color'] ?? 'غير محدد'),
              _buildDataRow('سعة المحرك:', extractedData['engineCapacity'] ?? 'غير محدد'),

              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: confidence >= 80 ? Colors.green.withOpacity(0.1) :
                         confidence >= 60 ? Colors.orange.withOpacity(0.1) :
                         Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: confidence >= 80 ? Colors.green :
                           confidence >= 60 ? Colors.orange :
                           Colors.red,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      confidence >= 80 ? Icons.check_circle :
                      confidence >= 60 ? Icons.warning :
                      Icons.error,
                      color: confidence >= 80 ? Colors.green :
                             confidence >= 60 ? Colors.orange :
                             Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'مستوى الثقة: ${confidence.toStringAsFixed(0)}%',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: confidence >= 80 ? Colors.green :
                                 confidence >= 60 ? Colors.orange :
                                 Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),
              const Text(
                'هل تريد استخدام هذه البيانات أم تفضل الإدخال اليدوي؟',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إدخال يدوي'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('استخدام البيانات المستخرجة'),
          ),
        ],
      ),
    ) ?? false;
  }

  Widget _buildDataRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: value == 'غير محدد' ? Colors.grey : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _saveItem() async {
    if (!_formKey.currentState!.validate()) return;

    // For editing, images are optional (can keep existing ones)
    final isEditing = widget.editItem != null;

    if (!isEditing && _motorFingerprintImage == null) {
      AppUtils.showSnackBar(context, 'يرجى اختيار صورة بصمة الموتور', isError: true);
      return;
    }
    if (!isEditing && _chassisImage == null) {
      AppUtils.showSnackBar(context, 'يرجى اختيار صورة رقم الشاسيه', isError: true);
      return;
    }
    if (_selectedWarehouseId == null || _selectedWarehouseId!.isEmpty) {
      AppUtils.showSnackBar(context, 'خطأ في تحديد المخزن الرئيسي', isError: true);
      return;
    }

    // التحقق من وجود الإنترنت قبل البدء
    final isOnline = await _dataService.isOnline();
    if (!isOnline) {
      AppUtils.showSnackBar(
        context,
        'لا يوجد اتصال بالإنترنت. يرجى التأكد من الاتصال قبل حفظ الصنف لضمان عدم فقدان البيانات.',
        isError: true,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Upload images (only if new images are provided)
      String motorImageUrl = isEditing ? widget.editItem!.motorFingerprintImageUrl : '';
      String chassisImageUrl = isEditing ? widget.editItem!.chassisImageUrl : '';

      if (_motorFingerprintImage != null) {
        motorImageUrl = await _imageService.uploadImage(
          _motorFingerprintImage!,
          'motor_fingerprint_${DateTime.now().millisecondsSinceEpoch}.jpg',
        ) ?? '';
      }

      if (_chassisImage != null) {
        chassisImageUrl = await _imageService.uploadImage(
          _chassisImage!,
          'chassis_image_${DateTime.now().millisecondsSinceEpoch}.jpg',
        ) ?? '';
      }

      final now = DateTime.now();

      if (isEditing) {
        // Update existing item
        final updatedItem = widget.editItem!.copyWith(
          type: _typeController.text.trim(),
          model: _modelController.text.trim(),
          color: _colorController.text.trim(),
          brand: _brandController.text.trim(),
          countryOfOrigin: _countryController.text.trim(),
          yearOfManufacture: int.parse(_yearController.text.trim()),
          purchasePrice: double.parse(_purchasePriceController.text.trim()),
          suggestedSellingPrice: double.parse(_sellingPriceController.text.trim()),
          motorFingerprintImageUrl: motorImageUrl,
          motorFingerprintText: _motorFingerprintController.text.trim(),
          chassisImageUrl: chassisImageUrl,
          chassisNumber: _chassisNumberController.text.trim(),
          currentWarehouseId: _selectedWarehouseId!,
          updatedAt: now,
        );

        await _dataService.updateItem(updatedItem);

        if (mounted) {
          AppUtils.showSnackBar(context, 'تم تحديث الصنف بنجاح');
          Navigator.of(context).pop(true);
        }
      } else {
        // Create new item with unique ID
        // Format: timestamp_motorFingerprintHash to ensure uniqueness
        final uniqueId = '${DateTime.now().millisecondsSinceEpoch}_${_motorFingerprintController.text.trim().hashCode.abs()}';
        final item = ItemModel(
          id: uniqueId,
          type: _typeController.text.trim(),
          model: _modelController.text.trim(),
          color: _colorController.text.trim(),
          brand: _brandController.text.trim(),
          countryOfOrigin: _countryController.text.trim(),
          yearOfManufacture: int.parse(_yearController.text.trim()),
          purchasePrice: double.parse(_purchasePriceController.text.trim()),
          suggestedSellingPrice: double.parse(_sellingPriceController.text.trim()),
          motorFingerprintImageUrl: motorImageUrl,
          motorFingerprintText: _motorFingerprintController.text.trim(),
          chassisImageUrl: chassisImageUrl,
          chassisNumber: _chassisNumberController.text.trim(),
          currentWarehouseId: _selectedWarehouseId!,
          status: 'متاح',
          createdAt: now,
          updatedAt: now,
          createdBy: authProvider.currentUser!.id,
        );

        await _dataService.createItem(item);

        if (mounted) {
          AppUtils.showSnackBar(context, 'تم إضافة الصنف بنجاح');
          Navigator.of(context).pop(true);
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          isEditing ? 'خطأ في تحديث الصنف: $e' : 'خطأ في إضافة الصنف: $e',
          isError: true
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.editItem != null ? 'تعديل الصنف' : 'إضافة صنف جديد'),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildMotorFingerprintSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildChassisSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildBasicInfoSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildPricingSection(),
              const SizedBox(height: AppConstants.largePadding),
              _buildWarehouseSection(),
              const SizedBox(height: AppConstants.largePadding * 2),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMotorFingerprintSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'بصمة الموتور',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Image capture button
            SizedBox(
              width: double.infinity,
              height: 200,
              child: _motorFingerprintImage != null
                  ? Stack(
                      children: [
                        Container(
                          width: double.infinity,
                          height: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                            image: DecorationImage(
                              image: MemoryImage(_motorFingerprintImage!),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                          top: 8,
                          right: 8,
                          child: IconButton(
                            onPressed: _isProcessingImage ? null : _captureMotorFingerprint,
                            icon: const Icon(Icons.folder_open),
                            style: IconButton.styleFrom(
                              backgroundColor: Colors.black54,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    )
                  : InkWell(
                      onTap: _isProcessingImage ? null : _captureMotorFingerprint,
                      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Theme.of(context).colorScheme.outline,
                            style: BorderStyle.solid,
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (_isProcessingImage)
                              const CircularProgressIndicator()
                            else
                              Icon(
                                Icons.folder_open,
                                size: 48,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            const SizedBox(height: AppConstants.smallPadding),
                            Text(
                              _isProcessingImage
                                  ? 'جاري معالجة الصورة...'
                                  : 'اضغط لاختيار صورة بصمة الموتور',
                              style: Theme.of(context).textTheme.bodyLarge,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Extracted text field
            TextFormField(
              controller: _motorFingerprintController,
              decoration: const InputDecoration(
                labelText: 'النص المستخرج من بصمة الموتور',
                hintText: 'سيتم استخراج النص تلقائياً من الصورة',
                helperText: 'يمكنك تعديل النص المستخرج إذا لزم الأمر',
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'بصمة الموتور مطلوبة';
                }
                if (value.trim().length < 5) {
                  return 'بصمة الموتور يجب أن تكون 5 أحرف على الأقل';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChassisSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'رقم الشاسيه',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            // Camera capture section
            GestureDetector(
              onTap: _isProcessingChassisImage ? null : _captureChassisNumber,
              child: Container(
                width: double.infinity,
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _chassisImage != null ? Colors.green : Colors.grey,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[100],
                ),
                child: _chassisImage != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: Image.memory(
                          _chassisImage!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _isProcessingChassisImage
                                ? Icons.hourglass_empty
                                : Icons.folder_open,
                            size: 48,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(height: AppConstants.smallPadding),
                          Text(
                            _isProcessingChassisImage
                                ? 'جاري معالجة الصورة...'
                                : 'اضغط لاختيار صورة رقم الشاسيه',
                            style: Theme.of(context).textTheme.bodyLarge,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
              ),
            ),

            const SizedBox(height: AppConstants.defaultPadding),

            // Extracted text field
            TextFormField(
              controller: _chassisNumberController,
              decoration: const InputDecoration(
                labelText: 'النص المستخرج من رقم الشاسيه',
                hintText: 'سيتم استخراج النص تلقائياً من الصورة',
                helperText: 'يمكنك تعديل النص المستخرج إذا لزم الأمر',
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'رقم الشاسيه مطلوب';
                }
                if (value.trim().length < 5) {
                  return 'رقم الشاسيه يجب أن يكون 5 أحرف على الأقل';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الأساسية',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Vehicle type dropdown
            DropdownButtonFormField<String>(
              value: _typeController.text.isEmpty ? null : _typeController.text,
              decoration: const InputDecoration(
                labelText: 'نوع المركبة',
              ),
              items: _vehicleTypes.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _typeController.text = value ?? '';
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'نوع المركبة مطلوب';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Brand field
            TextFormField(
              controller: _brandController,
              decoration: const InputDecoration(
                labelText: 'الماركة',
                hintText: 'مثال: هوندا، ياماها، سوزوكي',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'الماركة مطلوبة';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Model field
            TextFormField(
              controller: _modelController,
              decoration: const InputDecoration(
                labelText: 'الموديل',
                hintText: 'مثال: CBR 150، YBR 125',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'الموديل مطلوب';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Color field
            TextFormField(
              controller: _colorController,
              decoration: const InputDecoration(
                labelText: 'اللون',
                hintText: 'مثال: أحمر، أزرق، أسود',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اللون مطلوب';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                // Country field
                Expanded(
                  child: TextFormField(
                    controller: _countryController,
                    decoration: const InputDecoration(
                      labelText: 'بلد المنشأ',
                      hintText: 'مثال: اليابان، الصين',
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'بلد المنشأ مطلوب';
                      }
                      return null;
                    },
                  ),
                ),
                
                const SizedBox(width: AppConstants.defaultPadding),
                
                // Year field
                Expanded(
                  child: TextFormField(
                    controller: _yearController,
                    decoration: const InputDecoration(
                      labelText: 'سنة الصنع',
                      hintText: '2024',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'سنة الصنع مطلوبة';
                      }
                      final year = int.tryParse(value.trim());
                      if (year == null) {
                        return 'سنة غير صحيحة';
                      }
                      final currentYear = DateTime.now().year;
                      if (year < 1990 || year > currentYear + 1) {
                        return 'سنة غير صحيحة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPricingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأسعار',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            Row(
              children: [
                // Purchase price field
                Expanded(
                  child: TextFormField(
                    controller: _purchasePriceController,
                    decoration: const InputDecoration(
                      labelText: 'سعر الشراء (ج.م)',
                      hintText: '50000',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'سعر الشراء مطلوب';
                      }
                      final price = double.tryParse(value.trim());
                      if (price == null || price <= 0) {
                        return 'سعر غير صحيح';
                      }
                      return null;
                    },
                  ),
                ),
                
                const SizedBox(width: AppConstants.defaultPadding),
                
                // Selling price field
                Expanded(
                  child: TextFormField(
                    controller: _sellingPriceController,
                    decoration: const InputDecoration(
                      labelText: 'سعر البيع المقترح (ج.م)',
                      hintText: '55000',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'سعر البيع مطلوب';
                      }
                      final sellingPrice = double.tryParse(value.trim());
                      if (sellingPrice == null || sellingPrice <= 0) {
                        return 'سعر غير صحيح';
                      }
                      final purchasePrice = double.tryParse(_purchasePriceController.text.trim());
                      if (purchasePrice != null && sellingPrice <= purchasePrice) {
                        return 'سعر البيع يجب أن يكون أكبر من سعر الشراء';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarehouseSection() {
    final selectedWarehouse = _warehouses.firstWhere(
      (warehouse) => warehouse.id == _selectedWarehouseId,
      orElse: () => WarehouseModel(
        id: '', name: 'المخزن الرئيسي للمؤسسة', address: '', type: 'main',
        isActive: true, createdAt: DateTime.now(), updatedAt: DateTime.now()
      ),
    );

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المخزن المحدد',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),

            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary,
                  width: 2,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warehouse,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          selectedWarehouse.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'سيتم إضافة الصنف تلقائياً للمخزن الرئيسي',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.check_circle,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveItem,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                widget.editItem != null ? 'تحديث الصنف' : 'حفظ الصنف',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Future<String?> _showMotorFingerprintDialog() async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إدخال بصمة الموتور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('يرجى إدخال بصمة الموتور يدوياً:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'بصمة الموتور',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.trim().isNotEmpty) {
                Navigator.pop(context, controller.text.trim());
              }
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
