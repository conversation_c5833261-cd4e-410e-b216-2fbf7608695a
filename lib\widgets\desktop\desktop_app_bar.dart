import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../core/theme/desktop_theme.dart';

/// Professional desktop-style app bar similar to Microsoft Office ribbon
class DesktopAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final List<Widget>? actions;
  final List<DesktopToolbarGroup>? toolbarGroups;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final double? elevation;

  const DesktopAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.actions,
    this.toolbarGroups,
    this.showBackButton = false,
    this.onBackPressed,
    this.backgroundColor,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? DesktopTheme.backgroundSecondary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Title Bar
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(horizontal: DesktopTheme.spacingMedium),
            child: Row(
              children: [
                // Leading/Back Button
                if (showBackButton || leading != null) ...[
                  leading ??
                      IconButton(
                        icon: const Icon(Icons.arrow_back),
                        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                        tooltip: 'العودة',
                        style: IconButton.styleFrom(
                          foregroundColor: DesktopTheme.textPrimary,
                        ),
                      ),
                  const SizedBox(width: DesktopTheme.spacingMedium),
                ],
                
                // Title and Subtitle
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: DesktopTheme.headingSmall,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          subtitle!,
                          style: DesktopTheme.bodySmall,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                
                // Actions
                if (actions != null) ...[
                  const SizedBox(width: DesktopTheme.spacingMedium),
                  Row(children: actions!),
                ],
              ],
            ),
          ),
          
          // Toolbar Groups (Ribbon-style)
          if (toolbarGroups != null && toolbarGroups!.isNotEmpty)
            Container(
              height: 80,
              decoration: const BoxDecoration(
                color: DesktopTheme.backgroundTertiary,
                border: Border(
                  top: BorderSide(
                    color: DesktopTheme.borderLight,
                    width: 1,
                  ),
                ),
              ),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: DesktopTheme.spacingMedium),
                child: Row(
                  children: toolbarGroups!
                      .map((group) => _buildToolbarGroup(group))
                      .expand((widget) => [
                            widget,
                            Container(
                              width: 1,
                              height: 60,
                              margin: const EdgeInsets.symmetric(horizontal: DesktopTheme.spacingMedium),
                              color: DesktopTheme.borderLight,
                            ),
                          ])
                      .take(toolbarGroups!.length * 2 - 1)
                      .toList(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildToolbarGroup(DesktopToolbarGroup group) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesktopTheme.spacingMedium,
        vertical: DesktopTheme.spacingSmall,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Tools
          Expanded(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: group.tools.map((tool) => _buildToolbarItem(tool)).toList(),
            ),
          ),
          
          // Group Label
          const SizedBox(height: DesktopTheme.spacingXSmall),
          Text(
            group.label,
            style: DesktopTheme.labelSmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarItem(DesktopToolbarItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: DesktopTheme.spacingXSmall),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: item.onPressed,
              borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall),
              child: Container(
                width: 48,
                height: 32,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall),
                  border: item.isSelected
                      ? Border.all(color: DesktopTheme.primaryBlue, width: 2)
                      : null,
                  color: item.isSelected
                      ? DesktopTheme.primaryBlue.withOpacity(0.1)
                      : null,
                ),
                child: Icon(
                  item.icon,
                  size: 20,
                  color: item.isSelected
                      ? DesktopTheme.primaryBlue
                      : DesktopTheme.textSecondary,
                ),
              ),
            ),
          ),
          const SizedBox(height: 1),
          Text(
            item.label,
            style: DesktopTheme.labelSmall.copyWith(
              color: item.isSelected
                  ? DesktopTheme.primaryBlue
                  : DesktopTheme.textTertiary,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize {
    double height = 60; // Title bar height
    if (toolbarGroups != null && toolbarGroups!.isNotEmpty) {
      height += 80; // Toolbar height
    }
    return Size.fromHeight(height);
  }
}

/// Represents a group of tools in the toolbar ribbon
class DesktopToolbarGroup {
  final String label;
  final List<DesktopToolbarItem> tools;

  const DesktopToolbarGroup({
    required this.label,
    required this.tools,
  });
}

/// Represents a single tool item in the toolbar
class DesktopToolbarItem {
  final String label;
  final IconData icon;
  final VoidCallback? onPressed;
  final bool isSelected;
  final String? tooltip;
  final List<LogicalKeyboardKey>? shortcutKeys;

  const DesktopToolbarItem({
    required this.label,
    required this.icon,
    this.onPressed,
    this.isSelected = false,
    this.tooltip,
    this.shortcutKeys,
  });
}
