import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import '../../models/invoice_model.dart';
import '../../models/item_model.dart';
import '../../services/data_service.dart';
import '../../services/windows/windows_image_service.dart';
import '../../core/utils/app_utils.dart';

// Type alias for the image service
typedef WindowsImagePickerService = WindowsImageService;

class ComprehensiveCustomerInquiryScreen extends StatefulWidget {
  const ComprehensiveCustomerInquiryScreen({super.key});

  @override
  State<ComprehensiveCustomerInquiryScreen> createState() => _ComprehensiveCustomerInquiryScreenState();
}

class _ComprehensiveCustomerInquiryScreenState extends State<ComprehensiveCustomerInquiryScreen> {
  final DataService _dataService = DataService.instance;
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _customerNameController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _phoneController = TextEditingController();
  final _invoiceNumberController = TextEditingController();

  // Date range
  DateTime? _startDate;
  DateTime? _endDate;

  // Search results
  List<InvoiceModel> _searchResults = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Selected invoice details
  InvoiceModel? _selectedInvoice;
  ItemModel? _selectedItem;

  @override
  void dispose() {
    _customerNameController.dispose();
    _nationalIdController.dispose();
    _phoneController.dispose();
    _invoiceNumberController.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check if at least one search criteria is provided
    if (_customerNameController.text.trim().isEmpty &&
        _nationalIdController.text.trim().isEmpty &&
        _phoneController.text.trim().isEmpty &&
        _invoiceNumberController.text.trim().isEmpty &&
        _startDate == null &&
        _endDate == null) {
      setState(() {
        _errorMessage = 'يرجى إدخال معيار بحث واحد على الأقل';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _searchResults.clear();
    });

    try {
      final allInvoices = await _dataService.getInvoices();

      List<InvoiceModel> filteredInvoices = allInvoices.where((invoice) {
        // Filter by customer name
        if (_customerNameController.text.trim().isNotEmpty) {
          final customerData = invoice.customerData;
          if (customerData != null) {
            final customerName = customerData['fullName'] ??
                               customerData['customerName'] ??
                               customerData['name'] ?? '';
            if (!customerName.toLowerCase().contains(_customerNameController.text.trim().toLowerCase())) {
              return false;
            }
          } else {
            return false;
          }
        }

        // Filter by national ID
        if (_nationalIdController.text.trim().isNotEmpty) {
          final customerData = invoice.customerData;
          if (customerData != null) {
            final nationalId = customerData['nationalId'] ?? '';
            if (!nationalId.contains(_nationalIdController.text.trim())) {
              return false;
            }
          } else {
            return false;
          }
        }

        // Filter by phone
        if (_phoneController.text.trim().isNotEmpty) {
          final customerData = invoice.customerData;
          if (customerData != null) {
            final phone = customerData['phone'] ?? '';
            if (!phone.contains(_phoneController.text.trim())) {
              return false;
            }
          } else {
            return false;
          }
        }

        // Filter by invoice number
        if (_invoiceNumberController.text.trim().isNotEmpty) {
          if (!invoice.invoiceNumber.toLowerCase().contains(_invoiceNumberController.text.trim().toLowerCase())) {
            return false;
          }
        }

        // Filter by date range
        if (_startDate != null) {
          if (invoice.createdAt.isBefore(_startDate!)) {
            return false;
          }
        }

        if (_endDate != null) {
          final endOfDay = DateTime(_endDate!.year, _endDate!.month, _endDate!.day, 23, 59, 59);
          if (invoice.createdAt.isAfter(endOfDay)) {
            return false;
          }
        }

        return true;
      }).toList();

      // Sort by creation date (newest first)
      filteredInvoices.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _searchResults = filteredInvoices;
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _errorMessage = 'خطأ في البحث: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('استعلام العملاء الشامل'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Search form
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'معايير البحث',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Customer name field
                        TextFormField(
                          controller: _customerNameController,
                          decoration: const InputDecoration(
                            labelText: 'اسم العميل',
                            hintText: 'ادخل اسم العميل',
                            prefixIcon: Icon(Icons.person),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // National ID field
                        TextFormField(
                          controller: _nationalIdController,
                          decoration: const InputDecoration(
                            labelText: 'رقم الهوية',
                            hintText: 'ادخل رقم الهوية',
                            prefixIcon: Icon(Icons.credit_card),
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                        const SizedBox(height: 16),

                        // Phone field
                        TextFormField(
                          controller: _phoneController,
                          decoration: const InputDecoration(
                            labelText: 'رقم الهاتف',
                            hintText: 'ادخل رقم الهاتف',
                            prefixIcon: Icon(Icons.phone),
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.phone,
                        ),
                        const SizedBox(height: 16),

                        // Invoice number field
                        TextFormField(
                          controller: _invoiceNumberController,
                          decoration: const InputDecoration(
                            labelText: 'رقم الفاتورة',
                            hintText: 'ادخل رقم الفاتورة',
                            prefixIcon: Icon(Icons.receipt),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Date range
                        Row(
                          children: [
                            Expanded(
                              child: InkWell(
                                onTap: () async {
                                  final date = await showDatePicker(
                                    context: context,
                                    initialDate: _startDate ?? DateTime.now(),
                                    firstDate: DateTime(2020),
                                    lastDate: DateTime.now(),
                                  );
                                  if (date != null) {
                                    setState(() {
                                      _startDate = date;
                                    });
                                  }
                                },
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'من تاريخ',
                                    prefixIcon: Icon(Icons.calendar_today),
                                    border: OutlineInputBorder(),
                                  ),
                                  child: Text(
                                    _startDate != null
                                        ? DateFormat('yyyy/MM/dd').format(_startDate!)
                                        : 'اختر التاريخ',
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: InkWell(
                                onTap: () async {
                                  final date = await showDatePicker(
                                    context: context,
                                    initialDate: _endDate ?? DateTime.now(),
                                    firstDate: DateTime(2020),
                                    lastDate: DateTime.now(),
                                  );
                                  if (date != null) {
                                    setState(() {
                                      _endDate = date;
                                    });
                                  }
                                },
                                child: InputDecorator(
                                  decoration: const InputDecoration(
                                    labelText: 'إلى تاريخ',
                                    prefixIcon: Icon(Icons.calendar_today),
                                    border: OutlineInputBorder(),
                                  ),
                                  child: Text(
                                    _endDate != null
                                        ? DateFormat('yyyy/MM/dd').format(_endDate!)
                                        : 'اختر التاريخ',
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Search button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _performSearch,
                            icon: _isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Icon(Icons.search),
                            label: Text(_isLoading ? 'جاري البحث...' : 'بحث'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[700],
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Error message
              if (_errorMessage != null)
                Card(
                  color: Colors.red[50],
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Icon(Icons.error, color: Colors.red[700]),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style: TextStyle(color: Colors.red[700]),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Search results
              if (_searchResults.isNotEmpty)
                Card(
                  child: Column(
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16.0),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(8),
                            topRight: Radius.circular(8),
                          ),
                        ),
                        child: Text(
                          'نتائج البحث (${_searchResults.length} فاتورة)',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 400, // Fixed height to prevent overflow
                        child: ListView.builder(
                          itemCount: _searchResults.length,
                          itemBuilder: (context, index) {
                            final invoice = _searchResults[index];
                            return _InvoiceListTile(
                              invoice: invoice,
                              onTap: () => _showInvoiceDetails(invoice),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // عرض تفاصيل الفاتورة مع الصور
  Future<void> _showInvoiceDetails(InvoiceModel invoice) async {
    try {
      // تحميل تفاصيل الصنف
      final item = await _dataService.getItemById(invoice.itemId);

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => Dialog(
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.9,
            child: DefaultTabController(
              length: 3,
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue[700],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.receipt, color: Colors.white),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'تفاصيل الفاتورة ${invoice.invoiceNumber}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close, color: Colors.white),
                        ),
                      ],
                    ),
                  ),

                  // Tabs
                  const TabBar(
                    labelColor: Colors.blue,
                    unselectedLabelColor: Colors.grey,
                    tabs: [
                      Tab(icon: Icon(Icons.info), text: 'معلومات الفاتورة'),
                      Tab(icon: Icon(Icons.motorcycle), text: 'تفاصيل المركبة'),
                      Tab(icon: Icon(Icons.image), text: 'الصور والوثائق'),
                    ],
                  ),

                  // Tab content
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildInvoiceInfoTab(invoice),
                        _buildVehicleDetailsTab(item),
                        _buildImagesTab(invoice, item),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل تفاصيل الفاتورة: $e', isError: true);
      }
    }
  }

  // تبويبة معلومات الفاتورة
  Widget _buildInvoiceInfoTab(InvoiceModel invoice) {
    final customerData = invoice.customerData ?? {};

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الفاتورة
          _buildInfoSection(
            'معلومات الفاتورة',
            [
              _buildInfoRow('رقم الفاتورة', invoice.invoiceNumber),
              _buildInfoRow('التاريخ', _formatDate(invoice.createdAt)),
              _buildInfoRow('المبلغ الإجمالي', '${invoice.sellingPrice.toStringAsFixed(2)} ج.م'),
              _buildInfoRow('حالة الدفع', invoice.status),
              _buildInfoRow('طريقة الدفع', 'نقدي'),
            ],
          ),

          const SizedBox(height: 16),

          // معلومات العميل
          _buildInfoSection(
            'معلومات العميل',
            [
              _buildInfoRow('الاسم الكامل', customerData['fullName'] ?? 'غير محدد'),
              _buildInfoRow('رقم الهوية', customerData['nationalId'] ?? 'غير محدد'),
              _buildInfoRow('رقم الهاتف', customerData['phone'] ?? 'غير محدد'),
              _buildInfoRow('العنوان', customerData['address'] ?? 'غير محدد'),
              _buildInfoRow('المهنة', customerData['profession'] ?? 'غير محدد'),
            ],
          ),

          const SizedBox(height: 16),

          // معلومات البائع
          _buildInfoSection(
            'معلومات البائع',
            [
              _buildInfoRow('اسم البائع', invoice.createdBy),
              _buildInfoRow('نوع البائع', invoice.type == 'agent' ? 'وكيل' : 'مباشر'),
            ],
          ),
        ],
      ),
    );
  }

  // تبويبة تفاصيل المركبة
  Widget _buildVehicleDetailsTab(ItemModel? item) {
    if (item == null) {
      return const Center(
        child: Text('لا توجد تفاصيل للمركبة'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection(
            'تفاصيل المركبة',
            [
              _buildInfoRow('النوع', item.type),
              _buildInfoRow('الماركة', item.brand),
              _buildInfoRow('الموديل', item.model),
              _buildInfoRow('اللون', item.color),
              _buildInfoRow('رقم الشاسية', item.chassisNumber),
              _buildInfoRow('بصمة الموتور', item.motorFingerprintText),
              _buildInfoRow('سعر الشراء', '${item.purchasePrice.toStringAsFixed(2)} ج.م'),
              _buildInfoRow('سعر البيع', '${item.suggestedSellingPrice.toStringAsFixed(2)} ج.م'),
              _buildInfoRow('الحالة', item.status),
              _buildInfoRow('المخزن الحالي', item.currentWarehouseId),
            ],
          ),
        ],
      ),
    );
  }

  // تبويبة الصور والوثائق
  Widget _buildImagesTab(InvoiceModel invoice, ItemModel? item) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الصور والوثائق',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),

          // صور المركبة
          if (item != null) ...[
            _buildImageSection(
              'صورة بصمة الموتور',
              item.motorFingerprintImageUrl,
              'motor_fingerprint_${item.id}',
            ),
            const SizedBox(height: 16),
            _buildImageSection(
              'صورة رقم الشاسية',
              item.chassisImageUrl,
              'chassis_${item.id}',
            ),
            const SizedBox(height: 16),
          ],

          // صور بطاقة العميل
          if (invoice.customerData != null) ...[
            _buildImageSection(
              'صورة بطاقة الهوية - الوجه',
              _getCustomerIdImageUrl(invoice, 'front'),
              'id_front_${invoice.id}',
            ),
            const SizedBox(height: 16),
            _buildImageSection(
              'صورة بطاقة الهوية - الظهر',
              _getCustomerIdImageUrl(invoice, 'back'),
              'id_back_${invoice.id}',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection(String title, String? imageUrl, String fileName) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                ),
                if (imageUrl != null && imageUrl.isNotEmpty)
                  ElevatedButton.icon(
                    onPressed: () => _downloadImage(imageUrl, fileName, context),
                    icon: const Icon(Icons.download, size: 16),
                    label: const Text('تحميل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            if (imageUrl != null && imageUrl.isNotEmpty)
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.contain,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return const Center(child: CircularProgressIndicator());
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error, color: Colors.red),
                            Text('خطأ في تحميل الصورة'),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              )
            else
              Container(
                height: 100,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.image_not_supported, color: Colors.grey),
                      Text('لا توجد صورة متاحة'),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadImage(String imageUrl, String fileName, BuildContext context) async {
    try {
      final windowsImageService = WindowsImagePickerService.instance;
      await windowsImageService.downloadImageFromUrl(imageUrl, fileName);

      AppUtils.showSnackBar(context, 'تم تحميل الصورة بنجاح');
    } catch (e) {
      AppUtils.showSnackBar(context, 'خطأ في تحميل الصورة: $e', isError: true);
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(date);
  }

  // الحصول على رابط صورة بطاقة العميل
  String? _getCustomerIdImageUrl(InvoiceModel invoice, String side) {
    if (kDebugMode) {
      print('🔍 البحث عن صورة بطاقة العميل - $side');
      print('   Invoice ID: ${invoice.id}');
      print('   Customer Data: ${invoice.customerData}');
      print('   Customer ID Images: ${invoice.customerIdImages}');
      print('   Additional Data: ${invoice.additionalData}');
    }

    // البحث في customerData أولاً
    if (invoice.customerData != null) {
      final customerData = invoice.customerData!;

      String? imageUrl;
      if (side == 'front') {
        imageUrl = customerData['idCardFrontUrl'] ??
                   customerData['nationalIdFrontUrl'] ??
                   customerData['frontIdUrl'] ??
                   customerData['idFrontUrl'] ??
                   customerData['customerIdFrontUrl'];
      } else {
        imageUrl = customerData['idCardBackUrl'] ??
                   customerData['nationalIdBackUrl'] ??
                   customerData['backIdUrl'] ??
                   customerData['idBackUrl'] ??
                   customerData['customerIdBackUrl'];
      }

      if (imageUrl != null && imageUrl.isNotEmpty) {
        if (kDebugMode) {
          print('   ✅ وجدت في customerData: $imageUrl');
        }
        return imageUrl;
      }
    }

    // البحث في customerIdImages
    if (invoice.customerIdImages != null && invoice.customerIdImages!.isNotEmpty) {
      if (side == 'front' && invoice.customerIdImages!.isNotEmpty) {
        final imageUrl = invoice.customerIdImages![0];
        if (kDebugMode) {
          print('   ✅ وجدت في customerIdImages[0]: $imageUrl');
        }
        return imageUrl;
      } else if (side == 'back' && invoice.customerIdImages!.length > 1) {
        final imageUrl = invoice.customerIdImages![1];
        if (kDebugMode) {
          print('   ✅ وجدت في customerIdImages[1]: $imageUrl');
        }
        return imageUrl;
      }
    }

    // البحث في additionalData
    if (invoice.additionalData != null) {
      final additionalData = invoice.additionalData!;
      String? imageUrl;
      if (side == 'front') {
        imageUrl = additionalData['customerIdFrontUrl'] ??
                   additionalData['idCardFrontUrl'] ??
                   additionalData['frontIdUrl'] ??
                   additionalData['idFrontUrl'];
      } else {
        imageUrl = additionalData['customerIdBackUrl'] ??
                   additionalData['idCardBackUrl'] ??
                   additionalData['backIdUrl'] ??
                   additionalData['idBackUrl'];
      }

      if (imageUrl != null && imageUrl.isNotEmpty) {
        if (kDebugMode) {
          print('   ✅ وجدت في additionalData: $imageUrl');
        }
        return imageUrl;
      }
    }

    if (kDebugMode) {
      print('   ❌ لم توجد صورة بطاقة العميل - $side');
    }
    return null;
  }
}

class _InvoiceListTile extends StatelessWidget {
  final InvoiceModel invoice;
  final VoidCallback onTap;

  const _InvoiceListTile({
    required this.invoice,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final customerData = invoice.customerData;
    final customerName = customerData != null
        ? (customerData['fullName'] ?? customerData['customerName'] ?? customerData['name'] ?? 'غير محدد')
        : 'غير محدد';

    return ListTile(
      title: Text('فاتورة ${invoice.invoiceNumber}'),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('العميل: $customerName'),
          Text('التاريخ: ${DateFormat('yyyy/MM/dd').format(invoice.createdAt)}'),
          Text('المبلغ: ${invoice.sellingPrice} جنيه'),
        ],
      ),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: onTap,
    );
  }





  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(date);
  }
}