import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for app notifications
class NotificationModel {
  final String id;
  final String title;
  final String message;
  final String type; // 'document_status', 'payment', 'transfer', 'general'
  final String? targetUserId; // Specific user or null for all
  final String? targetRole; // Specific role or null for all
  final String? relatedId; // Related document/invoice/item ID
  final Map<String, dynamic>? data; // Additional data
  final bool isRead;
  final DateTime createdAt;
  final String createdBy;
  final DateTime? readAt;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    this.targetUserId,
    this.targetRole,
    this.relatedId,
    this.data,
    this.isRead = false,
    required this.createdAt,
    required this.createdBy,
    this.readAt,
  });

  // Factory constructor from Firestore document
  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationModel(
      id: doc.id,
      title: data['title'] ?? '',
      message: data['message'] ?? '',
      type: data['type'] ?? '',
      targetUserId: data['targetUserId'],
      targetRole: data['targetRole'],
      relatedId: data['relatedId'],
      data: data['data'] != null 
          ? Map<String, dynamic>.from(data['data']) 
          : null,
      isRead: _parseBoolValue(data['isRead']),
      createdAt: _parseDateTime(data['createdAt']),
      createdBy: data['createdBy'] ?? '',
      readAt: data['readAt'] != null
          ? _parseDateTime(data['readAt'])
          : null,
    );
  }

  // Helper method to safely parse boolean values
  static bool _parseBoolValue(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is int) return value == 1;
    if (value is String) {
      final lower = value.toLowerCase();
      return lower == 'true' || lower == '1';
    }
    return false;
  }

  // Helper method to safely parse DateTime values
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();

    // Handle Timestamp from Firestore
    if (value is Timestamp) {
      try {
        return value.toDate();
      } catch (e) {
        return DateTime.now();
      }
    }

    // Handle String dates
    if (value is String) {
      try {
        // Try ISO 8601 format first
        return DateTime.parse(value);
      } catch (e) {
        try {
          // Try milliseconds since epoch
          final millis = int.tryParse(value);
          if (millis != null) {
            return DateTime.fromMillisecondsSinceEpoch(millis);
          }
        } catch (e2) {
          // Ignore and return default
        }
        return DateTime.now();
      }
    }

    // Handle int (milliseconds since epoch)
    if (value is int) {
      try {
        return DateTime.fromMillisecondsSinceEpoch(value);
      } catch (e) {
        return DateTime.now();
      }
    }

    return DateTime.now();
  }

  // Factory constructor from Map (for local database)
  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      type: map['type'] ?? '',
      targetUserId: map['targetUserId'],
      targetRole: map['targetRole'],
      relatedId: map['relatedId'],
      data: _parseData(map['data']),
      isRead: _parseBoolValue(map['isRead']),
      createdAt: DateTime.parse(map['createdAt']),
      createdBy: map['createdBy'] ?? '',
      readAt: map['readAt'] != null 
          ? DateTime.parse(map['readAt']) 
          : null,
    );
  }

  // Convert to Map (for local database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': targetUserId ?? '', // Use targetUserId as userId for database compatibility
      'title': title,
      'message': message,
      'type': type,
      'targetUserId': targetUserId,
      'targetRole': targetRole,
      'relatedId': relatedId,
      'data': data,
      'isRead': isRead ? 1 : 0,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'readAt': readAt?.toIso8601String(),
    };
  }

  // Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'message': message,
      'type': type,
      'targetUserId': targetUserId,
      'targetRole': targetRole,
      'relatedId': relatedId,
      'data': data,
      'isRead': isRead,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
    };
  }

  // Copy with method for updates
  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    String? targetUserId,
    String? targetRole,
    String? relatedId,
    Map<String, dynamic>? data,
    bool? isRead,
    DateTime? createdAt,
    String? createdBy,
    DateTime? readAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      targetUserId: targetUserId ?? this.targetUserId,
      targetRole: targetRole ?? this.targetRole,
      relatedId: relatedId ?? this.relatedId,
      data: data ?? this.data,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      readAt: readAt ?? this.readAt,
    );
  }

  // Helper methods
  bool get isDocumentStatus => type == 'document_status';
  bool get isPayment => type == 'payment';
  bool get isTransfer => type == 'transfer';
  bool get isGeneral => type == 'general';

  String get displayType {
    switch (type) {
      case 'document_status':
        return 'تحديث حالة الجواب';
      case 'payment':
        return 'دفعة';
      case 'transfer':
        return 'تحويل';
      case 'general':
        return 'عام';
      default:
        return type;
    }
  }

  // Helper method to parse data from different formats
  static Map<String, dynamic>? _parseData(dynamic dataValue) {
    if (dataValue == null) return null;
    
    try {
      if (dataValue is String) {
        return null; // Skip JSON parsing for now
      } else if (dataValue is Map) {
        return Map<String, dynamic>.from(dataValue);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, type: $type, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper method to format creation time
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }
}

/// Service for managing notifications
class NotificationService {
  // static const String _notificationsCollection = 'notifications'; // Commented out as not used

  /// Create document status notification
  static NotificationModel createDocumentStatusNotification({
    required String documentId,
    required String itemName,
    required String oldStatus,
    required String newStatus,
    required String agentId,
    required String updatedBy,
  }) {
    final statusMessages = {
      'sent_to_manufacturer': 'تم إرسال بيانات البصمة للشركة المصنعة',
      'received_from_manufacturer': 'تم استلام الجواب من الشركة المصنعة',
      'sent_to_sale_point': 'تم إرسال الجواب لنقطة البيع',
      'ready_for_pickup': 'الجواب جاهز للاستلام',
    };

    return NotificationModel(
      id: '${DateTime.now().millisecondsSinceEpoch}_doc_status',
      title: 'تحديث حالة الجواب',
      message: 'تم تحديث حالة جواب $itemName إلى: ${statusMessages[newStatus] ?? newStatus}',
      type: 'document_status',
      targetUserId: agentId,
      relatedId: documentId,
      data: {
        'documentId': documentId,
        'itemName': itemName,
        'oldStatus': oldStatus,
        'newStatus': newStatus,
      },
      createdAt: DateTime.now(),
      createdBy: updatedBy,
    );
  }

  /// Create payment notification
  static NotificationModel createPaymentNotification({
    required String agentId,
    required String agentName,
    required double amount,
    required String paymentType,
    required String createdBy,
  }) {
    return NotificationModel(
      id: '${DateTime.now().millisecondsSinceEpoch}_payment',
      title: 'تسجيل دفعة جديدة',
      message: 'تم تسجيل $paymentType بمبلغ ${amount.toStringAsFixed(2)} جنيه للوكيل $agentName',
      type: 'payment',
      targetUserId: agentId,
      data: {
        'agentId': agentId,
        'agentName': agentName,
        'amount': amount,
        'paymentType': paymentType,
      },
      createdAt: DateTime.now(),
      createdBy: createdBy,
    );
  }

  /// Create transfer notification
  static NotificationModel createTransferNotification({
    required String itemName,
    required int quantity,
    required String sourceWarehouse,
    required String targetWarehouse,
    required String? targetUserId,
    required String createdBy,
  }) {
    return NotificationModel(
      id: '${DateTime.now().millisecondsSinceEpoch}_transfer',
      title: 'تحويل بضاعة',
      message: 'تم تحويل $quantity من $itemName من $sourceWarehouse إلى $targetWarehouse',
      type: 'transfer',
      targetUserId: targetUserId,
      data: {
        'itemName': itemName,
        'quantity': quantity,
        'sourceWarehouse': sourceWarehouse,
        'targetWarehouse': targetWarehouse,
      },
      createdAt: DateTime.now(),
      createdBy: createdBy,
    );
  }



  /// Create general notification
  static NotificationModel createGeneralNotification({
    required String title,
    required String message,
    String? targetUserId,
    String? targetRole,
    required String createdBy,
    Map<String, dynamic>? data,
  }) {
    return NotificationModel(
      id: '${DateTime.now().millisecondsSinceEpoch}_general',
      title: title,
      message: message,
      type: 'general',
      targetUserId: targetUserId,
      targetRole: targetRole,
      data: data,
      createdAt: DateTime.now(),
      createdBy: createdBy,
    );
  }
}
