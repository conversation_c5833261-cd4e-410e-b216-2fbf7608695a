import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for agent transfer invoices with variable debt calculation
class AgentTransferInvoiceModel {
  final String id;
  final String invoiceNumber;
  final String agentId; // Target agent ID
  final String agentName;
  final String warehouseId; // Source warehouse
  final List<TransferItem> items;
  final double totalCost; // Total cost price of items
  final double totalAgentPrice; // Total price agent will pay (cost + profit share)
  final double totalProfitShare; // Total profit share for company
  final String status; // 'pending', 'confirmed', 'delivered', 'paid'
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;
  final DateTime? deliveredAt;
  final DateTime? paidAt;
  final String? notes;
  final Map<String, dynamic>? additionalData;

  AgentTransferInvoiceModel({
    required this.id,
    required this.invoiceNumber,
    required this.agentId,
    required this.agentName,
    required this.warehouseId,
    required this.items,
    required this.totalCost,
    required this.totalAgentPrice,
    required this.totalProfitShare,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.deliveredAt,
    this.paidAt,
    this.notes,
    this.additionalData,
  });

  // Factory constructor from Firestore document
  factory AgentTransferInvoiceModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AgentTransferInvoiceModel(
      id: doc.id,
      invoiceNumber: data['invoiceNumber'] ?? '',
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      warehouseId: data['warehouseId'] ?? '',
      items: (data['items'] as List<dynamic>?)
          ?.map((item) => TransferItem.fromMap(item))
          .toList() ?? [],
      totalCost: (data['totalCost'] ?? 0.0).toDouble(),
      totalAgentPrice: (data['totalAgentPrice'] ?? 0.0).toDouble(),
      totalProfitShare: (data['totalProfitShare'] ?? 0.0).toDouble(),
      status: data['status'] ?? 'pending',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      deliveredAt: data['deliveredAt'] != null 
          ? (data['deliveredAt'] as Timestamp).toDate() 
          : null,
      paidAt: data['paidAt'] != null 
          ? (data['paidAt'] as Timestamp).toDate() 
          : null,
      notes: data['notes'],
      additionalData: data['additionalData'] != null 
          ? Map<String, dynamic>.from(data['additionalData']) 
          : null,
    );
  }

  // Factory constructor from Map (for local database)
  factory AgentTransferInvoiceModel.fromMap(Map<String, dynamic> map) {
    return AgentTransferInvoiceModel(
      id: map['id'] ?? '',
      invoiceNumber: map['invoiceNumber'] ?? '',
      agentId: map['agentId'] ?? '',
      agentName: map['agentName'] ?? '',
      warehouseId: map['warehouseId'] ?? '',
      items: _parseItems(map['items']),
      totalCost: (map['totalCost'] ?? 0.0).toDouble(),
      totalAgentPrice: (map['totalAgentPrice'] ?? 0.0).toDouble(),
      totalProfitShare: (map['totalProfitShare'] ?? 0.0).toDouble(),
      status: map['status'] ?? 'pending',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      createdBy: map['createdBy'] ?? '',
      deliveredAt: map['deliveredAt'] != null 
          ? DateTime.parse(map['deliveredAt']) 
          : null,
      paidAt: map['paidAt'] != null 
          ? DateTime.parse(map['paidAt']) 
          : null,
      notes: map['notes'],
      additionalData: _parseAdditionalData(map['additionalData']),
    );
  }

  // Convert to Map (for local database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'agentId': agentId,
      'agentName': agentName,
      'warehouseId': warehouseId,
      'items': items.map((item) => item.toMap()).toList(),
      'totalCost': totalCost,
      'totalAgentPrice': totalAgentPrice,
      'totalProfitShare': totalProfitShare,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'deliveredAt': deliveredAt?.toIso8601String(),
      'paidAt': paidAt?.toIso8601String(),
      'notes': notes,
      'additionalData': additionalData,
    };
  }

  // Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    return {
      'invoiceNumber': invoiceNumber,
      'agentId': agentId,
      'agentName': agentName,
      'warehouseId': warehouseId,
      'items': items.map((item) => item.toFirestore()).toList(),
      'totalCost': totalCost,
      'totalAgentPrice': totalAgentPrice,
      'totalProfitShare': totalProfitShare,
      'status': status,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'deliveredAt': deliveredAt != null ? Timestamp.fromDate(deliveredAt!) : null,
      'paidAt': paidAt != null ? Timestamp.fromDate(paidAt!) : null,
      'notes': notes,
      'additionalData': additionalData,
    };
  }

  // Copy with method for updates
  AgentTransferInvoiceModel copyWith({
    String? id,
    String? invoiceNumber,
    String? agentId,
    String? agentName,
    String? warehouseId,
    List<TransferItem>? items,
    double? totalCost,
    double? totalAgentPrice,
    double? totalProfitShare,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    DateTime? deliveredAt,
    DateTime? paidAt,
    String? notes,
    Map<String, dynamic>? additionalData,
  }) {
    return AgentTransferInvoiceModel(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      warehouseId: warehouseId ?? this.warehouseId,
      items: items ?? this.items,
      totalCost: totalCost ?? this.totalCost,
      totalAgentPrice: totalAgentPrice ?? this.totalAgentPrice,
      totalProfitShare: totalProfitShare ?? this.totalProfitShare,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      paidAt: paidAt ?? this.paidAt,
      notes: notes ?? this.notes,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  // Helper methods
  bool get isPending => status == 'pending';
  bool get isConfirmed => status == 'confirmed';
  bool get isDelivered => status == 'delivered';
  bool get isPaid => status == 'paid';
  
  int get totalItemsCount => items.fold(0, (total, item) => total + item.quantity);

  // Helper method to parse items from different formats
  static List<TransferItem> _parseItems(dynamic itemsData) {
    if (itemsData == null) return [];
    
    try {
      List<dynamic> itemsList;
      
      if (itemsData is String) {
        itemsList = [];
      } else if (itemsData is List) {
        itemsList = itemsData;
      } else {
        return [];
      }
      
      return itemsList
          .map((item) => TransferItem.fromMap(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Helper method to parse additional data from different formats
  static Map<String, dynamic>? _parseAdditionalData(dynamic additionalDataValue) {
    if (additionalDataValue == null) return null;
    
    try {
      if (additionalDataValue is String) {
        return null;
      } else if (additionalDataValue is Map) {
        return Map<String, dynamic>.from(additionalDataValue);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'AgentTransferInvoiceModel(id: $id, invoiceNumber: $invoiceNumber, agentName: $agentName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AgentTransferInvoiceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Model for transfer items with variable pricing
class TransferItem {
  final String itemId;
  final String itemName;
  final String brand;
  final String model;
  final int quantity;
  final double costPrice; // Cost price per item
  final double agentPrice; // Price agent will pay per item
  final double profitShare; // Company profit share per item
  final String? motorFingerprintText;

  TransferItem({
    required this.itemId,
    required this.itemName,
    required this.brand,
    required this.model,
    required this.quantity,
    required this.costPrice,
    required this.agentPrice,
    required this.profitShare,
    this.motorFingerprintText,
  });

  factory TransferItem.fromMap(Map<String, dynamic> map) {
    return TransferItem(
      itemId: map['itemId'] ?? '',
      itemName: map['itemName'] ?? '',
      brand: map['brand'] ?? '',
      model: map['model'] ?? '',
      quantity: map['quantity'] ?? 0,
      costPrice: (map['costPrice'] ?? 0.0).toDouble(),
      agentPrice: (map['agentPrice'] ?? 0.0).toDouble(),
      profitShare: (map['profitShare'] ?? 0.0).toDouble(),
      motorFingerprintText: map['motorFingerprintText'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'itemId': itemId,
      'itemName': itemName,
      'brand': brand,
      'model': model,
      'quantity': quantity,
      'costPrice': costPrice,
      'agentPrice': agentPrice,
      'profitShare': profitShare,
      'motorFingerprintText': motorFingerprintText,
    };
  }

  Map<String, dynamic> toFirestore() {
    return toMap();
  }

  // Calculate totals for this item
  double get totalCost => costPrice * quantity;
  double get totalAgentPrice => agentPrice * quantity;
  double get totalProfitShare => profitShare * quantity;

  @override
  String toString() {
    return 'TransferItem(itemId: $itemId, quantity: $quantity, agentPrice: $agentPrice)';
  }
}
