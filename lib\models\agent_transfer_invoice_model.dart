import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج مؤقت لفاتورة تحويل الوكيل لحل مشاكل البناء
class AgentTransferInvoiceModel {
  final String id;
  final String invoiceNumber;
  final String agentId;
  final String agentName;
  final List<Map<String, dynamic>> items;
  final double totalAmount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String status;
  final bool isActive;
  final String? createdBy;

  // Computed properties for compatibility
  double get totalAgentPrice => totalAmount;

  AgentTransferInvoiceModel({
    required this.id,
    required this.invoiceNumber,
    required this.agentId,
    required this.agentName,
    required this.items,
    required this.totalAmount,
    required this.createdAt,
    required this.updatedAt,
    this.status = 'pending',
    this.isActive = true,
    this.createdBy,
  });

  factory AgentTransferInvoiceModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return AgentTransferInvoiceModel(
      id: doc.id,
      invoiceNumber: data['invoiceNumber'] ?? '',
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      items: List<Map<String, dynamic>>.from(data['items'] ?? []),
      totalAmount: (data['totalAmount'] ?? 0.0).toDouble(),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      status: data['status'] ?? 'pending',
      isActive: data['isActive'] ?? true,
    );
  }

  factory AgentTransferInvoiceModel.fromMap(Map<String, dynamic> map) {
    return AgentTransferInvoiceModel(
      id: map['id'] ?? '',
      invoiceNumber: map['invoiceNumber'] ?? '',
      agentId: map['agentId'] ?? '',
      agentName: map['agentName'] ?? '',
      items: List<Map<String, dynamic>>.from(map['items'] ?? []),
      totalAmount: (map['totalAmount'] ?? 0.0).toDouble(),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      status: map['status'] ?? 'pending',
      isActive: (map['isActive'] ?? 1) == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'agentId': agentId,
      'agentName': agentName,
      'items': items,
      'totalAmount': totalAmount,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'status': status,
      'isActive': isActive ? 1 : 0,
    };
  }

  Map<String, dynamic> toFirestore() {
    return {
      'invoiceNumber': invoiceNumber,
      'agentId': agentId,
      'agentName': agentName,
      'items': items,
      'totalAmount': totalAmount,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'status': status,
      'isActive': isActive,
    };
  }
}
