import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';
import '../../services/auth_service.dart';
import '../../test/practical_scenarios_test.dart';

import '../../test/offline_sync_test.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';

class TestingScreen extends StatefulWidget {
  const TestingScreen({super.key});

  @override
  State<TestingScreen> createState() => _TestingScreenState();
}

class _TestingScreenState extends State<TestingScreen> {
  bool _isRunningTests = false;
  final List<String> _testResults = [];
  
  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    // التحقق من الصلاحيات - المدير الأعلى فقط
    if (!authProvider.isSuperAdmin) {
      return Scaffold(
        appBar: AppBar(title: const Text('اختبار النظام')),
        body: const Center(
          child: Text(
            'ليس لديك صلاحية للوصول إلى هذه الشاشة',
            style: TextStyle(fontSize: 18),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار النظام'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات الاختبار
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'اختبار السيناريوهات الشاملة',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'هذه الشاشة تتيح للمدير الأعلى تشغيل اختبارات شاملة للتأكد من عمل جميع وظائف التطبيق بشكل صحيح.',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // أزرار الاختبار
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      'اختبارات متاحة',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                    
                    // اختبار سريع
                    ElevatedButton.icon(
                      onPressed: _isRunningTests ? null : _runQuickTest,
                      icon: const Icon(Icons.flash_on),
                      label: const Text('اختبار سريع'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // اختبار شامل
                    ElevatedButton.icon(
                      onPressed: _isRunningTests ? null : _runFullTest,
                      icon: const Icon(Icons.assessment),
                      label: const Text('اختبار شامل'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),

                    const SizedBox(height: 8),

                    // اختبار العمل Offline
                    ElevatedButton.icon(
                      onPressed: _isRunningTests ? null : _runOfflineTest,
                      icon: const Icon(Icons.cloud_off),
                      label: const Text('اختبار العمل Offline'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),

                    const SizedBox(height: 8),
                    
                    // مسح النتائج
                    OutlinedButton.icon(
                      onPressed: _testResults.isEmpty ? null : _clearResults,
                      icon: const Icon(Icons.clear),
                      label: const Text('مسح النتائج'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // نتائج الاختبار
            Expanded(
              child: Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Row(
                        children: [
                          Text(
                            'نتائج الاختبار',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const Spacer(),
                          if (_isRunningTests)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                    ),
                    const Divider(height: 1),
                    Expanded(
                      child: _testResults.isEmpty
                          ? const Center(
                              child: Text(
                                'لم يتم تشغيل أي اختبارات بعد',
                                style: TextStyle(color: Colors.grey),
                              ),
                            )
                          : ListView.builder(
                              padding: const EdgeInsets.all(AppConstants.defaultPadding),
                              itemCount: _testResults.length,
                              itemBuilder: (context, index) {
                                final result = _testResults[index];
                                final isSuccess = result.contains('✅');
                                final isError = result.contains('❌');
                                
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 4),
                                  child: Text(
                                    result,
                                    style: TextStyle(
                                      fontFamily: 'monospace',
                                      fontSize: 12,
                                      color: isSuccess
                                          ? Colors.green
                                          : isError
                                              ? Colors.red
                                              : null,
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Future<void> _runQuickTest() async {
    setState(() {
      _isRunningTests = true;
      _testResults.clear();
    });
    
    try {
      // تشغيل الاختبار السريع مع التقاط النتائج
      await _captureTestOutput(() => PracticalScenariosTest.runQuickTest());
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إكمال الاختبار السريع بنجاح');
      }
    } catch (e) {
      _addTestResult('❌ خطأ في الاختبار السريع: $e');
      if (mounted) {
        AppUtils.showSnackBar(context, 'فشل الاختبار السريع: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRunningTests = false;
        });
      }
    }
  }
  
  Future<void> _runFullTest() async {
    setState(() {
      _isRunningTests = true;
      _testResults.clear();
    });
    
    try {
      // تشغيل الاختبار الشامل مع التقاط النتائج
      await _captureTestOutput(() => PracticalScenariosTest.runBasicTests());
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إكمال الاختبار الشامل بنجاح');
      }
    } catch (e) {
      _addTestResult('❌ خطأ في الاختبار الشامل: $e');
      if (mounted) {
        AppUtils.showSnackBar(context, 'فشل الاختبار الشامل: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRunningTests = false;
        });
      }
    }
  }
  
  Future<void> _captureTestOutput(Future<void> Function() testFunction) async {
    // محاكاة التقاط نتائج الاختبار
    // في التطبيق الحقيقي، يمكن استخدام مكتبة logging أو تخصيص print
    
    _addTestResult('🚀 بدء تشغيل الاختبارات...');
    _addTestResult('=' * 40);
    
    try {
      await testFunction();
      _addTestResult('✅ تم إكمال جميع الاختبارات بنجاح');
    } catch (e) {
      _addTestResult('❌ فشل في تشغيل الاختبارات: $e');
      rethrow;
    }
    
    _addTestResult('=' * 40);
    _addTestResult('انتهى الاختبار في ${DateTime.now().toString()}');
  }
  
  void _addTestResult(String result) {
    if (mounted) {
      setState(() {
        _testResults.add(result);
      });
    }
  }
  
  Future<void> _runOfflineTest() async {
    setState(() {
      _isRunningTests = true;
      _testResults.clear();
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final dataService = Provider.of<DataService>(context, listen: false);
      final authService = Provider.of<AuthService>(context, listen: false);

      if (authProvider.currentUser == null) {
        setState(() {
          _testResults.add('❌ خطأ: يجب تسجيل الدخول أولاً');
          _isRunningTests = false;
        });
        return;
      }

      _addTestResult('🔄 بدء اختبارات العمل Offline والمزامنة...');

      final offlineTest = OfflineSyncTest(dataService, authService);
      final results = await offlineTest.runOfflineSyncTests();

      // Display results
      _addTestResult('📊 نتائج اختبارات العمل Offline:');

      final testResults = results['testResults'] as Map<String, bool>;
      testResults.forEach((testName, passed) {
        final status = passed ? '✅' : '❌';
        final displayName = _getOfflineTestDisplayName(testName);
        _addTestResult('$status $displayName');
      });

      _addTestResult('');
      _addTestResult('📈 الملخص: ${results['summary']}');

      final errors = results['errors'] as List<String>;
      if (errors.isNotEmpty) {
        _addTestResult('');
        _addTestResult('⚠️ الأخطاء:');
        for (final error in errors) {
          _addTestResult('• $error');
        }
      }

    } catch (e) {
      _addTestResult('❌ خطأ في تشغيل اختبارات العمل Offline: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isRunningTests = false;
        });
      }
    }
  }

  String _getOfflineTestDisplayName(String testName) {
    switch (testName) {
      case 'offline_item_creation':
        return 'إنشاء الأصناف Offline';
      case 'offline_invoice_creation':
        return 'إنشاء الفواتير Offline';
      case 'offline_user_management':
        return 'إدارة المستخدمين Offline';
      case 'sync_queue_management':
        return 'إدارة قائمة المزامنة';
      case 'data_consistency':
        return 'تناسق البيانات';
      case 'conflict_resolution':
        return 'حل التعارضات';
      case 'network_state_handling':
        return 'التعامل مع حالة الشبكة';
      case 'large_data_sync':
        return 'مزامنة البيانات الكبيرة';
      default:
        return testName;
    }
  }

  void _clearResults() {
    setState(() {
      _testResults.clear();
    });
  }
}
