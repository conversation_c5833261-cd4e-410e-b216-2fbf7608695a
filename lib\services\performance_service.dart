import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  static PerformanceService get instance => _instance;

  // Performance metrics
  DateTime? _appStartTime;
  DateTime? _appReadyTime;
  final Map<String, DateTime> _screenLoadTimes = {};
  final Map<String, int> _memoryUsage = {};
  final List<String> _performanceLogs = [];

  // Cache management
  final Map<String, dynamic> _memoryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const int _maxCacheSize = 100;
  static const Duration _cacheExpiry = Duration(minutes: 30);

  // Lazy loading controllers
  final Map<String, bool> _loadedScreens = {};
  final Map<String, Completer<void>> _loadingCompleters = {};

  /// Initialize performance monitoring
  void initialize() {
    _appStartTime = DateTime.now();
    _startMemoryMonitoring();
    
    if (kDebugMode) {
      print('🚀 Performance monitoring initialized');
    }
  }

  /// Mark app as ready
  void markAppReady() {
    _appReadyTime = DateTime.now();
    if (_appStartTime != null) {
      final startupTime = _appReadyTime!.difference(_appStartTime!);
      _logPerformance('App startup time: ${startupTime.inMilliseconds}ms');
      
      if (kDebugMode) {
        print('⚡ App ready in ${startupTime.inMilliseconds}ms');
      }
    }
  }

  /// Start monitoring memory usage
  void _startMemoryMonitoring() {
    Timer.periodic(const Duration(seconds: 30), (timer) {
      _recordMemoryUsage();
    });
  }

  /// Record current memory usage
  void _recordMemoryUsage() {
    if (Platform.isAndroid || Platform.isIOS) {
      // This would typically use platform-specific methods to get memory usage
      // For now, we'll simulate it
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      _memoryUsage[timestamp] = _getEstimatedMemoryUsage();
      
      // Keep only last 100 entries
      if (_memoryUsage.length > 100) {
        final oldestKey = _memoryUsage.keys.first;
        _memoryUsage.remove(oldestKey);
      }
    }
  }

  /// Get estimated memory usage (simplified)
  int _getEstimatedMemoryUsage() {
    // This is a simplified estimation
    // In a real app, you'd use platform channels to get actual memory usage
    final cacheSize = _memoryCache.length * 1024; // Rough estimate
    const baseUsage = 50 * 1024 * 1024; // 50MB base
    return baseUsage + cacheSize;
  }

  /// Track screen load time
  void startScreenLoad(String screenName) {
    _screenLoadTimes['${screenName}_start'] = DateTime.now();
  }

  void endScreenLoad(String screenName) {
    final startTime = _screenLoadTimes['${screenName}_start'];
    if (startTime != null) {
      final endTime = DateTime.now();
      final loadTime = endTime.difference(startTime);
      _screenLoadTimes[screenName] = startTime;
      
      _logPerformance('Screen $screenName loaded in ${loadTime.inMilliseconds}ms');
      
      if (kDebugMode) {
        print('📱 Screen $screenName: ${loadTime.inMilliseconds}ms');
      }
    }
  }

  /// Cache management
  void cacheData(String key, dynamic data) {
    // Remove expired entries
    _cleanExpiredCache();
    
    // Remove oldest entries if cache is full
    if (_memoryCache.length >= _maxCacheSize) {
      final oldestKey = _cacheTimestamps.entries
          .reduce((a, b) => a.value.isBefore(b.value) ? a : b)
          .key;
      _memoryCache.remove(oldestKey);
      _cacheTimestamps.remove(oldestKey);
    }
    
    _memoryCache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
    
    if (kDebugMode) {
      print('💾 Cached data: $key (${_memoryCache.length}/$_maxCacheSize)');
    }
  }

  T? getCachedData<T>(String key) {
    _cleanExpiredCache();
    
    final data = _memoryCache[key];
    if (data != null && data is T) {
      if (kDebugMode) {
        print('🎯 Cache hit: $key');
      }
      return data;
    }
    
    if (kDebugMode) {
      print('❌ Cache miss: $key');
    }
    return null;
  }

  void _cleanExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _cacheExpiry) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
    }
    
    if (expiredKeys.isNotEmpty && kDebugMode) {
      print('🧹 Cleaned ${expiredKeys.length} expired cache entries');
    }
  }

  void clearCache() {
    _memoryCache.clear();
    _cacheTimestamps.clear();
    
    if (kDebugMode) {
      print('🗑️ Cache cleared');
    }
  }

  /// Lazy loading management
  Future<void> ensureScreenLoaded(String screenName, Future<void> Function() loader) async {
    if (_loadedScreens[screenName] == true) {
      return;
    }

    // Check if already loading
    if (_loadingCompleters.containsKey(screenName)) {
      return _loadingCompleters[screenName]!.future;
    }

    // Start loading
    final completer = Completer<void>();
    _loadingCompleters[screenName] = completer;

    try {
      startScreenLoad(screenName);
      await loader();
      endScreenLoad(screenName);
      
      _loadedScreens[screenName] = true;
      completer.complete();
    } catch (e) {
      completer.completeError(e);
      if (kDebugMode) {
        print('❌ Error loading screen $screenName: $e');
      }
    } finally {
      _loadingCompleters.remove(screenName);
    }
  }

  /// Preload critical screens
  Future<void> preloadCriticalScreens() async {
    final criticalScreens = [
      'home',
      'inventory',
      'sales',
    ];

    for (final screen in criticalScreens) {
      if (!_loadedScreens.containsKey(screen)) {
        // Simulate preloading
        await Future.delayed(const Duration(milliseconds: 100));
        _loadedScreens[screen] = true;
      }
    }

    if (kDebugMode) {
      print('🎯 Preloaded ${criticalScreens.length} critical screens');
    }
  }

  /// Image optimization
  Future<void> optimizeImages() async {
    try {
      // This would typically compress and optimize images
      // For now, we'll simulate the process
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (kDebugMode) {
        print('🖼️ Images optimized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error optimizing images: $e');
      }
    }
  }

  /// Database optimization
  Future<void> optimizeDatabase() async {
    try {
      // This would typically run database optimization queries
      await Future.delayed(const Duration(milliseconds: 300));
      
      if (kDebugMode) {
        print('🗄️ Database optimized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error optimizing database: $e');
      }
    }
  }

  /// Network optimization
  void optimizeNetworkRequests() {
    // Implement request batching, compression, etc.
    if (kDebugMode) {
      print('🌐 Network requests optimized');
    }
  }

  /// Performance logging
  void _logPerformance(String message) {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = '[$timestamp] $message';
    _performanceLogs.add(logEntry);
    
    // Keep only last 1000 logs
    if (_performanceLogs.length > 1000) {
      _performanceLogs.removeAt(0);
    }
  }

  /// Get performance metrics
  Map<String, dynamic> getPerformanceMetrics() {
    final metrics = <String, dynamic>{};
    
    if (_appStartTime != null && _appReadyTime != null) {
      metrics['startupTime'] = _appReadyTime!.difference(_appStartTime!).inMilliseconds;
    }
    
    metrics['cacheSize'] = _memoryCache.length;
    metrics['loadedScreens'] = _loadedScreens.length;
    metrics['memoryUsageHistory'] = Map.from(_memoryUsage);
    metrics['screenLoadTimes'] = Map.from(_screenLoadTimes);
    
    if (_memoryUsage.isNotEmpty) {
      final currentMemory = _memoryUsage.values.last;
      metrics['currentMemoryUsage'] = currentMemory;
      metrics['memoryUsageMB'] = (currentMemory / (1024 * 1024)).toStringAsFixed(2);
    }
    
    return metrics;
  }

  /// Get performance report
  String getPerformanceReport() {
    final metrics = getPerformanceMetrics();
    final buffer = StringBuffer();
    
    buffer.writeln('📊 Performance Report');
    buffer.writeln('=' * 30);
    
    if (metrics.containsKey('startupTime')) {
      buffer.writeln('🚀 Startup Time: ${metrics['startupTime']}ms');
    }
    
    if (metrics.containsKey('memoryUsageMB')) {
      buffer.writeln('💾 Memory Usage: ${metrics['memoryUsageMB']} MB');
    }
    
    buffer.writeln('🎯 Cache Size: ${metrics['cacheSize']} items');
    buffer.writeln('📱 Loaded Screens: ${metrics['loadedScreens']}');
    
    buffer.writeln('\n📋 Recent Performance Logs:');
    final recentLogs = _performanceLogs.take(10).toList();
    for (final log in recentLogs) {
      buffer.writeln('  $log');
    }
    
    return buffer.toString();
  }

  /// Save performance data
  Future<void> savePerformanceData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metrics = getPerformanceMetrics();
      
      await prefs.setString('performance_metrics', metrics.toString());
      await prefs.setStringList('performance_logs', _performanceLogs);
      
      if (kDebugMode) {
        print('💾 Performance data saved');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving performance data: $e');
      }
    }
  }

  /// Load performance data
  Future<void> loadPerformanceData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logs = prefs.getStringList('performance_logs');
      
      if (logs != null) {
        _performanceLogs.clear();
        _performanceLogs.addAll(logs);
      }
      
      if (kDebugMode) {
        print('📂 Performance data loaded');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading performance data: $e');
      }
    }
  }

  /// Cleanup resources
  void dispose() {
    clearCache();
    _screenLoadTimes.clear();
    _memoryUsage.clear();
    _loadedScreens.clear();
    _loadingCompleters.clear();
    
    if (kDebugMode) {
      print('🧹 Performance service disposed');
    }
  }
}
