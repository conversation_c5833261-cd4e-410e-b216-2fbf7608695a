import 'package:cloud_firestore/cloud_firestore.dart';

class ReportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Generate sales report
  static Future<Map<String, dynamic>> generateSalesReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final salesQuery = await _firestore
          .collection('sales_invoices')
          .where('date', isGreaterThanOrEqualTo: startDate)
          .where('date', isLessThanOrEqualTo: endDate)
          .get();

      double totalSales = 0;
      int totalInvoices = salesQuery.docs.length;

      for (var doc in salesQuery.docs) {
        final data = doc.data();
        totalSales += (data['total'] ?? 0).toDouble();
      }

      return {
        'totalSales': totalSales,
        'totalInvoices': totalInvoices,
        'averageInvoice': totalInvoices > 0 ? totalSales / totalInvoices : 0,
        'period': '${startDate.day}/${startDate.month}/${startDate.year} - ${endDate.day}/${endDate.month}/${endDate.year}',
      };
    } catch (e) {
      throw 'خطأ في إنشاء تقرير المبيعات: $e';
    }
  }

  // Generate inventory report
  static Future<Map<String, dynamic>> generateInventoryReport() async {
    try {
      final inventoryQuery = await _firestore.collection('inventory').get();

      int totalItems = 0;
      double totalValue = 0;
      int lowStockItems = 0;

      for (var doc in inventoryQuery.docs) {
        final data = doc.data();
        final quantityValue = data['quantity'] ?? 0;
        final quantity = int.parse(quantityValue.toString());
        final price = (data['price'] ?? 0).toDouble();
        final minStock = (data['minStock'] ?? 0).toInt();

        totalItems += quantity;
        totalValue = totalValue + (quantity * price);

        if (quantity <= minStock) {
          lowStockItems++;
        }
      }

      return {
        'totalItems': totalItems,
        'totalValue': totalValue,
        'lowStockItems': lowStockItems,
        'totalProducts': inventoryQuery.docs.length,
      };
    } catch (e) {
      throw 'خطأ في إنشاء تقرير المخزون: $e';
    }
  }

  // Generate agent statement
  static Future<Map<String, dynamic>> generateAgentStatement(String agentId) async {
    try {
      final agentQuery = await _firestore
          .collection('agents')
          .doc(agentId)
          .get();

      if (!agentQuery.exists) {
        throw 'الوكيل غير موجود';
      }

      final agentData = agentQuery.data()!;
      
      // Get agent transactions
      final transactionsQuery = await _firestore
          .collection('agent_transactions')
          .where('agentId', isEqualTo: agentId)
          .orderBy('date', descending: true)
          .get();

      double totalDebit = 0;
      double totalCredit = 0;

      for (var doc in transactionsQuery.docs) {
        final data = doc.data();
        final type = data['type'] ?? '';
        final amount = (data['amount'] ?? 0).toDouble();

        if (type == 'debit') {
          totalDebit += amount;
        } else if (type == 'credit') {
          totalCredit += amount;
        }
      }

      return {
        'agentName': agentData['name'] ?? '',
        'agentPhone': agentData['phone'] ?? '',
        'totalDebit': totalDebit,
        'totalCredit': totalCredit,
        'balance': totalDebit - totalCredit,
        'transactionCount': transactionsQuery.docs.length,
        'transactions': transactionsQuery.docs.map((doc) => doc.data()).toList(),
      };
    } catch (e) {
      throw 'خطأ في إنشاء كشف حساب الوكيل: $e';
    }
  }

  // Generate warehouse movement report
  static Future<List<Map<String, dynamic>>> generateWarehouseMovementReport({
    required DateTime startDate,
    required DateTime endDate,
    String? warehouseId,
  }) async {
    try {
      Query query = _firestore
          .collection('warehouse_movements')
          .where('date', isGreaterThanOrEqualTo: startDate)
          .where('date', isLessThanOrEqualTo: endDate);

      if (warehouseId != null) {
        query = query.where('warehouseId', isEqualTo: warehouseId);
      }

      final movementsQuery = await query.orderBy('date', descending: true).get();

      return movementsQuery.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();
    } catch (e) {
      throw 'خطأ في إنشاء تقرير حركة المخزون: $e';
    }
  }
}
