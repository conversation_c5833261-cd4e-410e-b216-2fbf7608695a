import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';

class EncryptionService {
  static final EncryptionService _instance = EncryptionService._internal();
  factory EncryptionService() => _instance;
  EncryptionService._internal();

  static EncryptionService get instance => _instance;

  /// Generate a random salt for password hashing
  String generateSalt([int length = 32]) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// Hash password with salt using PBKDF2-like approach
  String hashPassword(String password, String salt) {
    try {
      // Combine password and salt
      final combined = password + salt;
      
      // Apply multiple rounds of hashing for security
      var hash = combined;
      for (int i = 0; i < 10000; i++) {
        final bytes = utf8.encode(hash);
        final digest = sha256.convert(bytes);
        hash = digest.toString();
      }
      
      return hash;
    } catch (e) {
      if (kDebugMode) {
        print('Error hashing password: $e');
      }
      rethrow;
    }
  }

  /// Verify password against stored hash and salt
  bool verifyPassword(String password, String storedHash, String salt) {
    try {
      final computedHash = hashPassword(password, salt);
      return computedHash == storedHash;
    } catch (e) {
      if (kDebugMode) {
        print('Error verifying password: $e');
      }
      return false;
    }
  }

  /// Create password hash with embedded salt (for storage)
  String createPasswordHash(String password) {
    try {
      final salt = generateSalt();
      final hash = hashPassword(password, salt);
      
      // Combine salt and hash with a separator
      return '$salt:$hash';
    } catch (e) {
      if (kDebugMode) {
        print('Error creating password hash: $e');
      }
      rethrow;
    }
  }

  /// Verify password against stored hash with embedded salt
  bool verifyPasswordHash(String password, String storedHashWithSalt) {
    try {
      final parts = storedHashWithSalt.split(':');
      if (parts.length != 2) {
        if (kDebugMode) {
          print('Invalid hash format');
        }
        return false;
      }
      
      final salt = parts[0];
      final storedHash = parts[1];
      
      return verifyPassword(password, storedHash, salt);
    } catch (e) {
      if (kDebugMode) {
        print('Error verifying password hash: $e');
      }
      return false;
    }
  }

  /// Check password strength
  PasswordStrength checkPasswordStrength(String password) {
    if (password.isEmpty) {
      return PasswordStrength.empty;
    }
    
    int score = 0;
    
    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // Character variety checks
    if (password.contains(RegExp(r'[a-z]'))) score++; // lowercase
    if (password.contains(RegExp(r'[A-Z]'))) score++; // uppercase
    if (password.contains(RegExp(r'[0-9]'))) score++; // numbers
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score++; // special chars
    
    // Common patterns check (reduce score)
    if (password.toLowerCase().contains('password') ||
        password.toLowerCase().contains('123456') ||
        password.toLowerCase().contains('qwerty')) {
      score -= 2;
    }
    
    // Return strength based on score
    if (score <= 1) return PasswordStrength.weak;
    if (score <= 3) return PasswordStrength.medium;
    if (score <= 5) return PasswordStrength.strong;
    return PasswordStrength.veryStrong;
  }

  /// Get password strength description
  String getPasswordStrengthDescription(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.empty:
        return 'كلمة المرور فارغة';
      case PasswordStrength.weak:
        return 'ضعيفة';
      case PasswordStrength.medium:
        return 'متوسطة';
      case PasswordStrength.strong:
        return 'قوية';
      case PasswordStrength.veryStrong:
        return 'قوية جداً';
    }
  }

  /// Get password strength color (returns hex color code)
  String getPasswordStrengthColor(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.empty:
        return '#9E9E9E'; // grey
      case PasswordStrength.weak:
        return '#F44336'; // red
      case PasswordStrength.medium:
        return '#FF9800'; // orange
      case PasswordStrength.strong:
        return '#2196F3'; // blue
      case PasswordStrength.veryStrong:
        return '#4CAF50'; // green
    }
  }

  /// Generate secure random password
  String generateSecurePassword({
    int length = 12,
    bool includeUppercase = true,
    bool includeLowercase = true,
    bool includeNumbers = true,
    bool includeSpecialChars = true,
  }) {
    String chars = '';
    
    if (includeLowercase) chars += 'abcdefghijklmnopqrstuvwxyz';
    if (includeUppercase) chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (includeNumbers) chars += '0123456789';
    if (includeSpecialChars) chars += '!@#\$%^&*()_+-=[]{}|;:,.<>?';
    
    if (chars.isEmpty) {
      throw ArgumentError('At least one character type must be included');
    }
    
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// Validate password requirements
  List<String> validatePasswordRequirements(String password) {
    final errors = <String>[];
    
    if (password.length < 8) {
      errors.add('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
    }
    
    if (!password.contains(RegExp(r'[a-z]'))) {
      errors.add('يجب أن تحتوي على حرف صغير واحد على الأقل');
    }
    
    if (!password.contains(RegExp(r'[A-Z]'))) {
      errors.add('يجب أن تحتوي على حرف كبير واحد على الأقل');
    }
    
    if (!password.contains(RegExp(r'[0-9]'))) {
      errors.add('يجب أن تحتوي على رقم واحد على الأقل');
    }
    
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      errors.add('يجب أن تحتوي على رمز خاص واحد على الأقل');
    }
    
    // Check for common weak patterns
    if (password.toLowerCase().contains('password') ||
        password.toLowerCase().contains('123456') ||
        password.toLowerCase().contains('qwerty')) {
      errors.add('تجنب استخدام كلمات مرور شائعة');
    }
    
    return errors;
  }

  /// Simple encryption for sensitive data (not passwords)
  String encryptData(String data, String key) {
    try {
      final keyBytes = utf8.encode(key);
      final dataBytes = utf8.encode(data);
      
      // Simple XOR encryption (for basic obfuscation)
      final encrypted = <int>[];
      for (int i = 0; i < dataBytes.length; i++) {
        encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return base64.encode(encrypted);
    } catch (e) {
      if (kDebugMode) {
        print('Error encrypting data: $e');
      }
      rethrow;
    }
  }

  /// Simple decryption for sensitive data
  String decryptData(String encryptedData, String key) {
    try {
      final keyBytes = utf8.encode(key);
      final encryptedBytes = base64.decode(encryptedData);
      
      // Simple XOR decryption
      final decrypted = <int>[];
      for (int i = 0; i < encryptedBytes.length; i++) {
        decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return utf8.decode(decrypted);
    } catch (e) {
      if (kDebugMode) {
        print('Error decrypting data: $e');
      }
      rethrow;
    }
  }
}

enum PasswordStrength {
  empty,
  weak,
  medium,
  strong,
  veryStrong,
}
