import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../core/theme/desktop_theme.dart';
import '../../widgets/desktop/desktop_app_bar.dart';
import '../../widgets/desktop/desktop_navigation_rail.dart';
import '../../widgets/desktop/desktop_dashboard_cards.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';
import '../inventory/desktop_inventory_screen.dart';
import '../sales/desktop_sales_screen.dart';
import '../reports/desktop_reports_screen.dart';
import '../admin/desktop_admin_screen.dart';
import 'desktop_dashboard_screen.dart';

/// Professional desktop main screen with Office-style interface
class DesktopMainScreen extends StatefulWidget {
  const DesktopMainScreen({super.key});

  @override
  State<DesktopMainScreen> createState() => _DesktopMainScreenState();
}

class _DesktopMainScreenState extends State<DesktopMainScreen>
    with TickerProviderStateMixin {
  int _selectedIndex = 0;
  bool _isNavigationExpanded = true;
  late TabController _tabController;
  final List<DesktopTab> _openTabs = [];

  // Navigation destinations
  final List<DesktopNavigationDestination> _destinations = [
    const DesktopNavigationDestination(
      label: 'لوحة التحكم',
      icon: Icons.dashboard_outlined,
    ),
    const DesktopNavigationDestination(
      label: 'إدارة المخزون',
      icon: Icons.inventory_2_outlined,
      children: [
        DesktopNavigationDestination(
          label: 'عرض المخزون',
          icon: Icons.view_list_outlined,
        ),
        DesktopNavigationDestination(
          label: 'إضافة عنصر',
          icon: Icons.add_box_outlined,
        ),
        DesktopNavigationDestination(
          label: 'نقل البضائع',
          icon: Icons.swap_horiz_outlined,
        ),
      ],
    ),
    const DesktopNavigationDestination(
      label: 'إدارة المبيعات',
      icon: Icons.point_of_sale_outlined,
      children: [
        DesktopNavigationDestination(
          label: 'الفواتير',
          icon: Icons.receipt_long_outlined,
        ),
        DesktopNavigationDestination(
          label: 'إنشاء فاتورة',
          icon: Icons.add_shopping_cart_outlined,
        ),
        DesktopNavigationDestination(
          label: 'العملاء',
          icon: Icons.people_outlined,
        ),
      ],
    ),
    const DesktopNavigationDestination(
      label: 'التقارير',
      icon: Icons.analytics_outlined,
      children: [
        DesktopNavigationDestination(
          label: 'تقارير المبيعات',
          icon: Icons.trending_up_outlined,
        ),
        DesktopNavigationDestination(
          label: 'تقارير المخزون',
          icon: Icons.assessment_outlined,
        ),
        DesktopNavigationDestination(
          label: 'تقارير الوكلاء',
          icon: Icons.account_balance_outlined,
        ),
      ],
    ),
    const DesktopNavigationDestination(
      label: 'الإدارة',
      icon: Icons.admin_panel_settings_outlined,
      children: [
        DesktopNavigationDestination(
          label: 'إدارة المستخدمين',
          icon: Icons.manage_accounts_outlined,
        ),
        DesktopNavigationDestination(
          label: 'إدارة المخازن',
          icon: Icons.warehouse_outlined,
        ),
        DesktopNavigationDestination(
          label: 'الإعدادات',
          icon: Icons.settings_outlined,
        ),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 0, vsync: this);
    _openInitialTab();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _openInitialTab() {
    _openTab(const DesktopTab(
      id: 'dashboard',
      title: 'لوحة التحكم',
      icon: Icons.dashboard_outlined,
      content: DesktopDashboardScreen(),
      canClose: false,
    ));
  }

  void _openTab(DesktopTab tab) {
    setState(() {
      // Check if tab is already open
      final existingIndex = _openTabs.indexWhere((t) => t.id == tab.id);
      if (existingIndex != -1) {
        _tabController.animateTo(existingIndex);
        return;
      }

      // Add new tab
      _openTabs.add(tab);
      _tabController.dispose();
      _tabController = TabController(length: _openTabs.length, vsync: this);
      _tabController.animateTo(_openTabs.length - 1);
    });
  }

  void _closeTab(int index) {
    if (_openTabs[index].canClose) {
      setState(() {
        _openTabs.removeAt(index);
        _tabController.dispose();
        _tabController = TabController(length: _openTabs.length, vsync: this);
        if (index > 0) {
          _tabController.animateTo(index - 1);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesktopTheme.backgroundPrimary,
      body: Column(
        children: [
          // App Bar
          _buildAppBar(),
          
          // Tab Bar
          if (_openTabs.isNotEmpty) _buildTabBar(),
          
          // Main Content
          Expanded(
            child: Row(
              children: [
                // Navigation Rail
                _buildNavigationRail(),
                
                // Content Area
                Expanded(
                  child: _buildContentArea(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    final authProvider = Provider.of<AuthProvider>(context);
    
    return DesktopAppBar(
      title: 'نظام إدارة النقل - الفرحان',
      subtitle: 'نسخة سطح المكتب الاحترافية',
      toolbarGroups: [
        DesktopToolbarGroup(
          label: 'ملف',
          tools: [
            DesktopToolbarItem(
              label: 'جديد',
              icon: Icons.add,
              onPressed: () => _showNewItemMenu(),
            ),
            DesktopToolbarItem(
              label: 'فتح',
              icon: Icons.folder_open,
              onPressed: () => _showOpenMenu(),
            ),
            DesktopToolbarItem(
              label: 'حفظ',
              icon: Icons.save,
              onPressed: () => _saveCurrentTab(),
            ),
          ],
        ),
        DesktopToolbarGroup(
          label: 'عرض',
          tools: [
            DesktopToolbarItem(
              label: 'التنقل',
              icon: Icons.menu,
              isSelected: _isNavigationExpanded,
              onPressed: () => setState(() {
                _isNavigationExpanded = !_isNavigationExpanded;
              }),
            ),
            DesktopToolbarItem(
              label: 'تحديث',
              icon: Icons.refresh,
              onPressed: () => _refreshData(),
            ),
          ],
        ),
        DesktopToolbarGroup(
          label: 'أدوات',
          tools: [
            DesktopToolbarItem(
              label: 'بحث',
              icon: Icons.search,
              onPressed: () => _showGlobalSearch(),
            ),
            DesktopToolbarItem(
              label: 'إعدادات',
              icon: Icons.settings,
              onPressed: () => _openSettingsTab(),
            ),
          ],
        ),
      ],
      actions: [
        // Notifications
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () => _showNotifications(),
          tooltip: 'الإشعارات',
        ),
        
        // User Menu
        PopupMenuButton<String>(
          onSelected: (value) => _handleUserMenuAction(value, authProvider),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  Icon(Icons.person_outline),
                  SizedBox(width: 12),
                  Text('الملف الشخصي'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: Colors.red),
                  SizedBox(width: 12),
                  Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: DesktopTheme.spacingMedium,
              vertical: DesktopTheme.spacingSmall,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: DesktopTheme.primaryBlue.withOpacity(0.1),
                  child: Text(
                    authProvider.currentUser?.fullName.substring(0, 1).toUpperCase() ?? 'م',
                    style: DesktopTheme.titleSmall.copyWith(
                      color: DesktopTheme.primaryBlue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: DesktopTheme.spacingSmall),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      authProvider.currentUser?.fullName ?? 'مستخدم',
                      style: DesktopTheme.titleSmall,
                    ),
                    Text(
                      authProvider.userRoleDisplayName,
                      style: DesktopTheme.bodySmall,
                    ),
                  ],
                ),
                const SizedBox(width: DesktopTheme.spacingXSmall),
                const Icon(Icons.arrow_drop_down, size: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 40,
      decoration: const BoxDecoration(
        color: DesktopTheme.backgroundSecondary,
        border: Border(
          bottom: BorderSide(
            color: DesktopTheme.borderLight,
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        labelColor: DesktopTheme.textPrimary,
        unselectedLabelColor: DesktopTheme.textSecondary,
        labelStyle: DesktopTheme.bodyMedium,
        unselectedLabelStyle: DesktopTheme.bodyMedium,
        indicator: BoxDecoration(
          color: DesktopTheme.primaryBlue.withOpacity(0.1),
          border: const Border(
            bottom: BorderSide(
              color: DesktopTheme.primaryBlue,
              width: 2,
            ),
          ),
        ),
        tabs: _openTabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;

          return Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(tab.icon, size: 16),
                const SizedBox(width: DesktopTheme.spacingSmall),
                Text(tab.title),
                if (tab.canClose) ...[
                  const SizedBox(width: DesktopTheme.spacingSmall),
                  InkWell(
                    onTap: () => _closeTab(index),
                    child: const Icon(Icons.close, size: 16),
                  ),
                ],
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildNavigationRail() {
    return ExpandableDesktopNavigationRail(
      selectedIndex: _selectedIndex,
      destinations: _destinations,
      extended: _isNavigationExpanded,
      onDestinationSelected: _onNavigationDestinationSelected,
      header: Container(
        padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: DesktopTheme.primaryBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.business,
                color: DesktopTheme.primaryBlue,
                size: 24,
              ),
            ),
            if (_isNavigationExpanded) ...[
              const SizedBox(width: DesktopTheme.spacingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الفرحان',
                      style: DesktopTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Text(
                      'نظام إدارة النقل',
                      style: DesktopTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildContentArea() {
    if (_openTabs.isEmpty) {
      return const Center(
        child: Text('لا توجد تبويبات مفتوحة'),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: _openTabs.map((tab) => tab.content).toList(),
    );
  }

  void _onNavigationDestinationSelected(int index) {
    setState(() {
      _selectedIndex = index;
    });

    // Open corresponding tab based on selection
    switch (index) {
      case 0:
        // Dashboard - already open by default
        break;
      case 1:
        _openTab(const DesktopTab(
          id: 'inventory',
          title: 'إدارة المخزون',
          icon: Icons.inventory_2_outlined,
          content: DesktopInventoryScreen(),
        ));
        break;
      case 2:
        _openTab(const DesktopTab(
          id: 'sales',
          title: 'إدارة المبيعات',
          icon: Icons.point_of_sale_outlined,
          content: DesktopSalesScreen(),
        ));
        break;
      case 3:
        _openTab(const DesktopTab(
          id: 'reports',
          title: 'التقارير',
          icon: Icons.analytics_outlined,
          content: DesktopReportsScreen(),
        ));
        break;
      case 4:
        _openTab(const DesktopTab(
          id: 'admin',
          title: 'الإدارة',
          icon: Icons.admin_panel_settings_outlined,
          content: DesktopAdminScreen(),
        ));
        break;
    }
  }

  // Toolbar actions
  void _showNewItemMenu() {
    // TODO: Implement new item menu
  }

  void _showOpenMenu() {
    // TODO: Implement open menu
  }

  void _saveCurrentTab() {
    // TODO: Implement save functionality
  }

  void _refreshData() {
    final dataService = Provider.of<DataService>(context, listen: false);
    dataService.syncFromFirebase();
  }

  void _showGlobalSearch() {
    // TODO: Implement global search
  }

  void _openSettingsTab() {
    _openTab(const DesktopTab(
      id: 'settings',
      title: 'الإعدادات',
      icon: Icons.settings_outlined,
      content: Center(child: Text('شاشة الإعدادات')),
    ));
  }

  void _showNotifications() {
    // TODO: Implement notifications panel
  }

  void _handleUserMenuAction(String value, AuthProvider authProvider) {
    switch (value) {
      case 'profile':
        _openTab(const DesktopTab(
          id: 'profile',
          title: 'الملف الشخصي',
          icon: Icons.person_outline,
          content: Center(child: Text('الملف الشخصي')),
        ));
        break;
      case 'logout':
        authProvider.signOut();
        break;
    }
  }
}

/// Represents a tab in the desktop interface
class DesktopTab {
  final String id;
  final String title;
  final IconData icon;
  final Widget content;
  final bool canClose;

  const DesktopTab({
    required this.id,
    required this.title,
    required this.icon,
    required this.content,
    this.canClose = true,
  });
}
