import 'package:flutter/material.dart';

import '../../core/utils/app_utils.dart';

/// القائمة الجانبية لتطبيق Windows
/// تحتوي على جميع أقسام التطبيق مع إمكانية الطي والتوسيع
class WindowsSidebar extends StatefulWidget {
  final bool isExpanded;
  final double width;
  final String currentScreen;
  final Function(String) onScreenChanged;
  final VoidCallback onToggle;

  const WindowsSidebar({
    Key? key,
    required this.isExpanded,
    required this.width,
    required this.currentScreen,
    required this.onScreenChanged,
    required this.onToggle,
  }) : super(key: key);

  @override
  State<WindowsSidebar> createState() => _WindowsSidebarState();
}

class _WindowsSidebarState extends State<WindowsSidebar> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _widthAnimation;
  
  // حالة توسيع الأقسام
  final Map<String, bool> _expandedSections = {
    'main': true,
    'inventory': false,
    'agents': false,
    'accounting': false,
    'reports': false,
    'settings': false,
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _widthAnimation = Tween<double>(
      begin: 64.0,
      end: 280.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    if (widget.isExpanded) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(WindowsSidebar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _widthAnimation,
      builder: (context, child) {
        return Container(
          width: _widthAnimation.value,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(2, 0),
              ),
            ],
          ),
          child: Column(
            children: [
              // زر الطي/التوسيع
              _buildToggleButton(),
              
              // قائمة الأقسام
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  children: [
                    _buildMainSection(),
                    _buildInventorySection(),
                    _buildAgentsSection(),
                    _buildAccountingSection(),
                    _buildReportsSection(),
                    const Divider(),
                    _buildSettingsSection(),
                  ],
                ),
              ),
              
              // معلومات إضافية في الأسفل
              if (widget.isExpanded) _buildFooter(),
            ],
          ),
        );
      },
    );
  }

  /// زر الطي/التوسيع
  Widget _buildToggleButton() {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          IconButton(
            onPressed: widget.onToggle,
            icon: Icon(
              widget.isExpanded ? Icons.menu_open : Icons.menu,
            ),
            tooltip: widget.isExpanded ? 'طي القائمة' : 'توسيع القائمة',
          ),
          if (widget.isExpanded) ...[
            const SizedBox(width: 8),
            Text(
              'القائمة الرئيسية',
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ],
        ],
      ),
    );
  }

  /// القسم الرئيسي
  Widget _buildMainSection() {
    return _buildSection(
      'main',
      'الرئيسية',
      Icons.home,
      [
        _buildMenuItem('dashboard', 'لوحة التحكم', Icons.dashboard),
        _buildMenuItem('overview', 'نظرة عامة', Icons.analytics),
        _buildMenuItem('quick_actions', 'إجراءات سريعة', Icons.flash_on),
      ],
    );
  }

  /// قسم إدارة المخزون
  Widget _buildInventorySection() {
    return _buildSection(
      'inventory',
      'إدارة المخزون',
      Icons.inventory,
      [
        _buildMenuItem('inventory_list', 'قائمة المخزون', Icons.list),
        _buildMenuItem('add_item', 'إضافة صنف', Icons.add_box),
        _buildMenuItem('categories', 'الفئات', Icons.category),
        _buildMenuItem('stock_movements', 'حركة المخزون', Icons.swap_horiz),
        _buildMenuItem('low_stock', 'مخزون منخفض', Icons.warning),
      ],
    );
  }

  /// قسم إدارة الوكلاء
  Widget _buildAgentsSection() {
    return _buildSection(
      'agents',
      'إدارة الوكلاء',
      Icons.people,
      [
        _buildMenuItem('agents_list', 'قائمة الوكلاء', Icons.list),
        _buildMenuItem('add_agent', 'إضافة وكيل', Icons.person_add),
        _buildMenuItem('agent_transactions', 'معاملات الوكلاء', Icons.receipt),
        _buildMenuItem('agent_payments', 'دفعات الوكلاء', Icons.payment),
        _buildMenuItem('agent_reports', 'تقارير الوكلاء', Icons.assessment),
      ],
    );
  }

  /// قسم النظام المحاسبي
  Widget _buildAccountingSection() {
    return _buildSection(
      'accounting',
      'النظام المحاسبي',
      Icons.receipt_long,
      [
        _buildMenuItem('journal', 'دفتر اليومية', Icons.book),
        _buildMenuItem('accounts', 'دليل الحسابات', Icons.account_tree),
        _buildMenuItem('trial_balance', 'ميزان المراجعة', Icons.balance),
        _buildMenuItem('financial_reports', 'التقارير المالية', Icons.trending_up),
        _buildMenuItem('invoices', 'الفواتير', Icons.description),
      ],
    );
  }

  /// قسم التقارير
  Widget _buildReportsSection() {
    return _buildSection(
      'reports',
      'التقارير',
      Icons.analytics,
      [
        _buildMenuItem('sales_reports', 'تقارير المبيعات', Icons.trending_up),
        _buildMenuItem('inventory_reports', 'تقارير المخزون', Icons.inventory_2),
        _buildMenuItem('financial_reports', 'التقارير المالية', Icons.account_balance),
        _buildMenuItem('custom_reports', 'تقارير مخصصة', Icons.tune),
      ],
    );
  }

  /// قسم الإعدادات
  Widget _buildSettingsSection() {
    return _buildSection(
      'settings',
      'الإعدادات',
      Icons.settings,
      [
        _buildMenuItem('general_settings', 'إعدادات عامة', Icons.settings),
        _buildMenuItem('user_management', 'إدارة المستخدمين', Icons.manage_accounts),
        _buildMenuItem('backup', 'النسخ الاحتياطي', Icons.backup),
        _buildMenuItem('about', 'حول التطبيق', Icons.info),
      ],
    );
  }

  /// بناء قسم قابل للطي
  Widget _buildSection(String sectionId, String title, IconData icon, List<Widget> children) {
    final isExpanded = _expandedSections[sectionId] ?? false;
    
    return Column(
      children: [
        _buildSectionHeader(sectionId, title, icon, isExpanded),
        if (widget.isExpanded && isExpanded) ...children,
      ],
    );
  }

  /// بناء رأس القسم
  Widget _buildSectionHeader(String sectionId, String title, IconData icon, bool isExpanded) {
    return InkWell(
      onTap: () {
        if (widget.isExpanded) {
          setState(() {
            _expandedSections[sectionId] = !isExpanded;
          });
        } else {
          // إذا كانت القائمة مطوية، توسيعها أولاً
          widget.onToggle();
        }
      },
      child: Container(
        height: 48,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Icon(icon, size: 20),
            if (widget.isExpanded) ...[
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Icon(
                isExpanded ? Icons.expand_less : Icons.expand_more,
                size: 20,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء عنصر القائمة
  Widget _buildMenuItem(String screenId, String title, IconData icon) {
    final isActive = widget.currentScreen == screenId;
    
    return InkWell(
      onTap: () => widget.onScreenChanged(screenId),
      child: Container(
        height: 40,
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: isActive 
              ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
              : null,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: isActive 
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              if (widget.isExpanded) ...[
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: isActive 
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurface,
                      fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// تذييل القائمة الجانبية
  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.cloud_done,
                size: 16,
                color: Colors.green,
              ),
              const SizedBox(width: 8),
              Text(
                'متزامن',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'الإصدار 1.0.0',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }
}
