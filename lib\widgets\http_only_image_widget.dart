import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../core/theme/desktop_theme.dart';

/// HTTP-only image widget that converts all HTTPS URLs to HTTP
class HttpOnlyImageWidget extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;

  const HttpOnlyImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.onTap,
  });

  /// Convert HTTPS URL to HTTP to bypass SSL completely
  String _convertToHttp(String url) {
    if (url.isEmpty || url.trim().isEmpty) {
      if (kDebugMode) {
        print('❌ HTTP-Only Image: Empty or null URL provided');
      }
      return '';
    }

    // Validate URL format
    try {
      final uri = Uri.parse(url);
      if (!uri.hasScheme || uri.host.isEmpty) {
        if (kDebugMode) {
          print('❌ HTTP-Only Image: Invalid URL format: $url');
        }
        return '';
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ HTTP-Only Image: URL parsing error: $e');
      }
      return '';
    }

    // Convert ALL HTTPS URLs to HTTP
    if (url.startsWith('https://')) {
      final httpUrl = url.replaceFirst('https://', 'http://');
      if (kDebugMode) {
        print('🔧 HTTP-Only Image: Converting HTTPS to HTTP');
        print('   Original: $url');
        print('   HTTP: $httpUrl');
      }
      return httpUrl;
    }

    return url;
  }

  @override
  Widget build(BuildContext context) {
    final httpUrl = _convertToHttp(imageUrl);

    Widget child;

    // Handle empty URLs
    if (httpUrl.isEmpty) {
      if (kDebugMode) {
        print('❌ HTTP-Only Image: Empty URL');
      }
      child = _buildErrorWidget(context);
    } else {
      // Use simple Image.network with HTTP URL
      child = Image.network(
        httpUrl,
        width: width,
        height: height,
        fit: fit,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return _buildLoadingWidget(context);
        },
        errorBuilder: (context, error, stackTrace) {
          if (kDebugMode) {
            print('❌ HTTP-Only Image: Error loading $httpUrl - $error');
          }
          return _buildErrorWidget(context);
        },
        headers: const {
          'User-Agent': 'El-Farhan-HTTP/1.0',
          'Accept': 'image/*',
          'Cache-Control': 'no-cache',
        },
      );
    }

    // Apply border radius if provided
    if (borderRadius != null) {
      child = ClipRRect(
        borderRadius: borderRadius!,
        child: child,
      );
    }

    // Apply tap handler if provided
    if (onTap != null) {
      child = InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        child: child,
      );
    }

    return child;
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return Container(
      width: width,
      height: height,
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: const Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    return Container(
      width: width,
      height: height,
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: const Icon(Icons.broken_image),
    );
  }
}

/// HTTP-only item image widget for thumbnails
class HttpOnlyItemImageWidget extends StatelessWidget {
  final String imageUrl;
  final double size;
  final VoidCallback? onTap;

  const HttpOnlyItemImageWidget({
    super.key,
    required this.imageUrl,
    this.size = 80,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return HttpOnlyImageWidget(
      imageUrl: imageUrl,
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(8.0),
      onTap: onTap,
    );
  }
}

/// Test widget to verify HTTP-only approach works
class HttpOnlyTestWidget extends StatelessWidget {
  const HttpOnlyTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Text('اختبار HTTP-Only:'),
        const SizedBox(height: 16),
        
        // Test with HTTP URL
        Container(
          width: 150,
          height: 100,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.green),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const HttpOnlyImageWidget(
            imageUrl: 'http://res.cloudinary.com/demo/image/upload/sample.jpg',
            fit: BoxFit.cover,
          ),
        ),
        
        const SizedBox(height: 8),
        const Text('HTTP URL (يجب أن تعمل)', style: TextStyle(fontSize: 12)),
        
        const SizedBox(height: 16),
        
        // Test with HTTPS URL (will be converted to HTTP)
        Container(
          width: 150,
          height: 100,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.blue),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const HttpOnlyImageWidget(
            imageUrl: 'https://res.cloudinary.com/demo/image/upload/sample.jpg',
            fit: BoxFit.cover,
          ),
        ),
        
        const SizedBox(height: 8),
        const Text('HTTPS URL (سيتم تحويلها إلى HTTP)', style: TextStyle(fontSize: 12)),
      ],
    );
  }
}
