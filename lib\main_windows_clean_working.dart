import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'dart:io';

// Firebase configuration
import 'firebase_options_windows.dart' as windows_options;

// Core providers
import 'providers/auth_provider.dart';
import 'services/data_service.dart';

// Network
import 'core/network/custom_http_client.dart';
import 'core/network/custom_cache_manager.dart';
import 'core/network/ultimate_ssl_bypass.dart';

// Core services

// Screens
import 'screens/auth/login_screen.dart';
import 'screens/desktop/desktop_main_screen.dart';

// Theme

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize SQLite FFI for Windows
  if (Platform.isWindows) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;

    // ULTIMATE SSL BYPASS - Convert all HTTPS to HTTP
    initializeUltimateSSLBypass();

    // Initialize custom HTTP client for image loading
    initializeImageHttpOverrides();
    CustomHttpClient().initialize();
    initializeCloudinaryCacheManager();
  }

  try {
    await Firebase.initializeApp(
      options: windows_options.DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialized successfully');
  } catch (e) {
    print('❌ Firebase initialization failed: $e');
  }

  runApp(const ElFarhanApp());
}

class ElFarhanApp extends StatelessWidget {
  const ElFarhanApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        Provider<DataService>(create: (context) => DataService.instance),
      ],
      child: MaterialApp(
        title: 'El Farhan Transport - Windows',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          fontFamily: 'Cairo',
          textTheme: const TextTheme(
            bodyLarge: TextStyle(fontSize: 16),
            bodyMedium: TextStyle(fontSize: 14),
          ),
          appBarTheme: const AppBarTheme(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            elevation: 2,
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        locale: const Locale('ar', 'SA'),
        supportedLocales: const [
          Locale('ar', 'SA'),
          Locale('en', 'US'),
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        home: Consumer<AuthProvider>(
          builder: (context, authProvider, child) {
            if (authProvider.isAuthenticated) {
              return const DesktopMainScreen();
            } else {
              return const LoginScreen();
            }
          },
        ),
      ),
    );
  }
}



// Fix SSL certificate issues on Windows for Cloudinary and Firebase
class CloudinaryHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final client = super.createHttpClient(context);

    // More aggressive SSL bypass for development
    client.badCertificateCallback = (X509Certificate cert, String host, int port) {
      if (kDebugMode) {
        print('🔒 SSL Certificate check for: $host:$port');
        print('🔒 Certificate subject: ${cert.subject}');
        print('🔒 Certificate issuer: ${cert.issuer}');
      }

      // Allow all Cloudinary and Firebase domains
      final trustedDomains = [
        'cloudinary.com',
        'res.cloudinary.com',
        'api.cloudinary.com',
        'firebaseapp.com',
        'googleapis.com',
        'firebase.googleapis.com',
        'firestore.googleapis.com',
        'storage.googleapis.com',
        'gstatic.com',
        'google.com',
      ];

      final isAllowed = trustedDomains.any((domain) => host.contains(domain));

      if (kDebugMode) {
        print('🔒 SSL Certificate ${isAllowed ? 'ALLOWED' : 'REJECTED'} for: $host');
      }

      return isAllowed;
    };

    // Enhanced timeout and connection settings
    client.connectionTimeout = const Duration(seconds: 60);
    client.idleTimeout = const Duration(seconds: 60);

    // Completely disable certificate verification for all domains
    client.badCertificateCallback = (cert, host, port) {
      if (kDebugMode) {
        print('🔓 SSL BYPASS: Allowing all certificates for $host:$port');
      }
      return true; // Allow ALL certificates (bypass SSL completely)
    };

    return client;
  }
}
