import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/advanced_report_service.dart';

/// شاشة التقارير البسيطة للويندوز
class SimpleReportsScreen extends StatefulWidget {
  const SimpleReportsScreen({super.key});

  @override
  State<SimpleReportsScreen> createState() => _SimpleReportsScreenState();
}

class _SimpleReportsScreenState extends State<SimpleReportsScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _currentReport;
  String _selectedReportType = 'sales';

  final List<Map<String, dynamic>> _reportTypes = [
    {
      'id': 'sales',
      'title': 'تقرير المبيعات',
      'icon': Icons.shopping_cart,
      'color': Colors.green,
    },
    {
      'id': 'inventory',
      'title': 'تقرير المخزون',
      'icon': Icons.inventory,
      'color': Colors.blue,
    },
    {
      'id': 'agents',
      'title': 'تقرير الوكلاء',
      'icon': Icons.people,
      'color': Colors.orange,
    },
    {
      'id': 'financial',
      'title': 'التقرير المالي',
      'icon': Icons.account_balance,
      'color': Colors.purple,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  size: 32,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  'التقارير والإحصائيات',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 16),
                      SizedBox(width: 4),
                      Text(
                        'Firebase متصل',
                        style: TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Report Type Selection
            Text(
              'اختر نوع التقرير:',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Report Type Cards
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _reportTypes.length,
                itemBuilder: (context, index) {
                  final reportType = _reportTypes[index];
                  final isSelected = _selectedReportType == reportType['id'];
                  
                  return Container(
                    width: 200,
                    margin: const EdgeInsets.only(right: 16),
                    child: Card(
                      elevation: isSelected ? 8 : 2,
                      color: isSelected ? reportType['color'].withOpacity(0.1) : null,
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            _selectedReportType = reportType['id'];
                            _currentReport = null;
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                reportType['icon'],
                                size: 32,
                                color: reportType['color'],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                reportType['title'],
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: isSelected ? reportType['color'] : null,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Generate Report Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _generateReport,
                icon: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.analytics),
                label: Text(
                  _isLoading ? 'جاري إنشاء التقرير...' : 'إنشاء التقرير',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Report Results
            if (_currentReport != null) ...[
              Text(
                'نتائج التقرير:',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 16),
              
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: _buildReportContent(),
                  ),
                ),
              ),
            ] else ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.analytics_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'اختر نوع التقرير واضغط "إنشاء التقرير" لعرض النتائج',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildReportContent() {
    if (_currentReport == null) return const SizedBox();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Report Header
          Row(
            children: [
              Icon(
                _getReportIcon(),
                size: 24,
                color: _getReportColor(),
              ),
              const SizedBox(width: 8),
              Text(
                _getReportTitle(),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                'تم إنشاؤه: ${_formatDateTime(_currentReport!['generatedAt'])}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          
          const Divider(height: 32),
          
          // Report Data
          ..._buildReportData(),
        ],
      ),
    );
  }

  List<Widget> _buildReportData() {
    final List<Widget> widgets = [];

    _currentReport!.forEach((key, value) {
      if (key == 'generatedAt') return;

      widgets.add(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  _translateKey(key),
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Expanded(
                flex: 3,
                child: Text(
                  _formatValue(value),
                  style: TextStyle(
                    color: Colors.grey[700],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });

    return widgets;
  }

  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final reportService = Provider.of<AdvancedReportService>(context, listen: false);
      Map<String, dynamic> report;

      final now = DateTime.now();
      final startDate = DateTime(now.year, now.month, 1);
      final endDate = now;

      switch (_selectedReportType) {
        case 'sales':
          report = await reportService.generateSalesReport(
            startDate: startDate,
            endDate: endDate,
          );
          break;
        case 'inventory':
          report = await reportService.generateInventoryReport();
          break;
        case 'agents':
          report = await reportService.generateAgentPerformanceReport(
            startDate: startDate,
            endDate: endDate,
          );
          break;
        case 'financial':
          report = await reportService.generateProfitLossReport(
            startDate: startDate,
            endDate: endDate,
          );
          break;
        default:
          report = {'error': 'نوع تقرير غير مدعوم'};
      }

      setState(() {
        _currentReport = report;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إنشاء التقرير: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  IconData _getReportIcon() {
    final reportType = _reportTypes.firstWhere(
      (type) => type['id'] == _selectedReportType,
      orElse: () => _reportTypes[0],
    );
    return reportType['icon'];
  }

  Color _getReportColor() {
    final reportType = _reportTypes.firstWhere(
      (type) => type['id'] == _selectedReportType,
      orElse: () => _reportTypes[0],
    );
    return reportType['color'];
  }

  String _getReportTitle() {
    final reportType = _reportTypes.firstWhere(
      (type) => type['id'] == _selectedReportType,
      orElse: () => _reportTypes[0],
    );
    return reportType['title'];
  }

  String _translateKey(String key) {
    const translations = {
      'totalSales': 'إجمالي المبيعات',
      'totalInvoices': 'عدد الفواتير',
      'averageInvoiceValue': 'متوسط قيمة الفاتورة',
      'totalItems': 'إجمالي الأصناف',
      'lowStockItems': 'أصناف منخفضة المخزون',
      'outOfStockItems': 'أصناف نفدت من المخزون',
      'totalValue': 'إجمالي القيمة',
      'totalAgents': 'إجمالي الوكلاء',
      'totalPayments': 'إجمالي المدفوعات',
      'totalOutstanding': 'إجمالي المستحقات',
      'collectionRate': 'معدل التحصيل',
    };
    return translations[key] ?? key;
  }

  String _formatValue(dynamic value) {
    if (value is double) {
      return '${value.toStringAsFixed(2)} ج.م';
    } else if (value is int) {
      return value.toString();
    } else if (value is Map) {
      return 'بيانات مفصلة (${value.length} عنصر)';
    } else {
      return value.toString();
    }
  }

  String _formatDateTime(String dateTimeString) {
    try {
      final dateTime = DateTime.parse(dateTimeString);
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateTimeString;
    }
  }
}
