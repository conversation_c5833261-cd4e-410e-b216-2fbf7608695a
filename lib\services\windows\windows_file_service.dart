import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// خدمة إدارة الملفات المحسنة للويندوز
class WindowsFileService {
  static WindowsFileService? _instance;
  static WindowsFileService get instance => _instance ??= WindowsFileService._();
  
  WindowsFileService._();

  bool _isInitialized = false;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('📁 Initializing Windows File Service...');
      }
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Windows File Service initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Windows File Service initialization failed: $e');
      }
    }
  }

  /// اختيار ملف واحد
  Future<File?> pickSingleFile({
    String? dialogTitle,
    List<String>? allowedExtensions,
    FileType fileType = FileType.any,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (kDebugMode) {
        print('📁 Opening file picker for single file...');
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: fileType,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
        dialogTitle: dialogTitle ?? 'اختر ملف',
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        
        if (kDebugMode) {
          print('✅ File selected: ${file.path}');
          print('   Size: ${await file.length()} bytes');
        }
        
        return file;
      } else {
        if (kDebugMode) {
          print('⚠️ No file selected');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking file: $e');
      }
      return null;
    }
  }

  /// اختيار عدة ملفات
  Future<List<File>> pickMultipleFiles({
    String? dialogTitle,
    List<String>? allowedExtensions,
    FileType fileType = FileType.any,
    int? maxFiles,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (kDebugMode) {
        print('📁 Opening file picker for multiple files...');
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: fileType,
        allowedExtensions: allowedExtensions,
        allowMultiple: true,
        dialogTitle: dialogTitle ?? 'اختر الملفات',
      );

      if (result != null) {
        List<File> files = [];
        int count = 0;
        
        for (var platformFile in result.files) {
          if (platformFile.path != null) {
            if (maxFiles != null && count >= maxFiles) break;
            files.add(File(platformFile.path!));
            count++;
          }
        }
        
        if (kDebugMode) {
          print('✅ ${files.length} files selected');
        }
        
        return files;
      } else {
        if (kDebugMode) {
          print('⚠️ No files selected');
        }
        return [];
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking multiple files: $e');
      }
      return [];
    }
  }

  /// حفظ ملف في مجلد محدد
  Future<String?> saveFile({
    required String fileName,
    required Uint8List data,
    String? dialogTitle,
    List<String>? allowedExtensions,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (kDebugMode) {
        print('💾 Opening save dialog...');
      }

      String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: dialogTitle ?? 'حفظ الملف',
        fileName: fileName,
        allowedExtensions: allowedExtensions,
      );

      if (outputFile != null) {
        final file = File(outputFile);
        await file.writeAsBytes(data);
        
        if (kDebugMode) {
          print('✅ File saved: $outputFile');
          print('   Size: ${data.length} bytes');
        }
        
        return outputFile;
      } else {
        if (kDebugMode) {
          print('⚠️ Save cancelled');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving file: $e');
      }
      return null;
    }
  }

  /// اختيار مجلد
  Future<String?> pickDirectory({
    String? dialogTitle,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (kDebugMode) {
        print('📂 Opening directory picker...');
      }

      String? selectedDirectory = await FilePicker.platform.getDirectoryPath(
        dialogTitle: dialogTitle ?? 'اختر مجلد',
      );

      if (selectedDirectory != null) {
        if (kDebugMode) {
          print('✅ Directory selected: $selectedDirectory');
        }
        return selectedDirectory;
      } else {
        if (kDebugMode) {
          print('⚠️ No directory selected');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking directory: $e');
      }
      return null;
    }
  }

  /// إنشاء مجلد في مسار التطبيق
  Future<Directory?> createAppDirectory(String folderName) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final newDir = Directory(path.join(appDir.path, folderName));
      
      if (!await newDir.exists()) {
        await newDir.create(recursive: true);
        if (kDebugMode) {
          print('✅ Directory created: ${newDir.path}');
        }
      }
      
      return newDir;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating directory: $e');
      }
      return null;
    }
  }

  /// نسخ ملف إلى مجلد التطبيق
  Future<File?> copyFileToAppDirectory(File sourceFile, String targetFolder) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final targetDir = Directory(path.join(appDir.path, targetFolder));
      
      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }
      
      final fileName = path.basename(sourceFile.path);
      final targetFile = File(path.join(targetDir.path, fileName));
      
      await sourceFile.copy(targetFile.path);
      
      if (kDebugMode) {
        print('✅ File copied: ${sourceFile.path} -> ${targetFile.path}');
      }
      
      return targetFile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error copying file: $e');
      }
      return null;
    }
  }

  /// حذف ملف
  Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        if (kDebugMode) {
          print('✅ File deleted: $filePath');
        }
        return true;
      } else {
        if (kDebugMode) {
          print('⚠️ File does not exist: $filePath');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting file: $e');
      }
      return false;
    }
  }

  /// الحصول على معلومات الملف
  Future<Map<String, dynamic>?> getFileInfo(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return null;
      }
      
      final stat = await file.stat();
      
      return {
        'path': filePath,
        'name': path.basename(filePath),
        'extension': path.extension(filePath),
        'size': stat.size,
        'modified': stat.modified,
        'accessed': stat.accessed,
        'type': stat.type.toString(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting file info: $e');
      }
      return null;
    }
  }

  /// قراءة ملف نصي
  Future<String?> readTextFile(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return null;
      }
      
      final content = await file.readAsString();
      
      if (kDebugMode) {
        print('✅ Text file read: $filePath (${content.length} characters)');
      }
      
      return content;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error reading text file: $e');
      }
      return null;
    }
  }

  /// كتابة ملف نصي
  Future<bool> writeTextFile(String filePath, String content) async {
    try {
      final file = File(filePath);
      
      // Create directory if it doesn't exist
      await file.parent.create(recursive: true);
      
      await file.writeAsString(content);
      
      if (kDebugMode) {
        print('✅ Text file written: $filePath (${content.length} characters)');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error writing text file: $e');
      }
      return false;
    }
  }

  /// الحصول على مسار مجلد التطبيق
  Future<String> getAppDocumentsPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  /// الحصول على مسار مجلد التحميلات
  Future<String?> getDownloadsPath() async {
    try {
      if (Platform.isWindows) {
        final userProfile = Platform.environment['USERPROFILE'];
        if (userProfile != null) {
          return path.join(userProfile, 'Downloads');
        }
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting downloads path: $e');
      }
      return null;
    }
  }

  /// فتح ملف بالتطبيق الافتراضي
  Future<bool> openFileWithDefaultApp(String filePath) async {
    try {
      if (Platform.isWindows) {
        await Process.run('start', ['', filePath], runInShell: true);
        if (kDebugMode) {
          print('✅ File opened with default app: $filePath');
        }
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error opening file with default app: $e');
      }
      return false;
    }
  }

  /// فتح مجلد في مستكشف الملفات
  Future<bool> openFolderInExplorer(String folderPath) async {
    try {
      if (Platform.isWindows) {
        await Process.run('explorer', [folderPath]);
        if (kDebugMode) {
          print('✅ Folder opened in explorer: $folderPath');
        }
        return true;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error opening folder in explorer: $e');
      }
      return false;
    }
  }
}
