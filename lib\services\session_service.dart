import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';

import '../models/user_model.dart';

class SessionService {
  static final SessionService _instance = SessionService._internal();
  factory SessionService() => _instance;
  SessionService._internal();

  static SessionService get instance => _instance;

  // Session data
  UserModel? _currentUser;
  String? _sessionToken;
  DateTime? _sessionStartTime;
  DateTime? _lastActivityTime;

  // Session settings
  static const Duration _sessionTimeout = Duration(hours: 8);
  static const Duration _extendedSessionTimeout = Duration(days: 30);
  static const String _sessionKey = 'user_session';
  static const String _rememberMeKey = 'remember_me';

  // Session monitoring
  Timer? _sessionTimer;
  bool _rememberMe = false;

  /// Initialize session service
  Future<void> initialize() async {
    await _loadSavedSession();
    _startSessionMonitoring();
    
    if (kDebugMode) {
      print('🔐 Session service initialized');
    }
  }

  /// Start session monitoring
  void _startSessionMonitoring() {
    _sessionTimer?.cancel();
    _sessionTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkSessionValidity();
    });
  }

  /// Check session validity
  void _checkSessionValidity() {
    if (_currentUser == null || _lastActivityTime == null) {
      return;
    }

    final now = DateTime.now();
    final sessionDuration = _rememberMe ? _extendedSessionTimeout : _sessionTimeout;
    
    if (now.difference(_lastActivityTime!) > sessionDuration) {
      _expireSession();
    }
  }

  /// Create new session
  Future<void> createSession({
    required UserModel user,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      _currentUser = user;
      _rememberMe = rememberMe;
      _sessionStartTime = DateTime.now();
      _lastActivityTime = DateTime.now();
      _sessionToken = _generateSessionToken(user.id, password);

      await _saveSession();
      _startSessionMonitoring();

      if (kDebugMode) {
        print('🔐 Session created for user: ${user.fullName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating session: $e');
      }
      rethrow;
    }
  }

  /// Generate session token
  String _generateSessionToken(String userId, String password) {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final data = '$userId:$password:$timestamp';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Update last activity time
  void updateActivity() {
    if (_currentUser != null) {
      _lastActivityTime = DateTime.now();
      _saveSession(); // Save updated activity time
    }
  }

  /// Save session to storage
  Future<void> _saveSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (_currentUser != null) {
        final sessionData = {
          'user': _currentUser!.toMap(),
          'sessionToken': _sessionToken,
          'sessionStartTime': _sessionStartTime?.toIso8601String(),
          'lastActivityTime': _lastActivityTime?.toIso8601String(),
          'rememberMe': _rememberMe,
        };

        await prefs.setString(_sessionKey, jsonEncode(sessionData));
        await prefs.setBool(_rememberMeKey, _rememberMe);

        if (kDebugMode) {
          print('💾 Session saved');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving session: $e');
      }
    }
  }

  /// Load saved session
  Future<void> _loadSavedSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionData = prefs.getString(_sessionKey);
      
      if (sessionData != null) {
        final session = jsonDecode(sessionData) as Map<String, dynamic>;
        
        _currentUser = UserModel.fromMap(session['user']);
        _sessionToken = session['sessionToken'];
        _sessionStartTime = session['sessionStartTime'] != null 
            ? DateTime.parse(session['sessionStartTime']) 
            : null;
        _lastActivityTime = session['lastActivityTime'] != null 
            ? DateTime.parse(session['lastActivityTime']) 
            : null;
        _rememberMe = session['rememberMe'] ?? false;

        // Check if session is still valid
        if (_isSessionValid()) {
          if (kDebugMode) {
            print('🔐 Session restored for user: ${_currentUser!.fullName}');
          }
        } else {
          await _clearSession();
          if (kDebugMode) {
            print('⏰ Session expired, cleared');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading session: $e');
      }
      await _clearSession();
    }
  }

  /// Check if current session is valid
  bool _isSessionValid() {
    if (_currentUser == null || _lastActivityTime == null) {
      return false;
    }

    final now = DateTime.now();
    final sessionDuration = _rememberMe ? _extendedSessionTimeout : _sessionTimeout;
    
    return now.difference(_lastActivityTime!) <= sessionDuration;
  }

  /// Expire current session
  void _expireSession() {
    if (kDebugMode) {
      print('⏰ Session expired for user: ${_currentUser?.fullName}');
    }
    
    _clearSession();
    
    // Notify about session expiration
    // This could trigger a callback to show login screen
  }

  /// Clear session data
  Future<void> _clearSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_sessionKey);
      
      _currentUser = null;
      _sessionToken = null;
      _sessionStartTime = null;
      _lastActivityTime = null;
      _rememberMe = false;

      _sessionTimer?.cancel();

      if (kDebugMode) {
        print('🧹 Session cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing session: $e');
      }
    }
  }

  /// Logout user
  Future<void> logout() async {
    if (kDebugMode) {
      print('👋 User logged out: ${_currentUser?.fullName}');
    }
    
    await _clearSession();
  }

  /// Extend session
  Future<void> extendSession() async {
    if (_currentUser != null) {
      _lastActivityTime = DateTime.now();
      await _saveSession();
      
      if (kDebugMode) {
        print('⏰ Session extended');
      }
    }
  }

  /// Get session info
  Map<String, dynamic> getSessionInfo() {
    if (_currentUser == null) {
      return {
        'isLoggedIn': false,
        'user': null,
        'sessionDuration': null,
        'timeRemaining': null,
        'rememberMe': false,
      };
    }

    final now = DateTime.now();
    final sessionDuration = _rememberMe ? _extendedSessionTimeout : _sessionTimeout;
    final timeRemaining = sessionDuration - now.difference(_lastActivityTime!);
    
    return {
      'isLoggedIn': true,
      'user': _currentUser!.toMap(),
      'sessionStartTime': _sessionStartTime?.toIso8601String(),
      'lastActivityTime': _lastActivityTime?.toIso8601String(),
      'sessionDuration': sessionDuration.inMinutes,
      'timeRemaining': timeRemaining.inMinutes,
      'rememberMe': _rememberMe,
      'sessionToken': _sessionToken,
    };
  }

  /// Auto-login with saved credentials
  Future<bool> attemptAutoLogin() async {
    if (_currentUser != null && _isSessionValid()) {
      updateActivity();
      return true;
    }
    
    return false;
  }

  /// Check if user is logged in
  bool get isLoggedIn => _currentUser != null && _isSessionValid();

  /// Get current user
  UserModel? get currentUser => _currentUser;

  /// Get session token
  String? get sessionToken => _sessionToken;

  /// Check if remember me is enabled
  bool get rememberMe => _rememberMe;

  /// Get session duration in minutes
  int get sessionDurationMinutes {
    if (_sessionStartTime == null || _lastActivityTime == null) {
      return 0;
    }
    return _lastActivityTime!.difference(_sessionStartTime!).inMinutes;
  }

  /// Get time remaining in minutes
  int get timeRemainingMinutes {
    if (!isLoggedIn) return 0;
    
    final now = DateTime.now();
    final sessionDuration = _rememberMe ? _extendedSessionTimeout : _sessionTimeout;
    final timeRemaining = sessionDuration - now.difference(_lastActivityTime!);
    
    return timeRemaining.inMinutes.clamp(0, sessionDuration.inMinutes);
  }

  /// Check if session is about to expire (less than 10 minutes)
  bool get isSessionAboutToExpire {
    return isLoggedIn && timeRemainingMinutes <= 10;
  }

  /// Refresh session with new user data
  Future<void> refreshUserData(UserModel updatedUser) async {
    if (_currentUser != null && _currentUser!.id == updatedUser.id) {
      _currentUser = updatedUser;
      await _saveSession();
      
      if (kDebugMode) {
        print('🔄 User data refreshed in session');
      }
    }
  }

  /// Get session statistics
  Map<String, dynamic> getSessionStatistics() {
    return {
      'totalSessions': 1, // This could be tracked over time
      'currentSessionDuration': sessionDurationMinutes,
      'averageSessionDuration': sessionDurationMinutes, // This could be calculated from history
      'lastLoginTime': _sessionStartTime?.toIso8601String(),
      'loginMethod': _rememberMe ? 'remembered' : 'manual',
    };
  }

  /// Dispose session service
  void dispose() {
    _sessionTimer?.cancel();
    
    if (kDebugMode) {
      print('🧹 Session service disposed');
    }
  }
}
