import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../../models/warehouse_model.dart';
import '../../models/item_model.dart';
import '../../services/data_service.dart';
import '../../core/constants/app_constants.dart';

class DetailedInventoryReport extends StatefulWidget {
  const DetailedInventoryReport({super.key});

  @override
  State<DetailedInventoryReport> createState() => _DetailedInventoryReportState();
}

class _DetailedInventoryReportState extends State<DetailedInventoryReport> {
  List<WarehouseModel> _warehouses = [];
  List<ItemModel> _items = [];
  bool _isLoading = true;
  String? _selectedWarehouseId;
  String _selectedStatus = 'all';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final dataService = DataService.instance;

      final warehouses = await dataService.getWarehouses();
      final items = await dataService.getItems();

      if (mounted) {
        setState(() {
          _warehouses = warehouses;
          _items = items;
          _isLoading = false;
        });

        if (kDebugMode) {
          print('📊 Inventory Report Data Loaded:');
          print('   - Warehouses: ${warehouses.length}');
          print('   - Items: ${items.length}');

          // Debug item statuses
          print('   🔍 DETAILED ITEM STATUS DEBUG:');
          for (int i = 0; i < items.length; i++) {
            final item = items[i];
            print('   [$i] ${item.brand} ${item.model}:');
            print('       - Status: "${item.status}"');
            print('       - Status length: ${item.status.length}');
            print('       - Status bytes: ${item.status.codeUnits}');
            print('       - Is available: ${item.status == 'متاح'}');
            print('       - Trimmed status: "${item.status.trim()}"');
            print('       - Is trimmed available: ${item.status.trim() == 'متاح'}');
            print('       - Current warehouse: ${item.currentWarehouseId}');
            print('       - Purchase price: ${item.purchasePrice}');
            print('       - Suggested selling price: ${item.suggestedSellingPrice}');
          }

          // Debug status comparison with different variations
          final statusVariations = [
            'متاح',
            'متاح ',
            ' متاح',
            ' متاح ',
            'available',
            'Available',
            'AVAILABLE',
            'مباع',
            'محجوز',
          ];

          print('   🔍 STATUS VARIATIONS CHECK:');
          for (final status in statusVariations) {
            final count = items.where((item) => item.status == status).length;
            final trimmedCount = items.where((item) => item.status.trim() == status.trim()).length;
            if (count > 0 || trimmedCount > 0) {
              print('   📊 Status "$status": exact=$count, trimmed=$trimmedCount');
            }
          }

          // Get unique statuses
          final uniqueStatuses = items.map((item) => item.status).toSet().toList();
          print('   🔍 ALL UNIQUE STATUSES: $uniqueStatuses');

          // Count with trimmed comparison
          final availableCount = items.where((item) => item.status.trim() == 'متاح').length;
          final soldCount = items.where((item) => item.status.trim() == 'مباع').length;
          final reservedCount = items.where((item) => item.status.trim() == 'محجوز').length;

          print('   📊 FINAL COUNTS (trimmed):');
          print('   - Available items (متاح): $availableCount');
          print('   - Sold items (مباع): $soldCount');
          print('   - Reserved items (محجوز): $reservedCount');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      if (kDebugMode) {
        print('❌ Error loading inventory data: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.inventory_2, size: 24),
            ),
            const SizedBox(width: 12),
            const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'تقرير المخزون التفصيلي',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                Text(
                  'عرض تفصيلي لجميع الأصناف',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
          ],
        ),
        backgroundColor: const Color(0xFF1565C0),
        foregroundColor: Colors.white,
        elevation: 4,
        toolbarHeight: 70,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildReportContent(),
    );
  }

  Widget _buildReportContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFiltersCard(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildSummaryCards(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildInventoryTable(),
        ],
      ),
    );
  }

  Widget _buildFiltersCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.filter_list, color: Color(0xFF1565C0), size: 24),
              SizedBox(width: 8),
              Text(
                'فلاتر التقرير',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في الأصناف...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildWarehouseDropdown(),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatusDropdown(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedWarehouseId,
          hint: const Text('اختر المخزن'),
          isExpanded: true,
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('جميع المخازن'),
            ),
            ..._warehouses.map((warehouse) => DropdownMenuItem<String>(
              value: warehouse.id,
              child: Text(warehouse.name),
            )),
          ],
          onChanged: (value) {
            setState(() {
              _selectedWarehouseId = value;
            });
          },
        ),
      ),
    );
  }

  Widget _buildStatusDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedStatus,
          isExpanded: true,
          items: const [
            DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
            DropdownMenuItem(value: 'متاح', child: Text('متاح')),
            DropdownMenuItem(value: 'مباع', child: Text('مباع')),
            DropdownMenuItem(value: 'محجوز', child: Text('محجوز')),
          ],
          onChanged: (value) {
            setState(() {
              _selectedStatus = value!;
            });
          },
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final filteredItems = _getFilteredItems();
    final availableCount = filteredItems.where((item) => item.status.trim() == 'متاح').length;
    final soldCount = filteredItems.where((item) => item.status.trim() == 'مباع').length;
    final reservedCount = filteredItems.where((item) => item.status.trim() == 'محجوز').length;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'الأصناف المتاحة',
            availableCount.toString(),
            Colors.green,
            Icons.check_circle,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'الأصناف المباعة',
            soldCount.toString(),
            Colors.red,
            Icons.shopping_cart,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'الأصناف المحجوزة',
            reservedCount.toString(),
            Colors.orange,
            Icons.bookmark,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الأصناف',
            filteredItems.length.toString(),
            const Color(0xFF1565C0),
            Icons.inventory_2,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: color.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryTable() {
    final filteredItems = _getFilteredItems();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Icon(Icons.table_chart, color: Color(0xFF1565C0), size: 24),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل المخزون (${filteredItems.length} صنف)',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          if (filteredItems.isEmpty)
            const Padding(
              padding: EdgeInsets.all(40),
              child: Center(
                child: Text(
                  'لا توجد أصناف',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ),
            )
          else
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('الصنف')),
                  DataColumn(label: Text('الماركة')),
                  DataColumn(label: Text('الموديل')),
                  DataColumn(label: Text('اللون')),
                  DataColumn(label: Text('المخزن')),
                  DataColumn(label: Text('الحالة')),
                  DataColumn(label: Text('رقم الشاسيه')),
                ],
                rows: filteredItems.map((item) {
                  final warehouse = _warehouses.firstWhere(
                    (w) => w.id == item.currentWarehouseId,
                    orElse: () => WarehouseModel(
                      id: '',
                      name: 'غير محدد',
                      type: 'unknown',
                      address: '',
                      createdAt: DateTime.now(),
                      updatedAt: DateTime.now(),
                    ),
                  );

                  return DataRow(
                    cells: [
                      DataCell(Text(item.motorFingerprintText)),
                      DataCell(Text(item.brand)),
                      DataCell(Text(item.model)),
                      DataCell(Text(item.color)),
                      DataCell(Text(warehouse.name)),
                      DataCell(_buildStatusChip(item.status)),
                      DataCell(Text(item.chassisNumber)),
                    ],
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    IconData icon;

    switch (status) {
      case 'متاح':
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case 'مباع':
        color = Colors.red;
        icon = Icons.shopping_cart;
        break;
      case 'محجوز':
        color = Colors.orange;
        icon = Icons.bookmark;
        break;
      default:
        color = Colors.grey;
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            status,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  List<ItemModel> _getFilteredItems() {
    var filtered = _items.where((item) {
      // Filter by warehouse
      if (_selectedWarehouseId != null && item.currentWarehouseId != _selectedWarehouseId) {
        return false;
      }

      // Filter by status (using trimmed comparison)
      if (_selectedStatus != 'all' && item.status.trim() != _selectedStatus) {
        return false;
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return item.motorFingerprintText.toLowerCase().contains(query) ||
               item.brand.toLowerCase().contains(query) ||
               item.model.toLowerCase().contains(query) ||
               item.color.toLowerCase().contains(query) ||
               item.chassisNumber.toLowerCase().contains(query);
      }

      return true;
    }).toList();

    // Sort by creation date (newest first)
    filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return filtered;
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة الطباعة قيد التطوير')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة التصدير قيد التطوير')),
    );
  }
}
