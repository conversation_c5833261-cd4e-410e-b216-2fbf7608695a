import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'dart:convert';

/// خدمة التقارير المتقدمة
class AdvancedReportService {
  static final AdvancedReportService _instance = AdvancedReportService._internal();
  factory AdvancedReportService() => _instance;
  AdvancedReportService._internal();

  static AdvancedReportService get instance => _instance;

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (kDebugMode) {
      print('🔄 Initializing AdvancedReportService...');
    }
    
    try {
      // تهيئة الخدمة
      if (kDebugMode) {
        print('✅ AdvancedReportService initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize AdvancedReportService: $e');
      }
    }
  }

  /// إنشاء تقرير مبيعات متقدم
  Future<Map<String, dynamic>> generateAdvancedSalesReport({
    required DateTime startDate,
    required DateTime endDate,
    String? agentId,
    String? warehouseId,
  }) async {
    try {
      Query query = _firestore.collection('invoices')
          .where('date', isGreaterThanOrEqualTo: startDate)
          .where('date', isLessThanOrEqualTo: endDate);

      if (agentId != null) {
        query = query.where('agentId', isEqualTo: agentId);
      }

      if (warehouseId != null) {
        query = query.where('warehouseId', isEqualTo: warehouseId);
      }

      final QuerySnapshot snapshot = await query.get();
      
      double totalSales = 0;
      int totalInvoices = snapshot.docs.length;
      Map<String, double> salesByAgent = {};
      Map<String, double> salesByProduct = {};
      Map<String, int> quantityByProduct = {};

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final double invoiceTotal = (data['total'] ?? 0).toDouble();
        totalSales += invoiceTotal;

        // تجميع المبيعات حسب الوكيل
        final String agentName = data['agentName'] ?? 'غير محدد';
        salesByAgent[agentName] = (salesByAgent[agentName] ?? 0) + invoiceTotal;

        // تجميع المبيعات حسب المنتج
        final List<dynamic> items = data['items'] ?? [];
        for (var item in items) {
          final String productName = item['name'] ?? 'غير محدد';
          final double itemTotal = (item['total'] ?? 0).toDouble();
          final int quantity = (item['quantity'] ?? 0).toInt();

          salesByProduct[productName] = (salesByProduct[productName] ?? 0) + itemTotal;
          quantityByProduct[productName] = (quantityByProduct[productName] ?? 0) + quantity;
        }
      }

      return {
        'totalSales': totalSales,
        'totalInvoices': totalInvoices,
        'averageInvoiceValue': totalInvoices > 0 ? totalSales / totalInvoices : 0,
        'salesByAgent': salesByAgent,
        'salesByProduct': salesByProduct,
        'quantityByProduct': quantityByProduct,
        'period': {
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
        },
        'generatedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating advanced sales report: $e');
      }
      rethrow;
    }
  }

  /// إنشاء تقرير مخزون متقدم
  Future<Map<String, dynamic>> generateAdvancedInventoryReport({
    String? warehouseId,
    String? categoryId,
  }) async {
    try {
      Query query = _firestore.collection('inventory');

      if (warehouseId != null) {
        query = query.where('warehouseId', isEqualTo: warehouseId);
      }

      if (categoryId != null) {
        query = query.where('categoryId', isEqualTo: categoryId);
      }

      final QuerySnapshot snapshot = await query.get();
      
      int totalItems = snapshot.docs.length;
      int lowStockItems = 0;
      int outOfStockItems = 0;
      double totalValue = 0;
      Map<String, int> itemsByCategory = {};
      Map<String, double> valueByCategory = {};

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final int quantity = (data['quantity'] ?? 0).toInt();
        final double price = (data['price'] ?? 0).toDouble();
        final int minStock = (data['minStock'] ?? 0).toInt();
        final String category = data['category'] ?? 'غير محدد';

        if (quantity == 0) {
          outOfStockItems++;
        } else if (quantity <= minStock) {
          lowStockItems++;
        }

        final double itemValue = quantity * price;
        totalValue += itemValue;

        itemsByCategory[category] = (itemsByCategory[category] ?? 0) + 1;
        valueByCategory[category] = (valueByCategory[category] ?? 0) + itemValue;
      }

      return {
        'totalItems': totalItems,
        'lowStockItems': lowStockItems,
        'outOfStockItems': outOfStockItems,
        'totalValue': totalValue,
        'itemsByCategory': itemsByCategory,
        'valueByCategory': valueByCategory,
        'stockStatus': {
          'inStock': totalItems - outOfStockItems,
          'lowStock': lowStockItems,
          'outOfStock': outOfStockItems,
        },
        'generatedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating advanced inventory report: $e');
      }
      rethrow;
    }
  }

  /// إنشاء تقرير وكلاء متقدم
  Future<Map<String, dynamic>> generateAdvancedAgentsReport({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // الحصول على بيانات الوكلاء
      final QuerySnapshot agentsSnapshot = await _firestore.collection('agents').get();
      
      Map<String, dynamic> agentsData = {};
      
      for (var doc in agentsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final String agentId = doc.id;
        final String agentName = data['name'] ?? 'غير محدد';
        
        // الحصول على مبيعات الوكيل
        Query salesQuery = _firestore.collection('invoices')
            .where('agentId', isEqualTo: agentId);
            
        if (startDate != null) {
          salesQuery = salesQuery.where('date', isGreaterThanOrEqualTo: startDate);
        }
        
        if (endDate != null) {
          salesQuery = salesQuery.where('date', isLessThanOrEqualTo: endDate);
        }
        
        final QuerySnapshot salesSnapshot = await salesQuery.get();
        
        double totalSales = 0;
        int totalInvoices = salesSnapshot.docs.length;
        
        for (var saleDoc in salesSnapshot.docs) {
          final saleData = saleDoc.data() as Map<String, dynamic>;
          totalSales += (saleData['total'] ?? 0).toDouble();
        }
        
        agentsData[agentId] = {
          'name': agentName,
          'totalSales': totalSales,
          'totalInvoices': totalInvoices,
          'averageInvoiceValue': totalInvoices > 0 ? totalSales / totalInvoices : 0,
          'currentBalance': data['balance'] ?? 0,
          'phone': data['phone'] ?? '',
          'address': data['address'] ?? '',
        };
      }

      return {
        'agentsData': agentsData,
        'totalAgents': agentsData.length,
        'period': startDate != null && endDate != null ? {
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
        } : null,
        'generatedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating advanced agents report: $e');
      }
      rethrow;
    }
  }

  /// إنشاء تقرير مالي متقدم
  Future<Map<String, dynamic>> generateAdvancedFinancialReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      // الحصول على المبيعات
      final QuerySnapshot salesSnapshot = await _firestore.collection('invoices')
          .where('date', isGreaterThanOrEqualTo: startDate)
          .where('date', isLessThanOrEqualTo: endDate)
          .get();

      // الحصول على المدفوعات
      final QuerySnapshot paymentsSnapshot = await _firestore.collection('payments')
          .where('date', isGreaterThanOrEqualTo: startDate)
          .where('date', isLessThanOrEqualTo: endDate)
          .get();

      double totalSales = 0;
      double totalPayments = 0;
      double totalOutstanding = 0;

      // حساب إجمالي المبيعات
      for (var doc in salesSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        totalSales += (data['total'] ?? 0).toDouble();
      }

      // حساب إجمالي المدفوعات
      for (var doc in paymentsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        totalPayments += (data['amount'] ?? 0).toDouble();
      }

      totalOutstanding = totalSales - totalPayments;

      return {
        'totalSales': totalSales,
        'totalPayments': totalPayments,
        'totalOutstanding': totalOutstanding,
        'collectionRate': totalSales > 0 ? (totalPayments / totalSales) * 100 : 0,
        'period': {
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
        },
        'generatedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating advanced financial report: $e');
      }
      rethrow;
    }
  }

  /// تصدير التقرير إلى JSON
  String exportReportToJson(Map<String, dynamic> reportData) {
    try {
      return jsonEncode(reportData);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error exporting report to JSON: $e');
      }
      rethrow;
    }
  }

  /// تنسيق التاريخ للعرض
  String formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  /// تنسيق المبلغ للعرض
  String formatCurrency(double amount) {
    return NumberFormat('#,##0.00').format(amount);
  }

  /// إنشاء تقرير مبيعات بسيط
  Future<Map<String, dynamic>> generateSalesReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await generateAdvancedSalesReport(
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// إنشاء تقرير مخزون بسيط
  Future<Map<String, dynamic>> generateInventoryReport() async {
    return await generateAdvancedInventoryReport();
  }

  /// إنشاء تقرير أداء الوكلاء
  Future<Map<String, dynamic>> generateAgentPerformanceReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await generateAdvancedAgentsReport(
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// إنشاء تقرير الأرباح والخسائر
  Future<Map<String, dynamic>> generateProfitLossReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await generateAdvancedFinancialReport(
      startDate: startDate,
      endDate: endDate,
    );
  }
}
