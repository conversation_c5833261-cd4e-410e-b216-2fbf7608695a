# Al Farhan Transport - إعداد الإنتاج

## 🔧 **إعدادات التطبيق للإنتاج**

### 1. تحديث معلومات التطبيق

#### في `pubspec.yaml`:
```yaml
name: el_farhan_app
description: Al Farhan Light Transport - تطبيق إدارة النقل الخفيف
version: 1.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'
  flutter: ">=3.24.0"
```

#### في `android/app/build.gradle`:
```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        applicationId "com.alfarhan.transport"
        minSdkVersion 23
        targetSdkVersion 34
        versionCode 1
        versionName "1.0.0"
        
        // تحسينات الأداء
        multiDexEnabled true
        
        // دعم اللغة العربية
        resConfigs "ar", "en"
    }
    
    buildTypes {
        release {
            // تفعيل التحسين
            minifyEnabled true
            shrinkResources true
            
            // إعدادات ProGuard
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // توقيع التطبيق
            signingConfig signingConfigs.release
        }
    }
    
    // إعدادات التوقيع
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
}
```

### 2. إعداد ProGuard للحماية

#### في `android/app/proguard-rules.pro`:
```proguard
# Flutter
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Camera plugin
-keep class io.flutter.plugins.camera.** { *; }

# ML Kit
-keep class com.google.mlkit.** { *; }

# Prevent obfuscation of model classes
-keep class com.alfarhan.transport.models.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}
```

### 3. تحسين الأداء

#### في `android/app/src/main/AndroidManifest.xml`:
```xml
<application
    android:name="${applicationName}"
    android:exported="false"
    android:icon="@mipmap/ic_launcher"
    android:label="Al Farhan Transport"
    android:requestLegacyExternalStorage="true"
    android:usesCleartextTraffic="false"
    android:allowBackup="false"
    android:hardwareAccelerated="true"
    android:largeHeap="true">
    
    <!-- تحسين الأداء -->
    <meta-data
        android:name="io.flutter.embedding.android.NormalTheme"
        android:resource="@style/NormalTheme" />
    
    <!-- إعدادات Firebase -->
    <meta-data
        android:name="com.google.firebase.messaging.default_notification_icon"
        android:resource="@drawable/ic_notification" />
    
    <meta-data
        android:name="com.google.firebase.messaging.default_notification_color"
        android:resource="@color/colorAccent" />
        
    <!-- تحسين الكاميرا -->
    <meta-data
        android:name="com.google.mlkit.vision.DEPENDENCIES"
        android:value="ocr" />
</application>

<!-- الصلاحيات المطلوبة -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.VIBRATE" />

<!-- ميزات الجهاز -->
<uses-feature
    android:name="android.hardware.camera"
    android:required="true" />
<uses-feature
    android:name="android.hardware.camera.autofocus"
    android:required="false" />
```

### 4. إعداد الأيقونات

#### إنشاء أيقونات التطبيق:
```bash
# تثبيت flutter_launcher_icons
flutter pub add dev:flutter_launcher_icons

# إضافة إعدادات في pubspec.yaml
flutter_icons:
  android: true
  ios: false
  image_path: "assets/icons/app_icon.png"
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/icons/app_icon_foreground.png"

# إنشاء الأيقونات
flutter pub run flutter_launcher_icons:main
```

### 5. إعداد الشاشة الافتتاحية

#### في `android/app/src/main/res/drawable/launch_background.xml`:
```xml
<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@android:color/white" />
    <item>
        <bitmap
            android:gravity="center"
            android:src="@drawable/splash_logo" />
    </item>
</layer-list>
```

### 6. إعداد الألوان والثيمات

#### في `android/app/src/main/res/values/colors.xml`:
```xml
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorPrimary">#2196F3</color>
    <color name="colorPrimaryDark">#1976D2</color>
    <color name="colorAccent">#FF9800</color>
    <color name="backgroundColor">#FFFFFF</color>
</resources>
```

### 7. تحسين حجم التطبيق

#### في `pubspec.yaml`:
```yaml
flutter:
  uses-material-design: true
  
  # تحسين الأصول
  assets:
    - assets/images/
    - assets/icons/
  
  # إزالة الخطوط غير المستخدمة
  fonts: []
```

#### أوامر التحسين:
```bash
# تحليل حجم التطبيق
flutter build apk --analyze-size

# بناء مع تحسين الحجم
flutter build apk --release --shrink

# بناء منفصل لكل معمارية
flutter build apk --split-per-abi --release
```

### 8. إعداد الأمان

#### تشفير البيانات الحساسة:
```dart
// في lib/core/constants/app_constants.dart
class AppConstants {
  // استخدم متغيرات البيئة للمفاتيح الحساسة
  static const String encryptionKey = String.fromEnvironment('ENCRYPTION_KEY');
  static const String apiKey = String.fromEnvironment('API_KEY');
  
  // إعدادات الإنتاج
  static const bool isProduction = bool.fromEnvironment('PRODUCTION', defaultValue: false);
  static const bool enableLogging = !isProduction;
}
```

#### إخفاء المفاتيح الحساسة:
```bash
# بناء مع متغيرات البيئة
flutter build apk --release --dart-define=PRODUCTION=true --dart-define=ENCRYPTION_KEY=your_key
```

### 9. إعداد التحليلات والمراقبة

#### Firebase Analytics:
```dart
// في main.dart
import 'package:firebase_analytics/firebase_analytics.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة Firebase
  await Firebase.initializeApp();
  
  // تفعيل Analytics في الإنتاج فقط
  if (AppConstants.isProduction) {
    FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
  }
  
  runApp(MyApp());
}
```

#### Firebase Crashlytics:
```dart
// إعداد تقارير الأخطاء
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

void main() async {
  // تسجيل الأخطاء التلقائي
  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;
  
  // تسجيل الأخطاء غير المعالجة
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
}
```

### 10. اختبار الإنتاج

#### قائمة فحص ما قبل النشر:
- [ ] اختبار على أجهزة مختلفة
- [ ] اختبار الوضع الأوفلاين
- [ ] اختبار الأداء تحت الضغط
- [ ] فحص استهلاك البطارية
- [ ] اختبار الأمان
- [ ] فحص تسريب الذاكرة
- [ ] اختبار التحديثات التلقائية

#### أوامر الاختبار:
```bash
# اختبار الوحدة
flutter test

# اختبار التكامل
flutter test integration_test/

# اختبار الأداء
flutter drive --target=test_driver/perf_test.dart
```

### 11. إعداد التحديثات التلقائية

#### في `android/app/build.gradle`:
```gradle
dependencies {
    implementation 'com.google.android.play:core:1.10.3'
}
```

#### في التطبيق:
```dart
// فحص التحديثات
import 'package:in_app_update/in_app_update.dart';

void checkForUpdates() async {
  try {
    final info = await InAppUpdate.checkForUpdate();
    if (info.updateAvailability == UpdateAvailability.updateAvailable) {
      await InAppUpdate.performImmediateUpdate();
    }
  } catch (e) {
    print('Error checking for updates: $e');
  }
}
```

### 12. إعداد النسخ الاحتياطي التلقائي

```dart
// جدولة النسخ الاحتياطي
import 'package:workmanager/workmanager.dart';

void setupBackgroundTasks() {
  Workmanager().initialize(callbackDispatcher);
  
  // نسخ احتياطي يومي
  Workmanager().registerPeriodicTask(
    "daily-backup",
    "dailyBackup",
    frequency: Duration(hours: 24),
  );
}

void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case "dailyBackup":
        await BackupService.instance.createIncrementalBackup();
        break;
    }
    return Future.value(true);
  });
}
```

---

## 📋 **قائمة فحص الإنتاج النهائية**

### الوظائف:
- [ ] جميع الميزات تعمل بشكل صحيح
- [ ] لا توجد أخطاء في وضع الإنتاج
- [ ] الأداء مقبول على الأجهزة المختلفة

### الأمان:
- [ ] البيانات الحساسة مشفرة
- [ ] لا توجد مفاتيح API مكشوفة
- [ ] قواعد Firebase محدثة

### التوافق:
- [ ] يعمل على Android 6.0+
- [ ] يدعم الشاشات المختلفة
- [ ] النصوص العربية صحيحة

### الأداء:
- [ ] بدء التطبيق < 3 ثوانٍ
- [ ] استهلاك الذاكرة < 200MB
- [ ] حجم التطبيق < 50MB

### المراقبة:
- [ ] Firebase Analytics مفعل
- [ ] Crashlytics يعمل
- [ ] تقارير الأداء متاحة

**✅ جاهز للنشر!**
