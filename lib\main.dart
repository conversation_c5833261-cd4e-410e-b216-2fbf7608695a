import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// Firebase configuration
import 'firebase_options.dart';

// Core theme
import 'core/theme/app_theme.dart';

// Auth provider
import 'providers/auth_provider.dart';

// Services
import 'services/local_database_service.dart';
import 'services/firebase_service.dart';
import 'services/accounting_journal_service.dart';
import 'services/accounting_integration_service.dart';
import 'services/image_service.dart';
import 'services/enhanced_notification_service.dart';
import 'services/auto_sync_service.dart';
import 'services/sync_service.dart';
import 'services/security_service.dart';
import 'services/auth_service.dart';
import 'services/session_service.dart';

// Android screens
import 'screens/home/<USER>';
import 'screens/auth/login_screen.dart';

/// ���� ���� ����� El Farhan (��������� ������ �� Firebase)
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kDebugMode) {
    print('?? Starting El Farhan Android App with Firebase...');
  }

  // Initialize Firebase with Windows-compatible options
  try {
    FirebaseOptions options;
    if (defaultTargetPlatform == TargetPlatform.windows) {
      // Use Windows-specific options (Web SDK)
      options = windows_options.DefaultFirebaseOptions.windows;
    } else {
      // Use platform-specific options for other platforms
      options = DefaultFirebaseOptions.currentPlatform;
    }

    await Firebase.initializeApp(options: options);
    if (kDebugMode) {
      print('🔥 Firebase initialized successfully for ${defaultTargetPlatform.name}');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Firebase initialization failed: $e');
    }
  }

  // Initialize all services
  await _initializeServices();

  // Run the app
  runApp(const ElFarhanApp());
}

/// تهيئة جميع الخدمات
Future<void> _initializeServices() async {
  try {
    if (kDebugMode) {
      print('🔄 Initializing services...');
    }

    // Initialize local database first
    await LocalDatabaseService.instance.initialize();

    // Initialize Firebase service
    await FirebaseService.instance.initialize();

    // Initialize all other services
    await AccountingJournalService.instance.initialize();
    await AccountingIntegrationService.instance.initialize();
    await ImageService.instance.initialize(); // إضافة ImageService
    await EnhancedNotificationService.instance.initialize();
    await AutoSyncService.instance.initialize();
    await SyncService.instance.initialize();
    await SecurityService.instance.initialize();
    await AuthService.instance.initialize();
    await SessionService.instance.initialize();

    if (kDebugMode) {
      print('✅ All services initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('⚠️ Services initialization failed: $e');
    }
  }
}

/// ������� ������� ������ ��������� �� Firebase
class ElFarhanApp extends StatelessWidget {
  const ElFarhanApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Auth provider
        ChangeNotifierProvider(create: (_) => AuthProvider()),

        // Service providers
        Provider(create: (_) => LocalDatabaseService.instance),
        Provider(create: (_) => FirebaseService.instance),
        Provider(create: (_) => AccountingJournalService.instance),
        Provider(create: (_) => AccountingIntegrationService.instance),
        Provider(create: (_) => EnhancedNotificationService.instance),
        Provider(create: (_) => AutoSyncService.instance),
        Provider(create: (_) => SyncService.instance),
        Provider(create: (_) => SecurityService.instance),
        Provider(create: (_) => AuthService.instance),
        Provider(create: (_) => SessionService.instance),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            title: '�� ����� ����� ������',
            debugShowCheckedModeBanner: false,
            
            // Use the app theme
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            
            // Arabic localization
            locale: const Locale('ar', 'SA'),
            supportedLocales: const [
              Locale('ar', 'SA'),
              Locale('en', 'US'),
            ],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            
            // Main screen - use original Android HomeScreen
            home: authProvider.isAuthenticated 
                ? const HomeScreen() 
                : const LoginScreen(),
          );
        },
      ),
    );
  }
}
