import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// Firebase configuration
import 'firebase_options.dart';

// Core theme
import 'core/theme/app_theme.dart';

// Auth provider
import 'providers/auth_provider.dart';

// Services
import 'services/local_database_service.dart';
import 'services/firebase_service.dart';
import 'services/accounting_journal_service.dart';
import 'services/android/android_image_service.dart';
import 'services/android/android_session_service.dart';
import 'services/enhanced_notification_service.dart';
import 'services/auto_sync_service.dart';
import 'services/security_service.dart';
import 'services/auth_service.dart';

// Android screens
import 'screens/home/<USER>';
import 'screens/auth/login_screen.dart';

/// تطبيق آل فرحان الأصلي للأندرويد
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kDebugMode) {
    print('🚀 Starting El Farhan Android App...');
  }

  // Initialize Firebase
  try {
    // فحص ما إذا كان Firebase مهيأ بالفعل
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      if (kDebugMode) {
        print('🔥 Firebase initialized successfully');
      }
    } else {
      if (kDebugMode) {
        print('🔥 Firebase already initialized, using existing instance');
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Firebase initialization failed: $e');
    }
  }

  // Initialize all services
  await _initializeServices();

  // Run the app
  runApp(const ElFarhanApp());
}

/// تهيئة جميع الخدمات بشكل محسن للأداء
Future<void> _initializeServices() async {
  try {
    if (kDebugMode) {
      print('🔧 Initializing services with performance optimization...');
    }

    // المرحلة 1: الخدمات الأساسية (متتالية)
    await LocalDatabaseService.instance.initialize();
    if (kDebugMode) {
      print('✅ LocalDatabaseService initialized');
    }

    await FirebaseService.instance.initialize();
    if (kDebugMode) {
      print('✅ FirebaseService initialized');
    }

    // المرحلة 2: الخدمات المستقلة (متوازية)
    final parallelServices = await Future.wait([
      _initializeServiceSafely('AccountingJournalService', () => AccountingJournalService.instance.initialize()),
      _initializeServiceSafely('AndroidImageService', () => AndroidImageService.instance.initialize()),
      _initializeServiceSafely('SecurityService', () => SecurityService.instance.initialize()),
      _initializeServiceSafely('AuthService', () => AuthService.instance.initialize()),
    ]);

    // المرحلة 3: الخدمات التي تعتمد على الخدمات السابقة (متوازية)
    final dependentServices = await Future.wait([
      _initializeServiceSafely('NotificationService', () => EnhancedNotificationService.instance.initialize()),
      _initializeServiceSafely('AutoSyncService', () => AutoSyncService.instance.initialize()),
      _initializeServiceSafely('AndroidSessionService', () => AndroidSessionService.instance.initialize()),
    ]);

    if (kDebugMode) {
      print('🎉 All services initialized successfully!');
      print('⚡ Performance: ${parallelServices.length + dependentServices.length + 2} services initialized');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error initializing services: $e');
    }
  }
}

/// تهيئة خدمة بشكل آمن مع معالجة الأخطاء
Future<bool> _initializeServiceSafely(String serviceName, Future<void> Function() initFunction) async {
  try {
    await initFunction();
    if (kDebugMode) {
      print('✅ $serviceName initialized');
    }
    return true;
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error initializing $serviceName: $e');
    }
    return false;
  }
}

/// التطبيق الرئيسي
class ElFarhanApp extends StatelessWidget {
  const ElFarhanApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            title: 'آل فرحان للنقل الخفيف',
            debugShowCheckedModeBanner: false,
            
            // Use the app theme
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            
            // Arabic localization
            locale: const Locale('ar', 'SA'),
            supportedLocales: const [
              Locale('ar', 'SA'),
              Locale('en', 'US'),
            ],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            
            // Main screen - Android HomeScreen
            home: authProvider.isAuthenticated 
                ? const HomeScreen() 
                : const LoginScreen(),
          );
        },
      ),
    );
  }
}
