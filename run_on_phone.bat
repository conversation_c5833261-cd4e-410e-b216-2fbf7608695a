@echo off
echo ========================================
echo    تشغيل تطبيق آل فرحان على الهاتف
echo ========================================
echo.

echo [1/5] التحقق من Flutter...
flutter --version
if %errorlevel% neq 0 (
    echo خطأ: Flutter غير مثبت أو غير موجود في PATH
    pause
    exit /b 1
)

echo.
echo [2/5] التحقق من الأجهزة المتصلة...
flutter devices
if %errorlevel% neq 0 (
    echo خطأ: لا توجد أجهزة متصلة
    echo تأكد من:
    echo - توصيل الهاتف بكابل USB
    echo - تفعيل USB Debugging
    echo - قبول تصريح USB Debugging على الهاتف
    pause
    exit /b 1
)

echo.
echo [3/5] تنظيف المشروع...
flutter clean

echo.
echo [4/5] تحميل التبعيات...
flutter pub get

echo.
echo [5/5] تشغيل التطبيق على الهاتف...
echo ملاحظة: قد يستغرق هذا عدة دقائق في المرة الأولى
echo.

flutter run --debug

echo.
echo تم الانتهاء!
pause
