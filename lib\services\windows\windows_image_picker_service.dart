import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

/// خدمة اختيار الصور المتوافقة مع Windows
/// بديل لـ image_picker
class WindowsImagePickerService {
  static WindowsImagePickerService? _instance;
  static WindowsImagePickerService get instance => _instance ??= WindowsImagePickerService._();
  
  WindowsImagePickerService._();

  /// اختيار صورة من الملفات
  Future<WindowsPickedFile?> pickImage({
    WindowsImageSource source = WindowsImageSource.gallery,
    int? maxWidth,
    int? maxHeight,
    int? imageQuality,
  }) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final bytes = await file.readAsBytes();
        
        return WindowsPickedFile(
          path: file.path,
          name: result.files.single.name,
          bytes: bytes,
          size: result.files.single.size,
        );
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error picking image: $e');
      }
      return null;
    }
  }

  /// اختيار عدة صور
  Future<List<WindowsPickedFile>> pickMultipleImages({
    int? maxWidth,
    int? maxHeight,
    int? imageQuality,
    int? limit,
  }) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
      );

      if (result != null) {
        List<WindowsPickedFile> pickedFiles = [];
        
        for (var platformFile in result.files) {
          if (platformFile.path != null) {
            final file = File(platformFile.path!);
            final bytes = await file.readAsBytes();
            
            pickedFiles.add(WindowsPickedFile(
              path: file.path,
              name: platformFile.name,
              bytes: bytes,
              size: platformFile.size,
            ));
            
            // تحديد العدد الأقصى
            if (limit != null && pickedFiles.length >= limit) {
              break;
            }
          }
        }
        
        return pickedFiles;
      }
      
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error picking multiple images: $e');
      }
      return [];
    }
  }

  /// اختيار ملف عام
  Future<WindowsPickedFile?> pickFile({
    List<String>? allowedExtensions,
    FileType type = FileType.any,
  }) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: type,
        allowMultiple: false,
        allowedExtensions: allowedExtensions,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final bytes = await file.readAsBytes();
        
        return WindowsPickedFile(
          path: file.path,
          name: result.files.single.name,
          bytes: bytes,
          size: result.files.single.size,
        );
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error picking file: $e');
      }
      return null;
    }
  }

  /// حفظ صورة في مجلد محدد
  Future<String?> saveImage(Uint8List bytes, String fileName, {String? directory}) async {
    try {
      String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'حفظ الصورة',
        fileName: fileName,
        type: FileType.image,
      );

      if (outputFile != null) {
        final file = File(outputFile);
        await file.writeAsBytes(bytes);
        
        if (kDebugMode) {
          print('✅ Image saved: $outputFile');
        }
        
        return outputFile;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error saving image: $e');
      }
      return null;
    }
  }

  /// التحقق من صحة الصورة
  bool isValidImageFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  /// الحصول على معلومات الصورة
  Future<WindowsImageInfo?> getImageInfo(String path) async {
    try {
      final file = File(path);
      if (!await file.exists()) return null;

      final bytes = await file.readAsBytes();
      final stat = await file.stat();
      
      return WindowsImageInfo(
        path: path,
        size: stat.size,
        lastModified: stat.modified,
        width: null, // يحتاج مكتبة image لاستخراج الأبعاد
        height: null,
      );
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error getting image info: $e');
      }
      return null;
    }
  }
}

/// نموذج الملف المختار في Windows
class WindowsPickedFile {
  final String path;
  final String name;
  final Uint8List bytes;
  final int size;

  const WindowsPickedFile({
    required this.path,
    required this.name,
    required this.bytes,
    required this.size,
  });

  /// تحويل إلى XFile للتوافق مع الكود الموجود
  WindowsXFile toXFile() {
    return WindowsXFile(
      path: path,
      name: name,
      bytes: bytes,
      length: size,
    );
  }
}

/// بديل لـ XFile
class WindowsXFile {
  final String path;
  final String name;
  final Uint8List bytes;
  final int length;

  const WindowsXFile({
    required this.path,
    required this.name,
    required this.bytes,
    required this.length,
  });

  Future<Uint8List> readAsBytes() async {
    return bytes;
  }

  String get mimeType {
    final extension = path.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'bmp':
        return 'image/bmp';
      case 'webp':
        return 'image/webp';
      default:
        return 'application/octet-stream';
    }
  }
}

/// مصدر الصورة في Windows
enum WindowsImageSource {
  gallery, // من الملفات
  camera,  // غير متاح في Windows - سيتم استخدام gallery
}

/// معلومات الصورة
class WindowsImageInfo {
  final String path;
  final int size;
  final DateTime lastModified;
  final int? width;
  final int? height;

  const WindowsImageInfo({
    required this.path,
    required this.size,
    required this.lastModified,
    this.width,
    this.height,
  });
}
