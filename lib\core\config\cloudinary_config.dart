/// إعدادات Cloudinary لرفع الصور
class CloudinaryConfig {
  // إعدادات Cloudinary التجريبية - تعمل للاختبار
  static const String cloudName = 'dzh4fpnnw'; // Cloudinary demo account
  static const String uploadPreset = 'farhan'; // Default unsigned preset
  
  // URLs
  static String get uploadUrl => 'https://api.cloudinary.com/v1_1/$cloudName/image/upload';
  static String get baseUrl => 'https://res.cloudinary.com/$cloudName';
  
  // إعدادات الرفع
  static const Map<String, String> defaultUploadParams = {
    'quality': 'auto:good',
    'fetch_format': 'auto',
    'crop': 'limit',
    'width': '1920',
    'height': '1080',
  };
  
  // مجلدات التنظيم
  static const Map<String, String> folders = {
    'motor_fingerprints': 'motor_fingerprints',
    'chassis_images': 'chassis_images',
    'customer_id_cards': 'customer_id_cards',
    'composite_images': 'composite_images',
    'test_images': 'test_images',
  };
  
  /// الحصول على مجلد حسب نوع الصورة
  static String getFolderForImageType(String fileName) {
    if (fileName.contains('motor') || fileName.contains('fingerprint')) {
      return folders['motor_fingerprints']!;
    } else if (fileName.contains('chassis')) {
      return folders['chassis_images']!;
    } else if (fileName.contains('id_front') || fileName.contains('id_back') || fileName.contains('customer')) {
      return folders['customer_id_cards']!;
    } else if (fileName.contains('composite')) {
      return folders['composite_images']!;
    } else if (fileName.contains('test')) {
      return folders['test_images']!;
    } else {
      return 'general'; // مجلد افتراضي
    }
  }
  
  /// التحقق من صحة الإعدادات
  static bool get isConfigured {
    return cloudName.isNotEmpty &&
           uploadPreset.isNotEmpty;
  }
  
  /// رسالة خطأ في حالة عدم الإعداد
  static String get configurationError {
    if (!isConfigured) {
      return '''
إعدادات Cloudinary غير مكتملة!

يرجى تحديث الإعدادات في:
lib/core/config/cloudinary_config.dart

المطلوب:
1. cloudName: اسم الحساب في Cloudinary
2. uploadPreset: إعداد الرفع (unsigned preset)

للحصول على هذه القيم:
1. اذهب إلى https://cloudinary.com
2. سجل دخول أو أنشئ حساب جديد
3. من Dashboard احصل على Cloud Name
4. من Settings > Upload أنشئ Upload Preset جديد (unsigned)
''';
    }
    return '';
  }
}
