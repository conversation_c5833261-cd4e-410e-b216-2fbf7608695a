import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'data_service.dart';
import 'firebase_service.dart';
import 'sync_service.dart';

class AutoSyncService {
  static AutoSyncService? _instance;
  static AutoSyncService get instance {
    _instance ??= AutoSyncService._internal();
    return _instance!;
  }
  
  AutoSyncService._internal();

  final DataService _dataService = DataService.instance;
  final FirebaseService _firebaseService = FirebaseService.instance;

  Timer? _syncTimer;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  bool _isInitialized = false;
  bool _isSyncing = false;

  /// Initialize auto sync service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Listen to connectivity changes
      _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
        _onConnectivityChanged,
      );

      // Start periodic sync (every 30 seconds when online)
      _startPeriodicSync();

      // Initial sync if online
      final connectivityResults = await Connectivity().checkConnectivity();
      final hasConnection = connectivityResults.any((result) => result != ConnectivityResult.none);
      if (hasConnection) {
        await _performFullSync();
      }

      _isInitialized = true;
      debugPrint('✅ Auto sync service initialized');
    } catch (e) {
      debugPrint('❌ Failed to initialize auto sync service: $e');
    }
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(List<ConnectivityResult> results) {
    // Check if any connection is available
    final hasConnection = results.any((result) => result != ConnectivityResult.none);

    if (hasConnection) {
      debugPrint('📶 Internet connected - starting sync');
      _performFullSync();
    } else {
      debugPrint('📵 Internet disconnected - stopping sync');
      _stopPeriodicSync();
    }
  }

  /// Start periodic sync timer
  void _startPeriodicSync() {
    _stopPeriodicSync(); // Stop existing timer
    
    _syncTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      final connectivityResults = await Connectivity().checkConnectivity();
      final hasConnection = connectivityResults.any((result) => result != ConnectivityResult.none);
      if (hasConnection && !_isSyncing) {
        await _performFullSync();
      }
    });
  }

  /// Stop periodic sync timer
  void _stopPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = null;
  }

  /// Perform full sync of all data
  Future<void> _performFullSync() async {
    if (_isSyncing) return;

    _isSyncing = true;

    try {
      debugPrint('🔄 Starting auto sync...');

      // أولاً: معالجة طابور المزامنة للعمليات المؤجلة
      final syncService = SyncService.instance;
      await syncService.processSyncQueue();

      // ثانياً: مزامنة البيانات من Firebase
      await _syncUsers();
      await _syncWarehouses();
      await _syncItems();
      await _syncInvoices();
      await _syncAgentAccounts();
      await _syncDocumentTracking();
      await _syncNotifications();

      debugPrint('✅ Auto sync completed successfully');
    } catch (e) {
      debugPrint('❌ Auto sync failed: $e');
    } finally {
      _isSyncing = false;
    }
  }

  /// Sync users from Firebase
  Future<void> _syncUsers() async {
    try {
      await _dataService.syncUsersFromFirebase();
      debugPrint('✅ Users synced');
    } catch (e) {
      // Don't log auth errors as they're expected during sync
      if (!e.toString().contains('Firebase Auth Error') &&
          !e.toString().contains('كلمة المرور غير صحيحة') &&
          !e.toString().contains('Username sign in error')) {
        debugPrint('❌ Failed to sync users: $e');
      }
    }
  }

  /// Sync warehouses from Firebase
  Future<void> _syncWarehouses() async {
    try {
      await _dataService.syncWarehousesFromFirebase();
      debugPrint('✅ Warehouses synced');
    } catch (e) {
      debugPrint('❌ Failed to sync warehouses: $e');
    }
  }

  /// Sync items from Firebase
  Future<void> _syncItems() async {
    try {
      await _dataService.syncItemsFromFirebase();
      debugPrint('✅ Items synced');
    } catch (e) {
      debugPrint('❌ Failed to sync items: $e');
    }
  }

  /// Sync invoices from Firebase
  Future<void> _syncInvoices() async {
    try {
      await _dataService.syncInvoicesFromFirebase();
      debugPrint('✅ Invoices synced');
    } catch (e) {
      debugPrint('❌ Failed to sync invoices: $e');
    }
  }

  /// Sync notifications from Firebase
  Future<void> _syncNotifications() async {
    try {
      await _dataService.syncNotificationsFromFirebase();
      debugPrint('✅ Notifications synced');
    } catch (e) {
      debugPrint('❌ Failed to sync notifications: $e');
    }
  }

  /// Sync agent accounts from Firebase
  Future<void> _syncAgentAccounts() async {
    try {
      await DataService.instance.syncAgentAccountsFromFirebase();
      debugPrint('✅ Agent accounts synced');
    } catch (e) {
      debugPrint('❌ Failed to sync agent accounts: $e');
    }
  }

  /// Sync document tracking from Firebase
  Future<void> _syncDocumentTracking() async {
    try {
      await SyncService.instance.syncDocumentTracking();
      debugPrint('✅ Document tracking synced');
    } catch (e) {
      debugPrint('❌ Failed to sync document tracking: $e');
    }
  }

  /// Force sync now (manual trigger)
  Future<void> forceSyncNow() async {
    final connectivityResults = await Connectivity().checkConnectivity();
    final hasConnection = connectivityResults.any((result) => result != ConnectivityResult.none);
    if (hasConnection) {
      await _performFullSync();
    } else {
      debugPrint('⚠️ Cannot sync - no internet connection');
    }
  }

  /// Check if currently syncing
  bool get isSyncing => _isSyncing;

  /// Dispose resources
  void dispose() {
    _stopPeriodicSync();
    _connectivitySubscription?.cancel();
    _isInitialized = false;
    debugPrint('🛑 Auto sync service disposed');
  }
}
