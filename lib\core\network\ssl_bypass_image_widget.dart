import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import '../theme/desktop_theme.dart';

/// Image widget that completely bypasses SSL verification
class SSLBypassImageWidget extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;

  const SSLBypassImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
    this.onTap,
  });

  @override
  State<SSLBypassImageWidget> createState() => _SSLBypassImageWidgetState();
}

class _SSLBypassImageWidgetState extends State<SSLBypassImageWidget> {
  Uint8List? _imageBytes;
  bool _isLoading = true;
  String? _error;
  late final http.Client _httpClient;

  @override
  void initState() {
    super.initState();
    _initializeHttpClient();
    _loadImage();
  }

  void _initializeHttpClient() {
    // Create HttpClient with complete SSL bypass
    final httpClient = HttpClient();
    
    // Completely bypass SSL verification
    httpClient.badCertificateCallback = (cert, host, port) {
      if (kDebugMode) {
        print('🔓 SSL Bypass Image: Allowing certificate for $host:$port');
      }
      return true; // Allow ALL certificates
    };

    // Set timeouts
    httpClient.connectionTimeout = const Duration(seconds: 30);
    httpClient.idleTimeout = const Duration(seconds: 30);

    _httpClient = IOClient(httpClient);
  }

  Future<void> _loadImage() async {
    if (widget.imageUrl.isEmpty) {
      setState(() {
        _isLoading = false;
        _error = 'Empty URL';
      });
      return;
    }

    try {
      if (kDebugMode) {
        print('🖼️ SSL Bypass: Loading image ${widget.imageUrl}');
      }

      final response = await _httpClient.get(
        Uri.parse(widget.imageUrl),
        headers: {
          'User-Agent': 'El-Farhan-Desktop/1.0',
          'Accept': 'image/*',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        setState(() {
          _imageBytes = response.bodyBytes;
          _isLoading = false;
          _error = null;
        });
        
        if (kDebugMode) {
          print('✅ SSL Bypass: Image loaded successfully (${response.bodyBytes.length} bytes)');
        }
      } else {
        setState(() {
          _isLoading = false;
          _error = 'HTTP ${response.statusCode}';
        });
        
        if (kDebugMode) {
          print('❌ SSL Bypass: HTTP error ${response.statusCode}');
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
      
      if (kDebugMode) {
        print('❌ SSL Bypass: Error loading image: $e');
      }
    }
  }

  @override
  void dispose() {
    _httpClient.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget child;

    if (_isLoading) {
      child = widget.placeholder ?? _buildDefaultPlaceholder();
    } else if (_error != null || _imageBytes == null) {
      child = widget.errorWidget ?? _buildDefaultErrorWidget();
    } else {
      child = Image.memory(
        _imageBytes!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
      );
    }

    if (widget.borderRadius != null) {
      child = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: child,
      );
    }

    if (widget.onTap != null) {
      child = InkWell(
        onTap: widget.onTap,
        child: child,
      );
    }

    return child;
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: DesktopTheme.backgroundTertiary,
      child: const Center(
        child: CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }

  Widget _buildDefaultErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: DesktopTheme.surfaceMedium,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image_outlined,
            size: (widget.width ?? 80) * 0.3,
            color: DesktopTheme.textTertiary,
          ),
          const SizedBox(height: 4),
          Text(
            'فشل التحميل',
            style: DesktopTheme.bodySmall.copyWith(
              color: DesktopTheme.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
          if (kDebugMode && _error != null) ...[
            const SizedBox(height: 2),
            Text(
              _error!,
              style: DesktopTheme.bodySmall.copyWith(
                color: DesktopTheme.statusError,
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}

/// Specialized SSL bypass image widget for item thumbnails
class SSLBypassItemImageWidget extends StatelessWidget {
  final String imageUrl;
  final double size;
  final VoidCallback? onTap;

  const SSLBypassItemImageWidget({
    super.key,
    required this.imageUrl,
    this.size = 80,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SSLBypassImageWidget(
      imageUrl: imageUrl,
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
      onTap: onTap,
      placeholder: Container(
        width: size,
        height: size,
        color: DesktopTheme.backgroundTertiary,
        child: Icon(
          Icons.motorcycle_outlined,
          size: size * 0.4,
          color: DesktopTheme.textTertiary,
        ),
      ),
      errorWidget: Container(
        width: size,
        height: size,
        color: DesktopTheme.surfaceMedium,
        child: Icon(
          Icons.broken_image_outlined,
          size: size * 0.4,
          color: DesktopTheme.textTertiary,
        ),
      ),
    );
  }
}
