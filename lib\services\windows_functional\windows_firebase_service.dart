import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// خدمة Firebase للويندوز - متوافقة وظيفياً مع نسخة Android
/// تستخدم نفس قاعدة البيانات والمجموعات لضمان التكامل
class WindowsFirebaseService {
  static WindowsFirebaseService? _instance;
  static WindowsFirebaseService get instance => _instance ??= WindowsFirebaseService._();
  
  WindowsFirebaseService._();

  // Firebase instances - same as Android
  FirebaseAuth get auth => FirebaseAuth.instance;
  FirebaseFirestore get firestore => FirebaseFirestore.instance;

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  /// تهيئة Firebase للويندوز مع نفس إعدادات Android
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      // Configure Firestore for offline persistence (same as Android)
      await _configureFirestore();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Windows Firebase Service initialized - same database as Android');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing Windows Firebase Service: $e');
      }
      rethrow;
    }
  }

  /// تكوين Firestore للعمل دون اتصال (نفس Android)
  Future<void> _configureFirestore() async {
    try {
      // Enable offline persistence (same as Android)
      await firestore.enablePersistence(
        const PersistenceSettings(synchronizeTabs: true),
      );
      
      if (kDebugMode) {
        print('✅ Firestore offline persistence enabled for Windows');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Firestore persistence already enabled or failed: $e');
      }
    }
  }

  /// تسجيل الدخول بنفس طريقة Android
  Future<UserCredential> signInWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (kDebugMode) {
        print('✅ User signed in successfully on Windows: ${credential.user?.email}');
      }
      
      return credential;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error signing in on Windows: $e');
      }
      rethrow;
    }
  }

  /// إنشاء مستخدم جديد (نفس Android)
  Future<UserCredential> createUserWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      if (kDebugMode) {
        print('✅ User created successfully on Windows: ${credential.user?.email}');
      }
      
      return credential;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating user on Windows: $e');
      }
      rethrow;
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      await auth.signOut();
      if (kDebugMode) {
        print('✅ User signed out successfully on Windows');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error signing out on Windows: $e');
      }
      rethrow;
    }
  }

  /// التحقق من حالة المصادقة
  bool get isAuthenticated => auth.currentUser != null;

  /// الحصول على المستخدم الحالي
  User? get currentUser => auth.currentUser;

  /// الحصول على معرف المستخدم الحالي
  String? get currentUserId => auth.currentUser?.uid;

  /// التحقق من الاتصال بالإنترنت
  Future<bool> isOnline() async {
    try {
      // Try to get a document to check connectivity
      await firestore.collection('_connectivity_test').limit(1).get();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// فرض المزامنة عند العودة للاتصال
  Future<void> forceSyncWhenOnline() async {
    try {
      if (await isOnline()) {
        await firestore.enableNetwork();
        if (kDebugMode) {
          print('✅ Forced sync completed on Windows');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during force sync on Windows: $e');
      }
    }
  }

  // === CRUD Operations - Same collections as Android ===

  /// إضافة مستند جديد
  Future<DocumentReference> addDocument(String collection, Map<String, dynamic> data) async {
    try {
      final docRef = await firestore.collection(collection).add(data);
      if (kDebugMode) {
        print('✅ Document added to $collection: ${docRef.id}');
      }
      return docRef;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error adding document to $collection: $e');
      }
      rethrow;
    }
  }

  /// تحديث مستند موجود
  Future<void> updateDocument(String collection, String docId, Map<String, dynamic> data) async {
    try {
      await firestore.collection(collection).doc(docId).update(data);
      if (kDebugMode) {
        print('✅ Document updated in $collection: $docId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating document in $collection: $e');
      }
      rethrow;
    }
  }

  /// حذف مستند
  Future<void> deleteDocument(String collection, String docId) async {
    try {
      await firestore.collection(collection).doc(docId).delete();
      if (kDebugMode) {
        print('✅ Document deleted from $collection: $docId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting document from $collection: $e');
      }
      rethrow;
    }
  }

  /// الحصول على مستند بالمعرف
  Future<DocumentSnapshot> getDocument(String collection, String docId) async {
    try {
      final doc = await firestore.collection(collection).doc(docId).get();
      if (kDebugMode) {
        print('✅ Document retrieved from $collection: $docId');
      }
      return doc;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting document from $collection: $e');
      }
      rethrow;
    }
  }

  /// الحصول على مجموعة مستندات
  Future<QuerySnapshot> getCollection(String collection, {Query Function(Query)? queryBuilder}) async {
    try {
      Query query = firestore.collection(collection);
      if (queryBuilder != null) {
        query = queryBuilder(query);
      }
      
      final snapshot = await query.get();
      if (kDebugMode) {
        print('✅ Collection retrieved: $collection (${snapshot.docs.length} documents)');
      }
      return snapshot;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting collection $collection: $e');
      }
      rethrow;
    }
  }

  /// الاستماع لتغييرات مجموعة (Real-time updates)
  Stream<QuerySnapshot> listenToCollection(String collection, {Query Function(Query)? queryBuilder}) {
    try {
      Query query = firestore.collection(collection);
      if (queryBuilder != null) {
        query = queryBuilder(query);
      }
      
      if (kDebugMode) {
        print('✅ Listening to collection: $collection');
      }
      
      return query.snapshots();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error listening to collection $collection: $e');
      }
      rethrow;
    }
  }

  /// الاستماع لتغييرات مستند (Real-time updates)
  Stream<DocumentSnapshot> listenToDocument(String collection, String docId) {
    try {
      if (kDebugMode) {
        print('✅ Listening to document: $collection/$docId');
      }
      
      return firestore.collection(collection).doc(docId).snapshots();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error listening to document $collection/$docId: $e');
      }
      rethrow;
    }
  }

  // === Batch Operations ===

  /// تنفيذ عمليات متعددة في معاملة واحدة
  Future<void> runTransaction(Future<void> Function(Transaction) updateFunction) async {
    try {
      await firestore.runTransaction(updateFunction);
      if (kDebugMode) {
        print('✅ Transaction completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error running transaction: $e');
      }
      rethrow;
    }
  }

  /// تنفيذ عمليات متعددة في دفعة واحدة
  Future<void> runBatch(void Function(WriteBatch) batchFunction) async {
    try {
      final batch = firestore.batch();
      batchFunction(batch);
      await batch.commit();
      if (kDebugMode) {
        print('✅ Batch operation completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error running batch operation: $e');
      }
      rethrow;
    }
  }

  /// تنظيف الموارد
  void dispose() {
    // Clean up any resources if needed
    if (kDebugMode) {
      print('🧹 Windows Firebase Service disposed');
    }
  }
}
