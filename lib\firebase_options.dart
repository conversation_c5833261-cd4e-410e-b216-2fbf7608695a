// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBhLYEUsJm4Kw9ZHzQmlQma9CT6_a0lw3M',
    appId: '1:871976480343:web:5cce5ed86a01e8aa89fd12',
    messagingSenderId: '871976480343',
    projectId: 'al-farhan-c3a30',
    authDomain: 'al-farhan-c3a30.firebaseapp.com',
    storageBucket: 'al-farhan-c3a30.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBhLYEUsJm4Kw9ZHzQmlQma9CT6_a0lw3M',
    appId: '1:871976480343:android:5cce5ed86a01e8aa89fd12',
    messagingSenderId: '871976480343',
    projectId: 'al-farhan-c3a30',
    storageBucket: 'al-farhan-c3a30.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBhLYEUsJm4Kw9ZHzQmlQma9CT6_a0lw3M',
    appId: '1:871976480343:ios:5cce5ed86a01e8aa89fd12',
    messagingSenderId: '871976480343',
    projectId: 'al-farhan-c3a30',
    storageBucket: 'al-farhan-c3a30.firebasestorage.app',
    iosBundleId: 'com.alfarhan.transport',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBhLYEUsJm4Kw9ZHzQmlQma9CT6_a0lw3M',
    appId: '1:871976480343:macos:5cce5ed86a01e8aa89fd12',
    messagingSenderId: '871976480343',
    projectId: 'al-farhan-c3a30',
    storageBucket: 'al-farhan-c3a30.firebasestorage.app',
    iosBundleId: 'com.alfarhan.transport',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBhLYEUsJm4Kw9ZHzQmlQma9CT6_a0lw3M',
    appId: '1:871976480343:web:5cce5ed86a01e8aa89fd12',
    messagingSenderId: '871976480343',
    projectId: 'al-farhan-c3a30',
    storageBucket: 'al-farhan-c3a30.firebasestorage.app',
  );
}
