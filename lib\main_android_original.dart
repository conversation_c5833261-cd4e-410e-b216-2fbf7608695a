import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// Firebase configuration
import 'firebase_options.dart';

// Core theme
import 'core/theme/app_theme.dart';

// Auth provider
import 'providers/auth_provider.dart';

// Services
import 'services/local_database_service.dart';
import 'services/firebase_service.dart';
import 'services/accounting_journal_service.dart';
import 'services/android/android_image_service.dart';
import 'services/android/android_session_service.dart';
import 'services/enhanced_notification_service.dart';
import 'services/auto_sync_service.dart';
import 'services/security_service.dart';
import 'services/auth_service.dart';

// Android screens
import 'screens/home/<USER>';
import 'screens/auth/login_screen.dart';

/// تطبيق آل فرحان الأصلي للأندرويد
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (kDebugMode) {
    print('🚀 Starting El Farhan Android App...');
  }

  // Initialize Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    if (kDebugMode) {
      print('🔥 Firebase initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Firebase initialization failed: $e');
    }
  }

  // Initialize all services
  await _initializeServices();

  // Run the app
  runApp(const ElFarhanApp());
}

/// تهيئة جميع الخدمات
Future<void> _initializeServices() async {
  try {
    if (kDebugMode) {
      print('🔧 Initializing services...');
    }

    // Initialize local database first
    await LocalDatabaseService.instance.initialize();
    if (kDebugMode) {
      print('✅ LocalDatabaseService initialized');
    }

    // Initialize Firebase service
    await FirebaseService.instance.initialize();
    if (kDebugMode) {
      print('✅ FirebaseService initialized');
    }

    // Initialize accounting journal service
    await AccountingJournalService.instance.initialize();
    if (kDebugMode) {
      print('✅ AccountingJournalService initialized');
    }

    // Initialize Android image service
    await AndroidImageService.instance.initialize();
    if (kDebugMode) {
      print('✅ AndroidImageService initialized');
    }

    // Initialize notification service
    await EnhancedNotificationService.instance.initialize();
    if (kDebugMode) {
      print('✅ NotificationService initialized');
    }

    // Initialize auto sync service
    await AutoSyncService.instance.initialize();
    if (kDebugMode) {
      print('✅ AutoSyncService initialized');
    }

    // Initialize security service
    await SecurityService.instance.initialize();
    if (kDebugMode) {
      print('✅ SecurityService initialized');
    }

    // Initialize auth service
    await AuthService.instance.initialize();
    if (kDebugMode) {
      print('✅ AuthService initialized');
    }

    // Initialize Android session service
    await AndroidSessionService.instance.initialize();
    if (kDebugMode) {
      print('✅ AndroidSessionService initialized');
    }

    if (kDebugMode) {
      print('🎉 All services initialized successfully!');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error initializing services: $e');
    }
  }
}

/// التطبيق الرئيسي
class ElFarhanApp extends StatelessWidget {
  const ElFarhanApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            title: 'آل فرحان للنقل الخفيف',
            debugShowCheckedModeBanner: false,
            
            // Use the app theme
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            
            // Arabic localization
            locale: const Locale('ar', 'SA'),
            supportedLocales: const [
              Locale('ar', 'SA'),
              Locale('en', 'US'),
            ],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            
            // Main screen - Android HomeScreen
            home: authProvider.isAuthenticated 
                ? const HomeScreen() 
                : const LoginScreen(),
          );
        },
      ),
    );
  }
}
