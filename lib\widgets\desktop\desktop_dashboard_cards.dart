import 'package:flutter/material.dart';
import '../../core/theme/desktop_theme.dart';

/// Professional dashboard card for displaying key metrics
class DesktopDashboardCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color iconColor;
  final Color? backgroundColor;
  final String? trend;
  final bool isPositiveTrend;
  final VoidCallback? onTap;
  final List<Widget>? actions;

  const DesktopDashboardCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    required this.iconColor,
    this.backgroundColor,
    this.trend,
    this.isPositiveTrend = true,
    this.onTap,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(DesktopTheme.radiusLarge),
        child: Container(
          padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
          decoration: DesktopTheme.cardDecoration.copyWith(
            color: backgroundColor ?? DesktopTheme.backgroundSecondary,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon and actions
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
                    decoration: BoxDecoration(
                      color: iconColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
                    ),
                    child: Icon(
                      icon,
                      size: 24,
                      color: iconColor,
                    ),
                  ),
                  const Spacer(),
                  if (actions != null) ...actions!,
                ],
              ),
              
              const SizedBox(height: DesktopTheme.spacingLarge),
              
              // Value
              Text(
                value,
                style: DesktopTheme.headingLarge.copyWith(
                  color: DesktopTheme.textPrimary,
                  fontWeight: FontWeight.w700,
                ),
              ),
              
              const SizedBox(height: DesktopTheme.spacingSmall),
              
              // Title
              Text(
                title,
                style: DesktopTheme.titleMedium.copyWith(
                  color: DesktopTheme.textSecondary,
                ),
              ),
              
              // Subtitle and trend
              if (subtitle != null || trend != null) ...[
                const SizedBox(height: DesktopTheme.spacingSmall),
                Row(
                  children: [
                    if (subtitle != null)
                      Expanded(
                        child: Text(
                          subtitle!,
                          style: DesktopTheme.bodySmall,
                        ),
                      ),
                    if (trend != null) ...[
                      Icon(
                        isPositiveTrend
                            ? Icons.trending_up
                            : Icons.trending_down,
                        size: 16,
                        color: isPositiveTrend
                            ? DesktopTheme.statusSuccess
                            : DesktopTheme.statusError,
                      ),
                      const SizedBox(width: DesktopTheme.spacingXSmall),
                      Text(
                        trend!,
                        style: DesktopTheme.bodySmall.copyWith(
                          color: isPositiveTrend
                              ? DesktopTheme.statusSuccess
                              : DesktopTheme.statusError,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Grid layout for dashboard cards with responsive design
class DesktopDashboardGrid extends StatelessWidget {
  final List<Widget> children;
  final int crossAxisCount;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final EdgeInsetsGeometry? padding;

  const DesktopDashboardGrid({
    super.key,
    required this.children,
    this.crossAxisCount = 4,
    this.childAspectRatio = 1.5,
    this.crossAxisSpacing = DesktopTheme.spacingMedium,
    this.mainAxisSpacing = DesktopTheme.spacingMedium,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Responsive grid columns based on screen width
        int columns = crossAxisCount;
        if (constraints.maxWidth < 600) {
          columns = 1;
        } else if (constraints.maxWidth < 900) {
          columns = 2;
        } else if (constraints.maxWidth < 1200) {
          columns = 3;
        }

        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: columns,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: crossAxisSpacing,
          mainAxisSpacing: mainAxisSpacing,
          padding: padding ?? const EdgeInsets.all(DesktopTheme.spacingMedium),
          children: children,
        );
      },
    );
  }
}

/// Chart card for displaying data visualizations
class DesktopChartCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget chart;
  final List<Widget>? actions;
  final double? height;

  const DesktopChartCard({
    super.key,
    required this.title,
    this.subtitle,
    required this.chart,
    this.actions,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: DesktopTheme.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: BoxDecoration(
              color: DesktopTheme.backgroundTertiary,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(DesktopTheme.radiusLarge),
                topRight: Radius.circular(DesktopTheme.radiusLarge),
              ),
              border: Border(
                bottom: BorderSide(
                  color: DesktopTheme.borderLight,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: DesktopTheme.titleLarge,
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: DesktopTheme.spacingXSmall),
                        Text(
                          subtitle!,
                          style: DesktopTheme.bodySmall,
                        ),
                      ],
                    ],
                  ),
                ),
                if (actions != null) ...actions!,
              ],
            ),
          ),
          
          // Chart content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
              child: chart,
            ),
          ),
        ],
      ),
    );
  }
}

/// Quick action card for common tasks
class DesktopQuickActionCard extends StatelessWidget {
  final String title;
  final String? description;
  final IconData icon;
  final Color iconColor;
  final VoidCallback? onTap;
  final bool isEnabled;

  const DesktopQuickActionCard({
    super.key,
    required this.title,
    this.description,
    required this.icon,
    required this.iconColor,
    this.onTap,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isEnabled ? onTap : null,
        borderRadius: BorderRadius.circular(DesktopTheme.radiusLarge),
        child: Container(
          padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
          decoration: DesktopTheme.cardDecoration.copyWith(
            color: isEnabled
                ? DesktopTheme.backgroundSecondary
                : DesktopTheme.surfaceMedium,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
                decoration: BoxDecoration(
                  color: isEnabled
                      ? iconColor.withOpacity(0.1)
                      : DesktopTheme.textTertiary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: isEnabled ? iconColor : DesktopTheme.textTertiary,
                ),
              ),
              
              const SizedBox(height: DesktopTheme.spacingMedium),
              
              Text(
                title,
                style: DesktopTheme.titleMedium.copyWith(
                  color: isEnabled
                      ? DesktopTheme.textPrimary
                      : DesktopTheme.textTertiary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              if (description != null) ...[
                const SizedBox(height: DesktopTheme.spacingSmall),
                Text(
                  description!,
                  style: DesktopTheme.bodySmall.copyWith(
                    color: isEnabled
                        ? DesktopTheme.textSecondary
                        : DesktopTheme.textTertiary,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Status indicator card
class DesktopStatusCard extends StatelessWidget {
  final String title;
  final String status;
  final Color statusColor;
  final IconData statusIcon;
  final String? lastUpdated;
  final VoidCallback? onTap;

  const DesktopStatusCard({
    super.key,
    required this.title,
    required this.status,
    required this.statusColor,
    required this.statusIcon,
    this.lastUpdated,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(DesktopTheme.radiusLarge),
        child: Container(
          padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
          decoration: DesktopTheme.cardDecoration,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: DesktopTheme.titleMedium,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: DesktopTheme.spacingSmall,
                      vertical: DesktopTheme.spacingXSmall,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          statusIcon,
                          size: 16,
                          color: statusColor,
                        ),
                        const SizedBox(width: DesktopTheme.spacingXSmall),
                        Text(
                          status,
                          style: DesktopTheme.bodySmall.copyWith(
                            color: statusColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              if (lastUpdated != null) ...[
                const SizedBox(height: DesktopTheme.spacingMedium),
                Text(
                  'آخر تحديث: $lastUpdated',
                  style: DesktopTheme.bodySmall,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
