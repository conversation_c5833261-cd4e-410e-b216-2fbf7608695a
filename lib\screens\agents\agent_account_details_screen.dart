import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import '../../core/utils/app_utils.dart';
import '../../models/professional_agent_account.dart';

/// شاشة تفاصيل حساب الوكيل
/// عرض شامل لجميع معاملات وتفاصيل الحساب
class AgentAccountDetailsScreen extends StatefulWidget {
  final ProfessionalAgentAccount account;

  const AgentAccountDetailsScreen({
    super.key,
    required this.account,
  });

  @override
  State<AgentAccountDetailsScreen> createState() => _AgentAccountDetailsScreenState();
}

class _AgentAccountDetailsScreenState extends State<AgentAccountDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildAccountHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAccountSummaryTab(),
                _buildTransactionsTab(),
                _buildAnalyticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('حساب ${widget.account.agentName}'),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.picture_as_pdf),
          onPressed: _exportToPDF,
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _refreshData,
        ),
      ],
    );
  }

  Widget _buildAccountHeader() {
    final account = widget.account;
    final balanceColor = account.currentBalance >= 0 ? Colors.green : Colors.red;
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // صورة الوكيل
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(color: Colors.white.withValues(alpha: 0.3), width: 2),
                ),
                child: Center(
                  child: Text(
                    account.agentName.isNotEmpty ? account.agentName[0] : 'و',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // معلومات الوكيل
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            account.agentName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (account.isVerified)
                          const Icon(Icons.verified, color: Colors.white, size: 20),
                      ],
                    ),
                    if (account.agentPhone != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        account.agentPhone!,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                    if (account.agentEmail != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        account.agentEmail!,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // الرصيد الحالي
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الرصيد الحالي',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${account.currentBalance.toStringAsFixed(2)} ج.م',
                  style: TextStyle(
                    color: account.currentBalance >= 0 ? Colors.lightGreen : Colors.orange,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 12),
          
          // حالة الحساب
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الحالة: ${account.getAccountStatus()}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
              Text(
                'آخر تحديث: ${AppUtils.formatDate(account.updatedAt)}',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: Colors.grey,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        tabs: const [
          Tab(
            icon: Icon(Icons.account_balance_wallet),
            text: 'ملخص الحساب',
          ),
          Tab(
            icon: Icon(Icons.receipt_long),
            text: 'المعاملات',
          ),
          Tab(
            icon: Icon(Icons.analytics),
            text: 'التحليلات',
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSummaryTab() {
    final account = widget.account;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCard(
            'المعلومات المالية',
            Icons.monetization_on,
            [
              _buildSummaryRow('إجمالي المبيعات', '${account.totalCustomerSales.toStringAsFixed(2)} ج.م'),
              _buildSummaryRow('إجمالي العمولات', '${account.totalAgentCommission.toStringAsFixed(2)} ج.م'),
              _buildSummaryRow('أرباح الشركة', '${account.totalCompanyProfits.toStringAsFixed(2)} ج.م'),
              _buildSummaryRow('المدفوعات المستلمة', '${account.totalPaymentsReceived.toStringAsFixed(2)} ج.م'),
              if (account.totalDebt > 0)
                _buildSummaryRow('المديونية الحالية', '${account.totalDebt.toStringAsFixed(2)} ج.م', isDebt: true),
              if (account.availableCredit > 0)
                _buildSummaryRow('الرصيد الدائن', '${account.availableCredit.toStringAsFixed(2)} ج.م', isCredit: true),
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildSummaryCard(
            'إحصائيات النشاط',
            Icons.bar_chart,
            [
              _buildSummaryRow('عدد المعاملات', '${account.transactionCount}'),
              _buildSummaryRow('آخر معاملة', account.lastTransactionDate != null 
                  ? AppUtils.formatDate(account.lastTransactionDate!) 
                  : 'لا توجد معاملات'),
              _buildSummaryRow('تاريخ الإنشاء', AppUtils.formatDate(account.createdAt)),
              _buildSummaryRow('آخر تحديث', AppUtils.formatDate(account.updatedAt)),
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildSummaryCard(
            'حالة التحقق',
            Icons.verified_user,
            [
              _buildSummaryRow('صحة البيانات', account.validateAccount() ? 'صحيح' : 'يحتاج مراجعة'),
              _buildSummaryRow('التحقق', account.isVerified ? 'محقق' : 'غير محقق'),
              _buildSummaryRow('الحالة', account.isActive ? 'نشط' : 'غير نشط'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'جدول المعاملات قيد التطوير',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.analytics_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'التحليلات قيد التطوير',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, IconData icon, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isDebt = false, bool isCredit = false}) {
    Color? valueColor;
    if (isDebt) valueColor = Colors.red;
    if (isCredit) valueColor = Colors.green;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 14),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "payment",
          onPressed: _addPayment,
          backgroundColor: Colors.green,
          child: const Icon(Icons.payment, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "pdf",
          onPressed: _exportToPDF,
          backgroundColor: Colors.red,
          child: const Icon(Icons.picture_as_pdf, color: Colors.white),
        ),
      ],
    );
  }

  void _addPayment() {
    AppUtils.showSnackBar(context, 'تسجيل الدفعة قيد التطوير');
  }

  void _exportToPDF() {
    AppUtils.showSnackBar(context, 'تصدير PDF قيد التطوير');
  }

  void _refreshData() {
    AppUtils.showSnackBar(context, 'تم تحديث البيانات');
  }
}
