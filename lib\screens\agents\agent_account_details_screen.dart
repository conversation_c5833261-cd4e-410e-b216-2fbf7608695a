import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_accounting_system.dart';
import '../../services/agent_accounting_service.dart';

/// شاشة تفاصيل حساب الوكيل
/// تعرض جميع المعاملات والحسابات بتفصيل دقيق
class AgentAccountDetailsScreen extends StatefulWidget {
  final AgentAccount agentAccount;

  const AgentAccountDetailsScreen({
    super.key,
    required this.agentAccount,
  });

  @override
  State<AgentAccountDetailsScreen> createState() => _AgentAccountDetailsScreenState();
}

class _AgentAccountDetailsScreenState extends State<AgentAccountDetailsScreen>
    with TickerProviderStateMixin {
  
  final AgentAccountingService _accountingService = AgentAccountingService();
  
  late TabController _tabController;
  late AgentAccount _currentAccount;
  
  bool _isLoading = false;
  String? _error;
  AgentTransactionType? _selectedFilter;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _currentAccount = widget.agentAccount;
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحديث بيانات الحساب
  Future<void> _refreshAccount() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final updatedAccount = await _accountingService.getAgentAccount(_currentAccount.agentId);
      if (updatedAccount != null) {
        setState(() {
          _currentAccount = updatedAccount;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'خطأ في تحديث البيانات: $e';
      });
    }
  }

  /// إعادة حساب الحساب
  Future<void> _recalculateAccount() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final recalculatedAccount = await _accountingService.recalculateAgentAccount(_currentAccount.agentId);
      setState(() {
        _currentAccount = recalculatedAccount;
        _isLoading = false;
      });

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إعادة حساب الحساب بنجاح');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إعادة الحساب: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text('حساب ${_currentAccount.agentName}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshAccount,
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.calculate),
            onPressed: _recalculateAccount,
            tooltip: 'إعادة حساب الحساب',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.account_balance), text: 'ملخص الحساب'),
            Tab(icon: Icon(Icons.list), text: 'المعاملات'),
            Tab(icon: Icon(Icons.analytics), text: 'التحليلات'),
          ],
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحديث بيانات الحساب...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(_error!, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshAccount,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildAccountSummaryTab(),
        _buildTransactionsTab(),
        _buildAnalyticsTab(),
      ],
    );
  }

  /// تبويب ملخص الحساب
  Widget _buildAccountSummaryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAgentInfoCard(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildBalanceCard(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildAccountSummaryCards(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildValidationCard(),
        ],
      ),
    );
  }

  /// بطاقة معلومات الوكيل
  Widget _buildAgentInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الوكيل',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).primaryColor,
                  child: Text(
                    _currentAccount.agentName.isNotEmpty ? _currentAccount.agentName[0] : 'و',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _currentAccount.agentName,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_currentAccount.agentPhone.isNotEmpty)
                        Text(
                          '📞 ${_currentAccount.agentPhone}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      Text(
                        'معرف الوكيل: ${_currentAccount.agentId}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة الرصيد
  Widget _buildBalanceCard() {
    final isDebt = _currentAccount.getCurrentDebt() > 0;
    final balanceColor = isDebt ? Colors.red : Colors.green;
    final balanceAmount = isDebt ? _currentAccount.getCurrentDebt() : _currentAccount.getCreditBalance();
    final balanceLabel = isDebt ? 'مدين للمؤسسة' : 'دائن لدى المؤسسة';

    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            colors: [balanceColor.withOpacity(0.1), balanceColor.withOpacity(0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            Text(
              'الرصيد الحالي',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppUtils.formatCurrency(balanceAmount),
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: balanceColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              balanceLabel,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: balanceColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقات ملخص الحساب
  Widget _buildAccountSummaryCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص الحساب',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppConstants.defaultPadding,
          mainAxisSpacing: AppConstants.defaultPadding,
          childAspectRatio: 1.2,
          children: [
            _buildSummaryCard(
              'البضاعة المستلمة',
              _currentAccount.totalGoodsReceived,
              Icons.input,
              Colors.blue,
            ),
            _buildSummaryCard(
              'البضاعة المسحوبة',
              _currentAccount.totalGoodsWithdrawn,
              Icons.output,
              Colors.orange,
            ),
            _buildSummaryCard(
              'إجمالي المبيعات',
              _currentAccount.totalCustomerSales,
              Icons.shopping_cart,
              Colors.green,
            ),
            _buildSummaryCard(
              'إجمالي العمولات',
              _currentAccount.totalAgentCommission,
              Icons.monetization_on,
              Colors.purple,
            ),
            _buildSummaryCard(
              'إجمالي المدفوعات',
              _currentAccount.totalPaymentsReceived,
              Icons.payment,
              Colors.teal,
            ),
            _buildSummaryCard(
              'عدد المعاملات',
              _currentAccount.transactions.length.toDouble(),
              Icons.list,
              Colors.indigo,
              isCount: true,
            ),
          ],
        ),
      ],
    );
  }

  /// بطاقة ملخص
  Widget _buildSummaryCard(
    String title,
    double value,
    IconData icon,
    Color color, {
    bool isCount = false,
  }) {
    return Card(
      elevation: 1,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.smallPadding),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: color),
            const SizedBox(height: 8),
            Text(
              isCount ? value.toInt().toString() : AppUtils.formatCurrency(value),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقة التحقق من صحة الحسابات
  Widget _buildValidationCard() {
    final isValid = _currentAccount.validateAccounts();
    final validationColor = isValid ? Colors.green : Colors.red;
    final validationIcon = isValid ? Icons.check_circle : Icons.error;
    final validationText = isValid ? 'الحسابات صحيحة' : 'يوجد خطأ في الحسابات';

    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: validationColor.withOpacity(0.1),
        ),
        child: Row(
          children: [
            Icon(validationIcon, color: validationColor, size: 32),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'حالة الحسابات',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    validationText,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: validationColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (!isValid)
                    Text(
                      'يرجى إعادة حساب الحساب',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
            ),
            if (!isValid)
              ElevatedButton(
                onPressed: _recalculateAccount,
                style: ElevatedButton.styleFrom(
                  backgroundColor: validationColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('إعادة حساب'),
              ),
          ],
        ),
      ),
    );
  }

  /// تبويب المعاملات
  Widget _buildTransactionsTab() {
    return Column(
      children: [
        // فلتر المعاملات
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: DropdownButtonFormField<AgentTransactionType?>(
            value: _selectedFilter,
            decoration: const InputDecoration(
              labelText: 'فلترة حسب نوع المعاملة',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.filter_list),
            ),
            items: [
              const DropdownMenuItem<AgentTransactionType?>(
                value: null,
                child: Text('جميع المعاملات'),
              ),
              ...AgentTransactionType.values.map((type) {
                return DropdownMenuItem<AgentTransactionType?>(
                  value: type,
                  child: Text(type.displayName),
                );
              }),
            ],
            onChanged: (value) {
              setState(() {
                _selectedFilter = value;
              });
            },
          ),
        ),
        // قائمة المعاملات
        Expanded(
          child: _buildTransactionsList(),
        ),
      ],
    );
  }

  /// قائمة المعاملات
  Widget _buildTransactionsList() {
    final filteredTransactions = _selectedFilter == null
        ? _currentAccount.transactions
        : _currentAccount.transactions.where((t) => t.type == _selectedFilter).toList();

    if (filteredTransactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد معاملات'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      itemCount: filteredTransactions.length,
      itemBuilder: (context, index) {
        final transaction = filteredTransactions[index];
        return _buildTransactionCard(transaction);
      },
    );
  }

  /// بطاقة المعاملة
  Widget _buildTransactionCard(AgentTransaction transaction) {
    final isPositive = transaction.amount > 0;
    final amountColor = isPositive ? Colors.green : Colors.red;
    final icon = _getTransactionIcon(transaction.type);
    final iconColor = _getTransactionColor(transaction.type);

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: iconColor.withOpacity(0.2),
          child: Icon(icon, color: iconColor, size: 20),
        ),
        title: Text(
          transaction.type.displayName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(transaction.description),
            Text(
              AppUtils.formatDateTime(transaction.createdAt),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            AppUtils.formatCurrency(transaction.amount.abs()),
            style: TextStyle(
              color: amountColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTransactionDetailRow('المبلغ', AppUtils.formatCurrency(transaction.amount)),
                _buildTransactionDetailRow('الرصيد بعد المعاملة', AppUtils.formatCurrency(transaction.balanceAfter)),
                _buildTransactionDetailRow('تاريخ المعاملة', AppUtils.formatDateTime(transaction.createdAt)),
                _buildTransactionDetailRow('المنشئ', transaction.createdBy),
                if (transaction.relatedInvoiceId != null)
                  _buildTransactionDetailRow('الفاتورة المرتبطة', transaction.relatedInvoiceId!),
                if (transaction.relatedTransferId != null)
                  _buildTransactionDetailRow('التحويل المرتبط', transaction.relatedTransferId!),
                if (transaction.relatedPaymentId != null)
                  _buildTransactionDetailRow('الدفعة المرتبطة', transaction.relatedPaymentId!),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// صف تفاصيل المعاملة
  Widget _buildTransactionDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// أيقونة المعاملة
  IconData _getTransactionIcon(AgentTransactionType type) {
    switch (type) {
      case AgentTransactionType.goodsReceived:
        return Icons.input;
      case AgentTransactionType.goodsWithdrawn:
        return Icons.output;
      case AgentTransactionType.customerSale:
        return Icons.shopping_cart;
      case AgentTransactionType.commission:
        return Icons.monetization_on;
      case AgentTransactionType.payment:
        return Icons.payment;
      case AgentTransactionType.adjustment:
        return Icons.tune;
      case AgentTransactionType.other:
        return Icons.more_horiz;
    }
  }

  /// لون المعاملة
  Color _getTransactionColor(AgentTransactionType type) {
    switch (type) {
      case AgentTransactionType.goodsReceived:
        return Colors.blue;
      case AgentTransactionType.goodsWithdrawn:
        return Colors.orange;
      case AgentTransactionType.customerSale:
        return Colors.green;
      case AgentTransactionType.commission:
        return Colors.purple;
      case AgentTransactionType.payment:
        return Colors.teal;
      case AgentTransactionType.adjustment:
        return Colors.indigo;
      case AgentTransactionType.other:
        return Colors.grey;
    }
  }

  /// تبويب التحليلات
  Widget _buildAnalyticsTab() {
    return const Center(
      child: Text('تبويب التحليلات - قيد التطوير'),
    );
  }
}
