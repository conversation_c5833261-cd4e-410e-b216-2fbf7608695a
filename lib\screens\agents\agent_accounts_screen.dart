import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';

import '../../services/data_service.dart';
import '../../core/utils/app_colors.dart';
import '../../core/utils/app_utils.dart';
import 'agent_statement_screen.dart';
import 'record_payment_screen.dart';
import '../../models/user_model.dart';

class AgentAccountSummary {
  final String agentId;
  final double totalSales;
  final double totalProfit;
  final double agentCommission;
  final double totalPayments;
  final double balance;
  final int totalInvoices;
  final DateTime? lastTransactionDate;

  AgentAccountSummary({
    required this.agentId,
    required this.totalSales,
    required this.totalProfit,
    required this.agentCommission,
    required this.totalPayments,
    required this.balance,
    required this.totalInvoices,
    this.lastTransactionDate,
  });
}

class AgentAccountsScreen extends StatefulWidget {
  const AgentAccountsScreen({super.key});

  @override
  State<AgentAccountsScreen> createState() => _AgentAccountsScreenState();
}

class _AgentAccountsScreenState extends State<AgentAccountsScreen> {
  List<UserModel> _agents = [];
  final Map<String, AgentAccountSummary> _accountSummaries = {};
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAgentAccounts();
  }

  Future<void> _loadAgentAccounts() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final dataService = Provider.of<DataService>(context, listen: false);
      
      // Load all agents
      final allUsers = await dataService.getAllUsers();
      _agents = allUsers.where((user) => user.role == 'agent').toList();
      
      // Load account summaries for each agent
      _accountSummaries.clear();
      for (final agent in _agents) {
        final summary = await _calculateAgentAccountSummary(agent.id);
        _accountSummaries[agent.id] = summary;
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل حسابات الوكلاء: $e', isError: true);
      }
    }
  }

  Future<AgentAccountSummary> _calculateAgentAccountSummary(String agentId) async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      // Get agent account (the proper way)
      final agentAccount = await dataService.getAgentAccount(agentId);

      if (agentAccount != null) {
        // Use calculated balance from agent account (more accurate)
        final calculatedBalance = agentAccount.calculatedBalance;

        if (kDebugMode) {
          print('📊 Using existing account for agent: $agentId');
          print('   Stored balance: ${agentAccount.currentBalance}');
          print('   Calculated balance: $calculatedBalance');
        }

        // Use the actual agent account data
        return AgentAccountSummary(
          agentId: agentId,
          totalSales: 0, // We'll calculate this from transactions
          totalProfit: 0, // We'll calculate this from transactions
          agentCommission: agentAccount.totalDebt, // Total debt is what agent owes company
          totalPayments: agentAccount.totalPaid,
          balance: calculatedBalance, // Use calculated balance
          totalInvoices: agentAccount.transactions.where((t) => t.type == 'debt').length,
          lastTransactionDate: agentAccount.transactions.isNotEmpty
              ? agentAccount.transactions.map((t) => t.timestamp).reduce((a, b) => a.isAfter(b) ? a : b)
              : null,
        );
      } else {
        // Fallback to correct calculation method if no account exists
        final invoices = await dataService.getAgentInvoices(agentId);
        final payments = await dataService.getAgentPayments(agentId);

        double totalSales = 0;
        double totalProfit = 0;
        double agentCommission = 0;
        double totalDebt = 0; // Total debt including purchase prices and company profit share
        int totalInvoices = invoices.length;

        if (kDebugMode) {
          print('📊 Calculating agent account summary for agent: $agentId');
          print('📊 Found ${invoices.length} invoices and ${payments.length} payments');
        }

        // Calculate totals from invoices
        for (final invoice in invoices) {
          // Only count customer sales as actual sales
          if (invoice.type == 'customer') {
            totalSales += invoice.sellingPrice;
            totalProfit += invoice.profitAmount;
            agentCommission += invoice.agentProfitShare;
          }

          // Calculate debt based on invoice type
          if (invoice.type == 'agent' ||
              invoice.type == 'goods' ||
              (invoice.customerData != null &&
               invoice.customerData!['transferType'] == 'warehouse_transfer') ||
              (invoice.additionalData != null &&
               invoice.additionalData!['transferType'] == 'goods_transfer')) {
            // Transfer invoice - agent owes full amount
            totalDebt += invoice.sellingPrice;
          } else if (invoice.type == 'customer' && invoice.companyProfitShare > 0) {
            // Customer sale - agent owes only company's profit share
            totalDebt += invoice.companyProfitShare;
          }

          if (kDebugMode) {
            print('📊 Invoice ${invoice.invoiceNumber}: '
                  'Type=${invoice.type}, '
                  'Sales=${invoice.sellingPrice}, '
                  'Purchase=${invoice.purchasePrice}, '
                  'AgentProfit=${invoice.agentProfitShare}, '
                  'CompanyProfit=${invoice.companyProfitShare}, '
                  'DebtAdded=${invoice.type == 'agent' ? invoice.sellingPrice : (invoice.type == 'customer' ? invoice.companyProfitShare : 0)}');
          }
        }

        // Calculate total payments
        double totalPayments = 0;
        for (final payment in payments) {
          totalPayments += payment['amount'] as double? ?? 0;
        }

        // Calculate credits from agent account if available
        double totalCredits = 0.0;
        if (account != null) {
          for (final transaction in account.transactions) {
            if (transaction.type == 'credit') {
              totalCredits += transaction.amount;
              if (kDebugMode) {
                print('💳 Credit transaction: ${transaction.description} = ${AppUtils.formatCurrency(transaction.amount)}');
              }
            }
          }
        }

        // Calculate correct balance (total debt - payments - credits)
        final balance = totalDebt - totalPayments - totalCredits;

        if (kDebugMode) {
          print('📊 Final calculations:');
          print('   Total Sales: $totalSales');
          print('   Total Debt: $totalDebt');
          print('   Total Payments: $totalPayments');
          print('   Total Credits: $totalCredits');
          print('   Balance: $balance');
          print('   Agent Commission: $agentCommission');
        }

        return AgentAccountSummary(
          agentId: agentId,
          totalSales: totalSales,
          totalProfit: totalProfit,
          agentCommission: agentCommission,
          totalPayments: totalPayments,
          balance: balance,
          totalInvoices: totalInvoices,
          lastTransactionDate: invoices.isNotEmpty
              ? invoices.map((i) => i.createdAt).reduce((a, b) => a.isAfter(b) ? a : b)
              : null,
        );
      }
    } catch (e) {
      debugPrint('Error calculating agent account summary: $e');
      return AgentAccountSummary(
        agentId: agentId,
        totalSales: 0,
        totalProfit: 0,
        agentCommission: 0,
        totalPayments: 0,
        balance: 0,
        totalInvoices: 0,
        lastTransactionDate: null,
      );
    }
  }

  List<UserModel> get _filteredAgents {
    if (_searchQuery.isEmpty) {
      return _agents;
    }
    final query = _searchQuery.toLowerCase().trim();
    return _agents.where((agent) {
      return agent.fullName.toLowerCase().contains(query) ||
             agent.username.toLowerCase().contains(query) ||
             agent.phone.toLowerCase().contains(query) ||
             agent.email.toLowerCase().contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حسابات الوكلاء'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAgentAccounts,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'البحث في الوكلاء...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          
          // Summary cards
          if (!_isLoading) _buildSummaryCards(),
          
          // Agents list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredAgents.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.people_outline,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _searchQuery.isEmpty ? 'لا يوجد وكلاء' : 'لا توجد نتائج للبحث',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadAgentAccounts,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _filteredAgents.length,
                          itemBuilder: (context, index) {
                            final agent = _filteredAgents[index];
                            final summary = _accountSummaries[agent.id];
                            return _buildAgentAccountCard(agent, summary);
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    double totalSales = 0;
    double totalCommissions = 0;
    // double totalPayments = 0; // Commented out as not used
    double totalBalance = 0;
    
    for (final summary in _accountSummaries.values) {
      totalSales += summary.totalSales;
      totalCommissions += summary.agentCommission;
      totalBalance += summary.balance;
    }
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي المبيعات',
              AppUtils.formatCurrency(totalSales),
              Icons.trending_up,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'إجمالي العمولات',
              AppUtils.formatCurrency(totalCommissions),
              Icons.account_balance_wallet,
              Colors.green,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              totalBalance >= 0 ? 'رصيد مستحق' : 'مديونية',
              AppUtils.formatCurrency(totalBalance.abs()),
              Icons.balance,
              totalBalance >= 0 ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgentAccountCard(UserModel agent, AgentAccountSummary? summary) {
    if (summary == null) {
      return Card(
        margin: const EdgeInsets.only(bottom: 12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text('خطأ في تحميل بيانات ${agent.fullName}'),
        ),
      );
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppColors.primary,
                  child: Text(
                    agent.fullName.substring(0, 1),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        agent.fullName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '@${agent.username}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                _buildBalanceChip(summary.balance),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Account details
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    'إجمالي المبيعات',
                    AppUtils.formatCurrency(summary.totalSales),
                    Icons.shopping_cart,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'العمولة المستحقة',
                    AppUtils.formatCurrency(summary.agentCommission),
                    Icons.account_balance_wallet,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    'المدفوعات',
                    AppUtils.formatCurrency(summary.totalPayments),
                    Icons.payment,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'عدد الفواتير',
                    summary.totalInvoices.toString(),
                    Icons.receipt,
                  ),
                ),
              ],
            ),
            
            if (summary.lastTransactionDate != null) ...[
              const SizedBox(height: 8),
              Text(
                'آخر معاملة: ${AppUtils.formatDate(summary.lastTransactionDate!)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Actions
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _showAgentDetails(agent, summary),
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('التفاصيل', style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    minimumSize: const Size(80, 32),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _showAddPaymentDialog(agent, summary),
                  icon: const Icon(Icons.payment, size: 16),
                  label: const Text('دفعة', style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    minimumSize: const Size(80, 32),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _generateAgentStatement(agent, summary),
                  icon: const Icon(Icons.description, size: 16),
                  label: const Text('كشف حساب', style: TextStyle(fontSize: 12)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    minimumSize: const Size(80, 32),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildBalanceChip(double balance) {
    final isPositive = balance >= 0;
    return Chip(
      label: Text(
        AppUtils.formatCurrency(balance.abs()),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: isPositive ? Colors.green : Colors.red,
      avatar: Icon(
        isPositive ? Icons.arrow_upward : Icons.arrow_downward,
        color: Colors.white,
        size: 16,
      ),
    );
  }

  void _showAgentDetails(UserModel agent, AgentAccountSummary summary) {
    // Navigate to detailed agent account screen
    AppUtils.showSnackBar(context, 'سيتم إضافة شاشة تفاصيل الحساب قريباً');
  }

  void _showAddPaymentDialog(UserModel agent, AgentAccountSummary summary) async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RecordPaymentScreen(
          agent: agent,
          currentBalance: summary.balance,
        ),
      ),
    );

    // Refresh data if payment was recorded successfully
    if (result == true) {
      _loadAgentAccounts();
    }
  }

  void _generateAgentStatement(UserModel agent, AgentAccountSummary summary) {
    // Navigate to detailed agent statement screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AgentStatementScreen(agent: agent),
      ),
    );
  }
}
