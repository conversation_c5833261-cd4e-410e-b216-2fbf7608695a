import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../core/network/ultimate_ssl_bypass.dart';
import '../core/theme/desktop_theme.dart';

/// Ultimate image widget that uses HTTP only (no SSL)
class UltimateImageWidget extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;
  final String? tooltip;

  const UltimateImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.onTap,
    this.tooltip,
  });

  @override
  State<UltimateImageWidget> createState() => _UltimateImageWidgetState();
}

class _UltimateImageWidgetState extends State<UltimateImageWidget> {
  Uint8List? _imageBytes;
  bool _isLoading = true;
  String? _error;
  final UltimateSSLBypass _ultimateBypass = UltimateSSLBypass();

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(UltimateImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    // Check if URL is valid
    if (widget.imageUrl.isEmpty || widget.imageUrl.trim().isEmpty) {
      if (kDebugMode) {
        print('❌ ULTIMATE IMAGE: Invalid or empty URL: ${widget.imageUrl}');
      }
      setState(() {
        _isLoading = false;
        _error = 'Invalid URL';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
      _imageBytes = null;
    });

    try {
      // Convert to HTTP and download
      final httpUrl = _ultimateBypass.convertToHttp(widget.imageUrl);
      
      if (kDebugMode) {
        print('🖼️ ULTIMATE IMAGE: Loading HTTP URL: $httpUrl');
        if (httpUrl != widget.imageUrl) {
          print('🔧 ULTIMATE IMAGE: Converted from HTTPS: ${widget.imageUrl}');
        }
      }

      final bytes = await _ultimateBypass.downloadImage(widget.imageUrl);
      
      if (mounted) {
        setState(() {
          _imageBytes = bytes;
          _isLoading = false;
          _error = bytes == null ? 'Failed to download' : null;
        });
      }

      if (kDebugMode) {
        if (bytes != null) {
          print('✅ ULTIMATE IMAGE: Loaded successfully (${bytes.length} bytes)');
        } else {
          print('❌ ULTIMATE IMAGE: Failed to load');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ ULTIMATE IMAGE: Error loading ${widget.imageUrl} - $e');
      }
      
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = e.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child;

    if (_isLoading) {
      child = _buildLoadingWidget();
    } else if (_error != null || _imageBytes == null) {
      child = _buildErrorWidget();
    } else {
      child = Image.memory(
        _imageBytes!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        errorBuilder: (context, error, stackTrace) {
          if (kDebugMode) {
            print('❌ ULTIMATE IMAGE: Memory image error - $error');
          }
          return _buildErrorWidget();
        },
      );
    }

    // Apply border radius
    if (widget.borderRadius != null) {
      child = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: child,
      );
    }

    // Apply tap handler
    if (widget.onTap != null) {
      child = InkWell(
        onTap: widget.onTap,
        child: child,
      );
    }

    // Apply tooltip
    if (widget.tooltip != null) {
      child = Tooltip(
        message: widget.tooltip!,
        child: child,
      );
    }

    return child;
  }

  Widget _buildLoadingWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: DesktopTheme.backgroundTertiary,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(strokeWidth: 2),
            SizedBox(height: 8),
            Text(
              'جاري التحميل...',
              style: TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: DesktopTheme.surfaceMedium,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image_outlined,
            size: (widget.width ?? 80) * 0.3,
            color: DesktopTheme.textTertiary,
          ),
          const SizedBox(height: 4),
          Text(
            'فشل التحميل',
            style: DesktopTheme.bodySmall.copyWith(
              color: DesktopTheme.textTertiary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          TextButton(
            onPressed: _loadImage,
            child: const Text(
              'إعادة المحاولة',
              style: TextStyle(fontSize: 10),
            ),
          ),
          if (kDebugMode && _error != null) ...[
            const SizedBox(height: 2),
            Text(
              _error!,
              style: DesktopTheme.bodySmall.copyWith(
                color: DesktopTheme.statusError,
                fontSize: 8,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}

/// Specialized ultimate image widget for item thumbnails
class UltimateItemImageWidget extends StatelessWidget {
  final String imageUrl;
  final double size;
  final VoidCallback? onTap;

  const UltimateItemImageWidget({
    super.key,
    required this.imageUrl,
    this.size = 80,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return UltimateImageWidget(
      imageUrl: imageUrl,
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
      onTap: onTap,
      tooltip: 'انقر لعرض الصورة بحجم أكبر',
    );
  }
}
