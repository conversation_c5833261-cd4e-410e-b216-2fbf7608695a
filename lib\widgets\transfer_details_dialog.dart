import 'package:flutter/material.dart';
import '../core/utils/app_utils.dart';

class TransferDetailsDialog extends StatelessWidget {
  final Map<String, dynamic> transaction;
  final String agentName;

  const TransferDetailsDialog({
    super.key,
    required this.transaction,
    required this.agentName,
  });

  @override
  Widget build(BuildContext context) {
    final reference = transaction['reference'] as String? ?? '-';
    final date = transaction['date'] as DateTime;
    final description = transaction['description'] as String;
    final amount = (transaction['debit'] as num?)?.toDouble() ?? 0.0;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: const BoxConstraints(maxWidth: 500),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withAlpha(51),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.swap_horiz,
                    color: Colors.blue,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تفاصيل فاتورة التحويل',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'الوكيل: $agentName',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Transfer Details
            _buildDetailCard(
              context,
              'معلومات الفاتورة',
              [
                _buildDetailRow('رقم المرجع:', reference),
                _buildDetailRow('التاريخ:', AppUtils.formatDate(date)),
                _buildDetailRow('الوصف:', description),
                _buildDetailRow('القيمة:', AppUtils.formatCurrency(amount)),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Additional Info (if available)
            if (transaction.containsKey('warehouse_from') || 
                transaction.containsKey('items'))
              _buildDetailCard(
                context,
                'تفاصيل إضافية',
                [
                  if (transaction.containsKey('warehouse_from'))
                    _buildDetailRow('من المخزن:', 
                        transaction['warehouse_from'] as String? ?? '-'),
                  if (transaction.containsKey('warehouse_to'))
                    _buildDetailRow('إلى المخزن:', 
                        transaction['warehouse_to'] as String? ?? '-'),
                  if (transaction.containsKey('items_count'))
                    _buildDetailRow('عدد الأصناف:', 
                        '${transaction['items_count']} صنف'),
                ],
              ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إغلاق'),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Navigate to full invoice details
                    Navigator.of(context).pop();
                    _showFullInvoiceDetails(context, reference);
                  },
                  icon: const Icon(Icons.receipt_long),
                  label: const Text('عرض الفاتورة كاملة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailCard(BuildContext context, String title, List<Widget> details) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          ...details,
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFullInvoiceDetails(BuildContext context, String reference) {
    // TODO: Implement navigation to full invoice details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('عرض تفاصيل الفاتورة: $reference'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
