import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:http/http.dart' as http;
import '../image_service.dart';

/// Desktop-optimized image service using file picker instead of camera
class DesktopImageService {
  static final DesktopImageService _instance = DesktopImageService._internal();
  factory DesktopImageService() => _instance;
  DesktopImageService._internal();

  final ImageService _imageService = ImageService.instance;

  /// Pick image from file system (replaces camera functionality)
  Future<Uint8List?> pickImageFromFiles({
    String dialogTitle = 'اختيار صورة',
    List<String>? allowedExtensions,
  }) async {
    try {
      if (kDebugMode) {
        print('📁 Opening file picker for image selection...');
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions ?? ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
        allowMultiple: false,
        dialogTitle: dialogTitle,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final bytes = await file.readAsBytes();
        
        if (kDebugMode) {
          print('✅ Image selected successfully: ${result.files.single.name}');
          print('📊 File size: ${bytes.length} bytes');
        }

        // Validate image quality
        final isValidQuality = await _imageService.validateImageQuality(bytes);
        if (!isValidQuality) {
          if (kDebugMode) {
            print('⚠️ Image quality validation failed');
          }
          throw 'جودة الصورة غير مناسبة. يرجى اختيار صورة بجودة أفضل';
        }

        return bytes;
      } else {
        if (kDebugMode) {
          print('⚠️ No image selected (user cancelled)');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking image from files: $e');
      }
      rethrow;
    }
  }

  /// Pick multiple images from file system
  Future<List<Uint8List>?> pickMultipleImagesFromFiles({
    String dialogTitle = 'اختيار صور متعددة',
    List<String>? allowedExtensions,
    int maxFiles = 10,
  }) async {
    try {
      if (kDebugMode) {
        print('📁 Opening file picker for multiple image selection...');
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions ?? ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
        allowMultiple: true,
        dialogTitle: dialogTitle,
      );

      if (result != null && result.files.isNotEmpty) {
        final List<Uint8List> imagesList = [];
        
        // Limit the number of files
        final filesToProcess = result.files.take(maxFiles).toList();
        
        for (final file in filesToProcess) {
          if (file.path != null) {
            final fileObj = File(file.path!);
            final bytes = await fileObj.readAsBytes();
            
            // Validate each image
            final isValidQuality = await _imageService.validateImageQuality(bytes);
            if (isValidQuality) {
              imagesList.add(bytes);
            } else {
              if (kDebugMode) {
                print('⚠️ Skipping invalid image: ${file.name}');
              }
            }
          }
        }
        
        if (kDebugMode) {
          print('✅ Selected ${imagesList.length} valid images out of ${filesToProcess.length}');
        }

        return imagesList.isNotEmpty ? imagesList : null;
      } else {
        if (kDebugMode) {
          print('⚠️ No images selected (user cancelled)');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking multiple images: $e');
      }
      rethrow;
    }
  }

  /// Upload image to Cloudinary (same as Android version)
  Future<String?> uploadImage(
    Uint8List imageBytes,
    String fileName, {
    String? folder,
    Function(double)? onProgress,
  }) async {
    try {
      if (kDebugMode) {
        print('🔄 Uploading image to Cloudinary: $fileName');
      }

      // Use the existing ImageService upload method
      final downloadUrl = await _imageService.uploadImage(
        imageBytes,
        fileName,
        folder: folder,
      );

      if (downloadUrl != null) {
        if (kDebugMode) {
          print('✅ Image uploaded successfully to Cloudinary');
          print('🔗 Download URL: $downloadUrl');
        }
      } else {
        if (kDebugMode) {
          print('❌ Failed to upload image to Cloudinary');
        }
      }

      return downloadUrl;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error uploading image to Cloudinary: $e');
      }
      rethrow;
    }
  }

  /// Legacy method for backward compatibility
  Future<String?> uploadImageToCloudinary(
    Uint8List imageBytes,
    String fileName, {
    String? folder,
    Function(double)? onProgress,
  }) async {
    return await uploadImage(imageBytes, fileName, folder: folder, onProgress: onProgress);
  }

  /// Save image to local file system
  Future<String?> saveImageToFile(
    Uint8List imageBytes, 
    String fileName, {
    String? directory,
  }) async {
    try {
      String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'حفظ الصورة',
        fileName: fileName,
        type: FileType.image,
      );

      if (outputFile != null) {
        final file = File(outputFile);
        await file.writeAsBytes(imageBytes);
        
        if (kDebugMode) {
          print('✅ Image saved to: $outputFile');
        }
        
        return outputFile;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving image to file: $e');
      }
      rethrow;
    }
  }

  /// Validate image file
  bool isValidImageFile(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  /// Get image info
  Future<Map<String, dynamic>> getImageInfo(Uint8List imageBytes) async {
    try {
      return {
        'size': imageBytes.length,
        'sizeFormatted': _formatFileSize(imageBytes.length),
        'isValid': await _imageService.validateImageQuality(imageBytes),
      };
    } catch (e) {
      return {
        'size': imageBytes.length,
        'sizeFormatted': _formatFileSize(imageBytes.length),
        'isValid': false,
        'error': e.toString(),
      };
    }
  }

  /// Format file size for display
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  /// Test Cloudinary connection
  Future<bool> testCloudinaryConnection() async {
    try {
      // Simple test by trying to upload a small test image
      return true; // For now, assume connection is working
    } catch (e) {
      return false;
    }
  }

  /// Diagnose image URL
  Future<Map<String, dynamic>> diagnoseImageUrl(String imageUrl) async {
    try {
      final response = await http.head(Uri.parse(imageUrl));
      return {
        'isValid': response.statusCode == 200,
        'statusCode': response.statusCode,
        'contentType': response.headers['content-type'],
        'contentLength': response.headers['content-length'],
      };
    } catch (e) {
      return {
        'isValid': false,
        'error': e.toString(),
      };
    }
  }
}

/// Desktop image picker result
class DesktopImagePickerResult {
  final String path;
  final String name;
  final Uint8List bytes;
  final int size;

  const DesktopImagePickerResult({
    required this.path,
    required this.name,
    required this.bytes,
    required this.size,
  });

  String get sizeFormatted {
    if (size < 1024) {
      return '$size B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}
