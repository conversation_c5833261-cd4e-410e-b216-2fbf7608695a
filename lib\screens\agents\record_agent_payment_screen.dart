import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_accounting_system.dart';
import '../../providers/auth_provider.dart';
import '../../services/agent_accounting_service.dart';

/// شاشة تسجيل دفعة من الوكيل
/// تتكامل مع النظام المحاسبي الجديد
class RecordAgentPaymentScreen extends StatefulWidget {
  final AgentAccount agentAccount;
  final VoidCallback? onPaymentRecorded;

  const RecordAgentPaymentScreen({
    super.key,
    required this.agentAccount,
    this.onPaymentRecorded,
  });

  @override
  State<RecordAgentPaymentScreen> createState() => _RecordAgentPaymentScreenState();
}

class _RecordAgentPaymentScreenState extends State<RecordAgentPaymentScreen> {
  final AgentAccountingService _accountingService = AgentAccountingService();
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedPaymentMethod = 'نقدي';
  bool _isProcessing = false;

  final List<String> _paymentMethods = [
    'نقدي',
    'تحويل بنكي',
    'شيك',
    'فيزا',
    'ماستركارد',
    'محفظة إلكترونية',
    'حوالة',
    'أخرى',
  ];

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تسجيل الدفعة
  Future<void> _recordPayment() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      setState(() {
        _isProcessing = true;
      });

      final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
      if (currentUser == null) {
        throw 'يجب تسجيل الدخول أولاً';
      }

      final amount = double.parse(_amountController.text);
      
      if (kDebugMode) {
        print('💰 Recording payment: $amount from agent: ${widget.agentAccount.agentName}');
      }

      await _accountingService.recordPayment(
        agentId: widget.agentAccount.agentId,
        amount: amount,
        paymentMethod: _selectedPaymentMethod,
        userId: currentUser.id,
        notes: _notesController.text.trim(),
      );

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'تم تسجيل دفعة ${AppUtils.formatCurrency(amount)} من ${widget.agentAccount.agentName} بنجاح',
        );
        
        // استدعاء callback إذا كان موجود
        widget.onPaymentRecorded?.call();
        
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error recording payment: $e');
      }
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تسجيل الدفعة: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text('تسجيل دفعة من ${widget.agentAccount.agentName}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildAgentInfoCard(),
              const SizedBox(height: AppConstants.defaultPadding),
              _buildPaymentForm(),
              const SizedBox(height: AppConstants.largePadding),
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  /// بطاقة معلومات الوكيل
  Widget _buildAgentInfoCard() {
    final isDebt = widget.agentAccount.getCurrentDebt() > 0;
    final balanceColor = isDebt ? Colors.red : Colors.green;
    final balanceAmount = isDebt ? widget.agentAccount.getCurrentDebt() : widget.agentAccount.getCreditBalance();
    final balanceLabel = isDebt ? 'مدين للمؤسسة' : 'دائن لدى المؤسسة';
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الوكيل',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).primaryColor,
                  child: Text(
                    widget.agentAccount.agentName.isNotEmpty ? widget.agentAccount.agentName[0] : 'و',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.agentAccount.agentName,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (widget.agentAccount.agentPhone.isNotEmpty)
                        Text(
                          '📞 ${widget.agentAccount.agentPhone}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            const Divider(),
            
            // الرصيد الحالي
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: balanceColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: balanceColor.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الرصيد الحالي',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        balanceLabel,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: balanceColor,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    AppUtils.formatCurrency(balanceAmount),
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: balanceColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// نموذج الدفعة
  Widget _buildPaymentForm() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الدفعة',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            
            // مبلغ الدفعة
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'مبلغ الدفعة *',
                hintText: 'أدخل مبلغ الدفعة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.monetization_on),
                suffixText: 'ريال',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال مبلغ الدفعة';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'يرجى إدخال مبلغ صحيح';
                }
                // التحقق من أن المبلغ لا يتجاوز المديونية (إذا كان الوكيل مدين)
                final currentDebt = widget.agentAccount.getCurrentDebt();
                if (currentDebt > 0 && amount > currentDebt) {
                  return 'المبلغ أكبر من المديونية الحالية (${AppUtils.formatCurrency(currentDebt)})';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // طريقة الدفع
            DropdownButtonFormField<String>(
              value: _selectedPaymentMethod,
              decoration: const InputDecoration(
                labelText: 'طريقة الدفع *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.payment),
              ),
              items: _paymentMethods.map((method) {
                return DropdownMenuItem(
                  value: method,
                  child: Text(method),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPaymentMethod = value!;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى اختيار طريقة الدفع';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // ملاحظات
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                hintText: 'أضف أي ملاحظات حول الدفعة...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 3,
              maxLength: 500,
            ),
          ],
        ),
      ),
    );
  }

  /// زر التسجيل
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: _isProcessing ? null : _recordPayment,
        icon: _isProcessing
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.save),
        label: Text(
          _isProcessing ? 'جاري التسجيل...' : 'تسجيل الدفعة',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}
