import 'dart:convert';
import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'firebase_service.dart';
import 'local_database_service.dart';
import '../models/user_model.dart';
import '../core/constants/app_constants.dart';

class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  
  AuthService._();

  final FirebaseService _firebaseService = FirebaseService.instance;
  final LocalDatabaseService _localDb = LocalDatabaseService.instance;
  
  UserModel? _currentUser;
  
  // Get current user
  UserModel? get currentUser => _currentUser;
  
  // Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  // Initialize auth service
  Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('🔐 Initializing AuthService...');
      }

      // Try to restore session from local storage first - this is critical for offline mode
      await _restoreUserSession();

      // Check if user is already signed in with Firebase
      final firebaseUser = _firebaseService.currentUser;
      if (firebaseUser != null && _currentUser == null) {
        await _loadCurrentUser(firebaseUser.uid);
      }

      // If we have a restored user but no Firebase user, maintain the offline session
      if (_currentUser != null && firebaseUser == null) {
        if (kDebugMode) {
          print('🔄 Maintaining offline session for: ${_currentUser!.fullName}');
        }
        // Don't clear the user - maintain offline session
      }

      // Listen to auth state changes only if we can connect
      try {
        _firebaseService.auth.authStateChanges().listen(_onAuthStateChanged);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Firebase auth listener failed (offline mode): $e');
        }
      }

      if (kDebugMode) {
        print('✅ AuthService initialized successfully');
        if (_currentUser != null) {
          print('✅ Current user: ${_currentUser!.fullName} (${_currentUser!.role})');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing auth service: $e');
      }
    }
  }

  // Handle auth state changes
  void _onAuthStateChanged(User? firebaseUser) async {
    if (firebaseUser != null) {
      await _loadCurrentUser(firebaseUser.uid);
    } else {
      // Don't immediately clear user data - check if we should maintain offline session
      final prefs = await SharedPreferences.getInstance();
      final rememberLogin = prefs.getBool('remember_login') ?? false;

      if (!rememberLogin) {
        // Only clear if user didn't choose to remember login
        _currentUser = null;
        await _clearLocalUserData();
        if (kDebugMode) {
          print('🔓 User session cleared (not remembered)');
        }
      } else {
        if (kDebugMode) {
          print('🔄 Maintaining offline session (remember login enabled)');
        }
      }
    }
  }

  // Load current user data
  Future<void> _loadCurrentUser(String userId) async {
    try {
      final firebaseUser = _firebaseService.currentUser;
      if (firebaseUser == null) return;

      // Try to load from local database first by email
      final localUsers = await _localDb.query(
        'users',
        where: 'email = ?',
        whereArgs: [firebaseUser.email],
      );

      if (localUsers.isNotEmpty) {
        _currentUser = UserModel.fromMap(localUsers.first);
      } else {
        // Try to load from Firebase
        try {
          final querySnapshot = await _firebaseService.firestore
              .collection(AppConstants.usersCollection)
              .where('email', isEqualTo: firebaseUser.email)
              .limit(1)
              .get();

          if (querySnapshot.docs.isNotEmpty) {
            _currentUser = UserModel.fromFirestore(querySnapshot.docs.first);
            // Save to local database
            await _localDb.insert('users', _currentUser!.toMap());
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error loading from Firebase: $e');
          }
        }
      }

      if (_currentUser != null) {
        await _saveUserSession();
        if (kDebugMode) {
          print('Current user loaded: ${_currentUser!.fullName}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading current user: $e');
      }
    }
  }

  // Sign in with email and password
  Future<UserModel?> signInWithEmailAndPassword(String email, String password) async {
    try {
      if (kDebugMode) {
        print('🔐 Attempting Firebase sign in for: $email');
      }

      final credential = await _firebaseService.auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        if (kDebugMode) {
          print('✅ Firebase sign in successful for: $email');
        }

        await _loadCurrentUser(credential.user!.uid);
        await _saveUserSession();

        if (kDebugMode) {
          print('✅ User loaded and session saved: ${_currentUser?.fullName}');
        }

        return _currentUser;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        print('❌ Firebase Auth Error for $email: ${e.code} - ${e.message}');
      }
      throw _getAuthErrorMessage(e.code);
    } catch (e) {
      if (kDebugMode) {
        print('❌ General sign in error for $email: $e');
      }
      throw 'حدث خطأ أثناء تسجيل الدخول';
    }
  }

  // Sign in with username and password (Smart Local + Firebase hybrid)
  Future<UserModel?> signInWithUsername(String username, String password) async {
    try {
      if (kDebugMode) {
        print('🔐 Starting smart login for: $username');
        print('🔍 Password check: "$password" == "admin123" ? ${password == 'admin123'}');
        print('🔍 Username check: "$username" in [admin, ahmed] ? ${username == 'admin' || username == 'ahmed'}');
      }

      // PRIORITY CHECK: Super admin with default credentials
      // This should be checked FIRST before any Firebase or database operations

      if ((username == 'admin' || username == 'ahmed') && password == 'admin123') {
        if (kDebugMode) {
          print('🔑 Detected super admin default credentials - checking local database');
        }

        // Find super admin in local database
        final localUsers = await _localDb.query(
          'users',
          where: 'username = ? AND role = ?',
          whereArgs: [username, AppConstants.superAdminRole],
        );

        if (localUsers.isNotEmpty) {
          final user = UserModel.fromMap(localUsers.first);
          _currentUser = user;
          await _saveUserSession();

          if (kDebugMode) {
            print('✅ Super admin signed in with default credentials: ${user.fullName}');
            print('🔑 Bypassed all other authentication methods');
          }

          return user;
        } else {
          // Create default super admin if not exists
          if (kDebugMode) {
            print('🔧 Creating default super admin user');
          }

          final defaultAdmin = UserModel(
            id: 'default_admin_${DateTime.now().millisecondsSinceEpoch}',
            username: username,
            fullName: 'المدير الأعلى',
            email: '<EMAIL>',
            phone: '01000000000',
            role: AppConstants.superAdminRole,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            additionalData: {'password': 'admin123'},
          );

          // Save to local database
          await _localDb.insert('users', defaultAdmin.toMap());

          _currentUser = defaultAdmin;
          await _saveUserSession();

          if (kDebugMode) {
            print('✅ Default super admin created and signed in: ${defaultAdmin.fullName}');
            print('🔑 This ensures the app works on any computer');
          }

          return defaultAdmin;
        }
      }

      // Check internet connectivity
      final isOnline = await _isOnline();
      if (kDebugMode) {
        print('🌐 Internet status: ${isOnline ? "ONLINE" : "OFFLINE"}');
      }

      // Strategy 1: If ONLINE, try Firebase first for latest data
      if (isOnline) {
        try {
          if (kDebugMode) {
            print('🔍 Searching Firebase for user: $username');
          }

          final querySnapshot = await _firebaseService.firestore
              .collection(AppConstants.usersCollection)
              .where('username', isEqualTo: username)
              .limit(1)
              .get();

          if (querySnapshot.docs.isNotEmpty) {
            final userData = querySnapshot.docs.first.data();
            final email = userData['email'];

            if (kDebugMode) {
              print('✅ User found in Firebase: $email');
            }

            // Try Firebase authentication with better error handling
            try {
              if (kDebugMode) {
                print('🔄 Attempting Firebase authentication for: $email');
              }

              final user = await signInWithEmailAndPassword(email, password);

              if (kDebugMode) {
                print('✅ Firebase authentication successful for: $email');
              }

              return user;
            } catch (e) {
              if (kDebugMode) {
                print('⚠️ Firebase auth failed for $email: $e');
                print('🔄 Will try local authentication as fallback');
              }

              // Special check for super admin with default credentials
              if ((username == 'admin' || username == 'ahmed') && password == 'admin123') {
                if (kDebugMode) {
                  print('🔑 Detected super admin default credentials, will use local auth');
                }
              }
              // Continue to local authentication
            }
          } else {
            if (kDebugMode) {
              print('ℹ️ User not found in Firebase, trying local database');
            }
          }
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Firebase query failed, trying local database: $e');
          }
        }
      }

      // Strategy 2: Try local database (either offline or Firebase failed)
      if (kDebugMode) {
        print('🔍 Searching local database for user: $username');
      }

      final localUsers = await _localDb.query(
        'users',
        where: 'username = ?',
        whereArgs: [username],
      );

      if (localUsers.isNotEmpty) {
        final user = UserModel.fromMap(localUsers.first);

        if (kDebugMode) {
          print('✅ Found local user: ${user.fullName} (${user.role})');
          print('🔍 Has password in additionalData: ${user.additionalData?.containsKey('password') ?? false}');
          print('🔍 Is super admin: ${user.role == AppConstants.superAdminRole}');
          print('🔍 Username matches admin: ${username == 'admin' || username == 'ahmed'}');
        }

        // Note: Super admin authentication is handled at the top of the function
        // This section is for regular users only

        // For other users, check password in additionalData
        if (user.additionalData != null && user.additionalData!.containsKey('password')) {
          final storedPassword = user.additionalData!['password'];
          if (storedPassword == password) {
            _currentUser = user;
            await _saveUserSession();

            if (kDebugMode) {
              print('✅ User signed in locally: ${user.fullName} (${user.role})');
            }

            return user;
          } else {
            if (kDebugMode) {
              print('❌ Local password mismatch for user: ${user.fullName}');
            }
            throw 'كلمة المرور غير صحيحة';
          }
        }

        // If no password stored locally and we're online, try Firebase auth
        if (user.email.isNotEmpty && isOnline) {
          try {
            if (kDebugMode) {
              print('🔄 Trying Firebase auth for local user: ${user.email}');
            }
            return await signInWithEmailAndPassword(user.email, password);
          } catch (e) {
            if (kDebugMode) {
              print('❌ Firebase auth failed for local user: $e');
            }
            throw 'كلمة المرور غير صحيحة';
          }
        } else if (!isOnline) {
          if (kDebugMode) {
            print('⚠️ Offline mode: Cannot verify password for ${user.fullName}');
          }
          throw 'لا يمكن التحقق من كلمة المرور في وضع عدم الاتصال';
        }
      }

      // Strategy 3: If not found anywhere
      if (isOnline) {
        throw 'اسم المستخدم غير موجود';
      } else {
        throw 'اسم المستخدم غير موجود (وضع عدم الاتصال)';
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Login failed for $username: $e');
      }
      rethrow;
    }
  }

  // Create new user (only for super admin)
  Future<UserModel?> createUser({
    required String username,
    required String email,
    required String password,
    required String fullName,
    required String phone,
    required String role,
    String? warehouseId,
  }) async {
    try {
      // Check if current user is super admin
      if (_currentUser?.role != AppConstants.superAdminRole) {
        throw 'ليس لديك صلاحية لإنشاء مستخدمين جدد';
      }



      // Create Firebase user
      final credential = await _firebaseService.auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        final now = DateTime.now();
        final newUser = UserModel(
          id: credential.user!.uid,
          username: username,
          email: email,
          fullName: fullName,
          phone: phone,
          role: role,
          warehouseId: warehouseId,
          isActive: true,
          createdAt: now,
          updatedAt: now,
        );

        // Save to Firebase
        await _firebaseService.firestore
            .collection(AppConstants.usersCollection)
            .doc(newUser.id)
            .set(newUser.toFirestore());

        // Save to local database
        await _localDb.insert('users', newUser.toMap());

        if (kDebugMode) {
          print('New user created: ${newUser.fullName}');
        }

        return newUser;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        print('Create user error: ${e.code} - ${e.message}');
      }
      throw _getAuthErrorMessage(e.code);
    } catch (e) {
      if (kDebugMode) {
        print('Create user error: $e');
      }
      rethrow;
    }
  }

  // Update user profile
  Future<void> updateUserProfile(UserModel updatedUser) async {
    try {
      // Update in Firebase
      await _firebaseService.firestore
          .collection(AppConstants.usersCollection)
          .doc(updatedUser.id)
          .update(updatedUser.toFirestore());

      // Update in local database
      await _localDb.update(
        'users',
        updatedUser.toMap(),
        'id = ?',
        [updatedUser.id],
      );

      // Update current user if it's the same user
      if (_currentUser?.id == updatedUser.id) {
        _currentUser = updatedUser;
        await _saveUserSession();
      }

      if (kDebugMode) {
        print('User profile updated: ${updatedUser.fullName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating user profile: $e');
      }
      rethrow;
    }
  }

  // Change password
  Future<void> changePassword(String currentPassword, String newPassword) async {
    try {
      final user = _firebaseService.currentUser;
      if (user == null) throw 'المستخدم غير مسجل الدخول';

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );
      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);

      if (kDebugMode) {
        print('Password changed successfully');
      }
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        print('Change password error: ${e.code} - ${e.message}');
      }
      throw _getAuthErrorMessage(e.code);
    } catch (e) {
      if (kDebugMode) {
        print('Change password error: $e');
      }
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      // Sign out from Firebase
      await _firebaseService.signOut();

      // Clear current user
      _currentUser = null;

      // Clear all local session data
      await _clearLocalUserData();

      if (kDebugMode) {
        print('✅ User signed out successfully - all session data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Sign out error: $e');
      }
      rethrow;
    }
  }

  // Save user session to local storage
  Future<void> _saveUserSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_currentUser != null) {
        await prefs.setString('current_user_id', _currentUser!.id);
        await prefs.setString('current_user_role', _currentUser!.role);
        await prefs.setString('current_user_data', json.encode(_currentUser!.toMap()));
        await prefs.setBool('is_logged_in', true);
        await prefs.setString('user_email', _currentUser!.email);
        await prefs.setString('login_timestamp', DateTime.now().toIso8601String());

        // Always set session to be remembered (persistent login)
        await prefs.setBool('remember_login', true);
        await prefs.setString('session_token', 'persistent_${_currentUser!.id}_${DateTime.now().millisecondsSinceEpoch}');

        // Additional session data for better persistence
        await prefs.setString('last_activity', DateTime.now().toIso8601String());
        await prefs.setString('user_full_name', _currentUser!.fullName);
        await prefs.setString('user_warehouse_id', _currentUser!.warehouseId ?? '');

        if (kDebugMode) {
          print('✅ User session saved successfully for: ${_currentUser!.fullName}');
          print('   - Role: ${_currentUser!.role}');
          print('   - Warehouse: ${_currentUser!.warehouseId ?? "N/A"}');
          print('   - Remember login: true');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving user session: $e');
      }
    }
  }

  // Restore user session from local storage
  Future<void> _restoreUserSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool('is_logged_in') ?? false;
      final rememberLogin = prefs.getBool('remember_login') ?? false;
      final userDataString = prefs.getString('current_user_data');
      final sessionToken = prefs.getString('session_token');
      final loginTimestamp = prefs.getString('login_timestamp');

      if (kDebugMode) {
        print('🔍 Checking saved session...');
        print('   - isLoggedIn: $isLoggedIn');
        print('   - rememberLogin: $rememberLogin');
        print('   - hasUserData: ${userDataString != null}');
        print('   - hasSessionToken: ${sessionToken != null}');
        print('   - loginTimestamp: $loginTimestamp');
      }

      if (isLoggedIn && userDataString != null) {
        final userData = json.decode(userDataString);
        _currentUser = UserModel.fromMap(userData);

        // Always restore if remember login is enabled or if session is recent
        if (rememberLogin || _isRecentSession(loginTimestamp)) {
          if (kDebugMode) {
            print('✅ User session restored: ${_currentUser!.fullName} (${_currentUser!.role})');
          }
        } else {
          if (kDebugMode) {
            print('⏰ Session expired, clearing data');
          }
          _currentUser = null;
          await _clearLocalUserData();
        }
      } else {
        if (kDebugMode) {
          print('ℹ️ No valid session found to restore');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error restoring user session: $e');
      }
      // Clear corrupted session data
      await _clearLocalUserData();
    }
  }

  // Check if session is recent (within 30 days)
  bool _isRecentSession(String? loginTimestamp) {
    if (loginTimestamp == null) return false;

    try {
      final loginTime = DateTime.parse(loginTimestamp);
      final now = DateTime.now();
      final difference = now.difference(loginTime);

      // Session is valid for 30 days
      return difference.inDays < 30;
    } catch (e) {
      return false;
    }
  }

  // Check if error is a common sync-related error that shouldn't be logged
  bool _isCommonSyncError(String error) {
    final commonErrors = [
      'unknown-error',
      'network-request-failed',
      'too-many-requests',
      'كلمة المرور غير صحيحة',
      'حدث خطأ غير متوقع',
    ];

    return commonErrors.any((commonError) => error.contains(commonError));
  }



  // Clear local user data
  Future<void> _clearLocalUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user_id');
      await prefs.remove('current_user_role');
      await prefs.remove('current_user_data');
      await prefs.remove('is_logged_in');
      await prefs.remove('user_email');
      await prefs.remove('login_timestamp');
      await prefs.remove('remember_login');
      await prefs.remove('session_token');

      if (kDebugMode) {
        print('✅ All local user data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing local user data: $e');
      }
    }
  }

  // Check if device is online
  Future<bool> _isOnline() async {
    try {
      // Simple connectivity check
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // Get user-friendly error messages
  String _getAuthErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'المستخدم غير موجود';
      case 'wrong-password':
        return 'كلمة المرور غير صحيحة';
      case 'email-already-in-use':
        return 'البريد الإلكتروني مستخدم بالفعل';
      case 'weak-password':
        return 'كلمة المرور ضعيفة';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'user-disabled':
        return 'تم تعطيل هذا المستخدم';
      case 'too-many-requests':
        return 'تم تجاوز عدد المحاولات المسموح، حاول لاحقاً';
      case 'network-request-failed':
        return 'خطأ في الاتصال بالإنترنت';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }

  // Check user permissions
  bool hasPermission(String permission) {
    if (_currentUser == null) return false;

    switch (permission) {
      case 'manage_users':
        return _currentUser!.isSuperAdmin;
      case 'manage_inventory':
        return _currentUser!.isSuperAdmin || _currentUser!.isAdmin;
      case 'create_invoices':
        return _currentUser!.canCreateInvoices;
      case 'view_reports':
        return _currentUser!.canViewReports;
      case 'manage_documents':
        return _currentUser!.canManageDocuments;
      default:
        return false;
    }
  }


}
