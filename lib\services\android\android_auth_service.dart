import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// Import Android services
import 'android_session_service.dart';
import '../data_service.dart';

/// Android-specific authentication service with enhanced session management
class AndroidAuthService {
  static AndroidAuthService? _instance;
  static AndroidAuthService get instance => _instance ??= AndroidAuthService._();
  
  AndroidAuthService._();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  bool _isInitialized = false;
  Map<String, dynamic>? _currentUser;

  /// Initialize the auth service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🔐 Initializing Android Auth Service...');
      }

      // Check for existing session
      await _restoreUserSession();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Android Auth Service initialized successfully');
        if (_currentUser != null) {
          print('👤 Restored user session: ${_currentUser!['fullName']}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing Android Auth Service: $e');
      }
    }
  }

  /// Get current user
  Map<String, dynamic>? get currentUser => _currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentUser != null;

  /// Smart login with enhanced session management
  Future<Map<String, dynamic>> smartLogin({
    required String username,
    required String password,
    required bool rememberLogin,
  }) async {
    try {
      if (kDebugMode) {
        print('🔐 Starting Android smart login for: $username');
      }

      // Check if user exists in local database first
      final localUser = await _checkLocalUser(username, password);
      if (localUser != null) {
        if (kDebugMode) {
          print('✅ Local authentication successful');
        }
        
        // Save session
        await _saveUserSession(localUser, rememberLogin);
        
        return {
          'success': true,
          'user': localUser,
          'method': 'local',
          'message': 'تم تسجيل الدخول بنجاح',
        };
      }

      // Try Firebase authentication
      final firebaseResult = await _firebaseLogin(username, password);
      if (firebaseResult['success']) {
        final user = firebaseResult['user'];
        
        // Save session
        await _saveUserSession(user, rememberLogin);
        
        if (kDebugMode) {
          print('✅ Firebase authentication successful');
        }
        
        return {
          'success': true,
          'user': user,
          'method': 'firebase',
          'message': 'تم تسجيل الدخول بنجاح',
        };
      }

      return {
        'success': false,
        'message': 'اسم المستخدم أو كلمة المرور غير صحيحة',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in smart login: $e');
      }
      
      return {
        'success': false,
        'message': 'حدث خطأ أثناء تسجيل الدخول: $e',
      };
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      if (kDebugMode) {
        print('🚪 Logging out user...');
      }

      // Sign out from Firebase
      await _auth.signOut();
      
      // Clear session
      await AndroidSessionService.instance.clearSession();
      
      // Clear current user
      _currentUser = null;
      
      if (kDebugMode) {
        print('✅ User logged out successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error logging out: $e');
      }
    }
  }

  /// Restore user session on app start
  Future<void> _restoreUserSession() async {
    try {
      if (kDebugMode) {
        print('🔍 Checking for saved session...');
      }

      final sessionData = await AndroidSessionService.instance.loadUserSession();
      
      if (sessionData != null && sessionData['isValid'] == true) {
        _currentUser = sessionData['userData'];
        
        if (kDebugMode) {
          print('✅ User session restored: ${_currentUser!['fullName']}');
          print('   - Role: ${_currentUser!['role']}');
          print('   - Login time: ${sessionData['loginTimestamp']}');
        }
      } else {
        if (kDebugMode) {
          print('❌ No valid session found');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error restoring session: $e');
      }
    }
  }

  /// Save user session
  Future<void> _saveUserSession(Map<String, dynamic> user, bool rememberLogin) async {
    try {
      _currentUser = user;
      
      await AndroidSessionService.instance.saveUserSession(
        userId: user['id'],
        userData: user,
        rememberLogin: rememberLogin,
      );
      
      if (kDebugMode) {
        print('✅ User session saved: ${user['fullName']}');
        print('   - Remember login: $rememberLogin');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving session: $e');
      }
    }
  }

  /// Check local user credentials
  Future<Map<String, dynamic>?> _checkLocalUser(String username, String password) async {
    try {
      // Use DataService to check local credentials
      final dataService = DataService.instance;
      final users = await dataService.getAllUsers();
      
      for (final user in users) {
        if (user.username == username) {
          // Check password (in real app, this should be hashed)
          if (_verifyPassword(password, user.username)) {
            return {
              'id': user.id,
              'username': user.username,
              'email': user.email,
              'fullName': user.fullName,
              'phone': user.phone,
              'role': user.role,
              'warehouseId': user.warehouseId,
              'isActive': user.isActive,
            };
          }
        }
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking local user: $e');
      }
      return null;
    }
  }

  /// Firebase login
  Future<Map<String, dynamic>> _firebaseLogin(String username, String password) async {
    try {
      // Convert username to email format
      String email = username;
      if (!username.contains('@')) {
        email = '$<EMAIL>';
      }

      if (kDebugMode) {
        print('🔄 Attempting Firebase authentication for: $email');
      }

      // Sign in with Firebase
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Get user data from Firestore
        final userDoc = await _firestore
            .collection('users')
            .doc(credential.user!.uid)
            .get();

        if (userDoc.exists) {
          final userData = userDoc.data()!;
          userData['id'] = credential.user!.uid;
          
          return {
            'success': true,
            'user': userData,
          };
        }
      }

      return {
        'success': false,
        'message': 'بيانات المستخدم غير موجودة',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Firebase authentication failed: $e');
      }
      
      return {
        'success': false,
        'message': 'فشل في تسجيل الدخول عبر Firebase: $e',
      };
    }
  }

  /// Verify password (simple implementation - should be enhanced with proper hashing)
  bool _verifyPassword(String password, String username) {
    // Simple password verification - in real app, use proper hashing
    final validPasswords = {
      'admin': 'admin123',
      'ahmed': '123456',
      'manager': 'manager123',
    };
    
    return validPasswords[username] == password;
  }

  /// Update user activity
  Future<void> updateActivity() async {
    try {
      if (_currentUser != null) {
        // Update last activity in session
        await AndroidSessionService.instance.initialize(); // This updates last activity
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating activity: $e');
      }
    }
  }

  /// Check if session is still valid
  Future<bool> isSessionValid() async {
    try {
      return await AndroidSessionService.instance.isLoggedIn();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking session validity: $e');
      }
      return false;
    }
  }

  /// Refresh user data
  Future<void> refreshUserData() async {
    try {
      if (_currentUser != null) {
        final sessionData = await AndroidSessionService.instance.loadUserSession();
        if (sessionData != null && sessionData['isValid'] == true) {
          _currentUser = sessionData['userData'];
          
          if (kDebugMode) {
            print('✅ User data refreshed: ${_currentUser!['fullName']}');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error refreshing user data: $e');
      }
    }
  }
}
