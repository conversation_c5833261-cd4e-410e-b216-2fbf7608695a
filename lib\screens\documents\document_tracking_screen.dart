import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../widgets/ultimate_image_widget.dart';
// import 'package:share_plus/share_plus.dart'; // Removed for Windows compatibility
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../../models/document_tracking_model.dart';
import '../../models/item_model.dart';
import '../../services/data_service.dart';
import '../../services/composite_image_service.dart';
import '../../services/auth_service.dart';
import '../../services/local_database_service.dart';

class DocumentTrackingScreen extends StatefulWidget {
  const DocumentTrackingScreen({super.key});

  @override
  State<DocumentTrackingScreen> createState() => _DocumentTrackingScreenState();
}

class _DocumentTrackingScreenState extends State<DocumentTrackingScreen> {
  final DataService _dataService = DataService.instance;
  List<DocumentTrackingModel> _trackingList = [];
  List<DocumentTrackingModel> _filteredList = [];
  final Map<String, ItemModel> _itemsMap = {};
  bool _isLoading = true;
  String _searchQuery = '';
  String _statusFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Clear local data first to ensure fresh sync
      await _dataService.clearLocalDocumentTracking();

      // Force refresh document tracking data from Firebase
      _trackingList = await _dataService.getAllDocumentTracking(forceFromFirebase: true);

      if (kDebugMode) {
        print('📋 Tracking: Cleared local data and loaded ${_trackingList.length} document tracking records from Firebase');
      }

      // Load related items
      final itemIds = _trackingList.map((t) => t.itemId).toSet();
      for (final itemId in itemIds) {
        final item = await _dataService.getItemById(itemId);
        if (item != null) {
          _itemsMap[itemId] = item;
        }
      }

      _filterList();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات من Firebase'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterList() {
    List<DocumentTrackingModel> filtered = List.from(_trackingList);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((tracking) {
        final item = _itemsMap[tracking.itemId];
        return item?.motorFingerprintText.toLowerCase().contains(_searchQuery.toLowerCase()) == true ||
               item?.chassisNumber.toLowerCase().contains(_searchQuery.toLowerCase()) == true ||
               tracking.id.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Apply status filter
    if (_statusFilter != 'all') {
      filtered = filtered.where((tracking) => tracking.currentStatus == _statusFilter).toList();
    }

    setState(() {
      _filteredList = filtered;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تتبع الجوابات'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'مزامنة مع Firebase',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'البحث',
                    hintText: 'ابحث بالبصمة أو رقم الشاسيه',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                    _filterList();
                  },
                ),
                const SizedBox(height: 12),
                DropdownButtonFormField<String>(
                  value: _statusFilter,
                  decoration: const InputDecoration(
                    labelText: 'فلترة حسب الحالة',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                    DropdownMenuItem(value: 'pending', child: Text('في الانتظار')),
                    DropdownMenuItem(value: 'sent_to_manufacturer', child: Text('تم الإرسال للمصنع')),
                    DropdownMenuItem(value: 'received_from_manufacturer', child: Text('تم الاستلام من المصنع')),
                    DropdownMenuItem(value: 'delivered_to_agent', child: Text('تم تسليم الجواب للوكيل')),
                    DropdownMenuItem(value: 'ready_for_pickup', child: Text('جاهز للاستلام')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _statusFilter = value!;
                    });
                    _filterList();
                  },
                ),
              ],
            ),
          ),

          // List section
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredList.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.search_off, size: 64, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد نتائج',
                              style: TextStyle(fontSize: 18, color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadData,
                        child: ListView.builder(
                          padding: const EdgeInsets.all(16.0),
                          itemCount: _filteredList.length,
                          itemBuilder: (context, index) {
                            final tracking = _filteredList[index];
                            final item = _itemsMap[tracking.itemId];
                            return _buildTrackingCard(tracking, item);
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingCard(DocumentTrackingModel tracking, ItemModel? item) {
    final statusColor = _getStatusColor(tracking.currentStatus);
    final statusIcon = _getStatusIcon(tracking.currentStatus);
    final statusText = _getStatusDisplayName(tracking.currentStatus);

    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        onTap: () => _showTrackingDetails(tracking, item),
        borderRadius: BorderRadius.circular(8.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item info
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (item != null) ...[
                          Text(
                            '${item.brand} ${item.model}',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'بصمة الموتور: ${item.motorFingerprintText}',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ] else ...[
                          Text(
                            'معرف الصنف: ${tracking.itemId}',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: statusColor),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(statusIcon, size: 16, color: statusColor),
                        const SizedBox(width: 4),
                        Text(
                          statusText,
                          style: TextStyle(
                            color: statusColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Timestamps
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'تم الإنشاء: ${_formatDate(tracking.createdAt)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  Text(
                    'آخر تحديث: ${_formatDate(tracking.updatedAt)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showTrackingDetails(DocumentTrackingModel tracking, ItemModel? item) async {
    // Get invoice and customer details
    final invoice = await _getInvoiceDetails(tracking.invoiceId);
    final customer = invoice != null ? await _getCustomerDetails(invoice) : null;
    final agent = invoice != null ? await _getAgentDetails(invoice) : null;

    if (!mounted) return;

    // Navigate to full screen details
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DocumentTrackingDetailsScreen(
          tracking: tracking,
          item: item,
          invoice: invoice,
          customer: customer,
          agent: agent,
        ),
      ),
    );
  }

  // Helper methods for getting details
  Future<dynamic> _getInvoiceDetails(String invoiceId) async {
    try {
      final invoices = await _dataService.getInvoices();
      return invoices.firstWhere((invoice) => invoice.id == invoiceId);
    } catch (e) {
      return null;
    }
  }

  Future<Map<String, dynamic>?> _getCustomerDetails(dynamic invoice) async {
    try {
      if (invoice.customerData != null && invoice.customerData is Map) {
        return Map<String, dynamic>.from(invoice.customerData);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<dynamic> _getAgentDetails(dynamic invoice) async {
    try {
      final users = await _dataService.getUsers();
      return users.firstWhere((user) => user.id == invoice.createdBy);
    } catch (e) {
      return null;
    }
  }

  // Helper methods
  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.pending;
      case 'sent_to_manufacturer':
        return Icons.send;
      case 'received_from_manufacturer':
        return Icons.check_circle;
      case 'delivered_to_agent':
        return Icons.local_shipping;
      case 'ready_for_pickup':
        return Icons.done_all;
      default:
        return Icons.info;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'sent_to_manufacturer':
        return Colors.blue;
      case 'received_from_manufacturer':
        return Colors.green;
      case 'delivered_to_agent':
        return Colors.teal;
      case 'ready_for_pickup':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'sent_to_manager':
        return 'تم إرسال البيانات للمدير';
      case 'sent_to_manufacturer':
        return 'تم إرسال الجواب للشركة المصنعة';
      case 'received_from_manufacturer':
        return 'تم استلام الجواب من الشركة المصنعة';
      case 'delivered_to_agent':
        return 'تم التسليم الي الوكيل';
      case 'pending':
        return 'في الانتظار';
      case 'ready_for_pickup':
        return 'جاهز للاستلام';
      default:
        return status;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

// Document Tracking Details Screen - Full Screen Version
class DocumentTrackingDetailsScreen extends StatefulWidget {
  final DocumentTrackingModel tracking;
  final ItemModel? item;
  final dynamic invoice;
  final Map<String, dynamic>? customer;
  final dynamic agent;

  const DocumentTrackingDetailsScreen({
    super.key,
    required this.tracking,
    this.item,
    this.invoice,
    this.customer,
    this.agent,
  });

  @override
  State<DocumentTrackingDetailsScreen> createState() => _DocumentTrackingDetailsScreenState();
}

class _DocumentTrackingDetailsScreenState extends State<DocumentTrackingDetailsScreen> {
  final DataService _dataService = DataService.instance;
  late DocumentTrackingModel tracking;

  @override
  void initState() {
    super.initState();
    tracking = widget.tracking;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل تتبع الجواب'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Invoice Information Card
            if (widget.invoice != null) _buildInvoiceCard(),

            const SizedBox(height: 16),

            // Customer Information Card
            if (widget.customer != null) _buildCustomerCard(),

            const SizedBox(height: 16),

            // Agent Information Card
            if (widget.agent != null) _buildAgentCard(),

            const SizedBox(height: 16),

            // Item Information Card
            if (widget.item != null) _buildItemCard(),

            const SizedBox(height: 16),

            // Composite Image Card
            _buildCompositeImageSection(context),

            const SizedBox(height: 16),

            // Status Update Card (for managers only)
            _buildStatusUpdateCard(context),

            const SizedBox(height: 16),

            // Status History Card
            _buildStatusHistoryCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.receipt, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الفاتورة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Divider(),
            _buildDetailRow('رقم الفاتورة:', widget.invoice.invoiceNumber),
            _buildDetailRow('تاريخ الفاتورة:', _formatDate(widget.invoice.createdAt)),
            _buildDetailRow('سعر البيع:', '${_formatCurrency(widget.invoice.sellingPrice)} جنيه'),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.green[700]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات العميل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Divider(),
            _buildDetailRow('اسم العميل:', widget.customer!['fullName'] ?? widget.customer!['customerName'] ?? widget.customer!['name'] ?? 'غير محدد'),
            _buildDetailRow('رقم الهوية:', widget.customer!['nationalId'] ?? 'غير محدد'),
            _buildDetailRow('رقم الهاتف:', widget.customer!['phone'] ?? 'غير محدد'),
            _buildDetailRow('العنوان:', widget.customer!['address'] ?? 'غير محدد'),
          ],
        ),
      ),
    );
  }

  Widget _buildAgentCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person_outline, color: Colors.purple[700]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الوكيل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Divider(),
            _buildDetailRow('اسم الوكيل:', widget.agent.fullName ?? 'غير محدد'),
            _buildDetailRow('اسم المستخدم:', widget.agent.username ?? 'غير محدد'),
            _buildDetailRow('رقم الهاتف:', widget.agent.phone ?? 'غير محدد'),
          ],
        ),
      ),
    );
  }

  Widget _buildItemCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.motorcycle, color: Colors.orange[700]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات الموتور',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Divider(),
            _buildDetailRow('الماركة:', widget.item!.brand),
            _buildDetailRow('الموديل:', widget.item!.model),
            _buildDetailRow('بصمة الموتور:', widget.item!.motorFingerprintText),
            _buildDetailRow('رقم الشاسيه:', widget.item!.chassisNumber),
            _buildDetailRow('اللون:', widget.item!.color),
          ],
        ),
      ),
    );
  }

  Widget _buildCompositeImageSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.image, color: Colors.teal[700]),
                const SizedBox(width: 8),
                const Text(
                  'الصورة المجمعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Divider(),

            // Check if composite image exists
            if (tracking.compositeImagePath != null && tracking.compositeImagePath!.isNotEmpty)
              _buildExistingCompositeImage(context)
            else
              _buildCreateCompositeImageSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildExistingCompositeImage(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          height: 400,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: _buildImageWidget(tracking.compositeImagePath!),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _shareImage(context, tracking.compositeImagePath!),
                icon: const Icon(Icons.share),
                label: const Text('مشاركة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showFullScreenImage(context, tracking.compositeImagePath!),
                icon: const Icon(Icons.fullscreen),
                label: const Text('عرض كامل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCreateCompositeImageSection(BuildContext context) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24.0),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            children: [
              Icon(
                Icons.image_not_supported,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'الصورة المجمعة غير متوفرة',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'يمكنك إنشاء الصورة المجمعة إذا كانت الصور المطلوبة متوفرة',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () => _createCompositeImage(context),
                icon: const Icon(Icons.add_photo_alternate),
                label: const Text('إنشاء الصورة المجمعة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusHistoryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.history, color: Colors.indigo[700]),
                const SizedBox(width: 8),
                const Text(
                  'تاريخ الحالات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Divider(),
            ...tracking.statusHistory.map((history) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      _getStatusIcon(history.status),
                      size: 20,
                      color: _getStatusColor(history.status),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getStatusDisplayName(history.status),
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            _formatDate(history.timestamp),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.pending;
      case 'sent_to_manufacturer':
        return Icons.send;
      case 'received_from_manufacturer':
        return Icons.check_circle;
      case 'delivered_to_agent':
        return Icons.local_shipping;
      case 'ready_for_pickup':
        return Icons.done_all;
      default:
        return Icons.info;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'sent_to_manufacturer':
        return Colors.blue;
      case 'received_from_manufacturer':
        return Colors.green;
      case 'delivered_to_agent':
        return Colors.teal;
      case 'ready_for_pickup':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'sent_to_manufacturer':
        return 'تم الإرسال للمصنع';
      case 'received_from_manufacturer':
        return 'تم الاستلام من المصنع';
      case 'ready_for_pickup':
        return 'جاهز للاستلام';
      default:
        return status;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatCurrency(double amount) {
    return amount.toStringAsFixed(0);
  }

  Widget _buildImageWidget(String imagePath) {
    if (kDebugMode) {
      print('🖼️ Loading composite image for tracking ${tracking.id}:');
      print('   Path: $imagePath');
      print('   Invoice: ${widget.invoice?.invoiceNumber ?? "Unknown"}');
    }

    // Check if file exists for local paths
    if (!imagePath.startsWith('http')) {
      final file = File(imagePath);
      if (!file.existsSync()) {
        if (kDebugMode) {
          print('❌ Composite image file not found: $imagePath');
        }
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.image_not_supported, size: 48, color: Colors.grey),
              SizedBox(height: 8),
              Text('الصورة غير موجودة'),
              Text('المسار: غير صحيح', style: TextStyle(fontSize: 12, color: Colors.grey)),
            ],
          ),
        );
      } else {
        if (kDebugMode) {
          print('✅ Composite image file exists: $imagePath');
        }
      }
    }

    return imagePath.startsWith('http')
        ? Image.network(
            imagePath,
            fit: BoxFit.cover,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return const Center(child: CircularProgressIndicator());
            },
            errorBuilder: (context, error, stackTrace) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text('خطأ في تحميل الصورة من الإنترنت'),
                  ],
                ),
              );
            },
          )
        : Image.file(
            File(imagePath),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text('خطأ في تحميل الصورة المحلية'),
                  ],
                ),
              );
            },
          );
  }

  void _shareImage(BuildContext context, String imagePath) async {
    final messenger = ScaffoldMessenger.of(context);

    try {
      if (imagePath.startsWith('http')) {
        // For network images, we need to download first
        messenger.showSnackBar(
          const SnackBar(
            content: Text('جاري تحميل الصورة للمشاركة...'),
            backgroundColor: Colors.blue,
          ),
        );
        // TODO: Download and share network image
        return;
      }

      final file = File(imagePath);
      if (!file.existsSync()) {
        messenger.showSnackBar(
          const SnackBar(
            content: Text('الصورة غير موجودة'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // TODO: Implement share functionality for Windows
      // await Share.shareXFiles([XFile(imagePath)]);
    } catch (e) {
      messenger.showSnackBar(
        SnackBar(
          content: Text('خطأ في مشاركة الصورة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showFullScreenImage(BuildContext context, String imagePath) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: imagePath.startsWith('http')
                    ? UltimateImageWidget(
                        imageUrl: imagePath,
                        fit: BoxFit.contain,
                      )
                    : Image.file(
                        File(imagePath),
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.error, color: Colors.white, size: 48),
                                SizedBox(height: 16),
                                Text(
                                  'خطأ في تحميل الصورة',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
              ),
            ),
            Positioned(
              top: 40,
              right: 20,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close, color: Colors.white, size: 30),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _createCompositeImage(BuildContext context) async {
    final messenger = ScaffoldMessenger.of(context);

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إنشاء الصورة المجمعة...'),
            ],
          ),
        ),
      );

      // Get the invoice and item data
      final invoice = await _dataService.getInvoiceById(tracking.invoiceId);
      final item = await _dataService.getItemById(tracking.itemId);

      if (invoice == null || item == null) {
        Navigator.of(context).pop(); // Close loading dialog
        messenger.showSnackBar(
          const SnackBar(
            content: Text('لا يمكن العثور على بيانات الفاتورة أو الصنف'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Get image paths from invoice customer data
      final customerIdImages = invoice.customerIdImages;

      String motorFingerprintPath = '';
      String chassisPath = '';
      String customerIdImagePath = '';

      // Get motor fingerprint from item - check multiple possible locations
      if (item.additionalData != null) {
        motorFingerprintPath = item.additionalData!['motorFingerprintImagePath'] ??
                              item.additionalData!['motorFingerprintImage'] ??
                              item.additionalData!['fingerprintImagePath'] ?? '';
      }

      // If not found in additionalData, check item properties
      if (motorFingerprintPath.isEmpty) {
        // First try the motorFingerprintImageUrl (Cloudinary URL)
        if (item.motorFingerprintImageUrl.isNotEmpty) {
          motorFingerprintPath = item.motorFingerprintImageUrl;
        }
        // Fallback to local path based on fingerprint text
        else if (item.motorFingerprintText.isNotEmpty) {
          motorFingerprintPath = '/data/user/0/com.alfarhan.transport/app_flutter/motor_${item.motorFingerprintText}.jpg';
        }
      }

      // Get chassis image from item - check multiple possible locations
      if (item.additionalData != null) {
        chassisPath = item.additionalData!['chassisImagePath'] ??
                     item.additionalData!['chassisImage'] ?? '';
      }

      // If not found in additionalData, check item properties
      if (chassisPath.isEmpty) {
        // First try the chassisImageUrl (Cloudinary URL)
        if (item.chassisImageUrl.isNotEmpty) {
          chassisPath = item.chassisImageUrl;
        }
        // Fallback to local path based on chassis number
        else if (item.chassisNumber.isNotEmpty) {
          chassisPath = '/data/user/0/com.alfarhan.transport/app_flutter/chassis_${item.chassisNumber}.jpg';
        }
      }

      // Get customer ID image - check multiple possible locations
      if (customerIdImages != null && customerIdImages.isNotEmpty) {
        customerIdImagePath = customerIdImages.first;
      } else if (invoice.additionalData != null) {
        // Check in invoice additionalData
        final additionalImages = invoice.additionalData!['customerIdImages'];
        if (additionalImages != null && additionalImages is List && additionalImages.isNotEmpty) {
          customerIdImagePath = additionalImages.first.toString();
        }
      }

      // Debug: Print found image paths
      if (kDebugMode) {
        print('🔍 Debug - Image paths found:');
        print('   Motor fingerprint: $motorFingerprintPath');
        print('   Customer ID: $customerIdImagePath');
        print('   Chassis: $chassisPath');
        print('🔍 Debug - Item chassis info:');
        print('   item.chassisImageUrl: ${item.chassisImageUrl}');
        print('   item.chassisNumber: ${item.chassisNumber}');
        print('   additionalData chassis: ${item.additionalData?['chassisImagePath'] ?? 'null'}');
        print('   Item additionalData: ${item.additionalData}');
        print('   Invoice customerIdImages: ${invoice.customerIdImages}');
      }

      // Check if we have the required images
      if (motorFingerprintPath.isEmpty || customerIdImagePath.isEmpty) {
        if (mounted) {
          Navigator.of(context).pop(); // Close loading dialog
          messenger.showSnackBar(
            SnackBar(
              content: Text('الصور المطلوبة لإنشاء الصورة المجمعة غير متوفرة\n'
                          'بصمة الموتور: ${motorFingerprintPath.isEmpty ? "غير موجودة" : "موجودة"}\n'
                          'هوية العميل: ${customerIdImagePath.isEmpty ? "غير موجودة" : "موجودة"}'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 5),
            ),
          );
        }
        return;
      }

      // Download online images if needed
      String localMotorPath = motorFingerprintPath;
      String localCustomerPath = customerIdImagePath;
      String localChassisPath = chassisPath;

      // Check if motor fingerprint is online URL and download it
      if (motorFingerprintPath.startsWith('http')) {
        if (kDebugMode) {
          print('📥 Downloading motor fingerprint from: $motorFingerprintPath');
        }
        try {
          localMotorPath = await _downloadImageFromUrl(motorFingerprintPath, 'motor_${item.motorFingerprintText}');
        } catch (e) {
          if (kDebugMode) {
            print('❌ Failed to download motor fingerprint: $e');
          }
          if (mounted) {
            Navigator.of(context).pop();
            messenger.showSnackBar(
              const SnackBar(
                content: Text('فشل في تحميل صورة بصمة الموتور'),
                backgroundColor: Colors.red,
              ),
            );
          }
          return;
        }
      }

      // Check if customer ID is online URL and download it
      if (customerIdImagePath.startsWith('http')) {
        if (kDebugMode) {
          print('📥 Downloading customer ID from: $customerIdImagePath');
        }
        try {
          localCustomerPath = await _downloadImageFromUrl(customerIdImagePath, 'customer_${invoice.invoiceNumber}');
        } catch (e) {
          if (kDebugMode) {
            print('❌ Failed to download customer ID: $e');
          }
          if (mounted) {
            Navigator.of(context).pop();
            messenger.showSnackBar(
              const SnackBar(
                content: Text('فشل في تحميل صورة هوية العميل'),
                backgroundColor: Colors.red,
              ),
            );
          }
          return;
        }
      }

      // Check if chassis is online URL and download it
      if (chassisPath.isNotEmpty && chassisPath.startsWith('http')) {
        if (kDebugMode) {
          print('📥 Downloading chassis from: $chassisPath');
        }
        try {
          localChassisPath = await _downloadImageFromUrl(chassisPath, 'chassis_${item.chassisNumber}');
        } catch (e) {
          if (kDebugMode) {
            print('❌ Failed to download chassis image: $e');
          }
          // Continue without chassis image
          localChassisPath = '';
        }
      }

      // Now check if local files exist
      final motorFile = File(localMotorPath);
      final customerFile = File(localCustomerPath);

      if (!await motorFile.exists()) {
        if (kDebugMode) {
          print('❌ Motor fingerprint file does not exist: $localMotorPath');
        }
        if (mounted) {
          Navigator.of(context).pop();
          messenger.showSnackBar(
            const SnackBar(
              content: Text('ملف صورة بصمة الموتور غير موجود'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      if (!await customerFile.exists()) {
        if (kDebugMode) {
          print('❌ Customer ID file does not exist: $localCustomerPath');
        }
        if (mounted) {
          Navigator.of(context).pop();
          messenger.showSnackBar(
            const SnackBar(
              content: Text('ملف صورة هوية العميل غير موجود'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      if (kDebugMode) {
        print('✅ All required image files exist locally');
        print('   Motor fingerprint: ${await motorFile.length()} bytes');
        print('   Customer ID: ${await customerFile.length()} bytes');
      }

      // Update paths to use local files
      motorFingerprintPath = localMotorPath;
      customerIdImagePath = localCustomerPath;
      chassisPath = localChassisPath;

      // Create composite image with unique identifier
      final compositeService = CompositeImageService();
      final compositeImage = await compositeService.createCompositeImage(
        invoice: invoice,
        item: item,
        motorFingerprintImagePath: motorFingerprintPath,
        chassisImagePath: chassisPath.isNotEmpty ? chassisPath : '',
        customerIdImagePath: customerIdImagePath,
      );

      if (kDebugMode) {
        print('🖼️ Created composite image for tracking ${tracking.id}:');
        print('   Path: ${compositeImage.path}');
        print('   Invoice: ${invoice.invoiceNumber}');
        print('   Item: ${item.motorFingerprintText}');
      }

      // Update tracking with composite image path
      final updatedTracking = tracking.copyWith(
        compositeImagePath: compositeImage.path,
        updatedAt: DateTime.now(),
      );

      // Save to database
      await LocalDatabaseService.instance.update(
        'document_tracking',
        updatedTracking.toMap(),
        'id = ?',
        [tracking.id],
      );

      if (kDebugMode) {
        print('✅ Updated tracking ${tracking.id} with composite image path');
        print('   New path: ${updatedTracking.compositeImagePath}');
      }

      // Update local state
      setState(() {
        tracking = updatedTracking;
      });

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        messenger.showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء الصورة المجمعة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating composite image: $e');
        print('   Stack trace: ${StackTrace.current}');
      }

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        messenger.showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الصورة المجمعة: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Widget _buildStatusUpdateCard(BuildContext context) {
    // Check if user can update status (managers only)
    final currentUser = AuthService.instance.currentUser;
    if (currentUser == null ||
        (currentUser.role != 'super_admin' && currentUser.role != 'admin')) {
      return const SizedBox.shrink(); // Hide for non-managers
    }

    // Check if status can be updated (not final status)
    if (tracking.currentStatus == 'delivered_to_agent') {
      return const SizedBox.shrink(); // Hide for final status
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.update, color: Colors.green[700]),
                const SizedBox(width: 8),
                const Text(
                  'تحديث حالة الجواب',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Divider(),
            Text(
              'الحالة الحالية: ${_getStatusDisplayName(tracking.currentStatus)}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showStatusUpdateDialog(context),
                icon: const Icon(Icons.edit),
                label: const Text('تحديث الحالة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showStatusUpdateDialog(BuildContext context) async {
    final currentUser = AuthService.instance.currentUser;
    if (currentUser == null) return;

    // Determine next available status
    String? nextStatus;
    String nextStatusLabel = '';

    switch (tracking.currentStatus) {
      case 'sent_to_manager':
        nextStatus = 'sent_to_manufacturer';
        nextStatusLabel = 'تم إرسال الجواب للشركة المصنعة';
        break;
      case 'sent_to_manufacturer':
        nextStatus = 'received_from_manufacturer';
        nextStatusLabel = 'تم استلام الجواب من الشركة المصنعة';
        break;
      case 'received_from_manufacturer':
        nextStatus = 'delivered_to_agent';
        nextStatusLabel = 'تم التسليم الي الوكيل';
        break;
      default:
        // No next status available
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يمكن تحديث هذه الحالة'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
    }

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث حالة الجواب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الحالة الحالية: ${_getStatusDisplayName(tracking.currentStatus)}'),
            const SizedBox(height: 8),
            Text('الحالة الجديدة: $nextStatusLabel'),
            const SizedBox(height: 16),
            const Text('هل تريد تحديث حالة الجواب؟'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('تحديث'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Show loading
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('جاري تحديث الحالة...'),
              ],
            ),
          ),
        );

        // Update status
        await _dataService.updateDocumentTrackingStatus(
          documentId: tracking.id,
          newStatus: nextStatus,
          updatedBy: currentUser.id,
          notes: 'تم تحديث الحالة بواسطة ${currentUser.fullName}',
        );

        // Update local state - refresh from database
        final trackingList = await LocalDatabaseService.instance.query(
          'document_tracking',
          where: 'id = ?',
          whereArgs: [tracking.id],
        );

        if (trackingList.isNotEmpty) {
          final updatedTracking = DocumentTrackingModel.fromMap(trackingList.first);
          setState(() {
            tracking = updatedTracking;
          });
        }

        if (mounted) {
          Navigator.of(context).pop(); // Close loading dialog
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث حالة الجواب بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }

      } catch (e) {
        if (mounted) {
          Navigator.of(context).pop(); // Close loading dialog
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحديث الحالة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<String> _downloadImageFromUrl(String url, String fileName) async {
    try {
      if (kDebugMode) {
        print('📥 Starting download: $url');
      }

      // Get app documents directory
      final appDir = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${appDir.path}/images');

      // Create images directory if it doesn't exist
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }

      // Create file path with proper extension
      final extension = path.extension(url).isNotEmpty ? path.extension(url) : '.jpg';
      final filePath = '${imagesDir.path}/$fileName$extension';
      final file = File(filePath);

      // Check if file already exists
      if (await file.exists()) {
        if (kDebugMode) {
          print('✅ File already exists: $filePath');
        }
        return filePath;
      }

      // Download the image
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);

        if (kDebugMode) {
          print('✅ Downloaded successfully: $filePath (${response.bodyBytes.length} bytes)');
        }

        return filePath;
      } else {
        throw Exception('HTTP ${response.statusCode}: Failed to download image');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Download failed: $e');
      }
      throw Exception('Failed to download image: $e');
    }
  }
}