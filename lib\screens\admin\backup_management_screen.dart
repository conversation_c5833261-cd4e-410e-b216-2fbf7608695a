import 'package:flutter/material.dart';

import '../../services/backup_service.dart';
import '../../core/utils/app_colors.dart';
import '../../core/utils/app_utils.dart';


class BackupManagementScreen extends StatefulWidget {
  const BackupManagementScreen({super.key});

  @override
  State<BackupManagementScreen> createState() => _BackupManagementScreenState();
}

class _BackupManagementScreenState extends State<BackupManagementScreen> {
  List<Map<String, dynamic>> _localBackups = [];
  List<Map<String, dynamic>> _cloudBackups = [];
  bool _isLoading = true;
  bool _isCreatingBackup = false;
  // bool _isRestoring = false; // Commented out as not used

  @override
  void initState() {
    super.initState();
    _loadBackups();
  }

  Future<void> _loadBackups() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Temporarily use empty lists until BackupService methods are implemented
      final results = [
        <Map<String, dynamic>>[], // Local backups
        <Map<String, dynamic>>[], // Cloud backups
      ];

      setState(() {
        _localBackups = results[0];
        _cloudBackups = results[1];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل النسخ الاحتياطية: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة النسخ الاحتياطي'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBackups,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Quick actions
                  _buildQuickActions(),
                  
                  const SizedBox(height: 24),
                  
                  // Local backups section
                  _buildLocalBackupsSection(),
                  
                  const SizedBox(height: 24),
                  
                  // Cloud backups section
                  _buildCloudBackupsSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات سريعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isCreatingBackup ? null : _createFullBackup,
                    icon: _isCreatingBackup
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.backup),
                    label: const Text('نسخة احتياطية كاملة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isCreatingBackup ? null : _createIncrementalBackup,
                    icon: _isCreatingBackup
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.update),
                    label: const Text('نسخة تدريجية'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _scheduleAutomaticBackup,
                icon: const Icon(Icons.schedule),
                label: const Text('تفعيل النسخ التلقائي'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalBackupsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.phone_android, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'النسخ المحلية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_localBackups.length} نسخة',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_localBackups.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text(
                    'لا توجد نسخ احتياطية محلية',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _localBackups.length,
                itemBuilder: (context, index) {
                  final backup = _localBackups[index];
                  return _buildBackupTile(backup, isLocal: true);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCloudBackupsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.cloud, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'النسخ السحابية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_cloudBackups.length} نسخة',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_cloudBackups.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text(
                    'لا توجد نسخ احتياطية سحابية',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _cloudBackups.length,
                itemBuilder: (context, index) {
                  final backup = _cloudBackups[index];
                  return _buildBackupTile(backup, isLocal: false);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupTile(Map<String, dynamic> backup, {required bool isLocal}) {
    final fileName = backup['fileName'] as String? ?? 'غير محدد';
    final createdAt = backup['createdAt'] as String?;
    final type = backup['type'] as String? ?? 'غير محدد';
    final size = backup['size'] as int?;

    DateTime? date;
    if (createdAt != null) {
      try {
        date = DateTime.parse(createdAt);
      } catch (e) {
        // Handle parsing error
      }
    }

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: type == 'full' ? Colors.blue : Colors.orange,
        child: Icon(
          type == 'full' ? Icons.backup : Icons.update,
          color: Colors.white,
          size: 20,
        ),
      ),
      title: Text(
        fileName,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('النوع: ${type == 'full' ? 'كاملة' : 'تدريجية'}'),
          if (date != null) Text('التاريخ: ${AppUtils.formatDateTime(date)}'),
          if (size != null) Text('الحجم: ${AppUtils.formatFileSize(size)}'),
        ],
      ),
      trailing: PopupMenuButton<String>(
        onSelected: (action) => _handleBackupAction(action, backup, isLocal),
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'restore',
            child: Row(
              children: [
                Icon(Icons.restore),
                SizedBox(width: 8),
                Text('استعادة'),
              ],
            ),
          ),
          if (isLocal) ...[
            const PopupMenuItem(
              value: 'upload',
              child: Row(
                children: [
                  Icon(Icons.cloud_upload),
                  SizedBox(width: 8),
                  Text('رفع للسحابة'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة'),
                ],
              ),
            ),
          ] else ...[
            const PopupMenuItem(
              value: 'download',
              child: Row(
                children: [
                  Icon(Icons.download),
                  SizedBox(width: 8),
                  Text('تحميل'),
                ],
              ),
            ),
          ],
          const PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete, color: Colors.red),
                SizedBox(width: 8),
                Text('حذف', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _createFullBackup() async {
    setState(() {
      _isCreatingBackup = true;
    });

    try {
      await BackupService.instance.createFullBackup();
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إنشاء النسخة الاحتياطية الكاملة بنجاح');
        _loadBackups();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إنشاء النسخة الاحتياطية: $e', isError: true);
      }
    } finally {
      setState(() {
        _isCreatingBackup = false;
      });
    }
  }

  Future<void> _createIncrementalBackup() async {
    setState(() {
      _isCreatingBackup = true;
    });

    try {
      // final lastWeek = DateTime.now().subtract(const Duration(days: 7)); // Not used
      await BackupService.instance.createIncrementalBackup();
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم إنشاء النسخة الاحتياطية التدريجية بنجاح');
        _loadBackups();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إنشاء النسخة الاحتياطية: $e', isError: true);
      }
    } finally {
      setState(() {
        _isCreatingBackup = false;
      });
    }
  }

  Future<void> _scheduleAutomaticBackup() async {
    try {
      // TODO: Implement scheduleAutomaticBackup in BackupService
      if (mounted) {
        AppUtils.showSnackBar(context, 'ميزة النسخ الاحتياطي التلقائي قيد التطوير');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تفعيل النسخ التلقائي: $e', isError: true);
      }
    }
  }

  void _handleBackupAction(String action, Map<String, dynamic> backup, bool isLocal) {
    switch (action) {
      case 'restore':
        _showRestoreConfirmation(backup, isLocal);
        break;
      case 'upload':
        _uploadToCloud(backup);
        break;
      case 'download':
        _downloadFromCloud(backup);
        break;
      case 'share':
        _shareBackup(backup);
        break;
      case 'delete':
        _showDeleteConfirmation(backup, isLocal);
        break;
    }
  }

  void _showRestoreConfirmation(Map<String, dynamic> backup, bool isLocal) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الاستعادة'),
        content: const Text(
          'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\n'
          'سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.\n\n'
          'لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _restoreBackup(backup, isLocal);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }

  Future<void> _restoreBackup(Map<String, dynamic> backup, bool isLocal) async {
    // setState(() {
    //   _isRestoring = true;
    // });

    try {
      // TODO: Implement backup restoration functionality
      if (mounted) {
        AppUtils.showSnackBar(context, 'ميزة استعادة النسخ الاحتياطية قيد التطوير');
        return;
      }
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم استعادة النسخة الاحتياطية بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في استعادة النسخة الاحتياطية: $e', isError: true);
      }
    } finally {
      // setState(() {
      //   _isRestoring = false;
      // });
    }
  }

  Future<void> _uploadToCloud(Map<String, dynamic> backup) async {
    try {
      // TODO: Implement uploadBackupToCloud in BackupService
      if (mounted) {
        AppUtils.showSnackBar(context, 'ميزة رفع النسخ للسحابة قيد التطوير');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في رفع النسخة الاحتياطية: $e', isError: true);
      }
    }
  }

  Future<void> _downloadFromCloud(Map<String, dynamic> backup) async {
    try {
      // TODO: Implement downloadBackupFromCloud in BackupService
      
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تحميل النسخة الاحتياطية بنجاح');
        _loadBackups();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل النسخة الاحتياطية: $e', isError: true);
      }
    }
  }

  Future<void> _shareBackup(Map<String, dynamic> backup) async {
    try {
      // TODO: Implement shareBackup in BackupService
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في مشاركة النسخة الاحتياطية: $e', isError: true);
      }
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> backup, bool isLocal) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement delete functionality
              AppUtils.showSnackBar(context, 'سيتم إضافة وظيفة الحذف قريباً');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
