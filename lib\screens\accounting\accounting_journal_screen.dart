import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../core/utils/app_utils.dart';
import '../../models/accounting_models.dart';
import '../../services/accounting_journal_service.dart';

/// شاشة تقارير دفتر اليومية المحاسبي
/// تعرض جميع المعاملات المالية التي تمت في النظام
class AccountingJournalScreen extends StatefulWidget {
  const AccountingJournalScreen({super.key});

  @override
  State<AccountingJournalScreen> createState() => _AccountingJournalScreenState();
}

class _AccountingJournalScreenState extends State<AccountingJournalScreen> with TickerProviderStateMixin {
  final AccountingJournalService _journalService = AccountingJournalService.instance;
  
  late TabController _tabController;
  List<JournalEntryModel> _allEntries = [];
  List<JournalEntryModel> _filteredEntries = [];
  
  bool _isLoading = true;
  String _searchQuery = '';
  JournalEntryStatus? _selectedStatus;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadJournalEntries();
    
    // Listen to journal updates
    _journalService.onJournalEntryAdded = _onJournalEntryAdded;
    _journalService.onJournalEntryUpdated = _onJournalEntryUpdated;
    _journalService.onJournalEntryDeleted = _onJournalEntryDeleted;
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadJournalEntries() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (!_journalService.isInitialized) {
        await _journalService.initialize();
      }
      
      setState(() {
        _allEntries = _journalService.allJournalEntries;
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'فشل في تحميل القيود المحاسبية');
      }
    }
  }

  void _applyFilters() {
    _filteredEntries = _allEntries.where((entry) {
      bool matchesSearch = _searchQuery.isEmpty ||
          entry.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          entry.reference.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          entry.entryNumber.contains(_searchQuery);
      
      bool matchesStatus = _selectedStatus == null || entry.status == _selectedStatus;
      
      return matchesSearch && matchesStatus;
    }).toList();
  }

  void _onJournalEntryAdded(JournalEntryModel entry) {
    if (mounted) {
      setState(() {
        _allEntries.insert(0, entry);
        _applyFilters();
      });
    }
  }

  void _onJournalEntryUpdated(JournalEntryModel entry) {
    if (mounted) {
      setState(() {
        final index = _allEntries.indexWhere((e) => e.id == entry.id);
        if (index != -1) {
          _allEntries[index] = entry;
          _applyFilters();
        }
      });
    }
  }

  void _onJournalEntryDeleted(String entryId) {
    if (mounted) {
      setState(() {
        _allEntries.removeWhere((e) => e.id == entryId);
        _applyFilters();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildBody(),
      // لا نحتاج زر إضافة لأن القيود تُنشأ تلقائياً من العمليات
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('تقارير دفتر اليومية المحاسبي'),
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: _showSearchDialog,
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'accounts',
              child: ListTile(
                leading: Icon(Icons.account_tree),
                title: Text('دليل الحسابات'),
              ),
            ),
            const PopupMenuItem(
              value: 'trial_balance',
              child: ListTile(
                leading: Icon(Icons.balance),
                title: Text('ميزان المراجعة'),
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('تصدير البيانات'),
              ),
            ),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        tabs: [
          Tab(text: 'الكل (${_allEntries.length})'),
          Tab(text: 'مسودات (${_allEntries.where((e) => e.status == JournalEntryStatus.draft).length})'),
          Tab(text: 'مرحلة (${_allEntries.where((e) => e.status == JournalEntryStatus.posted).length})'),
          Tab(text: 'معتمدة (${_allEntries.where((e) => e.status == JournalEntryStatus.approved).length})'),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل القيود المحاسبية...'),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        _buildSummaryCards(),
        if (_searchQuery.isNotEmpty || _selectedStatus != null) _buildFilterHeader(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildEntriesList(_filteredEntries),
              _buildEntriesList(_filteredEntries.where((e) => e.status == JournalEntryStatus.draft).toList()),
              _buildEntriesList(_filteredEntries.where((e) => e.status == JournalEntryStatus.posted).toList()),
              _buildEntriesList(_filteredEntries.where((e) => e.status == JournalEntryStatus.approved).toList()),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCards() {
    final totalEntries = _allEntries.length;
    final totalDebits = _allEntries.fold<double>(0.0, (sum, entry) => sum + entry.totalDebit);
    final unbalancedEntries = _allEntries.where((e) => !e.isBalanced).length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي القيود',
              totalEntries.toString(),
              Icons.receipt_long,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'إجمالي المبالغ',
              AppUtils.formatCurrency(totalDebits, compact: true),
              Icons.monetization_on,
              Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'غير متوازنة',
              unbalancedEntries.toString(),
              Icons.warning,
              unbalancedEntries > 0 ? Colors.red : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Theme.of(context).primaryColor.withOpacity(0.1),
      child: Row(
        children: [
          const Icon(Icons.filter_list),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'الفلاتر النشطة: ${_searchQuery.isNotEmpty ? 'البحث: "$_searchQuery"' : ''} ${_selectedStatus != null ? 'الحالة: ${_getStatusName(_selectedStatus!)}' : ''}',
            ),
          ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              setState(() {
                _searchQuery = '';
                _selectedStatus = null;
                _searchController.clear();
                _applyFilters();
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEntriesList(List<JournalEntryModel> entries) {
    if (entries.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadJournalEntries,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: entries.length,
        itemBuilder: (context, index) {
          final entry = entries[index];
          return _buildJournalEntryCard(entry);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد معاملات مالية بعد',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر هنا جميع المعاملات المالية التي تتم في النظام تلقائياً',
            style: TextStyle(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildJournalEntryCard(JournalEntryModel entry) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _openEntry(entry),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(entry.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'قيد ${entry.entryNumber}',
                      style: TextStyle(
                        color: _getStatusColor(entry.status),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(entry.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getStatusName(entry.status),
                      style: TextStyle(
                        color: _getStatusColor(entry.status),
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    DateFormat('d/M/yyyy').format(entry.date),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                entry.description,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (entry.reference.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  'المرجع: ${entry.reference}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    size: 16,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${entry.lines.length} بند',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    AppUtils.formatCurrency(entry.totalDebit),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (!entry.isBalanced)
                    const Icon(
                      Icons.warning,
                      color: Colors.red,
                      size: 16,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // تم حذف زر الإضافة لأن القيود تُنشأ تلقائياً

  // Helper methods
  String _getStatusName(JournalEntryStatus status) {
    switch (status) {
      case JournalEntryStatus.draft:
        return 'مسودة';
      case JournalEntryStatus.posted:
        return 'مرحل';
      case JournalEntryStatus.approved:
        return 'معتمد';
      case JournalEntryStatus.rejected:
        return 'مرفوض';
      case JournalEntryStatus.cancelled:
        return 'ملغي';
    }
  }

  Color _getStatusColor(JournalEntryStatus status) {
    switch (status) {
      case JournalEntryStatus.draft:
        return Colors.orange;
      case JournalEntryStatus.posted:
        return Colors.blue;
      case JournalEntryStatus.approved:
        return Colors.green;
      case JournalEntryStatus.rejected:
        return Colors.red;
      case JournalEntryStatus.cancelled:
        return Colors.grey;
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'accounts':
        AppUtils.showInfoSnackBar(context, 'قريباً: دليل الحسابات');
        break;
      case 'trial_balance':
        AppUtils.showInfoSnackBar(context, 'قريباً: ميزان المراجعة');
        break;
      case 'export':
        AppUtils.showInfoSnackBar(context, 'قريباً: تصدير البيانات');
        break;
    }
  }

  // تم حذف دالة إنشاء قيد جديد لأن القيود تُنشأ تلقائياً من العمليات

  void _openEntry(JournalEntryModel entry) {
    // عرض تفاصيل القيد في dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل القيد ${entry.entryNumber}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('التاريخ: ${DateFormat('d/M/yyyy').format(entry.date)}'),
            Text('الوصف: ${entry.description}'),
            Text('المرجع: ${entry.reference}'),
            Text('إجمالي المدين: ${AppUtils.formatCurrency(entry.totalDebit)}'),
            Text('إجمالي الدائن: ${AppUtils.formatCurrency(entry.totalCredit)}'),
            Text('الحالة: ${_getStatusName(entry.status)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في القيود'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ادخل رقم القيد أو الوصف',
                prefixIcon: Icon(Icons.search),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<JournalEntryStatus?>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                labelText: 'حالة القيد',
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem<JournalEntryStatus?>(
                  value: null,
                  child: Text('جميع الحالات'),
                ),
                ...JournalEntryStatus.values.map((status) {
                  return DropdownMenuItem<JournalEntryStatus?>(
                    value: status,
                    child: Text(_getStatusName(status)),
                  );
                }),
              ],
              onChanged: (value) {
                _selectedStatus = value;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _searchQuery = _searchController.text;
                _applyFilters();
              });
              Navigator.pop(context);
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }
}
