import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../core/utils/app_utils.dart';
import '../models/accounting_models.dart';
import '../services/accounting_journal_service.dart';
import '../screens/accounting/accounting_journal_screen.dart';

/// بطاقة داشبورد دفتر اليومية المحاسبي
class AccountingJournalDashboardCard extends StatefulWidget {
  const AccountingJournalDashboardCard({Key? key}) : super(key: key);

  @override
  State<AccountingJournalDashboardCard> createState() => _AccountingJournalDashboardCardState();
}

class _AccountingJournalDashboardCardState extends State<AccountingJournalDashboardCard> {
  final AccountingJournalService _journalService = AccountingJournalService.instance;
  
  bool _isLoading = true;
  JournalStatistics? _statistics;
  List<JournalEntryModel> _recentEntries = [];

  @override
  void initState() {
    super.initState();
    _loadData();
    
    // Listen to updates
    _journalService.onJournalEntryAdded = _onDataUpdated;
    _journalService.onJournalEntryUpdated = _onDataUpdated;
    _journalService.onJournalEntryDeleted = (_) => _onDataUpdated(null);
  }

  Future<void> _loadData() async {
    try {
      if (!_journalService.isInitialized) {
        await _journalService.initialize();
      }
      
      setState(() {
        _statistics = _journalService.generateStatistics();
        _recentEntries = _journalService.allJournalEntries.take(3).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onDataUpdated(JournalEntryModel? entry) {
    if (mounted) {
      _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingCard();
    }

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildStatistics(),
            const SizedBox(height: 16),
            _buildRecentEntries(),
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Card(
      elevation: 4,
      child: Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 8),
              Text('جاري تحميل البيانات المحاسبية...'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.deepPurple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.receipt_long,
            color: Colors.deepPurple,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'تقارير دفتر اليومية المحاسبي',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'عرض جميع المعاملات المالية في النظام',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: _openJournalScreen,
          icon: const Icon(Icons.arrow_forward_ios),
        ),
      ],
    );
  }

  Widget _buildStatistics() {
    if (_statistics == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'إجمالي القيود',
                  _statistics!.totalEntries.toString(),
                  Icons.receipt,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatItem(
                  'المسودات',
                  _statistics!.draftEntries.toString(),
                  Icons.edit,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'المرحلة',
                  _statistics!.postedEntries.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatItem(
                  'إجمالي المبالغ',
                  AppUtils.formatCurrency(_statistics!.totalDebits, compact: true),
                  Icons.monetization_on,
                  Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentEntries() {
    if (_recentEntries.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Column(
            children: [
              Icon(Icons.receipt_long, color: Colors.grey, size: 32),
              SizedBox(height: 8),
              Text(
                'لا توجد معاملات مالية بعد',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'آخر القيود',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: _openJournalScreen,
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...(_recentEntries.map((entry) => _buildRecentEntryItem(entry))),
      ],
    );
  }

  Widget _buildRecentEntryItem(JournalEntryModel entry) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: InkWell(
        onTap: () => _openEntry(entry),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getStatusColor(entry.status).withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                entry.entryNumber,
                style: TextStyle(
                  color: _getStatusColor(entry.status),
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    entry.description,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    DateFormat('d/M/yyyy').format(entry.date),
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Text(
              AppUtils.formatCurrency(entry.totalDebit, compact: true),
              style: const TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _openJournalScreen,
            icon: const Icon(Icons.assessment, size: 16),
            label: const Text('عرض التقارير'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _openJournalScreen,
            icon: const Icon(Icons.analytics, size: 16),
            label: const Text('التفاصيل'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(JournalEntryStatus status) {
    switch (status) {
      case JournalEntryStatus.draft:
        return Colors.orange;
      case JournalEntryStatus.posted:
        return Colors.blue;
      case JournalEntryStatus.approved:
        return Colors.green;
      case JournalEntryStatus.rejected:
        return Colors.red;
      case JournalEntryStatus.cancelled:
        return Colors.grey;
    }
  }

  void _openJournalScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AccountingJournalScreen(),
      ),
    );
  }

  void _openEntry(JournalEntryModel entry) {
    // عرض تفاصيل القيد في dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل القيد ${entry.entryNumber}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('التاريخ: ${DateFormat('d/M/yyyy').format(entry.date)}'),
            Text('الوصف: ${entry.description}'),
            Text('المرجع: ${entry.reference}'),
            Text('إجمالي المدين: ${AppUtils.formatCurrency(entry.totalDebit)}'),
            Text('إجمالي الدائن: ${AppUtils.formatCurrency(entry.totalCredit)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // تم حذف دالة إنشاء قيد جديد لأن القيود تُنشأ تلقائياً من العمليات
}
