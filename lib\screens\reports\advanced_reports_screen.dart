import 'package:flutter/material.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/invoice_model.dart';
import '../../models/user_model.dart';
import '../../models/warehouse_model.dart';
import '../../services/data_service.dart';
import '../../services/report_service.dart';


class AdvancedReportsScreen extends StatefulWidget {
  const AdvancedReportsScreen({super.key});

  @override
  State<AdvancedReportsScreen> createState() => _AdvancedReportsScreenState();
}

class _AdvancedReportsScreenState extends State<AdvancedReportsScreen>
    with TickerProviderStateMixin {
  final DataService _dataService = DataService.instance;
  final ReportService _reportService = ReportService.instance;
  
  late TabController _tabController;
  
  List<InvoiceModel> _allInvoices = [];
  List<UserModel> _agents = [];
  List<WarehouseModel> _warehouses = [];
  
  bool _isLoading = true;
  bool _isGeneratingReport = false;
  
  // Filter variables
  DateTimeRange? _selectedDateRange;
  String? _selectedAgentId;
  String? _selectedWarehouseId;
  String _selectedStatus = 'all';
  String _selectedReportType = 'sales';
  
  // Statistics
  Map<String, dynamic> _reportStats = {};
  List<Map<String, dynamic>> _chartData = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      await Future.wait([
        _loadInvoices(),
        _loadAgents(),
        _loadWarehouses(),
      ]);
      
      if (!mounted) return;
      
      _calculateReportStatistics();
      _generateChartData();
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadInvoices() async {
    _allInvoices = await _dataService.getAgentInvoices('');
  }

  Future<void> _loadAgents() async {
    _agents = await _dataService.getUsersByRole('agent');
  }

  Future<void> _loadWarehouses() async {
    _warehouses = await _dataService.getWarehouses();
  }

  void _calculateReportStatistics() {
    final filteredInvoices = _getFilteredInvoices();
    
    double totalSales = 0;
    double totalProfit = 0;
    double totalCost = 0;
    int totalCount = filteredInvoices.length;
    int paidCount = 0;
    int pendingCount = 0;
    
    Map<String, double> agentSales = {};
    Map<String, double> warehouseSales = {};
    Map<String, int> dailySales = {};
    
    for (final invoice in filteredInvoices) {
      totalSales += invoice.sellingPrice;
      totalProfit += invoice.profitAmount;
      totalCost += invoice.purchasePrice;
      
      if (invoice.status == 'paid') paidCount++;
      if (invoice.status == 'pending') pendingCount++;
      
      // Agent sales
      final agentName = _agents
          .firstWhere((a) => a.id == invoice.agentId, orElse: () => UserModel(
                id: '',
                username: '',
                email: '',
                phone: '',
                fullName: 'غير محدد',
                role: '',
                isActive: false,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ))
          .fullName;
      agentSales[agentName] = (agentSales[agentName] ?? 0) + invoice.sellingPrice;
      
      // Warehouse sales
      final warehouseName = _warehouses
          .firstWhere((w) => w.id == invoice.warehouseId, orElse: () => WarehouseModel(
                id: '',
                name: 'غير محدد',
                type: 'main',
                address: '',
                isActive: false,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ))
          .name;
      warehouseSales[warehouseName] = (warehouseSales[warehouseName] ?? 0) + invoice.sellingPrice;
      
      // Daily sales
      final dayKey = AppUtils.formatDate(invoice.createdAt);
      dailySales[dayKey] = (dailySales[dayKey] ?? 0) + 1;
    }
    
    _reportStats = {
      'totalSales': totalSales,
      'totalProfit': totalProfit,
      'totalCost': totalCost,
      'totalCount': totalCount,
      'paidCount': paidCount,
      'pendingCount': pendingCount,
      'averageSale': totalCount > 0 ? totalSales / totalCount : 0,
      'profitMargin': totalSales > 0 ? (totalProfit / totalSales) * 100 : 0,
      'agentSales': agentSales,
      'warehouseSales': warehouseSales,
      'dailySales': dailySales,
    };
  }

  void _generateChartData() {
    _chartData.clear();
    
    switch (_selectedReportType) {
      case 'sales':
        _generateSalesChartData();
        break;
      case 'agents':
        _generateAgentsChartData();
        break;
      case 'warehouses':
        _generateWarehousesChartData();
        break;
      case 'daily':
        _generateDailyChartData();
        break;
    }
  }

  void _generateSalesChartData() {
    final agentSales = _reportStats['agentSales'] as Map<String, double>;
    _chartData = agentSales.entries
        .map((entry) => {
              'name': entry.key,
              'value': entry.value,
              'percentage': _reportStats['totalSales'] > 0 
                  ? (entry.value / _reportStats['totalSales']) * 100 
                  : 0,
            })
        .toList();
    _chartData.sort((a, b) => (b['value'] as double).compareTo(a['value'] as double));
  }

  void _generateAgentsChartData() {
    final agentSales = _reportStats['agentSales'] as Map<String, double>;
    _chartData = agentSales.entries
        .map((entry) => {
              'name': entry.key,
              'value': entry.value,
              'count': _getFilteredInvoices()
                  .where((i) => _agents
                      .firstWhere((a) => a.id == i.agentId, orElse: () => UserModel(
                            id: '',
                            username: '',
                            email: '',
                            phone: '',
                            fullName: 'غير محدد',
                            role: '',
                            isActive: false,
                            createdAt: DateTime.now(),
                            updatedAt: DateTime.now(),
                          ))
                      .fullName == entry.key)
                  .length,
            })
        .toList();
    _chartData.sort((a, b) => (b['value'] as double).compareTo(a['value'] as double));
  }

  void _generateWarehousesChartData() {
    final warehouseSales = _reportStats['warehouseSales'] as Map<String, double>;
    _chartData = warehouseSales.entries
        .map((entry) => {
              'name': entry.key,
              'value': entry.value,
              'count': _getFilteredInvoices()
                  .where((i) => _warehouses
                      .firstWhere((w) => w.id == i.warehouseId, orElse: () => WarehouseModel(
                            id: '',
                            name: 'غير محدد',
                            type: 'main',
                            address: '',
                            isActive: false,
                            createdAt: DateTime.now(),
                            updatedAt: DateTime.now(),
                          ))
                      .name == entry.key)
                  .length,
            })
        .toList();
    _chartData.sort((a, b) => (b['value'] as double).compareTo(a['value'] as double));
  }

  void _generateDailyChartData() {
    final dailySales = _reportStats['dailySales'] as Map<String, int>;
    _chartData = dailySales.entries
        .map((entry) => {
              'name': entry.key,
              'value': entry.value.toDouble(),
              'sales': _getFilteredInvoices()
                  .where((i) => AppUtils.formatDate(i.createdAt) == entry.key)
                  .fold(0.0, (sum, i) => sum + i.sellingPrice),
            })
        .toList();
    _chartData.sort((a, b) => DateTime.parse(a['name']).compareTo(DateTime.parse(b['name'])));
  }

  List<InvoiceModel> _getFilteredInvoices() {
    List<InvoiceModel> filtered = List.from(_allInvoices);
    
    // Date range filter
    if (_selectedDateRange != null) {
      filtered = filtered.where((invoice) {
        return invoice.createdAt.isAfter(_selectedDateRange!.start.subtract(const Duration(days: 1))) &&
               invoice.createdAt.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }
    
    // Agent filter
    if (_selectedAgentId != null) {
      filtered = filtered.where((invoice) => invoice.agentId == _selectedAgentId).toList();
    }
    
    // Warehouse filter
    if (_selectedWarehouseId != null) {
      filtered = filtered.where((invoice) => invoice.warehouseId == _selectedWarehouseId).toList();
    }
    
    // Status filter
    if (_selectedStatus != 'all') {
      filtered = filtered.where((invoice) => invoice.status == _selectedStatus).toList();
    }
    
    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير المتقدمة'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            onSelected: _handleExportAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'pdf',
                child: ListTile(
                  leading: Icon(Icons.picture_as_pdf),
                  title: Text('تصدير PDF'),
                ),
              ),
              const PopupMenuItem(
                value: 'excel',
                child: ListTile(
                  leading: Icon(Icons.table_chart),
                  title: Text('تصدير Excel'),
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text('مشاركة التقرير'),
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
            Tab(text: 'الرسوم البيانية', icon: Icon(Icons.bar_chart)),
            Tab(text: 'التفاصيل', icon: Icon(Icons.list_alt)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildFiltersSection(),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildStatisticsTab(),
                      _buildChartsTab(),
                      _buildDetailsTab(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      color: Colors.grey[50],
      child: Column(
        children: [
          // Report type selector
          Row(
            children: [
              const Text('نوع التقرير: ', style: TextStyle(fontWeight: FontWeight.bold)),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedReportType,
                  isExpanded: true,
                  items: const [
                    DropdownMenuItem(value: 'sales', child: Text('تقرير المبيعات')),
                    DropdownMenuItem(value: 'agents', child: Text('تقرير الوكلاء')),
                    DropdownMenuItem(value: 'warehouses', child: Text('تقرير المخازن')),
                    DropdownMenuItem(value: 'daily', child: Text('التقرير اليومي')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedReportType = value!;
                    });
                    _generateChartData();
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Filters row
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // Date range filter
                _buildFilterChip(
                  label: 'التاريخ',
                  value: _selectedDateRange != null
                      ? '${AppUtils.formatDate(_selectedDateRange!.start)} - ${AppUtils.formatDate(_selectedDateRange!.end)}'
                      : null,
                  onTap: _selectDateRange,
                ),
                const SizedBox(width: 8),

                // Agent filter
                if (_agents.isNotEmpty) ...[
                  _buildFilterChip(
                    label: 'الوكيل',
                    value: _selectedAgentId != null
                        ? _agents.firstWhere((a) => a.id == _selectedAgentId).fullName
                        : null,
                    onTap: () => _showAgentFilter(),
                  ),
                  const SizedBox(width: 8),
                ],

                // Warehouse filter
                if (_warehouses.isNotEmpty) ...[
                  _buildFilterChip(
                    label: 'المخزن',
                    value: _selectedWarehouseId != null
                        ? _warehouses.firstWhere((w) => w.id == _selectedWarehouseId).name
                        : null,
                    onTap: () => _showWarehouseFilter(),
                  ),
                  const SizedBox(width: 8),
                ],

                // Status filter
                _buildFilterChip(
                  label: 'الحالة',
                  value: _selectedStatus != 'all' ? _getStatusText(_selectedStatus) : null,
                  onTap: () => _showStatusFilter(),
                ),
                const SizedBox(width: 8),

                // Clear filters
                if (_hasActiveFilters()) ...[
                  ElevatedButton.icon(
                    onPressed: _clearFilters,
                    icon: const Icon(Icons.clear),
                    label: const Text('مسح الفلاتر'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    String? value,
    required VoidCallback onTap,
  }) {
    return FilterChip(
      label: Text(value != null ? '$label: $value' : label),
      selected: value != null,
      onSelected: (_) => onTap(),
      backgroundColor: Colors.white,
      selectedColor: Theme.of(context).primaryColor.withAlpha(51),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  bool _hasActiveFilters() {
    return _selectedAgentId != null ||
           _selectedWarehouseId != null ||
           _selectedStatus != 'all' ||
           _selectedDateRange != null;
  }

  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          // Main statistics cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المبيعات',
                  AppUtils.formatCurrency(_reportStats['totalSales'] ?? 0),
                  Icons.attach_money,
                  Colors.green,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  'إجمالي الأرباح',
                  AppUtils.formatCurrency(_reportStats['totalProfit'] ?? 0),
                  Icons.trending_up,
                  Colors.blue,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'عدد الفواتير',
                  '${_reportStats['totalCount'] ?? 0}',
                  Icons.receipt,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  'هامش الربح',
                  '${(_reportStats['profitMargin'] ?? 0).toStringAsFixed(1)}%',
                  Icons.percent,
                  Colors.purple,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Payment status breakdown
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'حالة المدفوعات',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  Row(
                    children: [
                      Expanded(
                        child: _buildPaymentStatusItem(
                          'مدفوعة',
                          _reportStats['paidCount'] ?? 0,
                          Colors.green,
                        ),
                      ),
                      Expanded(
                        child: _buildPaymentStatusItem(
                          'معلقة',
                          _reportStats['pendingCount'] ?? 0,
                          Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentStatusItem(String label, int count, Color color) {
    final total = (_reportStats['totalCount'] ?? 0) as int;
    final percentage = total > 0 ? (count / total) * 100 : 0;

    return Container(
      padding: const EdgeInsets.all(AppConstants.smallPadding),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            '$count',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(label),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getChartTitle(),
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.defaultPadding),
                  if (_chartData.isEmpty)
                    const Center(
                      child: Text('لا توجد بيانات للعرض'),
                    )
                  else
                    ..._chartData.take(10).map((data) => _buildChartItem(data)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartItem(Map<String, dynamic> data) {
    final maxValue = _chartData.isNotEmpty
        ? _chartData.map((d) => d['value'] as double).reduce((a, b) => a > b ? a : b)
        : 1.0;
    final percentage = maxValue > 0 ? (data['value'] as double) / maxValue : 0.0;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  data['name'] as String,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
              Text(
                _selectedReportType == 'daily'
                    ? '${(data['value'] as double).toInt()} فاتورة'
                    : AppUtils.formatCurrency(data['value'] as double),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: percentage,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    final filteredInvoices = _getFilteredInvoices();

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: filteredInvoices.length,
      itemBuilder: (context, index) {
        final invoice = filteredInvoices[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getStatusColor(invoice.status),
              child: Icon(
                _getStatusIcon(invoice.status),
                color: Colors.white,
                size: 16,
              ),
            ),
            title: Text('فاتورة رقم: ${invoice.invoiceNumber}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('التاريخ: ${AppUtils.formatDate(invoice.createdAt)}'),
                Text('المبلغ: ${AppUtils.formatCurrency(invoice.sellingPrice)}'),
              ],
            ),
            trailing: Text(
              _getStatusText(invoice.status),
              style: TextStyle(
                color: _getStatusColor(invoice.status),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }

  // Helper methods
  String _getChartTitle() {
    switch (_selectedReportType) {
      case 'sales':
        return 'توزيع المبيعات حسب الوكلاء';
      case 'agents':
        return 'أداء الوكلاء';
      case 'warehouses':
        return 'مبيعات المخازن';
      case 'daily':
        return 'المبيعات اليومية';
      default:
        return 'الرسم البياني';
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'معلقة';
      case 'paid':
        return 'مدفوعة';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'paid':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.pending;
      case 'paid':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.receipt;
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
      _calculateReportStatistics();
      _generateChartData();
    }
  }

  void _showAgentFilter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر الوكيل'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              ListTile(
                title: const Text('جميع الوكلاء'),
                leading: Radio<String?>(
                  value: null,
                  groupValue: _selectedAgentId,
                  onChanged: (value) {
                    setState(() {
                      _selectedAgentId = value;
                    });
                    Navigator.pop(context);
                    _calculateReportStatistics();
                    _generateChartData();
                  },
                ),
              ),
              ..._agents.map((agent) => ListTile(
                title: Text(agent.fullName),
                leading: Radio<String?>(
                  value: agent.id,
                  groupValue: _selectedAgentId,
                  onChanged: (value) {
                    setState(() {
                      _selectedAgentId = value;
                    });
                    Navigator.pop(context);
                    _calculateReportStatistics();
                    _generateChartData();
                  },
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  void _showWarehouseFilter() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر المخزن'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              ListTile(
                title: const Text('جميع المخازن'),
                leading: Radio<String?>(
                  value: null,
                  groupValue: _selectedWarehouseId,
                  onChanged: (value) {
                    setState(() {
                      _selectedWarehouseId = value;
                    });
                    Navigator.pop(context);
                    _calculateReportStatistics();
                    _generateChartData();
                  },
                ),
              ),
              ..._warehouses.map((warehouse) => ListTile(
                title: Text(warehouse.name),
                leading: Radio<String?>(
                  value: warehouse.id,
                  groupValue: _selectedWarehouseId,
                  onChanged: (value) {
                    setState(() {
                      _selectedWarehouseId = value;
                    });
                    Navigator.pop(context);
                    _calculateReportStatistics();
                    _generateChartData();
                  },
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  void _showStatusFilter() {
    final statusOptions = ['all', 'pending', 'paid', 'cancelled'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر الحالة'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: statusOptions.map((status) => ListTile(
              title: Text(status == 'all' ? 'جميع الحالات' : _getStatusText(status)),
              leading: Radio<String>(
                value: status,
                groupValue: _selectedStatus,
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value!;
                  });
                  Navigator.pop(context);
                  _calculateReportStatistics();
                  _generateChartData();
                },
              ),
            )).toList(),
          ),
        ),
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedAgentId = null;
      _selectedWarehouseId = null;
      _selectedStatus = 'all';
      _selectedDateRange = null;
    });
    _calculateReportStatistics();
    _generateChartData();
  }

  Future<void> _handleExportAction(String action) async {
    setState(() {
      _isGeneratingReport = true;
    });

    try {
      switch (action) {
        case 'pdf':
          await _exportToPDF();
          break;
        case 'excel':
          await _exportToExcel();
          break;
        case 'share':
          await _shareReport();
          break;
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في التصدير: $e', isError: true);
      }
    } finally {
      setState(() {
        _isGeneratingReport = false;
      });
    }
  }

  Future<void> _exportToPDF() async {
    // TODO: Implement PDF export
    if (mounted) {
      AppUtils.showSnackBar(context, 'ميزة تصدير PDF قيد التطوير');
    }
  }

  Future<void> _exportToExcel() async {
    // TODO: Implement Excel export
    if (mounted) {
      AppUtils.showSnackBar(context, 'ميزة تصدير Excel قيد التطوير');
    }
  }

  Future<void> _shareReport() async {
    // TODO: Implement report sharing
    if (mounted) {
      AppUtils.showSnackBar(context, 'ميزة مشاركة التقرير قيد التطوير');
    }
  }
}
