import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/user_model.dart';
import '../../models/agent_account_model.dart';
import '../../services/data_service.dart';
import '../../core/constants/app_constants.dart';

class DetailedAgentStatementScreen extends StatefulWidget {
  final String agentId;
  
  const DetailedAgentStatementScreen({
    super.key,
    required this.agentId,
  });

  @override
  State<DetailedAgentStatementScreen> createState() => _DetailedAgentStatementScreenState();
}

class _DetailedAgentStatementScreenState extends State<DetailedAgentStatementScreen> {
  UserModel? agent;
  AgentAccountModel? agentAccount;
  bool isLoading = true;

  // Filter variables
  String _selectedTransactionType = 'all';
  DateTime? _startDate;
  DateTime? _endDate;

  final List<String> _transactionTypes = [
    'all',
    'debt',
    'payment',
    'profit',
  ];

  final Map<String, String> _transactionTypeNames = {
    'all': 'جميع المعاملات',
    'debt': 'مبيعات',
    'payment': 'دفعات',
    'profit': 'أرباح',
  };

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);
      
      // Load agent details
      final users = await dataService.getUsers();
      agent = users.firstWhere((a) => a.id == widget.agentId && a.role == 'agent');
      
      // Load agent account
      agentAccount = await dataService.getAgentAccount(widget.agentId);
      
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('كشف حساب ${agent?.fullName ?? 'الوكيل'}'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printStatement,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareStatement,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : agentAccount == null
              ? const Center(
                  child: Text(
                    'لا توجد بيانات حساب لهذا الوكيل',
                    style: TextStyle(fontSize: 18),
                  ),
                )
              : _buildStatementContent(),
    );
  }

  Widget _buildStatementContent() {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          Container(
            color: Colors.white,
            child: const TabBar(
              labelColor: Color(0xFF1565C0),
              unselectedLabelColor: Colors.grey,
              indicatorColor: Color(0xFF1565C0),
              tabs: [
                Tab(
                  icon: Icon(Icons.dashboard),
                  text: 'نظرة عامة',
                ),
                Tab(
                  icon: Icon(Icons.account_balance_wallet),
                  text: 'كشف الحساب',
                ),
                Tab(
                  icon: Icon(Icons.analytics),
                  text: 'الإحصائيات',
                ),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildOverviewTab(),
                _buildStatementTab(),
                _buildStatisticsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // تبويبة نظرة عامة
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAgentInfo(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildAccountSummary(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildRecentTransactions(),
        ],
      ),
    );
  }

  // تبويبة كشف الحساب
  Widget _buildStatementTab() {
    return Column(
      children: [
        _buildFilters(),
        _buildAccountSummaryHeader(),
        Expanded(child: _buildAdvancedTransactionsTable()),
      ],
    );
  }

  // تبويبة الإحصائيات
  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPerformanceMetrics(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildMonthlyChart(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildTransactionTypeChart(),
        ],
      ),
    );
  }

  // دالة الفلاتر
  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedTransactionType,
                  decoration: const InputDecoration(
                    labelText: 'نوع المعاملة',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: _transactionTypes.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(_transactionTypeNames[type] ?? type),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedTransactionType = value!;
                    });
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'من تاريخ',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectStartDate(),
                        controller: TextEditingController(
                          text: _startDate != null
                            ? '${_startDate!.day}/${_startDate!.month}/${_startDate!.year}'
                            : '',
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextFormField(
                        decoration: const InputDecoration(
                          labelText: 'إلى تاريخ',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () => _selectEndDate(),
                        controller: TextEditingController(
                          text: _endDate != null
                            ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                            : '',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: _clearFilters,
                icon: const Icon(Icons.clear),
                label: const Text('مسح الفلاتر'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[600],
                  foregroundColor: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                'عدد المعاملات: ${_getFilteredTransactions().length}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAgentInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'بيانات الوكيل',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('الاسم: ${agent?.fullName ?? 'غير محدد'}'),
                      Text('الهاتف: ${agent?.phone ?? 'غير محدد'}'),
                      Text('البريد الإلكتروني: ${agent?.email ?? 'غير محدد'}'),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('اسم المستخدم: ${agent?.username ?? 'غير محدد'}'),
                      Text('تاريخ التسجيل: ${agent?.createdAt != null ? _formatDate(agent!.createdAt) : 'غير محدد'}'),
                      Text('الحالة: ${agent?.isActive == true ? 'نشط' : 'غير نشط'}'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSummary() {
    // Use accurate calculations from agent account
    final totals = agentAccount?.transactionTotals ?? {
      'totalDebt': 0.0,
      'totalPaid': 0.0,
      'totalCredits': 0.0,
      'balance': 0.0,
    };

    final balance = totals['balance'] ?? 0.0;
    final totalDebt = totals['totalDebt'] ?? 0.0;
    final totalPaid = totals['totalPaid'] ?? 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الحساب',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المديونية',
                    '${_formatCurrency(totalDebt)} ج.م',
                    Colors.red,
                    Icons.trending_up,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المدفوع',
                    '${_formatCurrency(totalPaid)} ج.م',
                    Colors.green,
                    Icons.trending_down,
                  ),
                ),
                const SizedBox(width: AppConstants.smallPadding),
                Expanded(
                  child: _buildSummaryCard(
                    'الرصيد الحالي',
                    '${_formatCurrency(balance)} ج.م',
                    balance >= 0 ? Colors.green : Colors.red,
                    balance >= 0 ? Icons.account_balance_wallet : Icons.warning,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList() {
    final transactions = agentAccount?.transactions ?? [];
    
    if (transactions.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(AppConstants.defaultPadding),
          child: Center(
            child: Text(
              'لا توجد معاملات لهذا الوكيل',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل المعاملات (${transactions.length} معاملة)',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: transactions.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final transaction = transactions[index];
                return _buildTransactionItem(transaction);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(AgentTransaction transaction) {
    final isDebt = transaction.type == 'debt';
    final color = isDebt ? Colors.red : Colors.green;
    final icon = isDebt ? Icons.arrow_upward : Icons.arrow_downward;

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withOpacity(0.1),
        child: Icon(icon, color: color),
      ),
      title: Text(
        isDebt ? 'مديونية' : 'دفعة',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(_formatDate(transaction.timestamp)),
          if (transaction.description.isNotEmpty) Text(transaction.description),
        ],
      ),
      trailing: Text(
        '${isDebt ? '+' : '-'}${_formatCurrency(transaction.amount)} ج.م',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: color,
          fontSize: 16,
        ),
      ),
    );
  }

  void _printStatement() {
    // TODO: Implement print functionality for Windows
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة الطباعة قيد التطوير')),
    );
  }

  void _shareStatement() {
    // TODO: Implement share functionality for Windows
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة المشاركة قيد التطوير')),
    );
  }

  // دوال اختيار التاريخ
  Future<void> _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now().subtract(const Duration(days: 30)),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _startDate = picked;
      });
    }
  }

  Future<void> _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: _startDate ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedTransactionType = 'all';
      _startDate = null;
      _endDate = null;
    });
  }

  // فلترة المعاملات
  List<AgentTransaction> _getFilteredTransactions() {
    if (agentAccount?.transactions == null) return [];

    var transactions = List<AgentTransaction>.from(agentAccount!.transactions);

    // فلترة حسب نوع المعاملة
    if (_selectedTransactionType != 'all') {
      transactions = transactions.where((transaction) {
        return transaction.type == _selectedTransactionType;
      }).toList();
    }

    // فلترة حسب التاريخ
    if (_startDate != null || _endDate != null) {
      transactions = transactions.where((transaction) {
        final transactionDate = transaction.timestamp;

        if (_startDate != null && transactionDate.isBefore(_startDate!)) {
          return false;
        }

        if (_endDate != null && transactionDate.isAfter(_endDate!.add(const Duration(days: 1)))) {
          return false;
        }

        return true;
      }).toList();
    }

    // ترتيب حسب التاريخ (الأحدث أولاً)
    transactions.sort((a, b) {
      return b.timestamp.compareTo(a.timestamp);
    });

    return transactions;
  }

  // رأس ملخص الحساب
  Widget _buildAccountSummaryHeader() {
    if (agentAccount == null) return const SizedBox.shrink();

    final filteredTransactions = _getFilteredTransactions();
    double totalDebit = 0.0;
    double totalCredit = 0.0;
    double runningBalance = 0.0;

    for (final transaction in filteredTransactions) {
      if (transaction.type == 'debt') {
        totalDebit += transaction.amount;
      } else if (transaction.type == 'payment' || transaction.type == 'profit') {
        totalCredit += transaction.amount;
      }
    }

    runningBalance = totalDebit - totalCredit;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[700]!, Colors.blue[500]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي المدين',
              '${_formatCurrency(totalDebit)} ج.م',
              Colors.red[700]!,
              Icons.trending_up,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildSummaryCard(
              'إجمالي الدائن',
              '${_formatCurrency(totalCredit)} ج.م',
              Colors.green[700]!,
              Icons.trending_down,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildSummaryCard(
              'الرصيد الحالي',
              '${_formatCurrency(runningBalance)} ج.م',
              runningBalance >= 0 ? Colors.blue[700]! : Colors.orange[700]!,
              runningBalance >= 0 ? Icons.account_balance_wallet : Icons.warning,
            ),
          ),
        ],
      ),
    );
  }



  // جدول المعاملات المتقدم
  Widget _buildAdvancedTransactionsTable() {
    final transactions = _getFilteredTransactions();

    if (transactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد معاملات',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Expanded(
                  flex: 2,
                  child: Text(
                    'التاريخ',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Expanded(
                  flex: 3,
                  child: Text(
                    'البيان',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'مدين',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red[700]),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'دائن',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green[700]),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'الرصيد',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue[700]),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Expanded(
                  flex: 1,
                  child: Text(
                    'إجراءات',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // Transactions
          Expanded(
            child: ListView.builder(
              itemCount: transactions.length,
              itemBuilder: (context, index) {
                final transaction = transactions[index];
                final runningBalance = _calculateRunningBalance(transactions, index);

                return _buildTransactionRow(transaction, runningBalance, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionRow(AgentTransaction transaction, double runningBalance, int index) {
    final isDebit = transaction.type == 'debt';
    final isCredit = transaction.type == 'payment' || transaction.type == 'profit';

    return InkWell(
      onTap: () => _navigateToTransactionDetails(transaction),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey[200]!),
          ),
          color: index % 2 == 0 ? Colors.white : Colors.blue[50],
        ),
        child: Row(
          children: [
            // التاريخ
            Expanded(
              flex: 2,
              child: Text(
                _formatDate(transaction.timestamp),
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ),

            // البيان
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    transaction.description,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    'مرجع: ${transaction.id}',
                    style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),

            // مدين
            Expanded(
              flex: 2,
              child: Text(
                isDebit ? '${_formatCurrency(transaction.amount)} ج.م' : '-',
                style: TextStyle(
                  color: isDebit ? Colors.red[700] : Colors.grey,
                  fontWeight: isDebit ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // دائن
            Expanded(
              flex: 2,
              child: Text(
                isCredit ? '${_formatCurrency(transaction.amount)} ج.م' : '-',
                style: TextStyle(
                  color: isCredit ? Colors.green[700] : Colors.grey,
                  fontWeight: isCredit ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // الرصيد
            Expanded(
              flex: 2,
              child: Text(
                '${_formatCurrency(runningBalance)} ج.م',
                style: TextStyle(
                  color: runningBalance >= 0 ? Colors.blue[700] : Colors.orange[700],
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // إجراءات
            Expanded(
              flex: 1,
              child: PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert, size: 16),
                onSelected: (value) => _handleTransactionAction(value, transaction),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'details',
                    child: Row(
                      children: [
                        Icon(Icons.info, size: 16),
                        SizedBox(width: 8),
                        Text('التفاصيل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'print',
                    child: Row(
                      children: [
                        Icon(Icons.print, size: 16),
                        SizedBox(width: 8),
                        Text('طباعة'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _calculateRunningBalance(List<AgentTransaction> transactions, int currentIndex) {
    double balance = 0.0;

    // Calculate balance up to current transaction (from oldest to current)
    for (int i = transactions.length - 1; i >= currentIndex; i--) {
      final transaction = transactions[i];

      switch (transaction.type) {
        case 'debt':
        case 'transfer':
        case 'goods':
          // Agent owes money
          balance += transaction.amount;
          break;
        case 'payment':
          // Agent paid money
          balance -= transaction.amount;
          break;
        case 'credit':
        case 'sale':
        case 'customer':
          // Agent gets credit (reduces debt)
          balance -= transaction.amount;
          break;
      }
    }

    return balance;
  }

  // دوال التبويبات المفقودة
  Widget _buildRecentTransactions() {
    final recentTransactions = _getFilteredTransactions().take(5).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'آخر المعاملات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (recentTransactions.isEmpty)
              const Text('لا توجد معاملات حديثة')
            else
              ...recentTransactions.map((transaction) => _buildTransactionTile(transaction)),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetrics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مؤشرات الأداء',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'متوسط المبيعات الشهرية',
                    '${_formatCurrency(_calculateMonthlyAverage())} ج.م',
                    Icons.trending_up,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildMetricCard(
                    'أفضل شهر',
                    _getBestMonth(),
                    Icons.star,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlyChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الرسم البياني الشهري',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: const Center(
                child: Text('الرسم البياني قيد التطوير'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionTypeChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع أنواع المعاملات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: const Center(
                child: Text('رسم بياني دائري قيد التطوير'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  double _calculateMonthlyAverage() {
    if (agentAccount?.transactions == null) return 0.0;

    final transactions = agentAccount!.transactions
        .where((t) => t.type == 'debt')
        .toList();

    if (transactions.isEmpty) return 0.0;

    final total = transactions.fold<double>(0.0, (sum, t) => sum + t.amount);
    return total / 12; // تقريبي للسنة
  }

  String _getBestMonth() {
    // حساب أفضل شهر بناءً على المبيعات
    return 'يناير 2024'; // مؤقت
  }

  // التنقل إلى تفاصيل المعاملة
  void _navigateToTransactionDetails(AgentTransaction transaction) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.6,
          height: MediaQuery.of(context).size.height * 0.7,
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[700],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.receipt, color: Colors.white),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'تفاصيل المعاملة',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailRow('نوع المعاملة', _getTransactionTypeName(transaction.type)),
                      _buildDetailRow('المبلغ', '${_formatCurrency(transaction.amount)} ج.م'),
                      _buildDetailRow('التاريخ', _formatDate(transaction.timestamp)),
                      _buildDetailRow('الوصف', transaction.description),
                      _buildDetailRow('المرجع', transaction.id!),
                      _buildDetailRow('رقم المعاملة', transaction.id!),

                      const SizedBox(height: 24),

                      // Action buttons
                      Row(
                        children: [
                          ElevatedButton.icon(
                            onPressed: () => _navigateToInvoice(transaction.id!),
                            icon: const Icon(Icons.receipt_long),
                            label: const Text('عرض التفاصيل'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton.icon(
                            onPressed: () => _printTransaction(transaction),
                            icon: const Icon(Icons.print),
                            label: const Text('طباعة'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _getTransactionTypeName(String type) {
    switch (type) {
      case 'debt':
        return 'مبيعات (مدين)';
      case 'payment':
        return 'دفعة (دائن)';
      case 'profit':
        return 'ربح (دائن)';
      default:
        return type;
    }
  }

  void _handleTransactionAction(String action, AgentTransaction transaction) {
    switch (action) {
      case 'details':
        _navigateToTransactionDetails(transaction);
        break;
      case 'print':
        _printTransaction(transaction);
        break;
    }
  }

  void _navigateToInvoice(String invoiceId) {
    Navigator.pop(context); // Close dialog first
    // TODO: Navigate to invoice details
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('التنقل إلى الفاتورة: $invoiceId')),
    );
  }

  void _printTransaction(AgentTransaction transaction) {
    // TODO: Implement print functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة الطباعة قيد التطوير')),
    );
  }

  Widget _buildTransactionTile(AgentTransaction transaction) {
    final type = transaction.type;
    final amount = transaction.amount;
    final date = transaction.timestamp;
    final description = transaction.description;

    Color color;
    IconData icon;

    switch (type) {
      case 'debt':
        color = Colors.red;
        icon = Icons.trending_up;
        break;
      case 'payment':
        color = Colors.green;
        icon = Icons.payment;
        break;
      case 'profit':
        color = Colors.blue;
        icon = Icons.monetization_on;
        break;
      default:
        color = Colors.grey;
        icon = Icons.receipt;
    }

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withOpacity(0.1),
        child: Icon(icon, color: color),
      ),
      title: Text(description),
      subtitle: Text(_formatDate(date)),
      trailing: Text(
        '${_formatCurrency(amount)} ج.م',
        style: TextStyle(
          color: type == 'payment' || type == 'profit' ? Colors.green : Colors.red,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatCurrency(double amount) {
    return amount.toStringAsFixed(2).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }
}
