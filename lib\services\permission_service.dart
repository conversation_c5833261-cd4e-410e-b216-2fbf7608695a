import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PermissionService {
  static PermissionService? _instance;
  static PermissionService get instance => _instance ??= PermissionService._();
  
  PermissionService._();

  Future<bool> requestCameraPermission() async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      if (kDebugMode) {
        print('Camera permission granted (desktop mode)');
      }
      return true;
    }

    try {
      final status = await Permission.camera.request();
      if (kDebugMode) {
        print('Camera permission status: $status');
      }
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting camera permission: $e');
      }
      return false;
    }
  }

  Future<bool> requestStoragePermission() async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      if (kDebugMode) {
        print('Storage permission granted (desktop mode)');
      }
      return true;
    }

    try {
      if (Platform.isAndroid) {
        final androidInfo = await _getAndroidVersion();
        if (kDebugMode) {
          print('Android SDK version: $androidInfo');
        }

        if (androidInfo >= 33) {
          // Android 13+ - request photos permission
          final photosStatus = await Permission.photos.request();
          if (kDebugMode) {
            print('Photos permission status: $photosStatus');
          }

          if (photosStatus.isDenied) {
            // Also try media images permission
            final mediaImagesStatus = await Permission.mediaLibrary.request();
            if (kDebugMode) {
              print('Media images permission status: $mediaImagesStatus');
            }
            return mediaImagesStatus.isGranted;
          }

          return photosStatus.isGranted;
        } else if (androidInfo >= 30) {
          // Android 11-12 - request storage permission
          final storageStatus = await Permission.storage.request();
          if (kDebugMode) {
            print('Storage permission status: $storageStatus');
          }
          return storageStatus.isGranted;
        } else {
          // Android 10 and below - request external storage
          final readStatus = await Permission.storage.request();
          if (kDebugMode) {
            print('Storage permission status: $readStatus');
          }
          return readStatus.isGranted;
        }
      } else {
        final status = await Permission.storage.request();
        return status.isGranted;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting storage permission: $e');
      }
      return false;
    }
  }

  Future<bool> requestPhotosPermission() async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      if (kDebugMode) {
        print('Photos permission granted (desktop mode)');
      }
      return true;
    }

    try {
      final status = await Permission.photos.request();
      if (kDebugMode) {
        print('Photos permission status: $status');
      }
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting photos permission: $e');
      }
      return false;
    }
  }

  Future<bool> requestCameraAndStoragePermissions() async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      if (kDebugMode) {
        print('All permissions granted (desktop mode)');
      }
      return true;
    }

    try {
      // Request camera permission first
      final cameraGranted = await requestCameraPermission();
      if (kDebugMode) {
        print('Camera permission result: $cameraGranted');
      }

      // For camera usage, we mainly need camera permission
      // Storage permission is optional for gallery access
      if (cameraGranted) {
        // Try to get storage permission but don't fail if denied
        final storageGranted = await requestStoragePermission();
        if (kDebugMode) {
          print('Storage permission result: $storageGranted');
          print('Camera and storage permissions: camera=$cameraGranted, storage=$storageGranted');
        }
        return true; // Camera is granted, which is the main requirement
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting camera and storage permissions: $e');
      }
      return false;
    }
  }

  /// Request storage permission with fallback options
  Future<bool> requestStoragePermissionWithFallback() async {
    if (kIsWeb || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      return true;
    }

    try {
      if (Platform.isAndroid) {
        final androidInfo = await _getAndroidVersion();

        if (androidInfo >= 33) {
          // Android 13+ - try multiple permission types
          var granted = await Permission.photos.request().isGranted;
          if (!granted) {
            granted = await Permission.mediaLibrary.request().isGranted;
          }
          return granted;
        } else {
          // Android 12 and below
          return await Permission.storage.request().isGranted;
        }
      }

      return await Permission.storage.request().isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting storage permission with fallback: $e');
      }
      return false;
    }
  }

  Future<int> _getAndroidVersion() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;
        return androidInfo.version.sdkInt;
      }
      return 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting Android version: $e');
      }
      return 33; // Default to Android 13+ if error
    }
  }

  Future<bool> hasCameraPermission() async {
    // For desktop/web, always return true
    return true;
  }

  String getPermissionStatusMessage(String status) {
    // Simple status message for desktop
    return 'مسموح';
  }

  Future<bool> requestNotificationPermission() async {
    // For desktop/web, always return true
    if (kDebugMode) {
      print('Notification permission granted (desktop mode)');
    }
    return true;
  }

  Future<bool> requestLocationPermission() async {
    // For desktop/web, always return true
    if (kDebugMode) {
      print('Location permission granted (desktop mode)');
    }
    return true;
  }

  Future<Map<String, bool>> getAllPermissionsStatus() async {
    // For desktop/web, all permissions are granted
    return {
      'camera': true,
      'storage': true,
      'photos': true,
      'notification': true,
      'location': true,
    };
  }
}
