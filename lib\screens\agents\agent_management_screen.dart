import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_account_model.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';
import '../../services/permissions_service.dart';
import '../reports/detailed_agent_statement_screen.dart';
// import '../../services/enhanced_pdf_service.dart'; // Removed - not compatible with Windows

class AgentManagementScreen extends StatefulWidget {
  const AgentManagementScreen({super.key});

  @override
  State<AgentManagementScreen> createState() => _AgentManagementScreenState();
}

class _AgentManagementScreenState extends State<AgentManagementScreen>
    with SingleTickerProviderStateMixin {
  final DataService _dataService = DataService.instance;
  late TabController _tabController;

  List<AgentAccountModel> _agentAccounts = [];
  List<UserModel> _agents = [];
  bool _isLoading = false;

  // Search and filter
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  final String _selectedFilter = 'all'; // all, active, inactive, debt, credit

  // Payment form
  final GlobalKey<FormState> _paymentFormKey = GlobalKey<FormState>();
  final TextEditingController _paymentAmountController = TextEditingController();
  final TextEditingController _paymentNotesController = TextEditingController();
  UserModel? _selectedAgentForPayment;

  // Statistics
  Map<String, dynamic> _statistics = {};

  // Recent Activities
  List<Map<String, dynamic>> _recentActivities = [];

  // Sorting
  String _sortBy = 'name'; // name, balance, lastTransaction, totalDebt
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _paymentAmountController.dispose();
    _paymentNotesController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    // Double check mounted state
    if (!mounted) {
      if (kDebugMode) {
        print('⚠️ _loadData called but widget is not mounted');
      }
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    } else {
      return;
    }

    try {
      if (!mounted) return;

      // Load agent accounts (use local only to avoid parsing errors)
      _agentAccounts = await _dataService.getAllAgentAccountsLocal();
      if (kDebugMode) {
        print('📊 Loaded ${_agentAccounts.length} agent accounts');
      }

      if (!mounted) return;

      // Load agent users
      _agents = await _dataService.getUsersByRole(AppConstants.agentRole);
      if (kDebugMode) {
        print('👥 Loaded ${_agents.length} agents');
      }

      // Create accounts for agents who don't have one
      for (final agent in _agents) {
        final hasAccount = _agentAccounts.any((account) => account.agentId == agent.id);
        if (!hasAccount) {
          if (kDebugMode) {
            print('🆕 Creating account for agent: ${agent.fullName}');
          }
          await _createAgentAccount(agent);
        }
      }

      if (!mounted) return;

      // Recalculate all agent account balances
      await _dataService.recalculateAllAgentAccounts();

      // Reload accounts after creating missing ones
      _agentAccounts = await _dataService.getAllAgentAccountsLocal();
      if (kDebugMode) {
        print('📊 After creating missing accounts: ${_agentAccounts.length} total accounts');
      }

      if (!mounted) return;

      // Calculate statistics
      await _calculateStatistics();

      // Load recent activities
      await _loadRecentActivities();

    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _refreshStatistics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _calculateStatistics();
      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تحديث الإحصائيات بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحديث الإحصائيات: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadRecentActivities() async {
    try {
      final activities = <Map<String, dynamic>>[];

      // تحميل آخر المعاملات من حسابات الوكلاء
      for (final account in _agentAccounts) {
        final recentTransactions = account.transactions
            .where((t) => t.timestamp.isAfter(DateTime.now().subtract(const Duration(days: 7))))
            .toList();

        recentTransactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));

        for (final transaction in recentTransactions.take(3)) {
          String title, subtitle;
          IconData icon;
          Color color;

          switch (transaction.type) {
            case 'sale':
              title = 'عملية بيع جديدة';
              subtitle = 'بيع بقيمة ${AppUtils.formatCurrency(transaction.amount)} للوكيل ${account.agentName}';
              icon = Icons.point_of_sale;
              color = Colors.green;
              break;
            case 'payment':
              title = 'تسجيل دفعة';
              subtitle = 'دفعة بقيمة ${AppUtils.formatCurrency(transaction.amount)} من الوكيل ${account.agentName}';
              icon = Icons.payment;
              color = Colors.blue;
              break;
            case 'transfer':
              title = 'تحويل بضاعة';
              subtitle = 'تحويل بقيمة ${AppUtils.formatCurrency(transaction.amount)} للوكيل ${account.agentName}';
              icon = Icons.transfer_within_a_station;
              color = Colors.orange;
              break;
            default:
              title = 'معاملة جديدة';
              subtitle = '${transaction.description} - ${account.agentName}';
              icon = Icons.receipt;
              color = Colors.grey;
          }

          activities.add({
            'title': title,
            'subtitle': subtitle,
            'icon': icon,
            'color': color,
            'time': transaction.timestamp,
            'agentName': account.agentName,
          });
        }
      }

      // ترتيب الأنشطة حسب التاريخ (الأحدث أولاً)
      activities.sort((a, b) => (b['time'] as DateTime).compareTo(a['time'] as DateTime));

      setState(() {
        _recentActivities = activities.take(5).toList(); // أحدث 5 أنشطة
      });

    } catch (e) {
      if (kDebugMode) {
        print('Error loading recent activities: $e');
      }
    }
  }

  Future<void> _calculateStatistics() async {
    try {
      double totalDebt = 0;
      double totalPaid = 0;
      double totalBalance = 0;
      int activeAgents = 0;
      int agentsWithDebt = 0;
      int agentsWithCredit = 0;

      // Get all invoices to calculate total sales and profits
      final allInvoices = await _dataService.getInvoices();
      double totalSales = 0;
      double totalCompanyProfits = 0;
      double totalAgentProfits = 0;
      double totalProfits = 0;
      double totalPurchasePrices = 0;
      double totalActualDebt = 0;

      for (final invoice in allInvoices) {
        if (invoice.agentId != null) {
          // Only count customer sales as actual sales
          if (invoice.type == 'customer') {
            totalSales += invoice.sellingPrice;
            totalCompanyProfits += invoice.companyProfitShare;
            totalAgentProfits += invoice.agentProfitShare;
            totalProfits += invoice.profitAmount;
          }

          // Always count purchase prices for cost tracking
          totalPurchasePrices += invoice.purchasePrice;

          // Calculate actual debt based on invoice type
          if (invoice.type == 'agent' ||
              invoice.type == 'goods' ||
              (invoice.customerData != null &&
               invoice.customerData!['transferType'] == 'warehouse_transfer') ||
              (invoice.additionalData != null &&
               invoice.additionalData!['transferType'] == 'goods_transfer')) {
            // Transfer invoice - agent owes full amount
            totalActualDebt += invoice.sellingPrice;
          } else if (invoice.type == 'customer' && invoice.companyProfitShare > 0) {
            // Customer sale - agent owes only company's profit share
            totalActualDebt += invoice.companyProfitShare;
          }
        }
      }

      for (final account in _agentAccounts) {
        totalDebt += account.totalDebt;
        totalPaid += account.totalPaid;
        totalBalance += account.currentBalance;

        // Find corresponding agent
        final agent = _agents.firstWhere(
          (a) => a.id == account.agentId,
          orElse: () => UserModel(
            id: '',
            fullName: '',
            username: '',
            email: '',
            phone: '',
            role: '',
            isActive: false,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        );

        if (agent.isActive) activeAgents++;
        if (account.currentBalance > 0) agentsWithDebt++;
        if (account.currentBalance < 0) agentsWithCredit++;
      }

      _statistics = {
        'totalAgents': _agents.length,
        'activeAgents': activeAgents,
        'totalDebt': totalDebt,
        'totalPaid': totalPaid,
        'totalBalance': totalBalance,
        'agentsWithDebt': agentsWithDebt,
        'agentsWithCredit': agentsWithCredit,
        'averageBalance': _agentAccounts.isNotEmpty ? totalBalance / _agentAccounts.length : 0,
        'totalSales': totalSales,
        'totalProfits': totalProfits,
        'totalCompanyProfits': totalCompanyProfits,
        'totalAgentProfits': totalAgentProfits,
        'totalPurchasePrices': totalPurchasePrices,
        'totalActualDebt': totalActualDebt,
        'profitSharingRatio': totalProfits > 0 ? (totalAgentProfits / totalProfits * 100) : 50.0,
      };
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في حساب الإحصائيات: $e', isError: true);
      }
    }
  }

  Future<void> _createAgentAccount(UserModel agent) async {
    try {
      final account = AgentAccountModel(
        id: 'agent_${agent.id}_${DateTime.now().millisecondsSinceEpoch}',
        agentId: agent.id,
        agentName: agent.fullName,
        agentPhone: agent.phone,
        totalDebt: 0.0,
        totalPaid: 0.0,
        currentBalance: 0.0,
        transactions: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: Provider.of<AuthProvider>(context, listen: false).currentUser!.id,
      );
      
      await _dataService.createOrUpdateAgentAccount(account);
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في إنشاء حساب الوكيل: $e', isError: true);
      }
    }
  }

  // Search and filter functions
  List<UserModel> get _filteredAgents {
    List<UserModel> filtered = List.from(_agents);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((agent) {
        return agent.fullName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               agent.username.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               agent.phone.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               agent.email.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Apply status filter
    switch (_selectedFilter) {
      case 'active':
        filtered = filtered.where((agent) => agent.isActive).toList();
        break;
      case 'inactive':
        filtered = filtered.where((agent) => !agent.isActive).toList();
        break;
      case 'debt':
        filtered = filtered.where((agent) {
          final account = _agentAccounts.firstWhere(
            (acc) => acc.agentId == agent.id,
            orElse: () => AgentAccountModel(
              id: '', agentId: '', agentName: '', agentPhone: '',
              totalDebt: 0, totalPaid: 0, currentBalance: 0, transactions: [],
              createdAt: DateTime.now(), updatedAt: DateTime.now(), createdBy: '',
            ),
          );
          return account.currentBalance > 0;
        }).toList();
        break;
      case 'credit':
        filtered = filtered.where((agent) {
          final account = _agentAccounts.firstWhere(
            (acc) => acc.agentId == agent.id,
            orElse: () => AgentAccountModel(
              id: '', agentId: '', agentName: '', agentPhone: '',
              totalDebt: 0, totalPaid: 0, currentBalance: 0, transactions: [],
              createdAt: DateTime.now(), updatedAt: DateTime.now(), createdBy: '',
            ),
          );
          return account.currentBalance < 0;
        }).toList();
        break;
    }

    // Apply sorting
    filtered.sort((a, b) {
      switch (_sortBy) {
        case 'name':
          return _sortAscending
              ? a.fullName.compareTo(b.fullName)
              : b.fullName.compareTo(a.fullName);
        case 'balance':
          final aAccount = _agentAccounts.firstWhere(
            (acc) => acc.agentId == a.id,
            orElse: () => AgentAccountModel(
              id: '', agentId: '', agentName: '', agentPhone: '',
              totalDebt: 0, totalPaid: 0, currentBalance: 0, transactions: [],
              createdAt: DateTime.now(), updatedAt: DateTime.now(), createdBy: '',
            ),
          );
          final bAccount = _agentAccounts.firstWhere(
            (acc) => acc.agentId == b.id,
            orElse: () => AgentAccountModel(
              id: '', agentId: '', agentName: '', agentPhone: '',
              totalDebt: 0, totalPaid: 0, currentBalance: 0, transactions: [],
              createdAt: DateTime.now(), updatedAt: DateTime.now(), createdBy: '',
            ),
          );
          return _sortAscending
              ? aAccount.currentBalance.compareTo(bAccount.currentBalance)
              : bAccount.currentBalance.compareTo(aAccount.currentBalance);
        case 'totalDebt':
          final aAccount = _agentAccounts.firstWhere(
            (acc) => acc.agentId == a.id,
            orElse: () => AgentAccountModel(
              id: '', agentId: '', agentName: '', agentPhone: '',
              totalDebt: 0, totalPaid: 0, currentBalance: 0, transactions: [],
              createdAt: DateTime.now(), updatedAt: DateTime.now(), createdBy: '',
            ),
          );
          final bAccount = _agentAccounts.firstWhere(
            (acc) => acc.agentId == b.id,
            orElse: () => AgentAccountModel(
              id: '', agentId: '', agentName: '', agentPhone: '',
              totalDebt: 0, totalPaid: 0, currentBalance: 0, transactions: [],
              createdAt: DateTime.now(), updatedAt: DateTime.now(), createdBy: '',
            ),
          );
          return _sortAscending
              ? aAccount.totalDebt.compareTo(bAccount.totalDebt)
              : bAccount.totalDebt.compareTo(aAccount.totalDebt);
        default:
          return 0;
      }
    });

    return filtered;
  }

  void _onSearchChanged(String value) {
    if (mounted) {
      setState(() {
        _searchQuery = value;
      });
    }
  }



  void _onSortChanged(String sortBy) {
    if (mounted) {
      setState(() {
        if (_sortBy == sortBy) {
          _sortAscending = !_sortAscending;
        } else {
          _sortBy = sortBy;
          _sortAscending = true;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Check permissions
        if (!PermissionsService.canAccess(authProvider.currentUser!, 'agent_payments')) {
          return Scaffold(
            appBar: AppBar(title: const Text('إدارة الوكلاء')),
            body: const Center(
              child: Text('ليس لديك صلاحية للوصول إلى هذه الشاشة'),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: const Text('إدارة الوكلاء'),
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
            elevation: 0,
            actions: [
              IconButton(
                icon: const Icon(Icons.picture_as_pdf),
                onPressed: _exportAgentsToPDF,
                tooltip: 'تصدير PDF',
              ),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  if (mounted) _loadData();
                },
                tooltip: 'تحديث البيانات',
              ),
              PopupMenuButton<String>(
                icon: const Icon(Icons.sort),
                tooltip: 'ترتيب',
                onSelected: _onSortChanged,
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'name',
                    child: Row(
                      children: [
                        const Icon(Icons.person, size: 20),
                        const SizedBox(width: 8),
                        const Text('الاسم'),
                        if (_sortBy == 'name') ...[
                          const Spacer(),
                          Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'balance',
                    child: Row(
                      children: [
                        const Icon(Icons.account_balance_wallet, size: 20),
                        const SizedBox(width: 8),
                        const Text('الرصيد'),
                        if (_sortBy == 'balance') ...[
                          const Spacer(),
                          Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
                        ],
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'totalDebt',
                    child: Row(
                      children: [
                        const Icon(Icons.trending_up, size: 20),
                        const SizedBox(width: 8),
                        const Text('إجمالي الدين'),
                        if (_sortBy == 'totalDebt') ...[
                          const Spacer(),
                          Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward, size: 16),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              tabs: const [
                Tab(text: 'نظرة عامة', icon: Icon(Icons.dashboard)),
                Tab(text: 'إدارة الحسابات', icon: Icon(Icons.account_balance)),
                Tab(text: 'تسجيل دفعة', icon: Icon(Icons.payment)),
              ],
            ),
          ),
          body: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(),
                    _buildAccountsTab(),
                    _buildPaymentsTab(),
                  ],
                ),
        );
      },
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Statistics Cards
          _buildStatisticsCards(),
          const SizedBox(height: AppConstants.largePadding),

          // Quick Actions
          _buildQuickActions(),
          const SizedBox(height: AppConstants.largePadding),

          // Recent Activity
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.analytics, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'إحصائيات عامة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            ElevatedButton.icon(
              onPressed: _refreshStatistics,
              icon: const Icon(Icons.refresh, size: 16),
              label: const Text('تحديث'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.defaultPadding),

        // استخدام LayoutBuilder لتحديد عدد الأعمدة بناءً على عرض الشاشة
        LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = 4; // افتراضي للشاشات الكبيرة
            if (constraints.maxWidth < 800) {
              crossAxisCount = 3;
            }
            if (constraints.maxWidth < 600) {
              crossAxisCount = 2;
            }

            return GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: AppConstants.defaultPadding,
              mainAxisSpacing: AppConstants.defaultPadding,
              childAspectRatio: 1.8, // نسبة أفضل للويندوز
              children: [
            _buildStatCard(
              'إجمالي الوكلاء',
              '${_statistics['totalAgents'] ?? 0}',
              Icons.people,
              Colors.blue,
            ),
            _buildStatCard(
              'الوكلاء النشطون',
              '${_statistics['activeAgents'] ?? 0}',
              Icons.verified_user,
              Colors.green,
            ),
            _buildStatCard(
              'إجمالي المديونية',
              AppUtils.formatCurrency(_statistics['totalDebt'] ?? 0),
              Icons.trending_up,
              Colors.orange,
            ),
            _buildStatCard(
              'إجمالي المدفوع',
              AppUtils.formatCurrency(_statistics['totalPaid'] ?? 0),
              Icons.payment,
              Colors.purple,
            ),
            _buildStatCard(
              'الرصيد الإجمالي',
              AppUtils.formatCurrency(_statistics['totalBalance'] ?? 0),
              Icons.account_balance,
              (_statistics['totalBalance'] ?? 0) >= 0 ? Colors.red : Colors.green,
            ),
            _buildStatCard(
              'متوسط الرصيد',
              AppUtils.formatCurrency(_statistics['averageBalance'] ?? 0),
              Icons.analytics,
              Colors.indigo,
            ),
            _buildStatCard(
              'إجمالي المبيعات',
              AppUtils.formatCurrency(_statistics['totalSales'] ?? 0),
              Icons.shopping_cart,
              Colors.teal,
            ),
            _buildStatCard(
              'أرباح المؤسسة',
              AppUtils.formatCurrency(_statistics['totalCompanyProfits'] ?? 0),
              Icons.business,
              Colors.deepOrange,
            ),
            _buildStatCard(
              'أرباح الوكلاء',
              AppUtils.formatCurrency(_statistics['totalAgentProfits'] ?? 0),
              Icons.person_pin_circle,
              Colors.cyan,
            ),
            _buildStatCard(
              'نسبة تقسيم الأرباح',
              '${(_statistics['profitSharingRatio'] ?? 50).toStringAsFixed(1)}%',
              Icons.pie_chart,
              Colors.amber,
            ),
            _buildStatCard(
              'المديونية الفعلية',
              AppUtils.formatCurrency(_statistics['totalActualDebt'] ?? 0),
              Icons.account_balance_wallet,
              Colors.red,
            ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shadowColor: color.withValues(alpha: 0.3),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05)
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, size: 28, color: color),
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontSize: 11,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                'تسجيل دفعة',
                'تسجيل دفعة جديدة لوكيل',
                Icons.payment,
                Colors.green,
                () => _tabController.animateTo(2),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: _buildQuickActionCard(
                'إدارة الحسابات',
                'عرض وإدارة حسابات الوكلاء',
                Icons.account_balance,
                Colors.blue,
                () => _tabController.animateTo(1),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            children: [
              Icon(icon, size: 40, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.history, color: Theme.of(context).primaryColor),
            const SizedBox(width: 8),
            Text(
              'النشاط الأخير',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: () => _loadRecentActivities(),
              icon: const Icon(Icons.refresh, size: 16),
              label: const Text('تحديث'),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: _recentActivities.isEmpty
                ? Column(
                    children: [
                      Icon(
                        Icons.inbox_outlined,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد أنشطة حديثة',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'ستظهر هنا آخر المعاملات والأنشطة',
                        style: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  )
                : Column(
                    children: _recentActivities.asMap().entries.map((entry) {
                      final index = entry.key;
                      final activity = entry.value;

                      return Column(
                        children: [
                          if (index > 0) const Divider(),
                          _buildActivityItem(
                            activity['title'],
                            activity['subtitle'],
                            activity['icon'],
                            activity['color'],
                            activity['time'],
                          ),
                        ],
                      );
                    }).toList(),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(String title, String subtitle, IconData icon, Color color, DateTime time) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: color.withValues(alpha: 0.1),
        child: Icon(icon, color: color),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Text(
        AppUtils.formatDateTime(time),
        style: Theme.of(context).textTheme.bodySmall,
      ),
    );
  }

  Widget _buildAccountsTab() {
    if (_agentAccounts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              'لا توجد حسابات وكلاء',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        if (mounted) await _loadData();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        itemCount: _agentAccounts.length,
        itemBuilder: (context, index) {
          final account = _agentAccounts[index];
          return _buildAccountCard(account);
        },
      ),
    );
  }

  Widget _buildAccountCard(AgentAccountModel account) {
    // Use calculated balance for more accuracy (following Android logic)
    final calculatedBalance = account.calculatedBalance;
    final hasDebt = calculatedBalance > 0;
    final hasCredit = calculatedBalance < 0;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: InkWell(
        onTap: () => _showAccountDetails(account),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Agent info
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: Text(
                      account.agentName.isNotEmpty ? account.agentName[0] : 'و',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.defaultPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          account.agentName,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          account.agentPhone,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Balance indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: hasDebt 
                          ? Colors.red.withValues(alpha: 0.1)
                          : hasCredit 
                              ? Colors.green.withValues(alpha: 0.1)
                              : Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: hasDebt 
                            ? Colors.red.withValues(alpha: 0.3)
                            : hasCredit 
                                ? Colors.green.withValues(alpha: 0.3)
                                : Colors.grey.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      hasDebt
                          ? 'مديون ${AppUtils.formatCurrency(calculatedBalance.abs())}'
                          : hasCredit
                              ? 'رصيد ${AppUtils.formatCurrency(calculatedBalance.abs())}'
                              : 'متوازن',
                      style: TextStyle(
                        color: hasDebt
                            ? Colors.red
                            : hasCredit
                                ? Colors.green
                                : Colors.grey,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Summary
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryItem(
                      'إجمالي المديونية',
                      AppUtils.formatCurrency(account.totalDebt),
                      Colors.orange,
                    ),
                  ),
                  Expanded(
                    child: _buildSummaryItem(
                      'إجمالي المدفوع',
                      AppUtils.formatCurrency(account.totalPaid),
                      Colors.green,
                    ),
                  ),
                  Expanded(
                    child: _buildSummaryItem(
                      'الرصيد الحالي',
                      AppUtils.formatCurrency(calculatedBalance.abs()),
                      calculatedBalance >= 0 ? Colors.red : Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPaymentsTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Quick payment section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تسجيل دفعة سريعة',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<UserModel>(
                          value: _selectedAgentForPayment,
                          decoration: const InputDecoration(
                            labelText: 'اختر الوكيل',
                            border: OutlineInputBorder(),
                          ),
                          items: _agents.map((agent) {
                            return DropdownMenuItem(
                              value: agent,
                              child: Text(agent.fullName),
                            );
                          }).toList(),
                          onChanged: (agent) {
                            if (mounted) {
                              setState(() {
                                _selectedAgentForPayment = agent;
                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: _selectedAgentForPayment != null
                            ? () => _showAddPaymentDialog(_selectedAgentForPayment!)
                            : null,
                        icon: const Icon(Icons.payment),
                        label: const Text('تسجيل دفعة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Recent payments list
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'آخر الدفعات المسجلة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: _buildRecentPaymentsList(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentPaymentsList() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _loadRecentPayments(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'خطأ في تحميل الدفعات: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        final payments = snapshot.data ?? [];

        if (payments.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.payment, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'لا توجد دفعات مسجلة حديثاً',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: payments.length,
          itemBuilder: (context, index) {
            final payment = payments[index];
            return _buildPaymentListItem(payment);
          },
        );
      },
    );
  }

  /// Load recent payments from all agents
  Future<List<Map<String, dynamic>>> _loadRecentPayments() async {
    try {
      // Get all payments from the last 30 days
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));

      final payments = await _dataService.getAllAgentPayments();

      // Filter recent payments and add agent info
      final recentPayments = <Map<String, dynamic>>[];

      for (final payment in payments) {
        final paymentDate = DateTime.parse(payment['createdAt']);
        if (paymentDate.isAfter(thirtyDaysAgo)) {
          // Find agent info
          final agentId = payment['agentId'];
          final agent = _agents.firstWhere(
            (a) => a.id == agentId,
            orElse: () => UserModel(
              id: agentId,
              username: 'unknown',
              email: '<EMAIL>',
              fullName: 'وكيل غير معروف',
              role: 'agent',
              phone: '',
              warehouseId: '',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          final paymentWithAgent = Map<String, dynamic>.from(payment);
          paymentWithAgent['agentName'] = agent.fullName;
          paymentWithAgent['agentPhone'] = agent.phone;

          recentPayments.add(paymentWithAgent);
        }
      }

      // Sort by date (newest first)
      recentPayments.sort((a, b) =>
        DateTime.parse(b['createdAt']).compareTo(DateTime.parse(a['createdAt']))
      );

      // Return only the latest 20 payments
      return recentPayments.take(20).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading recent payments: $e');
      }
      rethrow;
    }
  }

  /// Build payment list item widget
  Widget _buildPaymentListItem(Map<String, dynamic> payment) {
    final amount = (payment['amount'] ?? 0.0).toDouble();
    final agentName = payment['agentName'] ?? 'وكيل غير معروف';
    final notes = payment['notes'] ?? '';
    final createdAt = DateTime.parse(payment['createdAt']);
    final status = payment['status'] ?? 'confirmed';

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: status == 'confirmed' ? Colors.green : Colors.orange,
          child: Icon(
            status == 'confirmed' ? Icons.check : Icons.pending,
            color: Colors.white,
          ),
        ),
        title: Text(
          agentName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المبلغ: ${AppUtils.formatCurrency(amount)}'),
            if (notes.isNotEmpty) Text('ملاحظات: $notes'),
            Text(
              'التاريخ: ${AppUtils.formatDate(createdAt)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: Chip(
          label: Text(
            status == 'confirmed' ? 'مؤكد' : 'معلق',
            style: const TextStyle(fontSize: 12),
          ),
          backgroundColor: status == 'confirmed'
            ? Colors.green.withAlpha(51)
            : Colors.orange.withAlpha(51),
        ),
      ),
    );
  }



  void _showAccountDetails(AgentAccountModel account) {
    // Find the agent user model
    final agent = _agents.firstWhere(
      (a) => a.id == account.agentId,
      orElse: () => UserModel(
        id: account.agentId,
        username: 'unknown',
        email: '<EMAIL>',
        fullName: account.agentName,
        role: 'agent',
        phone: account.agentPhone,
        warehouseId: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    // Navigate to detailed statement screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DetailedAgentStatementScreen(agentId: agent.id),
      ),
    );
  }

  void _showEditProfitShareDialog(UserModel agent) {
    final TextEditingController profitController = TextEditingController(
      text: (agent.profitSharePercentage * 100).toStringAsFixed(0),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل نسبة ربح الوكيل: ${agent.fullName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'النسبة الحالية: ${(agent.profitSharePercentage * 100).toStringAsFixed(0)}%',
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: profitController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'نسبة ربح الوكيل (%)',
                hintText: 'أدخل النسبة من 0 إلى 100',
                border: OutlineInputBorder(),
                suffixText: '%',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'ملاحظة: النسبة المتبقية ستكون للمؤسسة',
              style: TextStyle(fontSize: 12, color: Colors.orange),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final percentageText = profitController.text.trim();
              if (percentageText.isEmpty) {
                AppUtils.showSnackBar(context, 'يرجى إدخال النسبة', isError: true);
                return;
              }

              final percentage = double.tryParse(percentageText);
              if (percentage == null || percentage < 0 || percentage > 100) {
                AppUtils.showSnackBar(context, 'يرجى إدخال نسبة صحيحة من 0 إلى 100', isError: true);
                return;
              }

              try {
                final updatedAgent = agent.copyWith(
                  profitSharePercentage: percentage / 100, // تحويل إلى عشري
                  updatedAt: DateTime.now(),
                );

                await _dataService.updateUser(updatedAgent);

                if (mounted) {
                  Navigator.pop(context);
                  AppUtils.showSnackBar(context, 'تم تحديث نسبة الربح بنجاح');

                  // إعادة تحميل البيانات
                  _loadData();
                }
              } catch (e) {
                if (mounted) {
                  AppUtils.showSnackBar(context, 'خطأ في تحديث نسبة الربح: $e', isError: true);
                }
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }





  void _showAddPaymentDialog(UserModel agent) {
    _selectedAgentForPayment = agent;
    _paymentAmountController.clear();
    _paymentNotesController.clear();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تسجيل دفعة للوكيل: ${agent.fullName}'),
        content: Form(
          key: _paymentFormKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _paymentAmountController,
                decoration: const InputDecoration(
                  labelText: 'مبلغ الدفعة',
                  prefixIcon: Icon(Icons.attach_money),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال مبلغ الدفعة';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'يرجى إدخال مبلغ صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppConstants.defaultPadding),
              TextFormField(
                controller: _paymentNotesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  prefixIcon: Icon(Icons.note),
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: _submitPayment,
            child: const Text('تسجيل الدفعة'),
          ),
        ],
      ),
    );
  }



  Future<void> _submitPayment() async {
    if (!_paymentFormKey.currentState!.validate()) {
      return;
    }

    if (_selectedAgentForPayment == null) {
      AppUtils.showSnackBar(context, 'خطأ: لم يتم اختيار الوكيل', isError: true);
      return;
    }

    try {
      final amount = double.parse(_paymentAmountController.text);
      final notes = _paymentNotesController.text.trim();

      await _dataService.addAgentPayment(
        agentId: _selectedAgentForPayment!.id,
        amount: amount,
        notes: notes.isNotEmpty ? notes : 'دفعة نقدية',
      );

      if (mounted) {
        Navigator.of(context).pop();
        AppUtils.showSnackBar(context, 'تم تسجيل الدفعة بنجاح');
        await _loadData();
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تسجيل الدفعة: $e', isError: true);
      }
    }
  }

  /// Export agents list to PDF
  Future<void> _exportAgentsToPDF() async {
    try {
      // Show loading
      if (mounted) {
        AppUtils.showSnackBar(context, 'جاري إنشاء قائمة الوكلاء...');
      }

      // PDF Service removed - not compatible with Windows
      // Using placeholder for Windows compatibility
      if (mounted) {
        AppUtils.showSnackBar(context, 'تصدير PDF غير متاح في نسخة Windows. استخدم التقارير المحسنة بدلاً من ذلك.');
      }

      if (mounted) {
        AppUtils.showSnackBar(context, '✅ تم تصدير قائمة الوكلاء بنجاح');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error exporting agents PDF: $e');
      }
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تصدير PDF: $e', isError: true);
      }
    }
  }

  /// Calculate agent account summary (following Android version logic)
  Future<Map<String, dynamic>> _calculateAgentAccountSummary(String agentId) async {
    try {
      if (kDebugMode) {
        print('📊 Calculating agent account summary for agent: $agentId');
      }

      // Get agent account first
      final agentAccount = await _dataService.getAgentAccount(agentId);

      if (agentAccount != null) {
        // Use calculated balance from agent account (more accurate)
        final calculatedBalance = agentAccount.calculatedBalance;

        // Calculate sales and profits from transactions
        double totalSales = 0.0;
        double totalProfit = 0.0;
        double agentCommission = 0.0;

        for (final transaction in agentAccount.transactions) {
          if (transaction.type == 'debt') {
            totalSales += transaction.amount;
            totalProfit += transaction.amount; // Simplified - assume all debt is profit
          }
        }

        // Use total debt as commission (simplified approach)
        agentCommission = agentAccount.totalDebt;

        if (kDebugMode) {
          print('📊 Using existing account for agent: $agentId');
          print('   Total Sales: $totalSales');
          print('   Total Profit: $totalProfit');
          print('   Agent Commission: $agentCommission');
          print('   Total Debt: ${agentAccount.totalDebt}');
          print('   Total Paid: ${agentAccount.totalPaid}');
          print('   Calculated Balance: $calculatedBalance');
        }

        return {
          'totalSales': totalSales,
          'totalProfit': totalProfit,
          'agentCommission': agentCommission,
          'totalPayments': agentAccount.totalPaid,
          'balance': calculatedBalance,
          'totalInvoices': agentAccount.transactions.where((t) => t.type == 'debt' || t.type == 'sale').length,
          'lastTransactionDate': agentAccount.transactions.isNotEmpty
              ? agentAccount.transactions.map((t) => t.timestamp).reduce((a, b) => a.isAfter(b) ? a : b)
              : null,
        };
      }

      // Fallback calculation if no account exists
      return await _calculateFromInvoicesAndPayments(agentId);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error calculating agent account summary: $e');
      }
      return {
        'totalSales': 0.0,
        'totalProfit': 0.0,
        'agentCommission': 0.0,
        'totalPayments': 0.0,
        'balance': 0.0,
        'totalInvoices': 0,
        'lastTransactionDate': null,
      };
    }
  }

  /// Fallback calculation from invoices and payments (Android version logic)
  Future<Map<String, dynamic>> _calculateFromInvoicesAndPayments(String agentId) async {
    try {
      // Get invoices for this agent
      final invoices = await _dataService.getInvoicesByAgent(agentId);
      final payments = await _dataService.getPaymentsByAgent(agentId);

      double totalSales = 0;
      double totalProfit = 0;
      double agentCommission = 0;
      double totalDebt = 0; // Total debt including purchase prices and company profit share
      int totalInvoices = invoices.length;

      if (kDebugMode) {
        print('📊 Fallback calculation for agent: $agentId');
        print('📊 Found ${invoices.length} invoices and ${payments.length} payments');
      }

      // Calculate totals from invoices (following Android logic)
      for (final invoice in invoices) {
        // Only count customer sales as actual sales
        if (invoice.type == 'customer') {
          totalSales += invoice.sellingPrice;
          totalProfit += invoice.profitAmount;
          agentCommission += invoice.agentProfitShare;
        }

        // Calculate debt based on invoice type (Android logic)
        if (invoice.type == 'agent' ||
            invoice.type == 'goods' ||
            (invoice.customerData != null &&
             invoice.customerData!['transferType'] == 'warehouse_transfer') ||
            (invoice.additionalData != null &&
             invoice.additionalData!['transferType'] == 'goods_transfer')) {
          // Transfer invoice - agent owes full amount
          totalDebt += invoice.sellingPrice;
        } else if (invoice.type == 'customer' && invoice.companyProfitShare > 0) {
          // Customer sale - agent owes only company's profit share
          totalDebt += invoice.companyProfitShare;
        }

        if (kDebugMode) {
          print('📊 Invoice ${invoice.invoiceNumber}: '
                'Type=${invoice.type}, '
                'Sales=${invoice.sellingPrice}, '
                'Purchase=${invoice.purchasePrice}, '
                'AgentProfit=${invoice.agentProfitShare}, '
                'CompanyProfit=${invoice.companyProfitShare}, '
                'DebtAdded=${invoice.type == 'agent' ? invoice.sellingPrice : (invoice.type == 'customer' ? invoice.companyProfitShare : 0)}');
        }
      }

      // Calculate total payments
      double totalPayments = 0;
      for (final paymentData in payments) {
        totalPayments += (paymentData['amount'] as double? ?? 0);
      }

      // Calculate correct balance (total debt - payments)
      final balance = totalDebt - totalPayments;

      if (kDebugMode) {
        print('📊 Fallback calculations:');
        print('   Total Sales: $totalSales');
        print('   Total Debt: $totalDebt');
        print('   Total Payments: $totalPayments');
        print('   Balance: $balance');
        print('   Agent Commission: $agentCommission');
      }

      return {
        'totalSales': totalSales,
        'totalProfit': totalProfit,
        'agentCommission': agentCommission,
        'totalPayments': totalPayments,
        'balance': balance,
        'totalInvoices': totalInvoices,
        'lastTransactionDate': invoices.isNotEmpty
            ? invoices.map((i) => i.createdAt).reduce((a, b) => a.isAfter(b) ? a : b)
            : null,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in fallback calculation: $e');
      }
      return {
        'totalSales': 0.0,
        'totalProfit': 0.0,
        'agentCommission': 0.0,
        'totalPayments': 0.0,
        'balance': 0.0,
        'totalInvoices': 0,
        'lastTransactionDate': null,
      };
    }
  }
}
