import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

/// Android-specific permission service with proper camera and storage handling
class AndroidPermissionService {
  static AndroidPermissionService? _instance;
  static AndroidPermissionService get instance => _instance ??= AndroidPermissionService._();
  
  AndroidPermissionService._();

  /// Request camera permission
  Future<bool> requestCameraPermission() async {
    try {
      if (kDebugMode) {
        print('📸 Requesting camera permission...');
      }

      final status = await Permission.camera.request();
      
      if (kDebugMode) {
        print('📸 Camera permission status: $status');
      }

      if (status.isGranted) {
        return true;
      } else if (status.isDenied) {
        if (kDebugMode) {
          print('❌ Camera permission denied');
        }
        return false;
      } else if (status.isPermanentlyDenied) {
        if (kDebugMode) {
          print('❌ Camera permission permanently denied - opening settings');
        }
        await openAppSettings();
        return false;
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting camera permission: $e');
      }
      return false;
    }
  }

  /// Request storage permission based on Android version
  Future<bool> requestStoragePermission() async {
    try {
      if (kDebugMode) {
        print('💾 Requesting storage permission...');
      }

      final androidVersion = await _getAndroidVersion();
      
      if (androidVersion >= 33) {
        // Android 13+ - Request photos permission
        final status = await Permission.photos.request();
        if (kDebugMode) {
          print('💾 Photos permission status (Android 13+): $status');
        }
        return status.isGranted;
      } else if (androidVersion >= 30) {
        // Android 11-12 - Request manage external storage
        final status = await Permission.manageExternalStorage.request();
        if (kDebugMode) {
          print('💾 Manage external storage permission status (Android 11-12): $status');
        }
        return status.isGranted;
      } else {
        // Android 10 and below - Request storage permission
        final status = await Permission.storage.request();
        if (kDebugMode) {
          print('💾 Storage permission status (Android 10-): $status');
        }
        return status.isGranted;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting storage permission: $e');
      }
      return false;
    }
  }

  /// Request both camera and storage permissions
  Future<bool> requestCameraAndStoragePermissions() async {
    try {
      if (kDebugMode) {
        print('🔐 Requesting camera and storage permissions...');
      }

      final cameraGranted = await requestCameraPermission();
      final storageGranted = await requestStoragePermission();

      final allGranted = cameraGranted && storageGranted;
      
      if (kDebugMode) {
        print('📸 Camera permission: ${cameraGranted ? "✅ Granted" : "❌ Denied"}');
        print('💾 Storage permission: ${storageGranted ? "✅ Granted" : "❌ Denied"}');
        print('🔐 All permissions: ${allGranted ? "✅ Granted" : "❌ Some denied"}');
      }

      return allGranted;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting permissions: $e');
      }
      return false;
    }
  }

  /// Check if camera permission is granted
  Future<bool> hasCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking camera permission: $e');
      }
      return false;
    }
  }

  /// Check if storage permission is granted
  Future<bool> hasStoragePermission() async {
    try {
      final androidVersion = await _getAndroidVersion();
      
      if (androidVersion >= 33) {
        final status = await Permission.photos.status;
        return status.isGranted;
      } else if (androidVersion >= 30) {
        final status = await Permission.manageExternalStorage.status;
        return status.isGranted;
      } else {
        final status = await Permission.storage.status;
        return status.isGranted;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking storage permission: $e');
      }
      return false;
    }
  }

  /// Request notification permission (Android 13+)
  Future<bool> requestNotificationPermission() async {
    try {
      if (kDebugMode) {
        print('🔔 Requesting notification permission...');
      }

      final androidVersion = await _getAndroidVersion();
      
      if (androidVersion >= 33) {
        final status = await Permission.notification.request();
        if (kDebugMode) {
          print('🔔 Notification permission status: $status');
        }
        return status.isGranted;
      } else {
        // For Android 12 and below, notifications are granted by default
        if (kDebugMode) {
          print('🔔 Notification permission granted (Android 12-)');
        }
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting notification permission: $e');
      }
      return false;
    }
  }

  /// Get Android SDK version
  Future<int> _getAndroidVersion() async {
    try {
      if (Platform.isAndroid) {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;
        return androidInfo.version.sdkInt;
      }
      return 0;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting Android version: $e');
      }
      return 33; // Default to Android 13+ if error
    }
  }

  /// Get permission status message in Arabic
  String getPermissionStatusMessage(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'مسموح';
      case PermissionStatus.denied:
        return 'مرفوض';
      case PermissionStatus.restricted:
        return 'محدود';
      case PermissionStatus.limited:
        return 'محدود جزئياً';
      case PermissionStatus.permanentlyDenied:
        return 'مرفوض نهائياً';
      case PermissionStatus.provisional:
        return 'مؤقت';
      default:
        return 'غير معروف';
    }
  }

  /// Check all required permissions
  Future<Map<String, bool>> checkAllPermissions() async {
    final permissions = <String, bool>{};
    
    try {
      permissions['camera'] = await hasCameraPermission();
      permissions['storage'] = await hasStoragePermission();
      
      final androidVersion = await _getAndroidVersion();
      if (androidVersion >= 33) {
        final notificationStatus = await Permission.notification.status;
        permissions['notification'] = notificationStatus.isGranted;
      } else {
        permissions['notification'] = true;
      }
      
      if (kDebugMode) {
        print('🔐 Permission status:');
        permissions.forEach((key, value) {
          print('   $key: ${value ? "✅" : "❌"}');
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking permissions: $e');
      }
    }
    
    return permissions;
  }

  /// Request all required permissions
  Future<bool> requestAllPermissions() async {
    try {
      if (kDebugMode) {
        print('🔐 Requesting all required permissions...');
      }

      final cameraAndStorage = await requestCameraAndStoragePermissions();
      final notification = await requestNotificationPermission();

      final allGranted = cameraAndStorage && notification;
      
      if (kDebugMode) {
        print('🔐 All permissions result: ${allGranted ? "✅ All granted" : "❌ Some denied"}');
      }

      return allGranted;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting all permissions: $e');
      }
      return false;
    }
  }
}
