# Firebase SDK Fix for Windows
# This file contains fixes for Firebase SDK compilation issues on Windows

# Disable specific warnings that Firebase SDK triggers
if(MSVC)
    # Disable C4996 warnings (deprecated functions)
    add_compile_options(/wd4996)

    # Disable other problematic warnings
    add_compile_options(/wd4244)  # conversion warnings
    add_compile_options(/wd4267)  # size_t conversion warnings
    add_compile_options(/wd4305)  # truncation warnings
    add_compile_options(/wd4800)  # forcing value to bool warnings
    add_compile_options(/wd4005)  # macro redefinition warnings

    # Set warning level to 3 instead of 4
    string(REGEX REPLACE "/W[0-4]" "/W3" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
    string(REGEX REPLACE "/W[0-4]" "/W3" CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")

    # Disable warnings as errors
    string(REGEX REPLACE "/WX" "" CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS}")
    string(REGEX REPLACE "/WX" "" CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")

    # Add necessary definitions (check if not already defined)
    if(NOT DEFINED _CRT_SECURE_NO_WARNINGS)
        add_definitions(-D_CRT_SECURE_NO_WARNINGS)
    endif()
    if(NOT DEFINED _CRT_NONSTDC_NO_DEPRECATE)
        add_definitions(-D_CRT_NONSTDC_NO_DEPRECATE)
    endif()
    add_definitions(-D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS)
    add_definitions(-DNOMINMAX)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    
    # Set C++ standard
    set(CMAKE_CXX_STANDARD 17)
    set(CMAKE_CXX_STANDARD_REQUIRED ON)
    
    message(STATUS "Firebase SDK fixes applied for MSVC")
endif()

# Additional fixes for specific Firebase components
macro(apply_firebase_fixes target_name)
    if(TARGET ${target_name})
        target_compile_definitions(${target_name} PRIVATE
            _CRT_SECURE_NO_WARNINGS
            _CRT_NONSTDC_NO_DEPRECATE
            _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS
        )
        
        if(MSVC)
            target_compile_options(${target_name} PRIVATE
                /wd4996  # deprecated functions
                /wd4244  # conversion warnings
                /wd4267  # size_t conversion
                /W3      # warning level 3
            )
        endif()
        
        message(STATUS "Applied Firebase fixes to ${target_name}")
    endif()
endmacro()

# Function to apply fixes to all Firebase targets
function(apply_firebase_fixes_to_all)
    # Common Firebase target names
    set(FIREBASE_TARGETS
        firebase_auth
        firebase_core
        firebase_firestore
        firebase_messaging
        firebase_storage
        firebase_auth_plugin
        firebase_core_plugin
        cloud_firestore_plugin
        firebase_messaging_plugin
        firebase_storage_plugin
    )
    
    foreach(target ${FIREBASE_TARGETS})
        apply_firebase_fixes(${target})
    endforeach()
endfunction()

# Configure download settings for Firebase SDK
set(CMAKE_DOWNLOAD_TIMEOUT 1200)  # 20 minutes timeout
set(CMAKE_DOWNLOAD_RETRY_COUNT 5)
set(CMAKE_TLS_VERIFY OFF)

# Set environment variables for better download performance
set(ENV{CURL_CA_BUNDLE} "")
set(ENV{SSL_VERIFY} "false")

message(STATUS "Firebase fix configuration loaded with enhanced download settings")
