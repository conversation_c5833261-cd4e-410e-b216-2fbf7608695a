import 'package:flutter/foundation.dart';
import '../../core/constants/app_constants.dart';
import '../../models/user_model.dart';
import '../../models/item_model.dart';
import '../../models/invoice_model.dart';
import '../../models/agent_account_model.dart';
import '../../models/warehouse_model.dart';
import '../../models/notification_model.dart';
import '../../models/document_tracking_model.dart';
import 'windows_firebase_service.dart';
import 'windows_local_database_service.dart';

/// خدمة البيانات للويندوز - متوافقة وظيفياً مع نسخة Android
/// تستخدم نفس نماذج البيانات ونفس مجموعات Firebase
class WindowsDataService {
  static WindowsDataService? _instance;
  static WindowsDataService get instance => _instance ??= WindowsDataService._();
  
  WindowsDataService._();

  final WindowsFirebaseService _firebaseService = WindowsFirebaseService.instance;
  final WindowsLocalDatabaseService _localDb = WindowsLocalDatabaseService.instance;

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  /// تهيئة خدمة البيانات
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Windows Data Service initialized - same data models as Android');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing Windows Data Service: $e');
      }
      rethrow;
    }
  }

  /// التحقق من الاتصال بالإنترنت
  Future<bool> _isOnline() async {
    return await _firebaseService.isOnline();
  }

  // === User Management - Same as Android ===

  /// الحصول على جميع المستخدمين
  Future<List<UserModel>> getAllUsers() async {
    try {
      // Try local database first
      final localUsers = await _localDb.query('users');
      
      if (localUsers.isNotEmpty) {
        return localUsers.map((user) => UserModel.fromMap(user)).toList();
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final snapshot = await _firebaseService.getCollection(AppConstants.usersCollection);
        final users = snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();
        
        // Cache locally
        for (final user in users) {
          await _localDb.insert('users', user.toMap());
        }
        
        return users;
      }

      return [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all users: $e');
      }
      return [];
    }
  }

  /// الحصول على مستخدم بالمعرف
  Future<UserModel?> getUserById(String userId) async {
    try {
      // Try local database first
      final localUsers = await _localDb.query('users', where: 'id = ?', whereArgs: [userId]);

      if (localUsers.isNotEmpty) {
        return UserModel.fromMap(localUsers.first);
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final doc = await _firebaseService.getDocument(AppConstants.usersCollection, userId);

        if (doc.exists) {
          final user = UserModel.fromFirestore(doc);
          await _localDb.insert('users', user.toMap());
          return user;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting user by ID: $e');
      }
      return null;
    }
  }

  /// إضافة مستخدم جديد
  Future<bool> addUser(UserModel user) async {
    try {
      // Add to local database first
      await _localDb.insert('users', user.toMap());

      // If online, sync to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.usersCollection)
            .doc(user.id)
            .set(user.toFirestore());
      }

      if (kDebugMode) {
        print('✅ User added successfully: ${user.id}');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error adding user: $e');
      }
      return false;
    }
  }

  // === Item Management - Same as Android ===

  /// الحصول على جميع الأصناف
  Future<List<ItemModel>> getAllItems() async {
    try {
      // Try local database first
      final localItems = await _localDb.query('items');
      
      if (localItems.isNotEmpty) {
        return localItems.map((item) => ItemModel.fromMap(item)).toList();
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final snapshot = await _firebaseService.getCollection(AppConstants.itemsCollection);
        final items = snapshot.docs.map((doc) => ItemModel.fromFirestore(doc)).toList();
        
        // Cache locally
        for (final item in items) {
          await _localDb.insert('items', item.toMap());
        }
        
        return items;
      }

      return [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all items: $e');
      }
      return [];
    }
  }

  /// الحصول على صنف بالمعرف
  Future<ItemModel?> getItemById(String itemId) async {
    try {
      // Try local database first
      final localItems = await _localDb.query('items', where: 'id = ?', whereArgs: [itemId]);
      
      if (localItems.isNotEmpty) {
        return ItemModel.fromMap(localItems.first);
      }
      
      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final doc = await _firebaseService.getDocument(AppConstants.itemsCollection, itemId);
        
        if (doc.exists) {
          final item = ItemModel.fromFirestore(doc);
          await _localDb.insert('items', item.toMap());
          return item;
        }
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting item by ID: $e');
      }
      return null;
    }
  }

  /// إضافة صنف جديد
  Future<bool> addItem(ItemModel item) async {
    try {
      // Add to local database first
      await _localDb.insert('items', item.toMap());

      // If online, sync to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.itemsCollection)
            .doc(item.id)
            .set(item.toFirestore());
      }

      if (kDebugMode) {
        print('✅ Item added successfully: ${item.id}');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error adding item: $e');
      }
      return false;
    }
  }

  // === Invoice Management - Same as Android ===

  /// الحصول على جميع الفواتير
  Future<List<InvoiceModel>> getAllInvoices() async {
    try {
      // Try local database first
      final localInvoices = await _localDb.query('invoices');
      
      if (localInvoices.isNotEmpty) {
        return localInvoices.map((invoice) => InvoiceModel.fromMap(invoice)).toList();
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final snapshot = await _firebaseService.getCollection(AppConstants.invoicesCollection);
        final invoices = snapshot.docs.map((doc) => InvoiceModel.fromFirestore(doc)).toList();
        
        // Cache locally
        for (final invoice in invoices) {
          await _localDb.insert('invoices', invoice.toMap());
        }
        
        return invoices;
      }

      return [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all invoices: $e');
      }
      return [];
    }
  }

  /// إضافة فاتورة جديدة
  Future<bool> addInvoice(InvoiceModel invoice) async {
    try {
      // Add to local database first
      await _localDb.insert('invoices', invoice.toMap());

      // If online, sync to Firebase
      if (await _isOnline()) {
        await _firebaseService.firestore
            .collection(AppConstants.invoicesCollection)
            .doc(invoice.id)
            .set(invoice.toFirestore());
      }

      if (kDebugMode) {
        print('✅ Invoice added successfully: ${invoice.id}');
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error adding invoice: $e');
      }
      return false;
    }
  }

  // === Agent Account Management - Same as Android ===

  /// الحصول على جميع حسابات الوكلاء
  Future<List<AgentAccountModel>> getAllAgentAccounts() async {
    try {
      // Try local database first
      final localAccounts = await _localDb.query('agent_accounts');
      
      if (localAccounts.isNotEmpty) {
        return localAccounts.map((account) => AgentAccountModel.fromMap(account)).toList();
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final snapshot = await _firebaseService.getCollection(AppConstants.agentAccountsCollection);
        final accounts = snapshot.docs.map((doc) => AgentAccountModel.fromFirestore(doc)).toList();
        
        // Cache locally
        for (final account in accounts) {
          await _localDb.insert('agent_accounts', account.toMap());
        }
        
        return accounts;
      }

      return [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all agent accounts: $e');
      }
      return [];
    }
  }

  // === Warehouse Management - Same as Android ===

  /// الحصول على جميع المخازن
  Future<List<WarehouseModel>> getAllWarehouses() async {
    try {
      // Try local database first
      final localWarehouses = await _localDb.query('warehouses');
      
      if (localWarehouses.isNotEmpty) {
        return localWarehouses.map((warehouse) => WarehouseModel.fromMap(warehouse)).toList();
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final snapshot = await _firebaseService.getCollection(AppConstants.warehousesCollection);
        final warehouses = snapshot.docs.map((doc) => WarehouseModel.fromFirestore(doc)).toList();
        
        // Cache locally
        for (final warehouse in warehouses) {
          await _localDb.insert('warehouses', warehouse.toMap());
        }
        
        return warehouses;
      }

      return [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all warehouses: $e');
      }
      return [];
    }
  }

  // === Notification Management - Same as Android ===

  /// الحصول على جميع الإشعارات
  Future<List<NotificationModel>> getAllNotifications() async {
    try {
      // Try local database first
      final localNotifications = await _localDb.query('notifications');
      
      if (localNotifications.isNotEmpty) {
        return localNotifications.map((notification) => NotificationModel.fromMap(notification)).toList();
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final snapshot = await _firebaseService.getCollection(AppConstants.notificationsCollection);
        final notifications = snapshot.docs.map((doc) => NotificationModel.fromFirestore(doc)).toList();
        
        // Cache locally
        for (final notification in notifications) {
          await _localDb.insert('notifications', notification.toMap());
        }
        
        return notifications;
      }

      return [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all notifications: $e');
      }
      return [];
    }
  }

  // === Document Tracking - Same as Android ===

  /// الحصول على جميع مستندات التتبع
  Future<List<DocumentTrackingModel>> getAllDocuments() async {
    try {
      // Try local database first
      final localDocuments = await _localDb.query('document_tracking');
      
      if (localDocuments.isNotEmpty) {
        return localDocuments.map((doc) => DocumentTrackingModel.fromMap(doc)).toList();
      }

      // If not found locally and online, fetch from Firebase
      if (await _isOnline()) {
        final snapshot = await _firebaseService.getCollection(AppConstants.documentTrackingCollection);
        final documents = snapshot.docs.map((doc) => DocumentTrackingModel.fromFirestore(doc)).toList();
        
        // Cache locally
        for (final document in documents) {
          await _localDb.insert('document_tracking', document.toMap());
        }
        
        return documents;
      }

      return [];
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting all documents: $e');
      }
      return [];
    }
  }

  // === Real-time Data Streams - Same as Android ===

  /// الاستماع لتغييرات الأصناف في الوقت الفعلي
  Stream<List<ItemModel>> listenToItems() {
    return _firebaseService.listenToCollection(AppConstants.itemsCollection)
        .map((snapshot) => snapshot.docs.map((doc) => ItemModel.fromFirestore(doc)).toList());
  }

  /// الاستماع لتغييرات الفواتير في الوقت الفعلي
  Stream<List<InvoiceModel>> listenToInvoices() {
    return _firebaseService.listenToCollection(AppConstants.invoicesCollection)
        .map((snapshot) => snapshot.docs.map((doc) => InvoiceModel.fromFirestore(doc)).toList());
  }

  /// الاستماع لتغييرات الإشعارات في الوقت الفعلي
  Stream<List<NotificationModel>> listenToNotifications() {
    return _firebaseService.listenToCollection(AppConstants.notificationsCollection)
        .map((snapshot) => snapshot.docs.map((doc) => NotificationModel.fromFirestore(doc)).toList());
  }

  /// تنظيف الموارد
  void dispose() {
    if (kDebugMode) {
      print('🧹 Windows Data Service disposed');
    }
  }
}
