import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج حساب الوكيل المبسط والموثوق
class SimpleAgentAccount {
  final String id;
  final String agentId;
  final String agentName;
  final double currentBalance; // الرصيد الحالي (موجب = دين للوكيل، سالب = دين على الوكيل)
  final double totalSales; // إجمالي المبيعات
  final double totalPayments; // إجمالي المدفوعات المستلمة
  final double totalCommission; // إجمالي العمولات
  final int transactionCount; // عدد المعاملات
  final DateTime lastTransactionDate; // تاريخ آخر معاملة
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  SimpleAgentAccount({
    required this.id,
    required this.agentId,
    required this.agentName,
    this.currentBalance = 0.0,
    this.totalSales = 0.0,
    this.totalPayments = 0.0,
    this.totalCommission = 0.0,
    this.transactionCount = 0,
    DateTime? lastTransactionDate,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  }) : lastTransactionDate = lastTransactionDate ?? createdAt;

  /// إنشاء من Firestore
  factory SimpleAgentAccount.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return SimpleAgentAccount(
      id: doc.id,
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      currentBalance: (data['currentBalance'] ?? 0.0).toDouble(),
      totalSales: (data['totalSales'] ?? 0.0).toDouble(),
      totalPayments: (data['totalPayments'] ?? 0.0).toDouble(),
      totalCommission: (data['totalCommission'] ?? 0.0).toDouble(),
      transactionCount: data['transactionCount'] ?? 0,
      lastTransactionDate: data['lastTransactionDate'] != null
          ? (data['lastTransactionDate'] as Timestamp).toDate()
          : DateTime.now(),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      isActive: data['isActive'] ?? true,
    );
  }

  /// إنشاء من Map (قاعدة البيانات المحلية)
  factory SimpleAgentAccount.fromMap(Map<String, dynamic> map) {
    return SimpleAgentAccount(
      id: map['id'] ?? '',
      agentId: map['agentId'] ?? '',
      agentName: map['agentName'] ?? '',
      currentBalance: (map['currentBalance'] ?? 0.0).toDouble(),
      totalSales: (map['totalSales'] ?? 0.0).toDouble(),
      totalPayments: (map['totalPayments'] ?? 0.0).toDouble(),
      totalCommission: (map['totalCommission'] ?? 0.0).toDouble(),
      transactionCount: map['transactionCount'] ?? 0,
      lastTransactionDate: map['lastTransactionDate'] != null
          ? DateTime.parse(map['lastTransactionDate'])
          : DateTime.now(),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      isActive: (map['isActive'] ?? 1) == 1,
    );
  }

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'agentId': agentId,
      'agentName': agentName,
      'currentBalance': currentBalance,
      'totalSales': totalSales,
      'totalPayments': totalPayments,
      'totalCommission': totalCommission,
      'transactionCount': transactionCount,
      'lastTransactionDate': lastTransactionDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isActive': isActive ? 1 : 0,
    };
  }

  /// تحويل إلى Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'agentId': agentId,
      'agentName': agentName,
      'currentBalance': currentBalance,
      'totalSales': totalSales,
      'totalPayments': totalPayments,
      'totalCommission': totalCommission,
      'transactionCount': transactionCount,
      'lastTransactionDate': Timestamp.fromDate(lastTransactionDate),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isActive': isActive,
    };
  }

  /// نسخ مع تعديل
  SimpleAgentAccount copyWith({
    String? id,
    String? agentId,
    String? agentName,
    double? currentBalance,
    double? totalSales,
    double? totalPayments,
    double? totalCommission,
    int? transactionCount,
    DateTime? lastTransactionDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return SimpleAgentAccount(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      currentBalance: currentBalance ?? this.currentBalance,
      totalSales: totalSales ?? this.totalSales,
      totalPayments: totalPayments ?? this.totalPayments,
      totalCommission: totalCommission ?? this.totalCommission,
      transactionCount: transactionCount ?? this.transactionCount,
      lastTransactionDate: lastTransactionDate ?? this.lastTransactionDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  /// الحصول على حالة الحساب
  String get balanceStatus {
    if (currentBalance > 0) {
      return 'دائن'; // الوكيل له رصيد
    } else if (currentBalance < 0) {
      return 'مدين'; // الوكيل عليه دين
    } else {
      return 'متوازن'; // الحساب متوازن
    }
  }

  /// الحصول على الرصيد المطلق
  double get absoluteBalance => currentBalance.abs();

  /// التحقق من وجود نشاط
  bool get hasActivity => transactionCount > 0;

  @override
  String toString() {
    return 'SimpleAgentAccount(id: $id, agent: $agentName, balance: $currentBalance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SimpleAgentAccount && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
