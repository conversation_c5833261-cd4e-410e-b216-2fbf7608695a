import 'dart:convert';

class BackupModel {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;
  final int size; // Size in bytes
  final String version;
  final BackupType type;
  final BackupStatus status;
  final String? filePath;
  final String? cloudUrl;
  final Map<String, dynamic> metadata;
  final List<String> includedTables;
  final String? checksum;

  BackupModel({
    required this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.size,
    required this.version,
    required this.type,
    required this.status,
    this.filePath,
    this.cloudUrl,
    required this.metadata,
    required this.includedTables,
    this.checksum,
  });

  factory BackupModel.fromJson(Map<String, dynamic> json) {
    return BackupModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      size: json['size'] as int,
      version: json['version'] as String,
      type: BackupType.values.firstWhere(
        (e) => e.toString() == 'BackupType.${json['type']}',
        orElse: () => BackupType.full,
      ),
      status: BackupStatus.values.firstWhere(
        (e) => e.toString() == 'BackupStatus.${json['status']}',
        orElse: () => BackupStatus.completed,
      ),
      filePath: json['filePath'] as String?,
      cloudUrl: json['cloudUrl'] as String?,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
      includedTables: List<String>.from(json['includedTables'] as List),
      checksum: json['checksum'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'size': size,
      'version': version,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'filePath': filePath,
      'cloudUrl': cloudUrl,
      'metadata': metadata,
      'includedTables': includedTables,
      'checksum': checksum,
    };
  }

  String toJsonString() => jsonEncode(toJson());

  factory BackupModel.fromJsonString(String jsonString) =>
      BackupModel.fromJson(jsonDecode(jsonString));

  BackupModel copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    int? size,
    String? version,
    BackupType? type,
    BackupStatus? status,
    String? filePath,
    String? cloudUrl,
    Map<String, dynamic>? metadata,
    List<String>? includedTables,
    String? checksum,
  }) {
    return BackupModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      size: size ?? this.size,
      version: version ?? this.version,
      type: type ?? this.type,
      status: status ?? this.status,
      filePath: filePath ?? this.filePath,
      cloudUrl: cloudUrl ?? this.cloudUrl,
      metadata: metadata ?? this.metadata,
      includedTables: includedTables ?? this.includedTables,
      checksum: checksum ?? this.checksum,
    );
  }

  @override
  String toString() {
    return 'BackupModel(id: $id, name: $name, type: $type, status: $status, size: $size)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BackupModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// Get formatted size string
  String get formattedSize {
    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get status text in Arabic
  String get statusText {
    switch (status) {
      case BackupStatus.pending:
        return 'في الانتظار';
      case BackupStatus.inProgress:
        return 'جاري الإنشاء';
      case BackupStatus.completed:
        return 'مكتملة';
      case BackupStatus.failed:
        return 'فشلت';
      case BackupStatus.corrupted:
        return 'تالفة';
    }
  }

  /// Get type text in Arabic
  String get typeText {
    switch (type) {
      case BackupType.full:
        return 'نسخة كاملة';
      case BackupType.incremental:
        return 'نسخة تزايدية';
      case BackupType.differential:
        return 'نسخة تفاضلية';
      case BackupType.manual:
        return 'نسخة يدوية';
      case BackupType.scheduled:
        return 'نسخة مجدولة';
    }
  }

  /// Check if backup is valid
  bool get isValid {
    return status == BackupStatus.completed && 
           filePath != null && 
           size > 0;
  }

  /// Check if backup is cloud-based
  bool get isCloudBased {
    return cloudUrl != null && cloudUrl!.isNotEmpty;
  }

  /// Get backup age in days
  int get ageInDays {
    return DateTime.now().difference(createdAt).inDays;
  }
}

/// Backup types
enum BackupType {
  full,
  incremental,
  differential,
  manual,
  scheduled,
}

/// Backup status
enum BackupStatus {
  pending,
  inProgress,
  completed,
  failed,
  corrupted,
}

/// Backup configuration
class BackupConfig {
  final bool autoBackup;
  final int maxBackups;
  final int retentionDays;
  final List<String> includedTables;
  final List<String> excludedTables;
  final bool compressBackups;
  final bool encryptBackups;
  final String? encryptionKey;
  final bool uploadToCloud;
  final String? cloudProvider;
  final Map<String, dynamic> cloudConfig;

  BackupConfig({
    this.autoBackup = false,
    this.maxBackups = 10,
    this.retentionDays = 30,
    this.includedTables = const [],
    this.excludedTables = const [],
    this.compressBackups = true,
    this.encryptBackups = false,
    this.encryptionKey,
    this.uploadToCloud = false,
    this.cloudProvider,
    this.cloudConfig = const {},
  });

  factory BackupConfig.fromJson(Map<String, dynamic> json) {
    return BackupConfig(
      autoBackup: json['autoBackup'] as bool? ?? false,
      maxBackups: json['maxBackups'] as int? ?? 10,
      retentionDays: json['retentionDays'] as int? ?? 30,
      includedTables: List<String>.from(json['includedTables'] as List? ?? []),
      excludedTables: List<String>.from(json['excludedTables'] as List? ?? []),
      compressBackups: json['compressBackups'] as bool? ?? true,
      encryptBackups: json['encryptBackups'] as bool? ?? false,
      encryptionKey: json['encryptionKey'] as String?,
      uploadToCloud: json['uploadToCloud'] as bool? ?? false,
      cloudProvider: json['cloudProvider'] as String?,
      cloudConfig: Map<String, dynamic>.from(json['cloudConfig'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'autoBackup': autoBackup,
      'maxBackups': maxBackups,
      'retentionDays': retentionDays,
      'includedTables': includedTables,
      'excludedTables': excludedTables,
      'compressBackups': compressBackups,
      'encryptBackups': encryptBackups,
      'encryptionKey': encryptionKey,
      'uploadToCloud': uploadToCloud,
      'cloudProvider': cloudProvider,
      'cloudConfig': cloudConfig,
    };
  }
}

/// Restore operation model
class RestoreOperation {
  final String id;
  final String backupId;
  final DateTime startedAt;
  final DateTime? completedAt;
  final RestoreStatus status;
  final List<String> restoredTables;
  final Map<String, int> restoredCounts;
  final String? errorMessage;
  final double progress;

  RestoreOperation({
    required this.id,
    required this.backupId,
    required this.startedAt,
    this.completedAt,
    required this.status,
    this.restoredTables = const [],
    this.restoredCounts = const {},
    this.errorMessage,
    this.progress = 0.0,
  });

  factory RestoreOperation.fromJson(Map<String, dynamic> json) {
    return RestoreOperation(
      id: json['id'] as String,
      backupId: json['backupId'] as String,
      startedAt: DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      status: RestoreStatus.values.firstWhere(
        (e) => e.toString() == 'RestoreStatus.${json['status']}',
        orElse: () => RestoreStatus.pending,
      ),
      restoredTables: List<String>.from(json['restoredTables'] as List? ?? []),
      restoredCounts: Map<String, int>.from(json['restoredCounts'] as Map? ?? {}),
      errorMessage: json['errorMessage'] as String?,
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'backupId': backupId,
      'startedAt': startedAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'status': status.toString().split('.').last,
      'restoredTables': restoredTables,
      'restoredCounts': restoredCounts,
      'errorMessage': errorMessage,
      'progress': progress,
    };
  }
}

/// Restore status
enum RestoreStatus {
  pending,
  inProgress,
  completed,
  failed,
  cancelled,
}
