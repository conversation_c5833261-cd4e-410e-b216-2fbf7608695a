import 'package:flutter_test/flutter_test.dart';
import 'package:el_farhan_app/models/agent_financial_account_model.dart';
import 'package:el_farhan_app/services/agent_financial_service.dart';

/// اختبارات شاملة لنظام الحسابات المالية للوكلاء
void main() {
  group('Agent Financial System Tests', () {
    late AgentFinancialService financialService;

    setUp(() {
      financialService = AgentFinancialService();
    });

    group('AgentFinancialAccount Model Tests', () {
      test('should calculate current balance correctly', () {
        // ترتيب البيانات
        final account = AgentFinancialAccount(
          id: 'test_account',
          agentId: 'agent_123',
          agentName: 'أحمد محمد',
          agentPhone: '***********',
          totalGoodsReceived: 10000.0,
          totalGoodsReturned: 1000.0,
          totalCustomerSales: 8000.0,
          totalAgentProfits: 1600.0,
          totalCompanyProfits: 6400.0,
          totalPaymentsReceived: 5000.0,
          currentDebt: 0.0,
          currentBalance: 0.0,
          transactions: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: 'test_user',
          lastUpdatedBy: 'test_user',
        );

        // تنفيذ الاختبار
        final calculatedBalance = account.calculateCurrentBalance();
        final expectedBalance = (10000.0 - 1000.0) + 6400.0 - 5000.0; // 15400

        // التحقق من النتيجة
        expect(calculatedBalance, equals(expectedBalance));
      });

      test('should calculate current debt correctly', () {
        // ترتيب البيانات - حالة مديونية
        final account = AgentFinancialAccount(
          id: 'test_account',
          agentId: 'agent_123',
          agentName: 'أحمد محمد',
          agentPhone: '***********',
          totalGoodsReceived: 10000.0,
          totalGoodsReturned: 0.0,
          totalCustomerSales: 5000.0,
          totalAgentProfits: 1000.0,
          totalCompanyProfits: 4000.0,
          totalPaymentsReceived: 2000.0,
          currentDebt: 0.0,
          currentBalance: 0.0,
          transactions: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: 'test_user',
          lastUpdatedBy: 'test_user',
        );

        // تنفيذ الاختبار
        final calculatedDebt = account.calculateCurrentDebt();
        final expectedDebt = 12000.0; // 10000 + 4000 - 2000

        // التحقق من النتيجة
        expect(calculatedDebt, equals(expectedDebt));
      });

      test('should calculate credit balance correctly', () {
        // ترتيب البيانات - حالة رصيد دائن
        final account = AgentFinancialAccount(
          id: 'test_account',
          agentId: 'agent_123',
          agentName: 'أحمد محمد',
          agentPhone: '***********',
          totalGoodsReceived: 5000.0,
          totalGoodsReturned: 0.0,
          totalCustomerSales: 8000.0,
          totalAgentProfits: 1600.0,
          totalCompanyProfits: 6400.0,
          totalPaymentsReceived: 15000.0,
          currentDebt: 0.0,
          currentBalance: 0.0,
          transactions: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: 'test_user',
          lastUpdatedBy: 'test_user',
        );

        // تنفيذ الاختبار
        final creditBalance = account.calculateCreditBalance();
        final expectedCredit = 3600.0; // 15000 - (5000 + 6400)

        // التحقق من النتيجة
        expect(creditBalance, equals(expectedCredit));
      });

      test('should validate accounts correctly', () {
        // ترتيب البيانات - حسابات صحيحة
        final account = AgentFinancialAccount(
          id: 'test_account',
          agentId: 'agent_123',
          agentName: 'أحمد محمد',
          agentPhone: '***********',
          totalGoodsReceived: 10000.0,
          totalGoodsReturned: 1000.0,
          totalCustomerSales: 8000.0,
          totalAgentProfits: 1600.0,
          totalCompanyProfits: 6400.0,
          totalPaymentsReceived: 5000.0,
          currentDebt: 10400.0, // الحساب الصحيح
          currentBalance: 10400.0, // الحساب الصحيح
          transactions: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: 'test_user',
          lastUpdatedBy: 'test_user',
        );

        // تنفيذ الاختبار
        final isValid = account.validateAccounts();

        // التحقق من النتيجة
        expect(isValid, isTrue);
      });

      test('should detect invalid accounts', () {
        // ترتيب البيانات - حسابات خاطئة
        final account = AgentFinancialAccount(
          id: 'test_account',
          agentId: 'agent_123',
          agentName: 'أحمد محمد',
          agentPhone: '***********',
          totalGoodsReceived: 10000.0,
          totalGoodsReturned: 1000.0,
          totalCustomerSales: 8000.0,
          totalAgentProfits: 1600.0,
          totalCompanyProfits: 6400.0,
          totalPaymentsReceived: 5000.0,
          currentDebt: 5000.0, // حساب خاطئ
          currentBalance: 8000.0, // حساب خاطئ
          transactions: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: 'test_user',
          lastUpdatedBy: 'test_user',
        );

        // تنفيذ الاختبار
        final isValid = account.validateAccounts();

        // التحقق من النتيجة
        expect(isValid, isFalse);
      });
    });

    group('AgentTransaction Model Tests', () {
      test('should create transaction correctly', () {
        // ترتيب البيانات
        final transaction = AgentTransaction(
          id: 'txn_123',
          type: 'goods_received',
          description: 'استلام بضاعة - فاتورة INV-001',
          amount: 5000.0,
          balanceAfter: 5000.0,
          relatedInvoiceId: 'inv_001',
          createdAt: DateTime.now(),
          createdBy: 'test_user',
        );

        // التحقق من النتيجة
        expect(transaction.id, equals('txn_123'));
        expect(transaction.type, equals('goods_received'));
        expect(transaction.amount, equals(5000.0));
        expect(transaction.balanceAfter, equals(5000.0));
        expect(transaction.relatedInvoiceId, equals('inv_001'));
      });

      test('should convert transaction to map correctly', () {
        // ترتيب البيانات
        final now = DateTime.now();
        final transaction = AgentTransaction(
          id: 'txn_123',
          type: 'payment_received',
          description: 'دفعة نقدية',
          amount: -2000.0,
          balanceAfter: 3000.0,
          relatedPaymentId: 'pay_001',
          metadata: {'paymentMethod': 'نقدي'},
          createdAt: now,
          createdBy: 'test_user',
        );

        // تنفيذ الاختبار
        final map = transaction.toMap();

        // التحقق من النتيجة
        expect(map['id'], equals('txn_123'));
        expect(map['type'], equals('payment_received'));
        expect(map['amount'], equals(-2000.0));
        expect(map['balanceAfter'], equals(3000.0));
        expect(map['relatedPaymentId'], equals('pay_001'));
        expect(map['metadata'], equals({'paymentMethod': 'نقدي'}));
        expect(map['createdBy'], equals('test_user'));
      });
    });

    group('Financial Calculations Tests', () {
      test('should handle zero values correctly', () {
        // ترتيب البيانات - قيم صفر
        final account = AgentFinancialAccount(
          id: 'test_account',
          agentId: 'agent_123',
          agentName: 'أحمد محمد',
          agentPhone: '***********',
          totalGoodsReceived: 0.0,
          totalGoodsReturned: 0.0,
          totalCustomerSales: 0.0,
          totalAgentProfits: 0.0,
          totalCompanyProfits: 0.0,
          totalPaymentsReceived: 0.0,
          currentDebt: 0.0,
          currentBalance: 0.0,
          transactions: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: 'test_user',
          lastUpdatedBy: 'test_user',
        );

        // تنفيذ الاختبار
        final balance = account.calculateCurrentBalance();
        final debt = account.calculateCurrentDebt();
        final credit = account.calculateCreditBalance();

        // التحقق من النتيجة
        expect(balance, equals(0.0));
        expect(debt, equals(0.0));
        expect(credit, equals(0.0));
      });

      test('should handle large numbers correctly', () {
        // ترتيب البيانات - أرقام كبيرة
        final account = AgentFinancialAccount(
          id: 'test_account',
          agentId: 'agent_123',
          agentName: 'أحمد محمد',
          agentPhone: '***********',
          totalGoodsReceived: 1000000.0,
          totalGoodsReturned: 50000.0,
          totalCustomerSales: 800000.0,
          totalAgentProfits: 160000.0,
          totalCompanyProfits: 640000.0,
          totalPaymentsReceived: 500000.0,
          currentDebt: 0.0,
          currentBalance: 0.0,
          transactions: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          createdBy: 'test_user',
          lastUpdatedBy: 'test_user',
        );

        // تنفيذ الاختبار
        final balance = account.calculateCurrentBalance();
        final expectedBalance = (1000000.0 - 50000.0) + 640000.0 - 500000.0; // 1090000

        // التحقق من النتيجة
        expect(balance, equals(expectedBalance));
      });
    });
  });
}
