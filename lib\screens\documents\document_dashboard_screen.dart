import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../models/document_tracking_model.dart';
import '../../services/data_service.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';

class DocumentDashboardScreen extends StatefulWidget {
  const DocumentDashboardScreen({super.key});

  @override
  State<DocumentDashboardScreen> createState() => _DocumentDashboardScreenState();
}

class _DocumentDashboardScreenState extends State<DocumentDashboardScreen> {
  final DataService _dataService = DataService.instance;
  
  List<DocumentTrackingModel> _allDocuments = [];
  Map<String, int> _statusCounts = {};
  bool _isLoading = true;
  String _selectedTimeFilter = 'all'; // all, today, week, month

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Clear local data first to ensure fresh sync
      await _dataService.clearLocalDocumentTracking();

      // Force refresh from Firebase to get latest data
      _allDocuments = await _dataService.getAllDocumentTracking(forceFromFirebase: true);

      if (kDebugMode) {
        print('📊 Dashboard: Cleared local data and loaded ${_allDocuments.length} document tracking records from Firebase');
      }

      // Apply time filter
      final filteredDocuments = _applyTimeFilter(_allDocuments);

      // Calculate status counts
      _calculateStatusCounts(filteredDocuments);

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تحديث البيانات من Firebase');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading dashboard data: $e');
      }
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    }
  }

  List<DocumentTrackingModel> _applyTimeFilter(List<DocumentTrackingModel> documents) {
    final now = DateTime.now();
    
    switch (_selectedTimeFilter) {
      case 'today':
        return documents.where((doc) {
          return doc.createdAt.year == now.year &&
                 doc.createdAt.month == now.month &&
                 doc.createdAt.day == now.day;
        }).toList();
      
      case 'week':
        final weekAgo = now.subtract(const Duration(days: 7));
        return documents.where((doc) => doc.createdAt.isAfter(weekAgo)).toList();
      
      case 'month':
        final monthAgo = now.subtract(const Duration(days: 30));
        return documents.where((doc) => doc.createdAt.isAfter(monthAgo)).toList();
      
      default:
        return documents;
    }
  }

  void _calculateStatusCounts(List<DocumentTrackingModel> documents) {
    _statusCounts = {};

    for (final doc in documents) {
      _statusCounts[doc.currentStatus] = (_statusCounts[doc.currentStatus] ?? 0) + 1;
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة معلومات الجوابات'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
            tooltip: 'مزامنة مع Firebase',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDashboardData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTimeFilterSection(),
                    const SizedBox(height: 20),
                    _buildSummaryCards(),
                    const SizedBox(height: 20),
                    _buildStatusBreakdown(),
                    const SizedBox(height: 20),
                    _buildRecentDocuments(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildTimeFilterSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'فترة العرض',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: [
                _buildFilterChip('الكل', 'all'),
                _buildFilterChip('اليوم', 'today'),
                _buildFilterChip('الأسبوع', 'week'),
                _buildFilterChip('الشهر', 'month'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    return FilterChip(
      label: Text(label),
      selected: _selectedTimeFilter == value,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedTimeFilter = value;
          });
          _loadDashboardData();
        }
      },
    );
  }

  Widget _buildSummaryCards() {
    final filteredDocuments = _applyTimeFilter(_allDocuments);
    final totalCount = filteredDocuments.length;
    final completedCount = filteredDocuments
        .where((doc) => doc.currentStatus == AppConstants.documentReceivedFromManufacturer)
        .length;
    final pendingCount = filteredDocuments
        .where((doc) => doc.currentStatus != AppConstants.documentReceivedFromManufacturer)
        .length;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الجوابات',
            totalCount.toString(),
            Icons.description,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'مكتملة',
            completedCount.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            'قيد المعالجة',
            pendingCount.toString(),
            Icons.pending,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBreakdown() {
    if (_statusCounts.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('لا توجد بيانات لعرضها'),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفصيل الحالات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._statusCounts.entries.map((entry) => _buildStatusRow(entry.key, entry.value)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String status, int count) {
    final statusInfo = _getStatusInfo(status);
    final total = _statusCounts.values.fold(0, (sum, count) => sum + count);
    final percentage = total > 0 ? (count / total * 100).round() : 0;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: statusInfo['color'],
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(statusInfo['label']),
          ),
          Text(
            '$count ($percentage%)',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getStatusInfo(String status) {
    switch (status) {
      case AppConstants.documentSentToManager:
        return {'label': 'تم إرسال البيانات للمدير', 'color': Colors.orange};
      case AppConstants.documentSentToManufacturer:
        return {'label': 'تم إرسال الجواب للشركة المصنعة', 'color': Colors.blue};
      case AppConstants.documentReceivedFromManufacturer:
        return {'label': 'تم استلام الجواب من الشركة المصنعة', 'color': Colors.green};
      default:
        return {'label': status, 'color': Colors.grey};
    }
  }

  Widget _buildRecentDocuments() {
    final recentDocuments = _applyTimeFilter(_allDocuments)
        .take(5)
        .toList();

    if (recentDocuments.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('لا توجد جوابات حديثة'),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الجوابات الحديثة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/document-tracking');
                  },
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...recentDocuments.map((doc) => _buildRecentDocumentItem(doc)),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentDocumentItem(DocumentTrackingModel document) {
    final statusInfo = _getStatusInfo(document.currentStatus);
    final customerName = document.additionalData?['customerName'] ?? 'غير محدد';
    final itemDescription = document.additionalData?['itemDescription'] ?? 'غير محدد';

    return ListTile(
      leading: Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          color: statusInfo['color'],
          shape: BoxShape.circle,
        ),
      ),
      title: Text(customerName),
      subtitle: Text(itemDescription),
      trailing: Text(
        AppUtils.formatDate(document.createdAt),
        style: const TextStyle(fontSize: 12),
      ),
      onTap: () {
        // Navigate to document details
        Navigator.pushNamed(
          context,
          '/document-details',
          arguments: document.id,
        );
      },
    );
  }
}
