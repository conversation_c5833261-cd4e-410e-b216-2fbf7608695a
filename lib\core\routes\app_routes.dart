import 'package:flutter/material.dart';
import '../../screens/documents/document_tracking_screen.dart';

class AppRoutes {
  static const String documentTracking = '/document-tracking';
  static const String documentDetails = '/document-details';

  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case documentTracking:
        return MaterialPageRoute(
          builder: (_) => const DocumentTrackingScreen(),
        );
      
      case documentDetails:
        // Handle document details with optional ID parameter
        // final documentId = settings.arguments as String?; // Not used
        return MaterialPageRoute(
          builder: (_) => const DocumentTrackingScreen(), // For now, redirect to tracking screen
        );
      
      default:
        return null; // Let the app handle unknown routes
    }
  }
}
