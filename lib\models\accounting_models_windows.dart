/// نماذج النظام المحاسبي المبسطة لنظام Windows
/// بدون Firebase dependencies
library;

/// أنواع الحسابات المحاسبية
enum AccountType {
  asset,      // أصول
  liability,  // خصوم
  equity,     // حقوق ملكية
  revenue,    // إيرادات
  expense,    // مصروفات
}

/// فئات الحسابات
enum AccountCategory {
  currentAssets,        // أصول متداولة
  fixedAssets,         // أصول ثابتة
  currentLiabilities,  // خصوم متداولة
  longTermLiabilities, // خصوم طويلة الأجل
  capital,             // رأس المال
  retainedEarnings,    // أرباح محتجزة
  operatingRevenue,    // إيرادات تشغيلية
  otherRevenue,        // إيرادات أخرى
  operatingExpenses,   // مصروفات تشغيلية
  administrativeExpenses, // مصروفات إدارية
}

/// حالة القيد المحاسبي
enum JournalEntryStatus {
  draft,     // مسودة
  posted,    // مرحل
  approved,  // معتمد
  rejected,  // مرفوض
}

/// نموذج الحساب المحاسبي
class AccountModel {
  final String id;
  final String code;
  final String nameAr;
  final String nameEn;
  final AccountType type;
  final AccountCategory category;
  final bool isActive;
  final double currentBalance;
  final String currency;
  final DateTime createdAt;
  final String createdBy;
  final int version;

  const AccountModel({
    required this.id,
    required this.code,
    required this.nameAr,
    required this.nameEn,
    required this.type,
    required this.category,
    this.isActive = true,
    this.currentBalance = 0.0,
    this.currency = 'EGP',
    required this.createdAt,
    required this.createdBy,
    this.version = 1,
  });

  /// تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'code': code,
      'nameAr': nameAr,
      'nameEn': nameEn,
      'type': type.name,
      'category': category.name,
      'isActive': isActive,
      'currentBalance': currentBalance,
      'currency': currency,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'version': version,
    };
  }

  /// إنشاء من Map
  factory AccountModel.fromMap(Map<String, dynamic> map) {
    return AccountModel(
      id: map['id'] ?? '',
      code: map['code'] ?? '',
      nameAr: map['nameAr'] ?? '',
      nameEn: map['nameEn'] ?? '',
      type: AccountType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => AccountType.asset,
      ),
      category: AccountCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => AccountCategory.currentAssets,
      ),
      isActive: map['isActive'] ?? true,
      currentBalance: (map['currentBalance'] ?? 0.0).toDouble(),
      currency: map['currency'] ?? 'EGP',
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      createdBy: map['createdBy'] ?? '',
      version: map['version'] ?? 1,
    );
  }

  AccountModel copyWith({
    String? id,
    String? code,
    String? nameAr,
    String? nameEn,
    AccountType? type,
    AccountCategory? category,
    bool? isActive,
    double? currentBalance,
    String? currency,
    DateTime? createdAt,
    String? createdBy,
    int? version,
  }) {
    return AccountModel(
      id: id ?? this.id,
      code: code ?? this.code,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      type: type ?? this.type,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      currentBalance: currentBalance ?? this.currentBalance,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      version: version ?? this.version,
    );
  }
}

/// نموذج بند القيد المحاسبي
class JournalEntryLineModel {
  final String id;
  final String accountId;
  final String accountCode;
  final String accountName;
  final double debitAmount;
  final double creditAmount;
  final String description;
  final int lineNumber;

  const JournalEntryLineModel({
    required this.id,
    required this.accountId,
    required this.accountCode,
    required this.accountName,
    required this.debitAmount,
    required this.creditAmount,
    required this.description,
    required this.lineNumber,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'accountId': accountId,
      'accountCode': accountCode,
      'accountName': accountName,
      'debitAmount': debitAmount,
      'creditAmount': creditAmount,
      'description': description,
      'lineNumber': lineNumber,
    };
  }

  factory JournalEntryLineModel.fromMap(Map<String, dynamic> map) {
    return JournalEntryLineModel(
      id: map['id'] ?? '',
      accountId: map['accountId'] ?? '',
      accountCode: map['accountCode'] ?? '',
      accountName: map['accountName'] ?? '',
      debitAmount: (map['debitAmount'] ?? 0.0).toDouble(),
      creditAmount: (map['creditAmount'] ?? 0.0).toDouble(),
      description: map['description'] ?? '',
      lineNumber: map['lineNumber'] ?? 0,
    );
  }
}

/// نموذج القيد المحاسبي
class JournalEntryModel {
  final String id;
  final String entryNumber;
  final DateTime date;
  final String description;
  final String reference;
  final List<JournalEntryLineModel> lines;
  final double totalDebit;
  final double totalCredit;
  final JournalEntryStatus status;
  final String currency;
  final DateTime createdAt;
  final String createdBy;

  const JournalEntryModel({
    required this.id,
    required this.entryNumber,
    required this.date,
    required this.description,
    required this.reference,
    required this.lines,
    required this.totalDebit,
    required this.totalCredit,
    required this.status,
    this.currency = 'EGP',
    required this.createdAt,
    required this.createdBy,
  });

  bool get isBalanced => (totalDebit - totalCredit).abs() < 0.01;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'entryNumber': entryNumber,
      'date': date.toIso8601String(),
      'description': description,
      'reference': reference,
      'lines': lines.map((line) => line.toMap()).toList(),
      'totalDebit': totalDebit,
      'totalCredit': totalCredit,
      'status': status.name,
      'currency': currency,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
    };
  }

  factory JournalEntryModel.fromMap(Map<String, dynamic> map) {
    return JournalEntryModel(
      id: map['id'] ?? '',
      entryNumber: map['entryNumber'] ?? '',
      date: DateTime.parse(map['date'] ?? DateTime.now().toIso8601String()),
      description: map['description'] ?? '',
      reference: map['reference'] ?? '',
      lines: (map['lines'] as List<dynamic>?)
          ?.map((line) => JournalEntryLineModel.fromMap(line))
          .toList() ?? [],
      totalDebit: (map['totalDebit'] ?? 0.0).toDouble(),
      totalCredit: (map['totalCredit'] ?? 0.0).toDouble(),
      status: JournalEntryStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => JournalEntryStatus.draft,
      ),
      currency: map['currency'] ?? 'EGP',
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      createdBy: map['createdBy'] ?? '',
    );
  }
}

/// إحصائيات دفتر اليومية
class JournalStatistics {
  final int totalEntries;
  final int draftEntries;
  final int postedEntries;
  final int approvedEntries;
  final int rejectedEntries;
  final double totalDebits;
  final double totalCredits;
  final int unbalancedEntries;
  final DateTime lastEntryDate;
  final String lastEntryNumber;
  final Map<String, int> entriesByMonth;
  final DateTime generatedAt;

  const JournalStatistics({
    required this.totalEntries,
    required this.draftEntries,
    required this.postedEntries,
    required this.approvedEntries,
    required this.rejectedEntries,
    required this.totalDebits,
    required this.totalCredits,
    required this.unbalancedEntries,
    required this.lastEntryDate,
    required this.lastEntryNumber,
    required this.entriesByMonth,
    required this.generatedAt,
  });

  bool get isBalanced => (totalDebits - totalCredits).abs() < 0.01;
}
