# Al Farhan Light Transport - دليل التوزيع والنشر

## 📱 **نظرة عامة**

تطبيق Al Farhan Light Transport هو تطبيق Flutter شامل لإدارة أعمال النقل الخفيف في مصر، يتضمن إدارة المخزون، المبيعات، حسابات الوكلاء، وتتبع جوابات الموتسيكل.

## 🛠️ **متطلبات النظام**

### للتطوير:
- Flutter SDK 3.24.0 أو أحدث
- Dart SDK 3.5.0 أو أحدث
- Android Studio أو VS Code
- Git

### للإنتاج:
- Android 6.0 (API level 23) أو أحدث
- 2GB RAM كحد أدنى (4GB مُوصى به)
- 500MB مساحة تخزين فارغة
- اتصال بالإنترنت (للمزامنة مع Firebase)

## 🔧 **إعداد البيئة**

### 1. تحضير المشروع للإنتاج

```bash
# تنظيف المشروع
flutter clean

# تحديث الحزم
flutter pub get

# فحص المشروع
flutter analyze

# تشغيل الاختبارات
flutter test
```

### 2. إعداد Firebase للإنتاج

1. **إنشاء مشروع Firebase جديد للإنتاج:**
   - اذهب إلى [Firebase Console](https://console.firebase.google.com)
   - أنشئ مشروع جديد باسم "Al Farhan Transport Prod"
   - فعّل Authentication, Firestore, Storage, Cloud Messaging

2. **تحديث ملفات التكوين:**
   ```bash
   # استبدل ملفات Firebase
   # android/app/google-services.json
   # ios/Runner/GoogleService-Info.plist
   ```

3. **إعداد قواعد الأمان:**
   ```javascript
   // Firestore Security Rules
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       // Users collection
       match /users/{userId} {
         allow read, write: if request.auth != null && 
           (request.auth.uid == userId || 
            get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin']);
       }
       
       // Items collection
       match /items/{itemId} {
         allow read: if request.auth != null;
         allow write: if request.auth != null && 
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin'];
       }
       
       // Invoices collection
       match /invoices/{invoiceId} {
         allow read: if request.auth != null;
         allow create: if request.auth != null;
         allow update, delete: if request.auth != null && 
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin'];
       }
       
       // Document tracking collection
       match /document_tracking/{docId} {
         allow read: if request.auth != null;
         allow create: if request.auth != null;
         allow update: if request.auth != null && 
           get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role in ['admin', 'super_admin'];
       }
     }
   }
   ```

### 3. إعداد التوقيع الرقمي (Android)

1. **إنشاء مفتاح التوقيع:**
   ```bash
   keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
   ```

2. **إنشاء ملف key.properties:**
   ```properties
   storePassword=<كلمة مرور المتجر>
   keyPassword=<كلمة مرور المفتاح>
   keyAlias=upload
   storeFile=<مسار ملف المفتاح>
   ```

## 🚀 **عملية البناء والنشر**

### 1. بناء تطبيق Android للإنتاج

```bash
# بناء APK
flutter build apk --release

# بناء App Bundle (مُوصى به للـ Play Store)
flutter build appbundle --release

# بناء APK منفصل لكل معمارية
flutter build apk --split-per-abi --release
```

### 2. اختبار البناء

```bash
# تثبيت واختبار APK
flutter install --release

# فحص حجم التطبيق
flutter build apk --analyze-size
```

### 3. إعداد متجر Google Play

1. **إنشاء حساب مطور:**
   - اذهب إلى [Google Play Console](https://play.google.com/console)
   - أنشئ حساب مطور (رسوم لمرة واحدة 25$)

2. **إنشاء تطبيق جديد:**
   - اسم التطبيق: "Al Farhan Light Transport"
   - الفئة: Business
   - اللغة الافتراضية: العربية

3. **رفع التطبيق:**
   - ارفع ملف .aab
   - أكمل معلومات التطبيق
   - أضف لقطات الشاشة والوصف

## 📋 **قائمة فحص ما قبل النشر**

### الوظائف الأساسية:
- [ ] تسجيل الدخول يعمل بشكل صحيح
- [ ] إضافة وتعديل الأصناف
- [ ] إنشاء الفواتير
- [ ] تتبع الجوابات
- [ ] التقارير والإحصائيات
- [ ] النسخ الاحتياطي والاستعادة

### الأداء:
- [ ] التطبيق يبدأ في أقل من 3 ثوانٍ
- [ ] التنقل سلس بدون تأخير
- [ ] استهلاك الذاكرة معقول (<200MB)
- [ ] البطارية لا تستنزف بسرعة

### الأمان:
- [ ] البيانات الحساسة مشفرة
- [ ] قواعد Firebase محدثة
- [ ] لا توجد مفاتيح API مكشوفة
- [ ] التحقق من الصلاحيات يعمل

### التوافق:
- [ ] يعمل على Android 6.0+
- [ ] يدعم الشاشات المختلفة
- [ ] يعمل بدون إنترنت (وضع أوفلاين)
- [ ] النصوص العربية تظهر بشكل صحيح

## 🔄 **استراتيجية التحديث**

### التحديثات الصغيرة (Patch):
- إصلاح الأخطاء
- تحسينات الأداء البسيطة
- رقم الإصدار: 1.0.1, 1.0.2, إلخ

### التحديثات المتوسطة (Minor):
- ميزات جديدة صغيرة
- تحسينات واجهة المستخدم
- رقم الإصدار: 1.1.0, 1.2.0, إلخ

### التحديثات الكبيرة (Major):
- ميزات جديدة كبيرة
- تغييرات جذرية في التصميم
- رقم الإصدار: 2.0.0, 3.0.0, إلخ

## 📊 **مراقبة ما بعد النشر**

### المقاييس المهمة:
- عدد التحميلات
- معدل الاحتفاظ بالمستخدمين
- تقييمات المتجر
- تقارير الأخطاء
- استخدام الميزات

### أدوات المراقبة:
- Google Play Console Analytics
- Firebase Analytics
- Firebase Crashlytics
- تقارير المستخدمين

## 🆘 **الدعم والصيانة**

### معلومات الاتصال:
- المطور: Motasem Salem
- WhatsApp: 01062606098
- البريد الإلكتروني: [يُضاف لاحقاً]

### جدول الصيانة:
- فحص يومي للأخطاء
- تحديث أسبوعي للبيانات
- نسخ احتياطية يومية
- مراجعة شهرية للأداء

## 📝 **سجل التغييرات**

### الإصدار 1.0.0 (ديسمبر 2024)
- الإطلاق الأولي
- جميع الميزات الأساسية
- دعم اللغة العربية
- تكامل Firebase

### الإصدارات المستقبلية:
- 1.1.0: تحسينات الكاميرا الذكية
- 1.2.0: تقارير متقدمة
- 2.0.0: دعم iOS

## 🔐 **الأمان والخصوصية**

### حماية البيانات:
- تشفير البيانات المحلية
- اتصال آمن مع Firebase
- عدم تخزين كلمات المرور بوضوح
- نسخ احتياطية مشفرة

### الخصوصية:
- لا يتم جمع بيانات شخصية غير ضرورية
- البيانات تُستخدم فقط لأغراض التطبيق
- إمكانية حذف البيانات عند الطلب

## 📞 **الدعم الفني**

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

1. **WhatsApp:** 01062606098
2. **داخل التطبيق:** قسم "اتصل بنا"
3. **متجر التطبيقات:** تقييم وتعليق

---

**© 2024 Al Farhan Light Transport. جميع الحقوق محفوظة.**
