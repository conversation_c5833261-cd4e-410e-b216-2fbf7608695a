import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../core/utils/app_utils.dart';
import '../../services/auth_service.dart';

/// الشريط العلوي لتطبيق Windows
/// يحتوي على شعار التطبيق، أدوات التنقل، البحث، والإشعارات
class WindowsTopBar extends StatefulWidget {
  final VoidCallback onMenuToggle;
  final VoidCallback onNewTab;
  final VoidCallback onSearch;
  final String currentScreen;

  const WindowsTopBar({
    super.key,
    required this.onMenuToggle,
    required this.onNewTab,
    required this.onSearch,
    required this.currentScreen,
  });

  @override
  State<WindowsTopBar> createState() => _WindowsTopBarState();
}

class _WindowsTopBarState extends State<WindowsTopBar> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearchFocused = false;
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 64,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            // زر القائمة الجانبية
            IconButton(
              onPressed: widget.onMenuToggle,
              icon: const Icon(Icons.menu),
              tooltip: 'تبديل القائمة الجانبية',
            ),
            
            const SizedBox(width: 16),
            
            // شعار التطبيق
            _buildLogo(),
            
            const SizedBox(width: 32),
            
            // أدوات التنقل السريع
            _buildQuickActions(),
            
            const Spacer(),
            
            // شريط البحث
            _buildSearchBar(),
            
            const SizedBox(width: 16),
            
            // الإشعارات والمعلومات
            _buildNotificationsArea(),
            
            const SizedBox(width: 16),
            
            // معلومات المستخدم
            _buildUserArea(),
          ],
        ),
      ),
    );
  }

  /// بناء شعار التطبيق
  Widget _buildLogo() {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.business,
            color: Colors.white,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'El Farhan',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'إدارة المخازن والوكلاء',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء أدوات التنقل السريع
  Widget _buildQuickActions() {
    return Row(
      children: [
        _buildQuickActionButton(
          icon: Icons.add,
          label: 'جديد',
          onPressed: widget.onNewTab,
          tooltip: 'إنشاء عنصر جديد (Ctrl+N)',
        ),
        const SizedBox(width: 8),
        _buildQuickActionButton(
          icon: Icons.refresh,
          label: 'تحديث',
          onPressed: _handleRefresh,
          tooltip: 'تحديث البيانات (F5)',
        ),
        const SizedBox(width: 8),
        _buildQuickActionButton(
          icon: Icons.save,
          label: 'حفظ',
          onPressed: _handleSave,
          tooltip: 'حفظ التغييرات (Ctrl+S)',
        ),
      ],
    );
  }

  /// بناء زر عمل سريع
  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: TextButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 16),
        label: Text(label),
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
      ),
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar() {
    return Container(
      width: 300,
      height: 36,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(
          color: _isSearchFocused 
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).dividerColor,
          width: _isSearchFocused ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(18),
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في التطبيق...',
          prefixIcon: const Icon(Icons.search, size: 20),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    setState(() {});
                  },
                  icon: const Icon(Icons.clear, size: 16),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        onChanged: (value) {
          setState(() {});
        },
        onSubmitted: (value) {
          if (value.isNotEmpty) {
            _performSearch(value);
          }
        },
        onTap: () {
          setState(() {
            _isSearchFocused = true;
          });
        },
        onTapOutside: (event) {
          setState(() {
            _isSearchFocused = false;
          });
        },
      ),
    );
  }

  /// بناء منطقة الإشعارات
  Widget _buildNotificationsArea() {
    return Row(
      children: [
        // الإشعارات
        Stack(
          children: [
            IconButton(
              onPressed: _showNotifications,
              icon: const Icon(Icons.notifications_outlined),
              tooltip: 'الإشعارات',
            ),
            // نقطة الإشعارات الجديدة
            Positioned(
              right: 8,
              top: 8,
              child: Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(width: 8),
        
        // حالة الاتصال
        _buildConnectionStatus(),
        
        const SizedBox(width: 8),
        
        // الوقت والتاريخ
        _buildDateTime(),
      ],
    );
  }

  /// بناء حالة الاتصال
  Widget _buildConnectionStatus() {
    return Tooltip(
      message: 'متصل بالإنترنت',
      child: Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          color: Colors.green,
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }

  /// بناء الوقت والتاريخ
  Widget _buildDateTime() {
    final now = DateTime.now();
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          DateFormat('HH:mm').format(now),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          DateFormat('d/M/yyyy').format(now),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  /// بناء منطقة المستخدم
  Widget _buildUserArea() {
    return PopupMenuButton<String>(
      onSelected: _handleUserMenuAction,
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'profile',
          child: ListTile(
            leading: Icon(Icons.person),
            title: Text('الملف الشخصي'),
          ),
        ),
        const PopupMenuItem(
          value: 'settings',
          child: ListTile(
            leading: Icon(Icons.settings),
            title: Text('الإعدادات'),
          ),
        ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'logout',
          child: ListTile(
            leading: Icon(Icons.logout, color: Colors.red),
            title: Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
          ),
        ),
      ],
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: Theme.of(context).colorScheme.primary,
            child: const Icon(
              Icons.person,
              color: Colors.white,
              size: 16,
            ),
          ),
          const SizedBox(width: 8),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'المدير العام',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '<EMAIL>',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
          const SizedBox(width: 4),
          const Icon(Icons.arrow_drop_down, size: 16),
        ],
      ),
    );
  }

  // معالجات الأحداث
  void _handleRefresh() {
    AppUtils.showInfoSnackBar(context, 'تم تحديث البيانات');
  }

  void _handleSave() {
    AppUtils.showInfoSnackBar(context, 'تم حفظ التغييرات');
  }

  void _performSearch(String query) {
    AppUtils.showInfoSnackBar(context, 'البحث عن: $query');
    widget.onSearch();
  }

  void _showNotifications() {
    AppUtils.showInfoSnackBar(context, 'عرض الإشعارات');
  }

  void _handleUserMenuAction(String action) {
    switch (action) {
      case 'profile':
        AppUtils.showInfoSnackBar(context, 'الملف الشخصي');
        break;
      case 'settings':
        AppUtils.showInfoSnackBar(context, 'الإعدادات');
        break;
      case 'logout':
        _handleLogout();
        break;
    }
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              AuthService.instance.signOut();
              AppUtils.showSuccessSnackBar(context, 'تم تسجيل الخروج بنجاح');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
