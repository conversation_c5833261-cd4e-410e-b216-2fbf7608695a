import 'package:flutter/material.dart';
import '../../core/theme/desktop_theme.dart';

/// Professional desktop data table with advanced features
class DesktopDataTable<T> extends StatefulWidget {
  final List<DesktopDataColumn> columns;
  final List<T> data;
  final Widget Function(T item, int index) rowBuilder;
  final bool sortAscending;
  final int? sortColumnIndex;
  final ValueChanged<int>? onSort;
  final bool showCheckboxColumn;
  final List<T>? selectedItems;
  final ValueChanged<List<T>>? onSelectionChanged;
  final String? emptyMessage;
  final Widget? emptyWidget;
  final bool showPagination;
  final int itemsPerPage;
  final int currentPage;
  final ValueChanged<int>? onPageChanged;
  final bool showSearch;
  final String? searchQuery;
  final ValueChanged<String>? onSearchChanged;
  final List<DesktopTableAction>? actions;
  final double? height;
  final bool stickyHeader;

  const DesktopDataTable({
    super.key,
    required this.columns,
    required this.data,
    required this.rowBuilder,
    this.sortAscending = true,
    this.sortColumnIndex,
    this.onSort,
    this.showCheckboxColumn = false,
    this.selectedItems,
    this.onSelectionChanged,
    this.emptyMessage,
    this.emptyWidget,
    this.showPagination = false,
    this.itemsPerPage = 25,
    this.currentPage = 0,
    this.onPageChanged,
    this.showSearch = false,
    this.searchQuery,
    this.onSearchChanged,
    this.actions,
    this.height,
    this.stickyHeader = true,
  });

  @override
  State<DesktopDataTable<T>> createState() => _DesktopDataTableState<T>();
}

class _DesktopDataTableState<T> extends State<DesktopDataTable<T>> {
  late ScrollController _horizontalController;
  late ScrollController _verticalController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _horizontalController = ScrollController();
    _verticalController = ScrollController();
    _searchController.text = widget.searchQuery ?? '';
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      decoration: DesktopTheme.cardDecoration,
      child: Column(
        children: [
          // Header with search and actions
          if (widget.showSearch || (widget.actions != null && widget.actions!.isNotEmpty))
            _buildTableHeader(),
          
          // Table
          Expanded(
            child: _buildTable(),
          ),
          
          // Pagination
          if (widget.showPagination)
            _buildPagination(),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
      decoration: BoxDecoration(
        color: DesktopTheme.backgroundTertiary,
        border: Border(
          bottom: BorderSide(
            color: DesktopTheme.borderLight,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Search
          if (widget.showSearch) ...[
            Expanded(
              child: SizedBox(
                height: 40,
                child: TextField(
                  controller: _searchController,
                  decoration: DesktopTheme.getInputDecoration(
                    labelText: 'البحث...',
                    prefixIcon: const Icon(Icons.search, size: 20),
                  ).copyWith(
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: DesktopTheme.spacingMedium,
                      vertical: DesktopTheme.spacingSmall,
                    ),
                  ),
                  onChanged: widget.onSearchChanged,
                ),
              ),
            ),
            const SizedBox(width: DesktopTheme.spacingMedium),
          ],
          
          // Actions
          if (widget.actions != null)
            ...widget.actions!.map((action) => Padding(
              padding: const EdgeInsets.only(left: DesktopTheme.spacingSmall),
              child: _buildActionButton(action),
            )),
        ],
      ),
    );
  }

  Widget _buildActionButton(DesktopTableAction action) {
    return ElevatedButton.icon(
      onPressed: action.onPressed,
      icon: Icon(action.icon, size: 16),
      label: Text(action.label),
      style: action.isPrimary
          ? DesktopTheme.primaryButtonStyle
          : DesktopTheme.secondaryButtonStyle,
    );
  }

  Widget _buildTable() {
    if (widget.data.isEmpty) {
      return _buildEmptyState();
    }

    return Scrollbar(
      controller: _horizontalController,
      thumbVisibility: true,
      child: Scrollbar(
        controller: _verticalController,
        thumbVisibility: true,
        child: SingleChildScrollView(
          controller: _horizontalController,
          scrollDirection: Axis.horizontal,
          child: SingleChildScrollView(
            controller: _verticalController,
            child: DataTable(
              sortAscending: widget.sortAscending,
              sortColumnIndex: widget.sortColumnIndex,
              showCheckboxColumn: widget.showCheckboxColumn,
              headingRowColor: WidgetStateProperty.all(DesktopTheme.backgroundTertiary),
              headingTextStyle: DesktopTheme.titleSmall.copyWith(
                fontWeight: FontWeight.w600,
              ),
              dataRowColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return DesktopTheme.primaryBlue.withOpacity(0.1);
                }
                if (states.contains(WidgetState.hovered)) {
                  return DesktopTheme.backgroundTertiary;
                }
                return DesktopTheme.backgroundSecondary;
              }),
              dataTextStyle: DesktopTheme.bodyMedium,
              columns: widget.columns.map((column) => DataColumn(
                label: Text(column.label),
                onSort: column.sortable && widget.onSort != null
                    ? (columnIndex, ascending) => widget.onSort!(columnIndex)
                    : null,
                numeric: column.numeric,
                tooltip: column.tooltip,
              )).toList(),
              rows: _buildDataRows(),
            ),
          ),
        ),
      ),
    );
  }

  List<DataRow> _buildDataRows() {
    final startIndex = widget.showPagination
        ? widget.currentPage * widget.itemsPerPage
        : 0;
    final endIndex = widget.showPagination
        ? (startIndex + widget.itemsPerPage).clamp(0, widget.data.length)
        : widget.data.length;

    return widget.data
        .sublist(startIndex, endIndex)
        .asMap()
        .entries
        .map((entry) {
          final index = entry.key + startIndex;
          final item = entry.value;
          final isSelected = widget.selectedItems?.contains(item) ?? false;

          return DataRow(
            selected: isSelected,
            onSelectChanged: widget.showCheckboxColumn && widget.onSelectionChanged != null
                ? (selected) {
                    final newSelection = List<T>.from(widget.selectedItems ?? []);
                    if (selected == true) {
                      newSelection.add(item);
                    } else {
                      newSelection.remove(item);
                    }
                    widget.onSelectionChanged!(newSelection);
                  }
                : null,
            cells: _buildDataCells(item, index),
          );
        })
        .toList();
  }

  List<DataCell> _buildDataCells(T item, int index) {
    // This should be implemented by the specific table implementation
    // For now, return empty cells based on column count
    return List.generate(
      widget.columns.length,
      (columnIndex) => const DataCell(Text('')),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: widget.emptyWidget ??
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.inbox_outlined,
                size: 64,
                color: DesktopTheme.textTertiary,
              ),
              const SizedBox(height: DesktopTheme.spacingMedium),
              Text(
                widget.emptyMessage ?? 'لا توجد بيانات',
                style: DesktopTheme.titleMedium.copyWith(
                  color: DesktopTheme.textTertiary,
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildPagination() {
    final totalPages = (widget.data.length / widget.itemsPerPage).ceil();
    
    return Container(
      padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
      decoration: BoxDecoration(
        color: DesktopTheme.backgroundTertiary,
        border: Border(
          top: BorderSide(
            color: DesktopTheme.borderLight,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            'عرض ${widget.currentPage * widget.itemsPerPage + 1}-${((widget.currentPage + 1) * widget.itemsPerPage).clamp(0, widget.data.length)} من ${widget.data.length}',
            style: DesktopTheme.bodySmall,
          ),
          const Spacer(),
          IconButton(
            onPressed: widget.currentPage > 0
                ? () => widget.onPageChanged?.call(widget.currentPage - 1)
                : null,
            icon: const Icon(Icons.chevron_left),
          ),
          Text(
            '${widget.currentPage + 1} من $totalPages',
            style: DesktopTheme.bodyMedium,
          ),
          IconButton(
            onPressed: widget.currentPage < totalPages - 1
                ? () => widget.onPageChanged?.call(widget.currentPage + 1)
                : null,
            icon: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }
}

/// Represents a column in the desktop data table
class DesktopDataColumn {
  final String label;
  final bool sortable;
  final bool numeric;
  final String? tooltip;
  final double? width;

  const DesktopDataColumn({
    required this.label,
    this.sortable = false,
    this.numeric = false,
    this.tooltip,
    this.width,
  });
}

/// Represents an action button in the table header
class DesktopTableAction {
  final String label;
  final IconData icon;
  final VoidCallback? onPressed;
  final bool isPrimary;
  final String? tooltip;

  const DesktopTableAction({
    required this.label,
    required this.icon,
    this.onPressed,
    this.isPrimary = false,
    this.tooltip,
  });
}
