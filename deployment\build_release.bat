@echo off
echo ========================================
echo Al Farhan Transport - Release Build Script
echo ========================================
echo.

:: Set variables
set APP_NAME=Al Farhan Transport
set VERSION_FILE=pubspec.yaml
set BUILD_DIR=build\app\outputs

:: Check if Flutter is installed
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    pause
    exit /b 1
)

echo ✓ Flutter is installed
echo.

:: Clean previous builds
echo 🧹 Cleaning previous builds...
flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Failed to clean project
    pause
    exit /b 1
)

:: Get dependencies
echo 📦 Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to get dependencies
    pause
    exit /b 1
)

:: Run code analysis
echo 🔍 Running code analysis...
flutter analyze
if %errorlevel% neq 0 (
    echo WARNING: Code analysis found issues
    echo Continue anyway? (y/n)
    set /p continue=
    if /i not "%continue%"=="y" (
        echo Build cancelled
        pause
        exit /b 1
    )
)

:: Run tests
echo 🧪 Running tests...
flutter test
if %errorlevel% neq 0 (
    echo WARNING: Some tests failed
    echo Continue anyway? (y/n)
    set /p continue=
    if /i not "%continue%"=="y" (
        echo Build cancelled
        pause
        exit /b 1
    )
)

:: Get current version
echo 📋 Getting version information...
for /f "tokens=2 delims=: " %%a in ('findstr "version:" %VERSION_FILE%') do set VERSION=%%a
echo Current version: %VERSION%
echo.

:: Build APK
echo 🔨 Building release APK...
flutter build apk --release
if %errorlevel% neq 0 (
    echo ERROR: Failed to build APK
    pause
    exit /b 1
)

:: Build App Bundle
echo 📱 Building release App Bundle...
flutter build appbundle --release
if %errorlevel% neq 0 (
    echo ERROR: Failed to build App Bundle
    pause
    exit /b 1
)

:: Build split APKs
echo 📱 Building split APKs...
flutter build apk --split-per-abi --release
if %errorlevel% neq 0 (
    echo ERROR: Failed to build split APKs
    pause
    exit /b 1
)

:: Create release directory
set RELEASE_DIR=releases\v%VERSION%
if not exist releases mkdir releases
if not exist "%RELEASE_DIR%" mkdir "%RELEASE_DIR%"

:: Copy build artifacts
echo 📋 Copying build artifacts...
copy "%BUILD_DIR%\flutter-apk\app-release.apk" "%RELEASE_DIR%\al-farhan-transport-v%VERSION%.apk"
copy "%BUILD_DIR%\bundle\release\app-release.aab" "%RELEASE_DIR%\al-farhan-transport-v%VERSION%.aab"

:: Copy split APKs
if exist "%BUILD_DIR%\flutter-apk\app-arm64-v8a-release.apk" (
    copy "%BUILD_DIR%\flutter-apk\app-arm64-v8a-release.apk" "%RELEASE_DIR%\al-farhan-transport-v%VERSION%-arm64-v8a.apk"
)
if exist "%BUILD_DIR%\flutter-apk\app-armeabi-v7a-release.apk" (
    copy "%BUILD_DIR%\flutter-apk\app-armeabi-v7a-release.apk" "%RELEASE_DIR%\al-farhan-transport-v%VERSION%-armeabi-v7a.apk"
)
if exist "%BUILD_DIR%\flutter-apk\app-x86_64-release.apk" (
    copy "%BUILD_DIR%\flutter-apk\app-x86_64-release.apk" "%RELEASE_DIR%\al-farhan-transport-v%VERSION%-x86_64.apk"
)

:: Generate build info
echo 📄 Generating build information...
echo Al Farhan Transport - Build Information > "%RELEASE_DIR%\build-info.txt"
echo ============================================ >> "%RELEASE_DIR%\build-info.txt"
echo Version: %VERSION% >> "%RELEASE_DIR%\build-info.txt"
echo Build Date: %date% %time% >> "%RELEASE_DIR%\build-info.txt"
echo Flutter Version: >> "%RELEASE_DIR%\build-info.txt"
flutter --version >> "%RELEASE_DIR%\build-info.txt"
echo. >> "%RELEASE_DIR%\build-info.txt"
echo Build Files: >> "%RELEASE_DIR%\build-info.txt"
echo - al-farhan-transport-v%VERSION%.apk (Universal APK) >> "%RELEASE_DIR%\build-info.txt"
echo - al-farhan-transport-v%VERSION%.aab (App Bundle for Play Store) >> "%RELEASE_DIR%\build-info.txt"
echo - al-farhan-transport-v%VERSION%-arm64-v8a.apk (ARM64) >> "%RELEASE_DIR%\build-info.txt"
echo - al-farhan-transport-v%VERSION%-armeabi-v7a.apk (ARM32) >> "%RELEASE_DIR%\build-info.txt"
echo - al-farhan-transport-v%VERSION%-x86_64.apk (x86_64) >> "%RELEASE_DIR%\build-info.txt"

:: Show file sizes
echo 📊 Build artifacts created:
echo.
dir "%RELEASE_DIR%\*.apk" "%RELEASE_DIR%\*.aab" /s
echo.

:: Analyze APK size
echo 📈 Analyzing APK size...
flutter build apk --analyze-size --release > "%RELEASE_DIR%\size-analysis.txt"

echo ✅ Build completed successfully!
echo.
echo 📁 Release files are in: %RELEASE_DIR%
echo.
echo 📋 Next steps:
echo 1. Test the APK on different devices
echo 2. Upload the .aab file to Google Play Console
echo 3. Update the version number for next release
echo 4. Create release notes
echo.

:: Ask if user wants to open release directory
echo Open release directory? (y/n)
set /p open_dir=
if /i "%open_dir%"=="y" (
    explorer "%RELEASE_DIR%"
)

echo.
echo Press any key to exit...
pause >nul
