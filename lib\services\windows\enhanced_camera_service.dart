import 'dart:typed_data';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image/image.dart' as img;
import '../../core/utils/app_utils.dart';

/// خدمة كاميرا محسنة للويندوز
/// تدعم التقاط الصور من الكاميرا أو اختيار من المعرض
class EnhancedCameraService {
  static final EnhancedCameraService _instance = EnhancedCameraService._internal();
  factory EnhancedCameraService() => _instance;
  EnhancedCameraService._internal();

  /// التقاط صورة من الكاميرا أو اختيار من المعرض
  Future<CapturedImage?> captureImage({
    required String title,
    required String description,
    bool allowCamera = true,
    bool allowGallery = true,
  }) async {
    try {
      if (kDebugMode) {
        print('📸 Starting image capture: $title');
      }

      // في بيئة الويندوز، نستخدم file picker مع خيارات متقدمة
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true,
        dialogTitle: title,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        
        if (file.bytes == null) {
          throw 'فشل في قراءة بيانات الصورة';
        }

        // التحقق من صحة الصورة
        final validatedImage = await _validateAndProcessImage(file.bytes!);
        
        if (validatedImage == null) {
          throw 'الصورة غير صالحة أو تالفة';
        }

        final capturedImage = CapturedImage(
          bytes: validatedImage,
          name: file.name,
          size: validatedImage.length,
          path: file.path,
        );

        if (kDebugMode) {
          print('✅ Image captured successfully: ${capturedImage.name}');
          print('   Size: ${(capturedImage.size / 1024).toStringAsFixed(1)} KB');
        }

        return capturedImage;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error capturing image: $e');
      }
      rethrow;
    }
  }

  /// التقاط صورة بصمة الموتور مع معالجة خاصة
  Future<CapturedImage?> captureMotorFingerprintImage() async {
    return await captureImage(
      title: 'التقاط صورة بصمة الموتور',
      description: 'اختر صورة واضحة لبصمة الموتور تحتوي على جميع البيانات المطلوبة',
      allowCamera: true,
      allowGallery: true,
    );
  }

  /// التقاط صورة الشاسيه مع معالجة خاصة
  Future<CapturedImage?> captureChassisImage() async {
    return await captureImage(
      title: 'التقاط صورة الشاسيه',
      description: 'اختر صورة واضحة لرقم الشاسيه',
      allowCamera: true,
      allowGallery: true,
    );
  }

  /// التقاط صورة هوية العميل
  Future<CapturedImage?> captureCustomerIdImage(String side) async {
    return await captureImage(
      title: 'التقاط صورة هوية العميل - $side',
      description: 'اختر صورة واضحة لهوية العميل ($side)',
      allowCamera: true,
      allowGallery: true,
    );
  }

  /// التحقق من صحة الصورة ومعالجتها
  Future<Uint8List?> _validateAndProcessImage(Uint8List imageBytes) async {
    try {
      // فك تشفير الصورة للتحقق من صحتها
      final image = img.decodeImage(imageBytes);
      
      if (image == null) {
        if (kDebugMode) {
          print('❌ Failed to decode image');
        }
        return null;
      }

      // التحقق من أبعاد الصورة
      if (image.width < 100 || image.height < 100) {
        if (kDebugMode) {
          print('❌ Image too small: ${image.width}x${image.height}');
        }
        throw 'الصورة صغيرة جداً. يجب أن تكون أكبر من 100x100 بكسل';
      }

      // التحقق من حجم الملف
      if (imageBytes.length > 10 * 1024 * 1024) { // 10 MB
        if (kDebugMode) {
          print('⚠️ Image too large, compressing...');
        }
        return await _compressImage(image);
      }

      // تحسين جودة الصورة إذا لزم الأمر
      final processedImage = await _enhanceImageQuality(image);
      
      if (processedImage != null) {
        return Uint8List.fromList(img.encodeJpg(processedImage, quality: 85));
      }

      return imageBytes;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating image: $e');
      }
      rethrow;
    }
  }

  /// ضغط الصورة
  Future<Uint8List> _compressImage(img.Image image) async {
    try {
      // تقليل الأبعاد إذا كانت كبيرة جداً
      img.Image resizedImage = image;
      
      if (image.width > 1920 || image.height > 1920) {
        final ratio = 1920 / (image.width > image.height ? image.width : image.height);
        resizedImage = img.copyResize(
          image,
          width: (image.width * ratio).round(),
          height: (image.height * ratio).round(),
          interpolation: img.Interpolation.cubic,
        );
      }

      // ضغط بجودة متوسطة
      final compressedBytes = img.encodeJpg(resizedImage, quality: 70);
      
      if (kDebugMode) {
        print('✅ Image compressed: ${(compressedBytes.length / 1024).toStringAsFixed(1)} KB');
      }

      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error compressing image: $e');
      }
      rethrow;
    }
  }

  /// تحسين جودة الصورة
  Future<img.Image?> _enhanceImageQuality(img.Image image) async {
    try {
      // تطبيق فلاتر تحسين الصورة
      img.Image enhanced = image;

      // تحسين التباين
      enhanced = img.contrast(enhanced, contrast: 110);

      // تطبيق فلتر شحذ خفيف
      enhanced = img.convolution(enhanced, filter: [
        0, -1, 0,
        -1, 5, -1,
        0, -1, 0
      ]);

      return enhanced;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error enhancing image: $e');
      }
      return image; // إرجاع الصورة الأصلية في حالة الخطأ
    }
  }

  /// حفظ الصورة في مجلد مؤقت
  Future<String> saveImageToTemp(Uint8List imageBytes, String fileName) async {
    try {
      final tempDir = Directory.systemTemp;
      final file = File('${tempDir.path}/$fileName');
      
      await file.writeAsBytes(imageBytes);
      
      if (kDebugMode) {
        print('✅ Image saved to temp: ${file.path}');
      }

      return file.path;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving image to temp: $e');
      }
      rethrow;
    }
  }

  /// التحقق من دعم الكاميرا
  Future<bool> isCameraSupported() async {
    // في بيئة الويندوز، نعتبر أن الكاميرا مدعومة دائماً
    // لأننا نستخدم file picker كبديل
    return true;
  }

  /// الحصول على معلومات الصورة
  Future<ImageInfo?> getImageInfo(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      
      if (image == null) return null;

      return ImageInfo(
        width: image.width,
        height: image.height,
        size: imageBytes.length,
        format: 'JPEG',
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting image info: $e');
      }
      return null;
    }
  }
}

/// نموذج الصورة الملتقطة
class CapturedImage {
  final Uint8List bytes;
  final String name;
  final int size;
  final String? path;

  CapturedImage({
    required this.bytes,
    required this.name,
    required this.size,
    this.path,
  });

  /// الحصول على حجم الصورة بالكيلوبايت
  double get sizeInKB => size / 1024;

  /// الحصول على حجم الصورة بالميجابايت
  double get sizeInMB => sizeInKB / 1024;

  @override
  String toString() {
    return 'CapturedImage(name: $name, size: ${sizeInKB.toStringAsFixed(1)} KB)';
  }
}

/// معلومات الصورة
class ImageInfo {
  final int width;
  final int height;
  final int size;
  final String format;

  ImageInfo({
    required this.width,
    required this.height,
    required this.size,
    required this.format,
  });

  @override
  String toString() {
    return 'ImageInfo(${width}x$height, ${(size / 1024).toStringAsFixed(1)} KB, $format)';
  }
}
