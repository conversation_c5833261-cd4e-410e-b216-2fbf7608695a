import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/constants/app_constants.dart';
import '../../models/agent_account_model.dart';
import '../../models/user_model.dart';
import '../../services/data_service.dart';
import '../../core/utils/app_utils.dart';

class RecordPaymentScreen extends StatefulWidget {
  final UserModel agent;

  const RecordPaymentScreen({
    super.key,
    required this.agent,
  });

  @override
  State<RecordPaymentScreen> createState() => _RecordPaymentScreenState();
}

class _RecordPaymentScreenState extends State<RecordPaymentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  
  bool _isLoading = false;
  String _paymentMethod = 'cash';

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _recordPayment() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      // Get current user from DataService
      const currentUserId = 'system'; // For now, use system as the creator

      if (currentUserId.isEmpty) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      final amount = double.parse(_amountController.text.trim());

      // Create payment transaction
      final transaction = AgentTransaction(
        id: AppUtils.generateId(),
        type: 'payment',
        amount: amount,
        description: 'دفعة نقدية - ${_notesController.text.trim().isNotEmpty ? _notesController.text.trim() : 'دفعة من الوكيل'}',
        timestamp: DateTime.now(),
        createdBy: currentUserId,
        metadata: {
          'paymentMethod': _paymentMethod,
          'notes': _notesController.text.trim(),
        },
      );

      // Add transaction to agent account
      await dataService.addAgentTransaction(widget.agent.id, transaction);

      if (kDebugMode) {
        print('Payment recorded for agent ${widget.agent.fullName}: $amount EGP');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تسجيل الدفعة بنجاح: ${amount.toStringAsFixed(2)} جنيه'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error recording payment: $e');
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تسجيل الدفعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تسجيل دفعة - ${widget.agent.fullName}'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Agent info card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معلومات الوكيل',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('الاسم: ${widget.agent.fullName}'),
                      Text('اسم المستخدم: ${widget.agent.username}'),
                      if (widget.agent.phone.isNotEmpty)
                        Text('الهاتف: ${widget.agent.phone}'),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Payment amount
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'مبلغ الدفعة *',
                  hintText: 'أدخل مبلغ الدفعة بالجنيه',
                  prefixIcon: Icon(Icons.attach_money),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال مبلغ الدفعة';
                  }
                  
                  final amount = double.tryParse(value.trim());
                  if (amount == null || amount <= 0) {
                    return 'يرجى إدخال مبلغ صحيح أكبر من صفر';
                  }
                  
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Payment method
              DropdownButtonFormField<String>(
                value: _paymentMethod,
                decoration: const InputDecoration(
                  labelText: 'طريقة الدفع',
                  prefixIcon: Icon(Icons.payment),
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'cash', child: Text('نقدي')),
                  DropdownMenuItem(value: 'bank_transfer', child: Text('تحويل بنكي')),
                  DropdownMenuItem(value: 'check', child: Text('شيك')),
                  DropdownMenuItem(value: 'other', child: Text('أخرى')),
                ],
                onChanged: (value) {
                  setState(() {
                    _paymentMethod = value!;
                  });
                },
              ),
              
              const SizedBox(height: 16),
              
              // Notes
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات (اختياري)',
                  hintText: 'أدخل أي ملاحظات إضافية',
                  prefixIcon: Icon(Icons.note),
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              
              const SizedBox(height: 24),
              
              // Record payment button
              ElevatedButton(
                onPressed: _isLoading ? null : _recordPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'تسجيل الدفعة',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
