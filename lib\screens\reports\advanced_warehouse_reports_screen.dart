import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/warehouse_model.dart';
import '../../models/item_model.dart';
import '../../models/invoice_model.dart';
import '../../models/inventory_movement_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';

class AdvancedWarehouseReportsScreen extends StatefulWidget {
  const AdvancedWarehouseReportsScreen({super.key});

  @override
  State<AdvancedWarehouseReportsScreen> createState() => _AdvancedWarehouseReportsScreenState();
}

class _AdvancedWarehouseReportsScreenState extends State<AdvancedWarehouseReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late DataService _dataService;
  
  // Data
  List<WarehouseModel> _warehouses = [];
  List<ItemModel> _items = [];
  List<InvoiceModel> _invoices = [];
  List<InventoryMovementModel> _movements = [];
  
  // Filters
  String? _selectedWarehouseId;
  DateTime? _startDate;
  DateTime? _endDate;
  String _selectedMovementType = 'all';
  String _selectedReportType = 'summary';
  
  // Loading states
  bool _isLoading = false;
  bool _isExporting = false;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _dataService = DataService.instance;
    _initializeDates();
    _loadData();
  }

  void _initializeDates() {
    _endDate = DateTime.now();
    _startDate = DateTime(_endDate!.year, _endDate!.month, 1); // First day of current month
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('🔄 Loading warehouse reports data...');
      }

      // Load warehouses
      _warehouses = await _dataService.getWarehouses();
      if (kDebugMode) {
        print('📦 Loaded ${_warehouses.length} warehouses');
      }

      // Load items
      _items = await _dataService.getItems();
      if (kDebugMode) {
        print('📱 Loaded ${_items.length} items');
      }

      // Load invoices for the selected period
      _invoices = await _dataService.getInvoicesInDateRange(_startDate!, _endDate!);
      if (kDebugMode) {
        print('🧾 Loaded ${_invoices.length} invoices for period ${_startDate!.day}/${_startDate!.month} - ${_endDate!.day}/${_endDate!.month}');
      }

      // Create synthetic movements from invoices and transfers since we don't have real inventory movements
      _movements = await _createMovementsFromInvoices();
      if (kDebugMode) {
        print('📊 Created ${_movements.length} movement records from invoices');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading data: $e');
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        AppUtils.showSnackBar(context, 'خطأ في تحميل البيانات: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'تقارير حركة المخازن المتطورة',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF1565C0),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: _isExporting ? null : _exportToPDF,
            tooltip: 'تصدير PDF',
          ),
          const SizedBox(width: 16),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.dashboard),
              text: 'نظرة عامة',
            ),
            Tab(
              icon: Icon(Icons.swap_horiz),
              text: 'حركات المخازن',
            ),
            Tab(
              icon: Icon(Icons.inventory),
              text: 'حركة المنتجات',
            ),
            Tab(
              icon: Icon(Icons.receipt_long),
              text: 'فواتير التحويل',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildFiltersSection(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildOverviewTab(),
                      _buildWarehouseMovementsTab(),
                      _buildProductMovementsTab(),
                      _buildTransferInvoicesTab(),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 2,
                child: _buildWarehouseDropdown(),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDatePicker('من تاريخ', _startDate, (date) {
                  setState(() {
                    _startDate = date;
                  });
                  _loadData();
                }),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDatePicker('إلى تاريخ', _endDate, (date) {
                  setState(() {
                    _endDate = date;
                  });
                  _loadData();
                }),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMovementTypeDropdown(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedWarehouseId,
          hint: const Text('اختر المخزن'),
          isExpanded: true,
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('جميع المخازن'),
            ),
            ..._warehouses.map((warehouse) => DropdownMenuItem<String>(
              value: warehouse.id,
              child: Text('${warehouse.name} (${_getWarehouseTypeText(warehouse.type)})'),
            )),
          ],
          onChanged: (value) {
            setState(() {
              _selectedWarehouseId = value;
            });
            _loadData();
          },
        ),
      ),
    );
  }

  Widget _buildMovementTypeDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedMovementType,
          isExpanded: true,
          items: const [
            DropdownMenuItem(value: 'all', child: Text('جميع الحركات')),
            DropdownMenuItem(value: 'in', child: Text('دخول')),
            DropdownMenuItem(value: 'out', child: Text('خروج')),
            DropdownMenuItem(value: 'transfer_in', child: Text('تحويل وارد')),
            DropdownMenuItem(value: 'transfer_out', child: Text('تحويل صادر')),
            DropdownMenuItem(value: 'sale', child: Text('بيع')),
          ],
          onChanged: (value) {
            setState(() {
              _selectedMovementType = value!;
            });
            _loadData();
          },
        ),
      ),
    );
  }

  Widget _buildDatePicker(String label, DateTime? date, Function(DateTime?) onChanged) {
    return InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          initialDate: date ?? DateTime.now(),
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
        );
        if (picked != null) {
          onChanged(picked);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            const Icon(Icons.calendar_today, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                date != null
                    ? '$label: ${date.day}/${date.month}/${date.year}'
                    : label,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getWarehouseTypeText(String type) {
    switch (type) {
      case 'main':
        return 'رئيسي';
      case 'showroom':
        return 'معرض';
      case 'agent':
        return 'وكيل';
      default:
        return type;
    }
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCards(),
          const SizedBox(height: 24),
          _buildWarehouseStatsGrid(),
          const SizedBox(height: 24),
          _buildRecentActivitiesCard(),
        ],
      ),
    );
  }

  Widget _buildWarehouseMovementsTab() {
    final filteredMovements = _getFilteredMovements();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'حركات المخازن (${filteredMovements.length})',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(0xFF1565C0),
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingRowColor: WidgetStateProperty.all(Colors.grey[50]),
                columns: const [
                  DataColumn(label: Text('التاريخ', style: TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text('الصنف', style: TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text('من مخزن', style: TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text('إلى مخزن', style: TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text('نوع الحركة', style: TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text('الكمية', style: TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text('القيمة', style: TextStyle(fontWeight: FontWeight.bold))),
                  DataColumn(label: Text('السبب', style: TextStyle(fontWeight: FontWeight.bold))),
                ],
                rows: filteredMovements.map((movement) {
                  return DataRow(
                    cells: [
                      DataCell(Text(AppUtils.formatDate(movement.timestamp))),
                      DataCell(
                        SizedBox(
                          width: 150,
                          child: Text(
                            '${movement.brand} ${movement.model}',
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      DataCell(Text(movement.sourceWarehouseName)),
                      DataCell(Text(movement.targetWarehouseName ?? '-')),
                      DataCell(_buildMovementTypeChip(movement.movementType)),
                      DataCell(Text(movement.quantity.toString())),
                      DataCell(Text(movement.totalCost != null ? '${movement.totalCost!.toStringAsFixed(0)} ج.م' : '-')),
                      DataCell(
                        SizedBox(
                          width: 120,
                          child: Text(
                            movement.reason,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductMovementsTab() {
    final productMovements = _getProductMovements();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'حركة المنتجات بالتفصيل',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(0xFF1565C0),
            ),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
            ),
            itemCount: productMovements.length,
            itemBuilder: (context, index) {
              final product = productMovements[index];
              return _buildProductMovementCard(product);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTransferInvoicesTab() {
    final transferInvoices = _getTransferInvoices();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'فواتير التحويل للوكلاء (${transferInvoices.length})',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(0xFF1565C0),
            ),
          ),
          const SizedBox(height: 16),
          if (transferInvoices.isEmpty)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: Text('لا توجد فواتير تحويل في الفترة المحددة'),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: transferInvoices.length,
              itemBuilder: (context, index) {
                final invoice = transferInvoices[index];
                return _buildTransferInvoiceCard(invoice);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final filteredMovements = _getFilteredMovements();
    final inCount = filteredMovements.where((m) => m.movementType == 'in' || m.movementType == 'transfer_in').length;
    final outCount = filteredMovements.where((m) => m.movementType == 'out' || m.movementType == 'transfer_out' || m.movementType == 'sale').length;
    final transferCount = filteredMovements.where((m) => m.movementType.contains('transfer')).length;
    final totalValue = filteredMovements.fold<double>(0, (sum, m) => sum + (m.totalCost ?? 0));

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الحركات',
            filteredMovements.length.toString(),
            const Color(0xFF1565C0),
            Icons.assessment,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'حركات الدخول',
            inCount.toString(),
            Colors.green,
            Icons.arrow_downward,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'حركات الخروج',
            outCount.toString(),
            Colors.red,
            Icons.arrow_upward,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'حركات التحويل',
            transferCount.toString(),
            Colors.blue,
            Icons.swap_horiz,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'إجمالي القيمة',
            '${totalValue.toStringAsFixed(0)} ج.م',
            Colors.orange,
            Icons.attach_money,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const Spacer(),
              Icon(Icons.trending_up, color: Colors.grey[400], size: 20),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseStatsGrid() {
    final warehouseStats = _calculateWarehouseStats();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات المخازن',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF1565C0),
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
          ),
          itemCount: warehouseStats.length,
          itemBuilder: (context, index) {
            final stat = warehouseStats[index];
            return _buildWarehouseStatCard(stat);
          },
        ),
      ],
    );
  }

  Widget _buildWarehouseStatCard(Map<String, dynamic> stat) {
    final warehouse = stat['warehouse'] as WarehouseModel;
    final movements = stat['movements'] as List<InventoryMovementModel>;
    final itemCount = stat['itemCount'] as int;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: _getWarehouseColor(warehouse.type).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  _getWarehouseIcon(warehouse.type),
                  color: _getWarehouseColor(warehouse.type),
                  size: 20,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  warehouse.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'الأصناف: $itemCount',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'الحركات: ${movements.length}',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 4),
            decoration: BoxDecoration(
              color: _getWarehouseColor(warehouse.type).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              _getWarehouseTypeText(warehouse.type),
              textAlign: TextAlign.center,
              style: TextStyle(
                color: _getWarehouseColor(warehouse.type),
                fontSize: 11,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivitiesCard() {
    final recentMovements = _movements
        .where((m) => _selectedWarehouseId == null ||
                     m.sourceWarehouseId == _selectedWarehouseId ||
                     m.targetWarehouseId == _selectedWarehouseId)
        .take(10)
        .toList();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الأنشطة الحديثة',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(0xFF1565C0),
            ),
          ),
          const SizedBox(height: 16),
          if (recentMovements.isEmpty)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: Text('لا توجد أنشطة حديثة'),
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: recentMovements.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final movement = recentMovements[index];
                return _buildActivityItem(movement);
              },
            ),
        ],
      ),
    );
  }

  // Helper methods
  List<InventoryMovementModel> _getFilteredMovements() {
    return _movements.where((movement) {
      // Filter by movement type
      if (_selectedMovementType != 'all' && movement.movementType != _selectedMovementType) {
        return false;
      }

      // Filter by warehouse
      if (_selectedWarehouseId != null) {
        return movement.sourceWarehouseId == _selectedWarehouseId ||
               movement.targetWarehouseId == _selectedWarehouseId;
      }

      return true;
    }).toList();
  }

  List<Map<String, dynamic>> _calculateWarehouseStats() {
    final stats = _warehouses.map((warehouse) {
      final warehouseMovements = _movements.where((m) =>
          m.sourceWarehouseId == warehouse.id || m.targetWarehouseId == warehouse.id).toList();
      final warehouseItems = _items.where((item) => item.currentWarehouseId == warehouse.id).length;

      return {
        'warehouse': warehouse,
        'movements': warehouseMovements,
        'itemCount': warehouseItems,
      };
    }).toList();

    if (kDebugMode) {
      print('📊 Warehouse stats calculated:');
      for (final stat in stats) {
        final warehouse = stat['warehouse'] as WarehouseModel;
        final movements = stat['movements'] as List;
        final itemCount = stat['itemCount'] as int;
        print('   ${warehouse.name}: ${movements.length} حركات, $itemCount أصناف');
      }
    }

    return stats;
  }

  List<Map<String, dynamic>> _getProductMovements() {
    final Map<String, List<InventoryMovementModel>> productGroups = {};

    for (final movement in _getFilteredMovements()) {
      final key = '${movement.brand}_${movement.model}';
      productGroups[key] ??= [];
      productGroups[key]!.add(movement);
    }

    final result = productGroups.entries.map((entry) {
      final movements = entry.value;
      final firstMovement = movements.first;

      return {
        'brand': firstMovement.brand,
        'model': firstMovement.model,
        'movements': movements,
        'totalQuantity': movements.fold<int>(0, (sum, m) => sum + m.quantity),
        'totalValue': movements.fold<double>(0, (sum, m) => sum + (m.totalCost ?? 0)),
      };
    }).toList();

    // Sort by total value descending
    result.sort((a, b) => (b['totalValue'] as double).compareTo(a['totalValue'] as double));

    if (kDebugMode) {
      print('📊 Product movements: ${result.length} products found');
      for (final product in result.take(3)) {
        print('   ${product['brand']} ${product['model']}: ${product['totalQuantity']} حركات, ${product['totalValue']} ج.م');
      }
    }

    return result;
  }

  List<InvoiceModel> _getTransferInvoices() {
    final transferInvoices = _invoices.where((invoice) {
      // Filter invoices that represent transfers to agents
      return invoice.agentId != null && invoice.agentId!.isNotEmpty;
    }).toList();

    if (kDebugMode) {
      print('📋 Transfer invoices: ${transferInvoices.length} found');
      for (final invoice in transferInvoices.take(3)) {
        print('   Invoice ${invoice.invoiceNumber}: Agent ${invoice.agentId}, Amount ${invoice.sellingPrice}');
      }
    }

    return transferInvoices;
  }

  Widget _buildMovementTypeChip(String type) {
    Color color;
    String text;
    IconData icon;

    switch (type) {
      case 'in':
        color = Colors.green;
        text = 'دخول';
        icon = Icons.arrow_downward;
        break;
      case 'out':
        color = Colors.red;
        text = 'خروج';
        icon = Icons.arrow_upward;
        break;
      case 'transfer_in':
        color = Colors.blue;
        text = 'تحويل وارد';
        icon = Icons.call_received;
        break;
      case 'transfer_out':
        color = Colors.orange;
        text = 'تحويل صادر';
        icon = Icons.call_made;
        break;
      case 'sale':
        color = Colors.purple;
        text = 'بيع';
        icon = Icons.point_of_sale;
        break;
      default:
        color = Colors.grey;
        text = type;
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(InventoryMovementModel movement) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getMovementColor(movement.movementType).withValues(alpha: 0.1),
        child: Icon(
          _getMovementIcon(movement.movementType),
          color: _getMovementColor(movement.movementType),
          size: 20,
        ),
      ),
      title: Text(
        '${movement.brand} ${movement.model}',
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        '${movement.sourceWarehouseName} → ${movement.targetWarehouseName ?? "خارج النظام"}',
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            AppUtils.formatDate(movement.timestamp),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 2),
          _buildMovementTypeChip(movement.movementType),
        ],
      ),
    );
  }

  Widget _buildProductMovementCard(Map<String, dynamic> product) {
    final movements = product['movements'] as List<InventoryMovementModel>;
    final totalQuantity = product['totalQuantity'] as int;
    final totalValue = product['totalValue'] as double;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${product['brand']} ${product['model']}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildProductStat('الحركات', movements.length.toString(), Icons.swap_horiz),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildProductStat('الكمية', totalQuantity.toString(), Icons.inventory),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildProductStat('القيمة الإجمالية', '${totalValue.toStringAsFixed(0)} ج.م', Icons.attach_money),
        ],
      ),
    );
  }

  Widget _buildProductStat(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, size: 20, color: const Color(0xFF1565C0)),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransferInvoiceCard(InvoiceModel invoice) {
    final item = _getItemById(invoice.itemId);
    final warehouse = _getWarehouseById(invoice.warehouseId);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.receipt_long, color: Colors.blue, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'فاتورة رقم: ${invoice.invoiceNumber}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      'التاريخ: ${AppUtils.formatDate(invoice.createdAt)}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${invoice.sellingPrice.toStringAsFixed(0)} ج.م',
                  style: const TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Divider(),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'العميل',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      invoice.customerName ?? 'غير محدد',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المخزن',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      warehouse?.name ?? 'غير محدد',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الصنف',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      item != null ? '${item.brand} ${item.model}' : 'غير محدد',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ربح الوكيل',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '${invoice.agentProfitShare.toStringAsFixed(0)} ج.م',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper methods for colors and icons
  Color _getWarehouseColor(String type) {
    switch (type) {
      case 'main':
        return const Color(0xFF1565C0);
      case 'showroom':
        return Colors.orange;
      case 'agent':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getWarehouseIcon(String type) {
    switch (type) {
      case 'main':
        return Icons.business;
      case 'showroom':
        return Icons.storefront;
      case 'agent':
        return Icons.person;
      default:
        return Icons.warehouse;
    }
  }

  Color _getMovementColor(String type) {
    switch (type) {
      case 'in':
        return Colors.green;
      case 'out':
        return Colors.red;
      case 'transfer_in':
        return Colors.blue;
      case 'transfer_out':
        return Colors.orange;
      case 'sale':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getMovementIcon(String type) {
    switch (type) {
      case 'in':
        return Icons.arrow_downward;
      case 'out':
        return Icons.arrow_upward;
      case 'transfer_in':
        return Icons.call_received;
      case 'transfer_out':
        return Icons.call_made;
      case 'sale':
        return Icons.point_of_sale;
      default:
        return Icons.help;
    }
  }

  // Create synthetic movements from invoices since we don't have real inventory movements
  Future<List<InventoryMovementModel>> _createMovementsFromInvoices() async {
    List<InventoryMovementModel> movements = [];

    for (final invoice in _invoices) {
      try {
        // Get item details
        final item = _getItemById(invoice.itemId);
        if (item == null) continue;

        // Get warehouse details
        final warehouse = _getWarehouseById(invoice.warehouseId);
        if (warehouse == null) continue;

        // Create movement for sale
        final movement = InventoryMovementModel(
          id: 'sale_${invoice.id}',
          itemId: invoice.itemId,
          itemName: '${item.brand} ${item.model}',
          brand: item.brand,
          model: item.model,
          movementType: 'sale',
          quantity: 1, // Assuming 1 item per invoice
          sourceWarehouseId: invoice.warehouseId,
          targetWarehouseId: null,
          sourceWarehouseName: warehouse.name,
          targetWarehouseName: 'عميل',
          reason: 'بيع للعميل ${invoice.customerName ?? "غير محدد"}',
          referenceId: invoice.id,
          unitCost: invoice.sellingPrice,
          totalCost: invoice.sellingPrice,
          timestamp: invoice.createdAt,
          createdBy: invoice.createdBy ?? 'system',
        );

        movements.add(movement);

        // If it's an agent sale, create transfer movement from main warehouse
        if (invoice.agentId != null && invoice.agentId!.isNotEmpty) {
          final mainWarehouse = _warehouses.firstWhere(
            (w) => w.type == 'main',
            orElse: () => warehouse,
          );

          final transferMovement = InventoryMovementModel(
            id: 'transfer_${invoice.id}',
            itemId: invoice.itemId,
            itemName: '${item.brand} ${item.model}',
            brand: item.brand,
            model: item.model,
            movementType: 'transfer_out',
            quantity: 1,
            sourceWarehouseId: mainWarehouse.id,
            targetWarehouseId: invoice.warehouseId,
            sourceWarehouseName: mainWarehouse.name,
            targetWarehouseName: warehouse.name,
            reason: 'تحويل للوكيل',
            referenceId: invoice.id,
            unitCost: item.purchasePrice,
            totalCost: item.purchasePrice,
            timestamp: invoice.createdAt.subtract(const Duration(hours: 1)), // Before sale
            createdBy: invoice.createdBy ?? 'system',
          );

          movements.add(transferMovement);
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error creating movement for invoice ${invoice.id}: $e');
        }
      }
    }

    // Sort by timestamp
    movements.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return movements;
  }

  // Helper methods to get related data
  dynamic _getAgentById(String? agentId) {
    if (agentId == null) return null;
    // This would typically come from a users/agents list
    return null; // Placeholder
  }

  ItemModel? _getItemById(String itemId) {
    try {
      return _items.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  WarehouseModel? _getWarehouseById(String warehouseId) {
    try {
      return _warehouses.firstWhere((warehouse) => warehouse.id == warehouseId);
    } catch (e) {
      return null;
    }
  }

  Future<void> _exportToPDF() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // TODO: Implement PDF export
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تصدير التقرير بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تصدير التقرير: $e', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }
}
