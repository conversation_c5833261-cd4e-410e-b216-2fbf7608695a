import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/desktop_theme.dart';
import '../../widgets/desktop/desktop_dashboard_cards.dart';
import '../../services/data_service.dart';
import '../reports/warehouse_movement_reports_screen.dart';
import '../reports/detailed_inventory_report.dart';
import '../reports/detailed_agent_statement_screen.dart';

class DesktopReportsScreen extends StatefulWidget {
  const DesktopReportsScreen({super.key});

  @override
  State<DesktopReportsScreen> createState() => _DesktopReportsScreenState();
}

class _DesktopReportsScreenState extends State<DesktopReportsScreen> {
  bool _isLoading = true;
  Map<String, dynamic> _reportStats = {};

  @override
  void initState() {
    super.initState();
    _loadReportStats();
  }

  Future<void> _loadReportStats() async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      final items = await dataService.getItems();
      final invoices = await dataService.getInvoices();
      final warehouses = await dataService.getWarehouses();
      final users = await dataService.getUsers();

      setState(() {
        _reportStats = {
          'totalItems': items.length,
          'totalInvoices': invoices.length,
          'totalWarehouses': warehouses.length,
          'totalAgents': users.where((u) => u.role == 'agent').length,
          'monthlyRevenue': invoices.where((i) {
            final now = DateTime.now();
            return i.createdAt.year == now.year && i.createdAt.month == now.month;
          }).fold<double>(0, (sum, invoice) => sum + invoice.sellingPrice),
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          _buildHeaderSection(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // Report Categories
          _buildReportCategories(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // Quick Stats
          _buildQuickStats(),

          const SizedBox(height: DesktopTheme.spacingXLarge),

          // Recent Reports
          _buildRecentReports(),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(DesktopTheme.spacingXLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DesktopTheme.accentOrange,
            DesktopTheme.accentOrange.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(DesktopTheme.radiusXLarge),
        boxShadow: DesktopTheme.elevatedShadow,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مركز التقارير والإحصائيات',
                  style: DesktopTheme.headingLarge.copyWith(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: DesktopTheme.spacingSmall),
                Text(
                  'تقارير شاملة ومفصلة لجميع عمليات النظام',
                  style: DesktopTheme.titleMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: DesktopTheme.spacingMedium),
                Row(
                  children: [
                    _buildQuickStat('التقارير المتاحة', '12', Icons.assessment),
                    const SizedBox(width: DesktopTheme.spacingXLarge),
                    _buildQuickStat('آخر تحديث', 'الآن', Icons.update),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(DesktopTheme.spacingLarge),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(DesktopTheme.radiusLarge),
            ),
            child: const Icon(
              Icons.analytics_outlined,
              size: 64,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 20),
        ),
        const SizedBox(width: DesktopTheme.spacingSmall),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: DesktopTheme.titleLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: DesktopTheme.bodySmall.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildReportCategories() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'فئات التقارير',
          style: DesktopTheme.headingMedium,
        ),
        const SizedBox(height: DesktopTheme.spacingLarge),
        DesktopDashboardGrid(
          crossAxisCount: 3,
          childAspectRatio: 1.2,
          children: [
            DesktopQuickActionCard(
              title: 'تقارير المخزون',
              description: 'تقارير شاملة عن حالة المخزون والحركات',
              icon: Icons.inventory_2_outlined,
              iconColor: DesktopTheme.primaryBlue,
              onTap: () => _showInventoryReports(),
            ),
            DesktopQuickActionCard(
              title: 'تقارير المبيعات',
              description: 'تقارير مفصلة عن المبيعات والإيرادات',
              icon: Icons.trending_up_outlined,
              iconColor: DesktopTheme.accentGreen,
              onTap: () => _showSalesReports(),
            ),
            DesktopQuickActionCard(
              title: 'تقارير الوكلاء',
              description: 'كشوف حسابات وتقارير الوكلاء',
              icon: Icons.people_outlined,
              iconColor: DesktopTheme.accentPurple,
              onTap: () => _showAgentReports(),
            ),
            DesktopQuickActionCard(
              title: 'تقارير المالية',
              description: 'التقارير المالية والمحاسبية',
              icon: Icons.account_balance_outlined,
              iconColor: DesktopTheme.accentOrange,
              onTap: () => _showFinancialReports(),
            ),
            DesktopQuickActionCard(
              title: 'تقارير العمليات',
              description: 'تقارير العمليات اليومية والأنشطة',
              icon: Icons.assignment_outlined,
              iconColor: DesktopTheme.accentRed,
              onTap: () => _showOperationalReports(),
            ),
            DesktopQuickActionCard(
              title: 'تقارير مخصصة',
              description: 'إنشاء تقارير مخصصة حسب الحاجة',
              icon: Icons.build_outlined,
              iconColor: DesktopTheme.textSecondary,
              onTap: () => _showCustomReports(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات سريعة',
          style: DesktopTheme.headingMedium,
        ),
        const SizedBox(height: DesktopTheme.spacingLarge),
        DesktopDashboardGrid(
          crossAxisCount: 4,
          childAspectRatio: 1.3,
          children: [
            DesktopDashboardCard(
              title: 'إجمالي الأصناف',
              value: _reportStats['totalItems'].toString(),
              subtitle: 'صنف في النظام',
              icon: Icons.inventory_2_outlined,
              iconColor: DesktopTheme.primaryBlue,
              onTap: () => _openInventoryReport(),
            ),
            DesktopDashboardCard(
              title: 'إجمالي الفواتير',
              value: _reportStats['totalInvoices'].toString(),
              subtitle: 'فاتورة مبيعات',
              icon: Icons.receipt_long_outlined,
              iconColor: DesktopTheme.accentGreen,
              onTap: () => _openSalesReport(),
            ),
            DesktopDashboardCard(
              title: 'عدد المخازن',
              value: _reportStats['totalWarehouses'].toString(),
              subtitle: 'مخزن نشط',
              icon: Icons.warehouse_outlined,
              iconColor: DesktopTheme.accentOrange,
              onTap: () => _openWarehouseReport(),
            ),
            DesktopDashboardCard(
              title: 'عدد الوكلاء',
              value: _reportStats['totalAgents'].toString(),
              subtitle: 'وكيل مسجل',
              icon: Icons.people_outlined,
              iconColor: DesktopTheme.accentPurple,
              onTap: () => _openAgentReport(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentReports() {
    final recentReports = [
      {
        'title': 'تقرير المخزون التفصيلي',
        'description': 'تقرير شامل لجميع الأصناف والمخازن',
        'date': 'اليوم',
        'icon': Icons.inventory_2_outlined,
        'color': DesktopTheme.primaryBlue,
        'action': () => _openInventoryReport(),
      },
      {
        'title': 'تقرير حركة المخزون',
        'description': 'تتبع جميع حركات الدخول والخروج',
        'date': 'اليوم',
        'icon': Icons.swap_horiz_outlined,
        'color': DesktopTheme.accentGreen,
        'action': () => _openMovementReport(),
      },
      {
        'title': 'كشف حساب الوكلاء',
        'description': 'تقرير مفصل لحسابات جميع الوكلاء',
        'date': 'أمس',
        'icon': Icons.account_balance_outlined,
        'color': DesktopTheme.accentOrange,
        'action': () => _openAgentStatementReport(),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'التقارير الأخيرة',
              style: DesktopTheme.headingMedium,
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: () => _viewAllReports(),
              icon: const Icon(Icons.arrow_forward),
              label: const Text('عرض جميع التقارير'),
            ),
          ],
        ),
        const SizedBox(height: DesktopTheme.spacingLarge),
        Container(
          decoration: DesktopTheme.cardDecoration,
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: recentReports.length,
            separatorBuilder: (context, index) => const Divider(
              color: DesktopTheme.borderLight,
              height: 1,
            ),
            itemBuilder: (context, index) {
              final report = recentReports[index];
              return ListTile(
                contentPadding: const EdgeInsets.all(DesktopTheme.spacingLarge),
                leading: Container(
                  padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
                  decoration: BoxDecoration(
                    color: (report['color'] as Color).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(DesktopTheme.radiusMedium),
                  ),
                  child: Icon(
                    report['icon'] as IconData,
                    color: report['color'] as Color,
                    size: 24,
                  ),
                ),
                title: Text(
                  report['title'] as String,
                  style: DesktopTheme.titleMedium,
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: DesktopTheme.spacingXSmall),
                    Text(
                      report['description'] as String,
                      style: DesktopTheme.bodyMedium,
                    ),
                    const SizedBox(height: DesktopTheme.spacingXSmall),
                    Text(
                      'آخر تحديث: ${report['date']}',
                      style: DesktopTheme.bodySmall.copyWith(
                        color: DesktopTheme.textTertiary,
                      ),
                    ),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.visibility_outlined),
                      onPressed: report['action'] as VoidCallback,
                      tooltip: 'عرض التقرير',
                    ),
                    IconButton(
                      icon: const Icon(Icons.file_download_outlined),
                      onPressed: () => _downloadReport(report['title'] as String),
                      tooltip: 'تحميل التقرير',
                    ),
                    IconButton(
                      icon: const Icon(Icons.print_outlined),
                      onPressed: () => _printReport(report['title'] as String),
                      tooltip: 'طباعة التقرير',
                    ),
                  ],
                ),
                onTap: report['action'] as VoidCallback,
              );
            },
          ),
        ),
      ],
    );
  }

  // Navigation Methods
  void _showInventoryReports() {
    showDialog(
      context: context,
      builder: (context) => _buildReportSelectionDialog(
        'تقارير المخزون',
        [
          {'title': 'تقرير المخزون التفصيلي', 'action': _openInventoryReport},
          {'title': 'تقرير حركة المخزون', 'action': _openMovementReport},
          {'title': 'تقرير الأصناف المتاحة', 'action': _openAvailableItemsReport},
          {'title': 'تقرير الأصناف المباعة', 'action': _openSoldItemsReport},
        ],
      ),
    );
  }

  void _showSalesReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقارير المبيعات قيد التطوير')),
    );
  }

  void _showAgentReports() {
    showDialog(
      context: context,
      builder: (context) => _buildReportSelectionDialog(
        'تقارير الوكلاء',
        [
          {'title': 'كشف حساب الوكلاء', 'action': _openAgentStatementReport},
          {'title': 'تقرير أرصدة الوكلاء', 'action': _openAgentBalanceReport},
          {'title': 'تقرير مدفوعات الوكلاء', 'action': _openAgentPaymentsReport},
        ],
      ),
    );
  }

  void _showFinancialReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('التقارير المالية قيد التطوير')),
    );
  }

  void _showOperationalReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقارير العمليات قيد التطوير')),
    );
  }

  void _showCustomReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('التقارير المخصصة قيد التطوير')),
    );
  }

  Widget _buildReportSelectionDialog(String title, List<Map<String, dynamic>> reports) {
    return AlertDialog(
      title: Text(title),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: reports.map((report) => ListTile(
            leading: const Icon(Icons.description_outlined),
            title: Text(report['title']),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).pop();
              report['action']();
            },
          )).toList(),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
      ],
    );
  }

  // Report Opening Methods
  void _openInventoryReport() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const DetailedInventoryReport(),
      ),
    );
  }

  void _openMovementReport() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const WarehouseMovementReportsScreen(),
      ),
    );
  }

  void _openAgentStatementReport() {
    // Show agent selection dialog first
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار الوكيل'),
        content: const Text('يرجى اختيار وكيل لعرض كشف حسابه'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const DetailedAgentStatementScreen(agentId: 'sample_agent'),
                ),
              );
            },
            child: const Text('عرض عينة'),
          ),
        ],
      ),
    );
  }

  void _openSalesReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقرير المبيعات قيد التطوير')),
    );
  }

  void _openWarehouseReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقرير المخازن قيد التطوير')),
    );
  }

  void _openAgentReport() {
    _openAgentStatementReport();
  }

  void _openAvailableItemsReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقرير الأصناف المتاحة قيد التطوير')),
    );
  }

  void _openSoldItemsReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقرير الأصناف المباعة قيد التطوير')),
    );
  }

  void _openAgentBalanceReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقرير أرصدة الوكلاء قيد التطوير')),
    );
  }

  void _openAgentPaymentsReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تقرير مدفوعات الوكلاء قيد التطوير')),
    );
  }

  void _viewAllReports() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('عرض جميع التقارير قيد التطوير')),
    );
  }

  void _downloadReport(String reportName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تحميل $reportName قيد التطوير')),
    );
  }

  void _printReport(String reportName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('طباعة $reportName قيد التطوير')),
    );
  }
}
