import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

/// خدمة اختيار الصور من الهاتف أو الكاميرا
class ImagePickerService {
  static final ImagePickerService _instance = ImagePickerService._internal();
  factory ImagePickerService() => _instance;
  ImagePickerService._internal();

  static ImagePickerService get instance => _instance;

  final ImagePicker _picker = ImagePicker();

  /// عرض خيارات اختيار الصورة (كاميرا أو معرض)
  Future<File?> showImageSourceDialog({
    required String title,
    bool allowCamera = true,
    bool allowGallery = true,
  }) async {
    if (!allowCamera && !allowGallery) {
      throw ArgumentError('يجب السماح بخيار واحد على الأقل');
    }

    // إذا كان خيار واحد فقط متاح، استخدمه مباشرة
    if (allowCamera && !allowGallery) {
      return await pickImageFromCamera();
    }
    if (allowGallery && !allowCamera) {
      return await pickImageFromGallery();
    }

    // إذا كان كلا الخيارين متاحين، اعرض حوار الاختيار
    // هذا سيتم تنفيذه في الواجهة
    return null;
  }

  /// التقاط صورة من الكاميرا
  Future<File?> pickImageFromCamera() async {
    try {
      // طلب إذن الكاميرا
      final cameraPermission = await Permission.camera.request();
      if (!cameraPermission.isGranted) {
        if (kDebugMode) {
          print('❌ Camera permission denied');
        }
        return null;
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85, // جودة جيدة مع حجم معقول
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        if (kDebugMode) {
          print('📸 Image captured from camera: ${image.path}');
        }
        return File(image.path);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking image from camera: $e');
      }
    }
    return null;
  }

  /// اختيار صورة من المعرض
  Future<File?> pickImageFromGallery() async {
    try {
      // طلب إذن الوصول للصور
      final storagePermission = await Permission.photos.request();
      if (!storagePermission.isGranted) {
        // جرب إذن التخزين العام للأجهزة القديمة
        final legacyPermission = await Permission.storage.request();
        if (!legacyPermission.isGranted) {
          if (kDebugMode) {
            print('❌ Storage permission denied');
          }
          return null;
        }
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85, // جودة جيدة مع حجم معقول
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        if (kDebugMode) {
          print('🖼️ Image selected from gallery: ${image.path}');
        }
        return File(image.path);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking image from gallery: $e');
      }
    }
    return null;
  }

  /// اختيار عدة صور من المعرض
  Future<List<File>> pickMultipleImagesFromGallery({int maxImages = 5}) async {
    try {
      // طلب إذن الوصول للصور
      final storagePermission = await Permission.photos.request();
      if (!storagePermission.isGranted) {
        final legacyPermission = await Permission.storage.request();
        if (!legacyPermission.isGranted) {
          if (kDebugMode) {
            print('❌ Storage permission denied');
          }
          return [];
        }
      }

      final List<XFile> images = await _picker.pickMultiImage(
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (images.isNotEmpty) {
        // تحديد العدد الأقصى
        final limitedImages = images.take(maxImages).toList();
        
        if (kDebugMode) {
          print('🖼️ Selected ${limitedImages.length} images from gallery');
        }
        
        return limitedImages.map((xFile) => File(xFile.path)).toList();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking multiple images from gallery: $e');
      }
    }
    return [];
  }

  /// التحقق من صحة الصورة
  Future<bool> validateImage(File imageFile) async {
    try {
      // التحقق من وجود الملف
      if (!await imageFile.exists()) {
        if (kDebugMode) {
          print('❌ Image file does not exist');
        }
        return false;
      }

      // التحقق من حجم الملف (أقل من 10 ميجا)
      final fileSize = await imageFile.length();
      if (fileSize > 10 * 1024 * 1024) { // 10 MB
        if (kDebugMode) {
          print('❌ Image file too large: ${fileSize / (1024 * 1024)} MB');
        }
        return false;
      }

      // التحقق من نوع الملف
      final fileName = imageFile.path.toLowerCase();
      if (!fileName.endsWith('.jpg') && 
          !fileName.endsWith('.jpeg') && 
          !fileName.endsWith('.png')) {
        if (kDebugMode) {
          print('❌ Invalid image format');
        }
        return false;
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating image: $e');
      }
      return false;
    }
  }

  /// حفظ الصورة في مجلد التطبيق
  Future<File?> saveImageToAppDirectory(File sourceFile, String fileName) async {
    try {
      final appDir = Directory('/data/user/0/com.alfarhan.transport/app_flutter');
      if (!await appDir.exists()) {
        await appDir.create(recursive: true);
      }

      final targetPath = '${appDir.path}/$fileName';
      final targetFile = await sourceFile.copy(targetPath);

      if (kDebugMode) {
        print('💾 Image saved to app directory: $targetPath');
      }

      return targetFile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving image to app directory: $e');
      }
      return null;
    }
  }

  /// حذف صورة من مجلد التطبيق
  Future<bool> deleteImageFromAppDirectory(String fileName) async {
    try {
      final appDir = Directory('/data/user/0/com.alfarhan.transport/app_flutter');
      final filePath = '${appDir.path}/$fileName';
      final file = File(filePath);

      if (await file.exists()) {
        await file.delete();
        if (kDebugMode) {
          print('🗑️ Image deleted from app directory: $filePath');
        }
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting image from app directory: $e');
      }
    }
    return false;
  }
}
