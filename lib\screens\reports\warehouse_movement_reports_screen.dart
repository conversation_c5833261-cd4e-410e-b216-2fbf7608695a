import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/warehouse_model.dart';
import '../../models/item_model.dart';
import '../../services/data_service.dart';
import '../../core/constants/app_constants.dart';
import 'advanced_warehouse_reports_screen.dart';

class WarehouseMovementReportsScreen extends StatefulWidget {
  const WarehouseMovementReportsScreen({super.key});

  @override
  State<WarehouseMovementReportsScreen> createState() => _WarehouseMovementReportsScreenState();
}

class _WarehouseMovementReportsScreenState extends State<WarehouseMovementReportsScreen> {
  List<WarehouseModel> _warehouses = [];
  List<ItemModel> _items = [];
  List<Map<String, dynamic>> _movements = [];
  bool _isLoading = true;
  String? _selectedWarehouseId;
  DateTime? _startDate;
  DateTime? _endDate;
  String _selectedMovementType = 'all';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final dataService = Provider.of<DataService>(context, listen: false);

      final warehouses = await dataService.getWarehouses();
      final items = await dataService.getItems();

      setState(() {
        _warehouses = warehouses;
        _items = items;
        _isLoading = false;
      });

      _loadMovements();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _loadMovements() async {
    // Generate sample movements data based on items and warehouses
    final movements = <Map<String, dynamic>>[];

    for (int i = 0; i < _items.length && i < 20; i++) {
      final item = _items[i];
      final warehouse = _warehouses.isNotEmpty ? _warehouses[i % _warehouses.length] : null;

      if (warehouse != null) {
        movements.add({
          'id': 'movement_${DateTime.now().millisecondsSinceEpoch}_$i',
          'itemId': item.id,
          'itemName': '${item.brand} ${item.model}',
          'warehouseId': warehouse.id,
          'warehouseName': warehouse.name,
          'movementType': i % 3 == 0 ? 'in' : (i % 3 == 1 ? 'out' : 'transfer'),
          'quantity': (i % 5) + 1,
          'timestamp': DateTime.now().subtract(Duration(days: i)),
          'reason': i % 3 == 0 ? 'استلام بضاعة' : (i % 3 == 1 ? 'بيع' : 'نقل'),
          'createdBy': 'admin_001',
        });
      }
    }

    setState(() {
      _movements = movements;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.assessment, size: 24),
            ),
            const SizedBox(width: 12),
            const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'تقارير حركة المخزون',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                Text(
                  'تتبع حركة البضائع والمخزون',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
          ],
        ),
        backgroundColor: const Color(0xFF1565C0),
        foregroundColor: Colors.white,
        elevation: 4,
        toolbarHeight: 70,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AdvancedWarehouseReportsScreen(),
                ),
              );
            },
            tooltip: 'التقارير المتطورة',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printReport,
            tooltip: 'طباعة التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildReportContent(),
    );
  }

  Widget _buildReportContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFiltersCard(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildSummaryCards(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildMovementsTable(),
        ],
      ),
    );
  }

  Widget _buildFiltersCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.filter_list, color: Color(0xFF1565C0), size: 24),
              SizedBox(width: 8),
              Text(
                'فلاتر التقرير',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildWarehouseDropdown(),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMovementTypeDropdown(),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDatePicker('من تاريخ', _startDate, (date) {
                  setState(() {
                    _startDate = date;
                  });
                  _loadMovements();
                }),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDatePicker('إلى تاريخ', _endDate, (date) {
                  setState(() {
                    _endDate = date;
                  });
                  _loadMovements();
                }),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedWarehouseId,
          hint: const Text('اختر المخزن'),
          isExpanded: true,
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('جميع المخازن'),
            ),
            ..._warehouses.map((warehouse) => DropdownMenuItem<String>(
              value: warehouse.id,
              child: Text(warehouse.name),
            )),
          ],
          onChanged: (value) {
            setState(() {
              _selectedWarehouseId = value;
            });
            _loadMovements();
          },
        ),
      ),
    );
  }

  Widget _buildMovementTypeDropdown() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: _selectedMovementType,
          isExpanded: true,
          items: const [
            DropdownMenuItem(value: 'all', child: Text('جميع الحركات')),
            DropdownMenuItem(value: 'in', child: Text('دخول')),
            DropdownMenuItem(value: 'out', child: Text('خروج')),
            DropdownMenuItem(value: 'transfer', child: Text('نقل')),
          ],
          onChanged: (value) {
            setState(() {
              _selectedMovementType = value!;
            });
            _loadMovements();
          },
        ),
      ),
    );
  }

  Widget _buildDatePicker(String label, DateTime? date, Function(DateTime?) onChanged) {
    return InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          initialDate: date ?? DateTime.now(),
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
        );
        if (picked != null) {
          onChanged(picked);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            const Icon(Icons.calendar_today, size: 20),
            const SizedBox(width: 8),
            Text(
              date != null
                  ? '$label: ${date.day}/${date.month}/${date.year}'
                  : label,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final filteredMovements = _getFilteredMovements();
    final inCount = filteredMovements.where((m) => m['movementType'] == 'in').length;
    final outCount = filteredMovements.where((m) => m['movementType'] == 'out').length;
    final transferCount = filteredMovements.where((m) => m['movementType'] == 'transfer').length;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'حركات الدخول',
            inCount.toString(),
            Colors.green,
            Icons.arrow_downward,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'حركات الخروج',
            outCount.toString(),
            Colors.red,
            Icons.arrow_upward,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'حركات النقل',
            transferCount.toString(),
            Colors.blue,
            Icons.swap_horiz,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الحركات',
            filteredMovements.length.toString(),
            const Color(0xFF1565C0),
            Icons.assessment,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: color.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMovementsTable() {
    final filteredMovements = _getFilteredMovements();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Icon(Icons.table_chart, color: Color(0xFF1565C0), size: 24),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل الحركات (${filteredMovements.length} حركة)',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          if (filteredMovements.isEmpty)
            Padding(
              padding: const EdgeInsets.all(40),
              child: Center(
                child: Column(
                  children: [
                    const Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
                    const SizedBox(height: 16),
                    const Text(
                      'لا توجد حركات مخزون في الفترة المحددة',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const AdvancedWarehouseReportsScreen(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.analytics),
                      label: const Text('جرب التقارير المتطورة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1565C0),
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('الصنف')),
                  DataColumn(label: Text('المخزن')),
                  DataColumn(label: Text('نوع الحركة')),
                  DataColumn(label: Text('الكمية')),
                  DataColumn(label: Text('السبب')),
                ],
                rows: filteredMovements.map((movement) {
                  return DataRow(
                    cells: [
                      DataCell(Text(_formatDate(movement['timestamp']))),
                      DataCell(Text(movement['itemName'])),
                      DataCell(Text(movement['warehouseName'])),
                      DataCell(_buildMovementTypeChip(movement['movementType'])),
                      DataCell(Text(movement['quantity'].toString())),
                      DataCell(Text(movement['reason'])),
                    ],
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMovementTypeChip(String type) {
    Color color;
    String label;
    IconData icon;

    switch (type) {
      case 'in':
        color = Colors.green;
        label = 'دخول';
        icon = Icons.arrow_downward;
        break;
      case 'out':
        color = Colors.red;
        label = 'خروج';
        icon = Icons.arrow_upward;
        break;
      case 'transfer':
        color = Colors.blue;
        label = 'نقل';
        icon = Icons.swap_horiz;
        break;
      default:
        color = Colors.grey;
        label = type;
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredMovements() {
    var filtered = _movements.where((movement) {
      // Filter by warehouse
      if (_selectedWarehouseId != null && movement['warehouseId'] != _selectedWarehouseId) {
        return false;
      }

      // Filter by movement type
      if (_selectedMovementType != 'all' && movement['movementType'] != _selectedMovementType) {
        return false;
      }

      // Filter by date range
      final movementDate = movement['timestamp'] as DateTime;
      if (_startDate != null && movementDate.isBefore(_startDate!)) {
        return false;
      }
      if (_endDate != null && movementDate.isAfter(_endDate!.add(const Duration(days: 1)))) {
        return false;
      }

      return true;
    }).toList();

    // Sort by date (newest first)
    filtered.sort((a, b) => (b['timestamp'] as DateTime).compareTo(a['timestamp'] as DateTime));

    return filtered;
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة الطباعة قيد التطوير')),
    );
  }

  void _exportReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('وظيفة التصدير قيد التطوير')),
    );
  }
}
