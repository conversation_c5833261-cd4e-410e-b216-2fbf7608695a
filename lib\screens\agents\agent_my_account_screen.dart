import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/agent_accounting_system.dart';
import '../../providers/auth_provider.dart';
import '../../services/agent_accounting_service.dart';

/// شاشة "حسابي" للوكيل
/// تعرض حساب الوكيل مع المؤسسة من منظور الوكيل
class AgentMyAccountScreen extends StatefulWidget {
  const AgentMyAccountScreen({super.key});

  @override
  State<AgentMyAccountScreen> createState() => _AgentMyAccountScreenState();
}

class _AgentMyAccountScreenState extends State<AgentMyAccountScreen>
    with TickerProviderStateMixin {
  
  final AgentAccountingService _accountingService = AgentAccountingService();
  
  late TabController _tabController;
  
  AgentAccount? _myAccount;
  bool _isLoading = true;
  String? _error;
  AgentTransactionType? _selectedFilter;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadMyAccount();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل حساب الوكيل الحالي
  Future<void> _loadMyAccount() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
      if (currentUser == null || currentUser.role != 'agent') {
        throw 'يجب تسجيل الدخول كوكيل';
      }

      if (kDebugMode) {
        print('📊 Loading agent account for: ${currentUser.fullName}');
      }

      _myAccount = await _accountingService.getAgentAccount(currentUser.id);

      if (kDebugMode) {
        print('✅ Loaded agent account: ${_myAccount?.agentName}');
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading agent account: $e');
      }
      setState(() {
        _isLoading = false;
        _error = 'خطأ في تحميل بيانات الحساب: $e';
      });
    }
  }

  /// تحديث الحساب
  Future<void> _refreshAccount() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final currentUser = Provider.of<AuthProvider>(context, listen: false).currentUser;
      if (currentUser != null) {
        _myAccount = await _accountingService.recalculateAgentAccount(currentUser.id);
      }

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        AppUtils.showSnackBar(context, 'تم تحديث بيانات الحساب بنجاح');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحديث الحساب: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('حسابي مع المؤسسة'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshAccount,
            tooltip: 'تحديث الحساب',
          ),
        ],
        bottom: _myAccount != null ? TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.account_balance), text: 'ملخص الحساب'),
            Tab(icon: Icon(Icons.list), text: 'كشف الحساب'),
          ],
        ) : null,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل بيانات الحساب...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(_error!, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadMyAccount,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_myAccount == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.account_balance, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لم يتم العثور على بيانات الحساب'),
          ],
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildAccountSummaryTab(),
        _buildAccountStatementTab(),
      ],
    );
  }

  /// تبويب ملخص الحساب
  Widget _buildAccountSummaryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBalanceCard(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildAccountSummaryCards(),
          const SizedBox(height: AppConstants.defaultPadding),
          _buildRecentTransactions(),
        ],
      ),
    );
  }

  /// بطاقة الرصيد
  Widget _buildBalanceCard() {
    final isDebt = _myAccount!.getCurrentDebt() > 0;
    final balanceColor = isDebt ? Colors.red : Colors.green;
    final balanceAmount = isDebt ? _myAccount!.getCurrentDebt() : _myAccount!.getCreditBalance();
    final balanceLabel = isDebt ? 'مديونيتك للمؤسسة' : 'رصيدك لدى المؤسسة';
    final balanceIcon = isDebt ? Icons.trending_up : Icons.trending_down;

    return Card(
      elevation: 3,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [balanceColor.withOpacity(0.1), balanceColor.withOpacity(0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            Icon(balanceIcon, size: 48, color: balanceColor),
            const SizedBox(height: 16),
            Text(
              balanceLabel,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              AppUtils.formatCurrency(balanceAmount),
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: balanceColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: balanceColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: balanceColor.withOpacity(0.3)),
              ),
              child: Text(
                isDebt ? 'يجب سداد هذا المبلغ' : 'رصيد متاح لك',
                style: TextStyle(
                  color: balanceColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بطاقات ملخص الحساب
  Widget _buildAccountSummaryCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص نشاطك التجاري',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppConstants.defaultPadding,
          mainAxisSpacing: AppConstants.defaultPadding,
          childAspectRatio: 1.2,
          children: [
            _buildSummaryCard(
              'البضاعة المستلمة',
              _myAccount!.totalGoodsReceived,
              Icons.input,
              Colors.blue,
              'إجمالي قيمة البضاعة التي استلمتها',
            ),
            _buildSummaryCard(
              'البضاعة المرتجعة',
              _myAccount!.totalGoodsWithdrawn,
              Icons.output,
              Colors.orange,
              'إجمالي قيمة البضاعة المرتجعة',
            ),
            _buildSummaryCard(
              'مبيعاتك',
              _myAccount!.totalCustomerSales,
              Icons.shopping_cart,
              Colors.green,
              'إجمالي مبيعاتك للعملاء',
            ),
            _buildSummaryCard(
              'عمولاتك',
              _myAccount!.totalAgentCommission,
              Icons.monetization_on,
              Colors.purple,
              'إجمالي العمولات المستحقة لك',
            ),
            _buildSummaryCard(
              'مدفوعاتك',
              _myAccount!.totalPaymentsReceived,
              Icons.payment,
              Colors.teal,
              'إجمالي المبالغ التي دفعتها',
            ),
            _buildSummaryCard(
              'معاملاتك',
              _myAccount!.transactions.length.toDouble(),
              Icons.list,
              Colors.indigo,
              'عدد المعاملات الإجمالي',
              isCount: true,
            ),
          ],
        ),
      ],
    );
  }

  /// بطاقة ملخص
  Widget _buildSummaryCard(
    String title,
    double value,
    IconData icon,
    Color color,
    String description, {
    bool isCount = false,
  }) {
    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: color.withOpacity(0.05),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 28, color: color),
            const SizedBox(height: 8),
            Text(
              isCount ? value.toInt().toString() : AppUtils.formatCurrency(value),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 2),
            Text(
              description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// المعاملات الأخيرة
  Widget _buildRecentTransactions() {
    final recentTransactions = _myAccount!.transactions.take(5).toList();
    
    if (recentTransactions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'آخر المعاملات',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => _tabController.animateTo(1),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: recentTransactions.length,
          itemBuilder: (context, index) {
            final transaction = recentTransactions[index];
            return _buildTransactionCard(transaction);
          },
        ),
      ],
    );
  }

  /// بطاقة المعاملة
  Widget _buildTransactionCard(AgentTransaction transaction) {
    final isPositive = transaction.amount > 0;
    final amountColor = isPositive ? Colors.green : Colors.red;
    final icon = _getTransactionIcon(transaction.type);
    final iconColor = _getTransactionColor(transaction.type);

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: iconColor.withOpacity(0.2),
          child: Icon(icon, color: iconColor, size: 20),
        ),
        title: Text(
          transaction.type.displayName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(transaction.description),
            Text(
              AppUtils.formatDateTime(transaction.createdAt),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: Text(
          AppUtils.formatCurrency(transaction.amount.abs()),
          style: TextStyle(
            color: amountColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// تبويب كشف الحساب
  Widget _buildAccountStatementTab() {
    return Column(
      children: [
        // فلتر المعاملات
        Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: DropdownButtonFormField<AgentTransactionType?>(
            value: _selectedFilter,
            decoration: const InputDecoration(
              labelText: 'فلترة حسب نوع المعاملة',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.filter_list),
            ),
            items: [
              const DropdownMenuItem<AgentTransactionType?>(
                value: null,
                child: Text('جميع المعاملات'),
              ),
              ...AgentTransactionType.values.map((type) {
                return DropdownMenuItem<AgentTransactionType?>(
                  value: type,
                  child: Text(type.displayName),
                );
              }),
            ],
            onChanged: (value) {
              setState(() {
                _selectedFilter = value;
              });
            },
          ),
        ),
        // قائمة المعاملات
        Expanded(
          child: _buildTransactionsList(),
        ),
      ],
    );
  }

  /// قائمة المعاملات
  Widget _buildTransactionsList() {
    final filteredTransactions = _selectedFilter == null
        ? _myAccount!.transactions
        : _myAccount!.transactions.where((t) => t.type == _selectedFilter).toList();

    if (filteredTransactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد معاملات'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      itemCount: filteredTransactions.length,
      itemBuilder: (context, index) {
        final transaction = filteredTransactions[index];
        return _buildDetailedTransactionCard(transaction);
      },
    );
  }

  /// بطاقة المعاملة المفصلة
  Widget _buildDetailedTransactionCard(AgentTransaction transaction) {
    final isPositive = transaction.amount > 0;
    final amountColor = isPositive ? Colors.green : Colors.red;
    final icon = _getTransactionIcon(transaction.type);
    final iconColor = _getTransactionColor(transaction.type);

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: iconColor.withOpacity(0.2),
          child: Icon(icon, color: iconColor, size: 20),
        ),
        title: Text(
          transaction.type.displayName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(transaction.description),
            Text(
              AppUtils.formatDateTime(transaction.createdAt),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              AppUtils.formatCurrency(transaction.amount.abs()),
              style: TextStyle(
                color: amountColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              isPositive ? 'دائن' : 'مدين',
              style: TextStyle(
                color: amountColor,
                fontSize: 12,
              ),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTransactionDetailRow('المبلغ', AppUtils.formatCurrency(transaction.amount)),
                _buildTransactionDetailRow('الرصيد بعد المعاملة', AppUtils.formatCurrency(transaction.balanceAfter)),
                _buildTransactionDetailRow('تاريخ المعاملة', AppUtils.formatDateTime(transaction.createdAt)),
                if (transaction.relatedInvoiceId != null)
                  _buildTransactionDetailRow('الفاتورة المرتبطة', transaction.relatedInvoiceId!),
                if (transaction.relatedTransferId != null)
                  _buildTransactionDetailRow('التحويل المرتبط', transaction.relatedTransferId!),
                if (transaction.relatedPaymentId != null)
                  _buildTransactionDetailRow('الدفعة المرتبطة', transaction.relatedPaymentId!),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// صف تفاصيل المعاملة
  Widget _buildTransactionDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// أيقونة المعاملة
  IconData _getTransactionIcon(AgentTransactionType type) {
    switch (type) {
      case AgentTransactionType.goodsReceived:
        return Icons.input;
      case AgentTransactionType.goodsWithdrawn:
        return Icons.output;
      case AgentTransactionType.customerSale:
        return Icons.shopping_cart;
      case AgentTransactionType.commission:
        return Icons.monetization_on;
      case AgentTransactionType.payment:
        return Icons.payment;
      case AgentTransactionType.adjustment:
        return Icons.tune;
      case AgentTransactionType.other:
        return Icons.more_horiz;
    }
  }

  /// لون المعاملة
  Color _getTransactionColor(AgentTransactionType type) {
    switch (type) {
      case AgentTransactionType.goodsReceived:
        return Colors.blue;
      case AgentTransactionType.goodsWithdrawn:
        return Colors.orange;
      case AgentTransactionType.customerSale:
        return Colors.green;
      case AgentTransactionType.commission:
        return Colors.purple;
      case AgentTransactionType.payment:
        return Colors.teal;
      case AgentTransactionType.adjustment:
        return Colors.indigo;
      case AgentTransactionType.other:
        return Colors.grey;
    }
  }
}
