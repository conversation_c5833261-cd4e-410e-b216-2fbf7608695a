import 'package:flutter/foundation.dart';
import '../models/agent_accounting_system.dart';
import '../models/transfer_model.dart';
import '../models/inventory_item_model.dart';
import 'agent_accounting_service.dart';
import 'data_service.dart';
import 'firebase_service.dart';

/// خدمة سحب البضاعة من الوكلاء
/// تتكامل مع النظام المحاسبي لتحديث حسابات الوكلاء تلقائياً
class GoodsWithdrawalService {
  static final GoodsWithdrawalService _instance = GoodsWithdrawalService._internal();
  factory GoodsWithdrawalService() => _instance;
  GoodsWithdrawalService._internal();

  final AgentAccountingService _accountingService = AgentAccountingService();
  final DataService _dataService = DataService.instance;
  final FirebaseService _firebaseService = FirebaseService.instance;

  /// سحب بضاعة من وكيل
  /// يقوم بإنشاء تحويل من مخزن الوكيل إلى المخزن الرئيسي
  /// ويحدث حساب الوكيل تلقائياً
  Future<String> withdrawGoodsFromAgent({
    required String agentId,
    required String fromWarehouse,
    required String toWarehouse,
    required List<TransferItemModel> items,
    required String userId,
    String? notes,
  }) async {
    try {
      if (kDebugMode) {
        print('📦 Starting goods withdrawal from agent: $agentId');
        print('   From warehouse: $fromWarehouse');
        print('   To warehouse: $toWarehouse');
        print('   Items count: ${items.length}');
      }

      // التحقق من أن المخزن المصدر ينتمي للوكيل
      if (!fromWarehouse.contains('agent_$agentId')) {
        throw 'المخزن المصدر لا ينتمي للوكيل المحدد';
      }

      // التحقق من توفر البضاعة في مخزن الوكيل
      await _validateItemsAvailability(fromWarehouse, items);

      // حساب القيمة الإجمالية للبضاعة المسحوبة
      final totalValue = _calculateTotalValue(items);

      // إنشاء رقم التحويل
      final transferNumber = 'WD-${DateTime.now().millisecondsSinceEpoch}';

      // إنشاء التحويل
      final transfer = TransferModel(
        id: 'transfer_${DateTime.now().millisecondsSinceEpoch}',
        transferNumber: transferNumber,
        fromWarehouse: fromWarehouse,
        toWarehouse: toWarehouse,
        items: items,
        status: 'completed',
        notes: notes ?? 'سحب بضاعة من الوكيل',
        createdAt: DateTime.now(),
        createdBy: userId,
        completedAt: DateTime.now(),
        completedBy: userId,
      );

      // حفظ التحويل
      await _dataService.saveTransfer(transfer.toMap());

      // تحديث المخزون
      await _updateInventory(transfer);

      // تحديث حساب الوكيل
      await _updateAgentAccount(agentId, transfer, totalValue, userId);

      if (kDebugMode) {
        print('✅ Goods withdrawal completed successfully');
        print('   Transfer ID: ${transfer.id}');
        print('   Total value: $totalValue');
      }

      return transfer.id;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error withdrawing goods from agent: $e');
      }
      rethrow;
    }
  }

  /// التحقق من توفر البضاعة في المخزن
  Future<void> _validateItemsAvailability(String warehouse, List<TransferItemModel> items) async {
    for (final item in items) {
      final inventoryItem = await _dataService.getInventoryItem(item.itemId, warehouse);
      
      if (inventoryItem == null) {
        throw 'الصنف ${item.itemName} غير موجود في المخزن';
      }

      if (inventoryItem.quantity < item.quantity) {
        throw 'الكمية المطلوبة من ${item.itemName} (${item.quantity}) أكبر من المتوفر (${inventoryItem.quantity})';
      }
    }
  }

  /// حساب القيمة الإجمالية للبضاعة
  double _calculateTotalValue(List<TransferItemModel> items) {
    double totalValue = 0.0;
    
    for (final item in items) {
      // استخدام سعر الشراء إذا كان متوفراً، وإلا استخدام سعر البيع
      final unitPrice = item.purchasePrice ?? item.unitPrice ?? 0.0;
      totalValue += item.quantity * unitPrice;
    }
    
    return totalValue;
  }

  /// تحديث المخزون
  Future<void> _updateInventory(TransferModel transfer) async {
    try {
      // تقليل الكمية من المخزن المصدر
      for (final item in transfer.items) {
        await _dataService.updateInventoryQuantity(
          item.itemId,
          transfer.fromWarehouse,
          -item.quantity, // تقليل الكمية
        );
      }

      // زيادة الكمية في المخزن المستهدف
      for (final item in transfer.items) {
        await _dataService.updateInventoryQuantity(
          item.itemId,
          transfer.toWarehouse,
          item.quantity, // زيادة الكمية
        );
      }

      if (kDebugMode) {
        print('✅ Inventory updated successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating inventory: $e');
      }
      rethrow;
    }
  }

  /// تحديث حساب الوكيل
  Future<void> _updateAgentAccount(
    String agentId,
    TransferModel transfer,
    double totalValue,
    String userId,
  ) async {
    try {
      // إعادة حساب حساب الوكيل لتضمين السحب الجديد
      await _accountingService.recalculateAgentAccount(agentId);

      if (kDebugMode) {
        print('✅ Agent account updated successfully');
        print('   Withdrawn value: $totalValue');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating agent account: $e');
      }
      rethrow;
    }
  }

  /// الحصول على تاريخ سحب البضاعة للوكيل
  Future<List<TransferModel>> getAgentWithdrawalHistory(String agentId) async {
    try {
      final allTransfers = await _dataService.getTransfers();
      
      // فلترة التحويلات التي تم سحبها من مخازن الوكيل
      final agentWithdrawals = allTransfers.where((transfer) {
        return transfer.fromWarehouse.contains('agent_$agentId') &&
               !transfer.toWarehouse.contains('agent_$agentId');
      }).toList();

      // ترتيب حسب التاريخ (الأحدث أولاً)
      agentWithdrawals.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return agentWithdrawals;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting agent withdrawal history: $e');
      }
      return [];
    }
  }

  /// الحصول على إحصائيات سحب البضاعة للوكيل
  Future<Map<String, dynamic>> getAgentWithdrawalStatistics(String agentId) async {
    try {
      final withdrawals = await getAgentWithdrawalHistory(agentId);
      
      double totalWithdrawnValue = 0.0;
      int totalWithdrawals = withdrawals.length;
      Map<String, int> itemsWithdrawn = {};
      
      for (final withdrawal in withdrawals) {
        // حساب القيمة الإجمالية
        totalWithdrawnValue += _calculateTotalValue(withdrawal.items);
        
        // عد الأصناف المسحوبة
        for (final item in withdrawal.items) {
          itemsWithdrawn[item.itemName] = (itemsWithdrawn[item.itemName] ?? 0) + item.quantity.toInt();
        }
      }

      // أكثر الأصناف سحباً
      final sortedItems = itemsWithdrawn.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      final topWithdrawnItems = sortedItems.take(5).toList();

      return {
        'totalWithdrawals': totalWithdrawals,
        'totalWithdrawnValue': totalWithdrawnValue,
        'averageWithdrawalValue': totalWithdrawals > 0 ? totalWithdrawnValue / totalWithdrawals : 0.0,
        'topWithdrawnItems': topWithdrawnItems.map((e) => {
          'itemName': e.key,
          'quantity': e.value,
        }).toList(),
        'lastWithdrawalDate': withdrawals.isNotEmpty ? withdrawals.first.createdAt.toIso8601String() : null,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting agent withdrawal statistics: $e');
      }
      return {};
    }
  }

  /// إلغاء سحب البضاعة (إرجاع البضاعة للوكيل)
  Future<void> reverseGoodsWithdrawal({
    required String transferId,
    required String userId,
    String? reason,
  }) async {
    try {
      if (kDebugMode) {
        print('🔄 Reversing goods withdrawal: $transferId');
      }

      // الحصول على التحويل الأصلي
      final originalTransfer = await _dataService.getTransferById(transferId);
      if (originalTransfer == null) {
        throw 'التحويل غير موجود';
      }

      // التحقق من أن التحويل مكتمل
      if (originalTransfer.status != 'completed') {
        throw 'لا يمكن إلغاء تحويل غير مكتمل';
      }

      // إنشاء تحويل عكسي
      final reverseTransfer = TransferModel(
        id: 'reverse_${DateTime.now().millisecondsSinceEpoch}',
        transferNumber: 'REV-${originalTransfer.transferNumber}',
        fromWarehouse: originalTransfer.toWarehouse,
        toWarehouse: originalTransfer.fromWarehouse,
        items: originalTransfer.items,
        status: 'completed',
        notes: 'إلغاء سحب البضاعة - ${reason ?? 'بدون سبب محدد'}',
        createdAt: DateTime.now(),
        createdBy: userId,
        completedAt: DateTime.now(),
        completedBy: userId,
      );

      // حفظ التحويل العكسي
      await _dataService.saveTransfer(reverseTransfer.toMap());

      // تحديث المخزون
      await _updateInventory(reverseTransfer);

      // تحديث حساب الوكيل
      final agentId = originalTransfer.fromWarehouse.replaceAll('agent_', '').split('_')[0];
      await _accountingService.recalculateAgentAccount(agentId);

      // تحديث حالة التحويل الأصلي
      await _dataService.updateTransferStatus(transferId, 'reversed');

      if (kDebugMode) {
        print('✅ Goods withdrawal reversed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error reversing goods withdrawal: $e');
      }
      rethrow;
    }
  }

  /// الحصول على تقرير شامل لسحب البضاعة
  Future<Map<String, dynamic>> getWithdrawalReport({
    DateTime? startDate,
    DateTime? endDate,
    String? agentId,
  }) async {
    try {
      final allTransfers = await _dataService.getTransfers();
      
      // فلترة التحويلات حسب المعايير
      var filteredTransfers = allTransfers.where((transfer) {
        // فقط التحويلات من مخازن الوكلاء
        if (!transfer.fromWarehouse.contains('agent_')) return false;
        
        // فلترة حسب الوكيل إذا تم تحديده
        if (agentId != null && !transfer.fromWarehouse.contains('agent_$agentId')) {
          return false;
        }
        
        // فلترة حسب التاريخ
        if (startDate != null && transfer.createdAt.isBefore(startDate)) {
          return false;
        }
        if (endDate != null && transfer.createdAt.isAfter(endDate)) {
          return false;
        }
        
        return true;
      }).toList();

      // حساب الإحصائيات
      double totalValue = 0.0;
      Map<String, double> agentTotals = {};
      Map<String, int> itemCounts = {};
      
      for (final transfer in filteredTransfers) {
        final value = _calculateTotalValue(transfer.items);
        totalValue += value;
        
        // إحصائيات الوكلاء
        final transferAgentId = transfer.fromWarehouse.replaceAll('agent_', '').split('_')[0];
        agentTotals[transferAgentId] = (agentTotals[transferAgentId] ?? 0.0) + value;
        
        // إحصائيات الأصناف
        for (final item in transfer.items) {
          itemCounts[item.itemName] = (itemCounts[item.itemName] ?? 0) + item.quantity.toInt();
        }
      }

      return {
        'totalWithdrawals': filteredTransfers.length,
        'totalValue': totalValue,
        'averageValue': filteredTransfers.isNotEmpty ? totalValue / filteredTransfers.length : 0.0,
        'agentTotals': agentTotals,
        'topItems': itemCounts.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value))
          ..take(10)
          ..map((e) => {'itemName': e.key, 'quantity': e.value}).toList(),
        'withdrawals': filteredTransfers.map((t) => t.toMap()).toList(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating withdrawal report: $e');
      }
      return {};
    }
  }
}
