import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/theme/app_colors.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';
import '../../services/permissions_service.dart';
import '../../screens/inventory/add_item_screen.dart';
import '../../screens/sales/create_invoice_screen.dart';
import '../../screens/notifications/enhanced_notifications_screen.dart';

import '../inventory/transfer_goods_screen.dart';
import '../auth/login_screen.dart';
import '../inventory/inventory_screen.dart';
import '../sales/sales_screen.dart';
import '../documents/document_tracking_screen.dart';
import '../documents/document_dashboard_screen.dart';
import '../../services/enhanced_notification_service.dart';

import '../admin/admin_settings_screen.dart';
import '../admin/warehouse_management_screen.dart';
import '../reports/reports_screen.dart';
import '../users/user_management_screen.dart';
import '../agents/enhanced_agent_management_screen.dart';
import '../agents/agent_my_account_screen.dart';
import '../settings/settings_screen.dart';
import '../admin/company_poster_screen.dart';
import '../reports/customer_inquiry_screen.dart' as reports;
import '../reports/advanced_warehouse_reports_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  bool _isSyncing = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // If not authenticated, redirect to login
        if (!authProvider.isAuthenticated) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const LoginScreen()),
            );
          });
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        return Scaffold(
          backgroundColor: Colors.grey[50], // خلفية فاتحة ومريحة للعين
          appBar: _buildAppBar(authProvider),
          body: _buildBody(authProvider),
          bottomNavigationBar: _buildBottomNavigationBar(authProvider),
          drawer: _buildDrawer(authProvider),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(AuthProvider authProvider) {
    return AppBar(
      elevation: 0,
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      title: LayoutBuilder(
        builder: (context, constraints) {
          // تحديد العرض المتاح للعنوان
          final availableWidth = constraints.maxWidth;
          final showFullTitle = availableWidth > 300;

          return Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColors.whiteWithOpacity(0.15),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: AppColors.whiteWithOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Image.asset(
                  'assets/images/logo.png',
                  height: 18,
                  width: 18,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(Icons.business, size: 18, color: Colors.white);
                  },
                ),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _getPageTitle(_selectedIndex),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.white,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (showFullTitle) ...[
                      Text(
                        'نظام إدارة النقل - الفرحان',
                        style: TextStyle(
                          fontSize: 10,
                          color: AppColors.whiteWithOpacity(0.8),
                          fontWeight: FontWeight.normal,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          );
        },
      ),
      toolbarHeight: 70, // Taller for desktop
      leading: Builder(
        builder: (context) => IconButton(
          icon: const Icon(Icons.menu),
          onPressed: () => Scaffold.of(context).openDrawer(),
          tooltip: 'القائمة',
        ),
      ),
      actions: [
        // Quick actions toolbar - محسن للشاشات الصغيرة
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          tooltip: 'المزيد',
          onSelected: (value) {
            switch (value) {
              case 'add_item':
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const AddItemScreen()),
                );
                break;
              case 'create_invoice':
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const CreateInvoiceScreen()),
                );
                break;
              case 'sync':
                if (!_isSyncing) _syncFromFirebase();
                break;
              case 'notifications':
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const EnhancedNotificationsScreen()),
                );
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'add_item',
              child: ListTile(
                leading: Icon(Icons.add_box_outlined),
                title: Text('إضافة عنصر جديد'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'create_invoice',
              child: ListTile(
                leading: Icon(Icons.receipt_long_outlined),
                title: Text('إنشاء فاتورة جديدة'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'sync',
              child: ListTile(
                leading: Icon(Icons.sync),
                title: Text('تزامن البيانات'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'notifications',
              child: ListTile(
                leading: Icon(Icons.notifications_outlined),
                title: Text('الإشعارات'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _handleProfileMenuAction(String value, AuthProvider authProvider) {
    // TODO: Implement profile menu actions
  }

  Widget _buildBody(AuthProvider authProvider) {
    return const Center(
      child: Text('محتوى التطبيق'),
    );
  }

  Widget _buildBottomNavigationBar(AuthProvider authProvider) {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: (index) {
        setState(() {
          _selectedIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'لوحة التحكم',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.inventory),
          label: 'المخزون',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.receipt),
          label: 'المبيعات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.analytics),
          label: 'التقارير',
        ),
      ],
    );
  }

  Widget _buildDrawer(AuthProvider authProvider) {
    return Drawer(
      child: ListView(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: AppColors.primary,
            ),
            child: const Text(
              'القائمة',
              style: TextStyle(color: Colors.white, fontSize: 24),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.home),
            title: const Text('الرئيسية'),
            onTap: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  Future<void> _syncFromFirebase() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      // await AutoSyncService.instance.performSync();
      // Temporary placeholder for sync functionality
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في المزامنة: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  String _getPageTitle(int index) {
    switch (index) {
      case 0:
        return 'لوحة التحكم';
      case 1:
        return 'إدارة المخزون';
      case 2:
        return 'إدارة المبيعات';
      case 3:
        return 'التقارير والإحصائيات';
      default:
        return 'نظام إدارة النقل';
    }
  }
}
