import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_constants.dart';
import '../../core/theme/app_colors.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';
import '../../services/permissions_service.dart';
import '../agents/agent_management_screen.dart';
import '../inventory/transfer_goods_screen.dart';
import '../auth/login_screen.dart';
import '../inventory/inventory_screen.dart';
import '../inventory/add_item_screen.dart';
import '../sales/create_invoice_screen.dart';
import '../sales/sales_screen.dart';
import '../documents/document_tracking_screen.dart';
import '../documents/document_dashboard_screen.dart';
import '../notifications/enhanced_notifications_screen.dart';
import '../../services/enhanced_notification_service.dart';

import '../agents/agent_account_screen.dart';
import '../admin/admin_settings_screen.dart';
import '../admin/warehouse_management_screen.dart';
import '../reports/reports_screen.dart';
import '../users/user_management_screen.dart';
import '../agents/comprehensive_agent_management_screen.dart';
import '../agents/agent_my_account_screen.dart';
import '../settings/settings_screen.dart';
import '../android/android_design_test_screen.dart';
import '../android/android_feature_test_screen.dart';
// import '../about/about_screen.dart'; // Removed - not compatible with Windows

import '../admin/company_poster_screen.dart';
import '../reports/customer_inquiry_screen.dart' as reports;
import '../reports/warehouse_movement_reports_screen.dart';
import '../reports/advanced_warehouse_reports_screen.dart';
import '../accounting/accounting_journal_screen.dart';
import '../../widgets/accounting_journal_dashboard_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  bool _isSyncing = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // If not authenticated, redirect to login
        if (!authProvider.isAuthenticated) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const LoginScreen()),
            );
          });
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        return Scaffold(
          backgroundColor: Colors.grey[50], // خلفية فاتحة ومريحة للعين
          appBar: _buildAppBar(authProvider),
          body: _buildBody(authProvider),
          bottomNavigationBar: _buildBottomNavigationBar(authProvider),
          drawer: _buildDrawer(authProvider),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(AuthProvider authProvider) {
    return AppBar(
      elevation: 0,
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      title: LayoutBuilder(
        builder: (context, constraints) {
          // تحديد العرض المتاح للعنوان
          final availableWidth = constraints.maxWidth;
          final showFullTitle = availableWidth > 300;

          return Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColors.whiteWithOpacity(0.15),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: AppColors.whiteWithOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Image.asset(
                  'assets/images/logo.png',
                  height: 18,
                  width: 18,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(Icons.business, size: 18, color: Colors.white);
                  },
                ),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _getPageTitle(_selectedIndex),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.white,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (showFullTitle) ...[
                      Text(
                        'نظام إدارة النقل - الفرحان',
                        style: TextStyle(
                          fontSize: 10,
                          color: AppColors.whiteWithOpacity(0.8),
                          fontWeight: FontWeight.normal,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          );
        },
      ),
      toolbarHeight: 70, // Taller for desktop
      leading: Builder(
        builder: (context) => IconButton(
          icon: const Icon(Icons.menu),
          onPressed: () => Scaffold.of(context).openDrawer(),
          tooltip: 'القائمة',
        ),
      ),
      actions: [
        // Quick actions toolbar
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            children: [
              // Quick add item
              IconButton(
                icon: const Icon(Icons.add_box_outlined),
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const AddItemScreen()),
                ),
                tooltip: 'إضافة عنصر جديد',
              ),
              // Quick create invoice
              IconButton(
                icon: const Icon(Icons.receipt_long_outlined),
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const CreateInvoiceScreen()),
                ),
                tooltip: 'إنشاء فاتورة جديدة',
              ),
              const VerticalDivider(color: Colors.white54, width: 20),
              // Sync button
              IconButton(
                icon: _isSyncing
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.sync),
                onPressed: _isSyncing ? null : () => _syncFromFirebase(),
                tooltip: _isSyncing ? 'جاري التزامن...' : 'تزامن البيانات',
              ),
              // Notification icon with badge
              FutureBuilder<int>(
                future: EnhancedNotificationService.instance.getUnreadNotificationsCount(),
                builder: (context, snapshot) {
                  final unreadCount = snapshot.data ?? 0;

                  return Stack(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.notifications_outlined),
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(builder: (context) => const EnhancedNotificationsScreen()),
                          );
                        },
                        tooltip: 'الإشعارات',
                      ),
                      if (unreadCount > 0)
                        Positioned(
                          right: 8,
                          top: 8,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 16,
                              minHeight: 16,
                            ),
                            child: Text(
                              unreadCount > 99 ? '99+' : unreadCount.toString(),
                              style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
              const VerticalDivider(color: Colors.white54, width: 20),
              // Profile menu
              PopupMenuButton<String>(
          onSelected: (value) => _handleProfileMenuAction(value, authProvider),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: ListTile(
                leading: Icon(Icons.person_outline),
                title: Text('الملف الشخصي'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings_outlined),
                title: Text('الإعدادات'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: ListTile(
                leading: Icon(Icons.logout, color: Colors.red),
                title: Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.white.withOpacity(0.2),
                      child: Text(
                        authProvider.currentUser?.fullName.substring(0, 1).toUpperCase() ?? 'م',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          authProvider.currentUser?.fullName ?? 'مستخدم',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          authProvider.userRoleDisplayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.8),
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ],
                    ),
                    const SizedBox(width: 4),
                    const Icon(Icons.arrow_drop_down, color: Colors.white),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBody(AuthProvider authProvider) {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboard(authProvider);
      case 1:
        return _buildInventoryView(authProvider);
      case 2:
        return _buildSalesView(authProvider);
      case 3:
        return _buildReportsView(authProvider);
      default:
        return _buildDashboard(authProvider);
    }
  }

  Widget _buildDashboard(AuthProvider authProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome message with modern design
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryWithOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: AppColors.blackWithOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.dashboard_outlined,
                    size: 32,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مرحباً، ${authProvider.currentUser?.fullName}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        authProvider.userRoleDisplayName,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'لوحة التحكم الرئيسية',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      const Icon(Icons.access_time, color: Colors.white, size: 20),
                      const SizedBox(height: 4),
                      Text(
                        'اليوم',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 10,
                        ),
                      ),
                      Text(
                        '${DateTime.now().day}/${DateTime.now().month}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),

          // Quick actions grid with modern design
          Row(
            children: [
              const Icon(Icons.flash_on, color: Colors.orange, size: 24),
              const SizedBox(width: 8),
              Text(
                'الإجراءات السريعة',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Responsive grid with improved layout
          LayoutBuilder(
            builder: (context, constraints) {
              int crossAxisCount = 2;
              double childAspectRatio = 1.1;

              if (constraints.maxWidth > 1200) {
                crossAxisCount = 4;
                childAspectRatio = 1.0;
              } else if (constraints.maxWidth > 800) {
                crossAxisCount = 3;
                childAspectRatio = 1.05;
              } else if (constraints.maxWidth > 600) {
                crossAxisCount = 2;
                childAspectRatio = 1.1;
              } else {
                crossAxisCount = 2;
                childAspectRatio = 1.15;
              }

              final cards = _buildQuickActionCards(authProvider);

              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: childAspectRatio,
                ),
                itemCount: cards.length,
                itemBuilder: (context, index) => cards[index],
              );
            },
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Accounting Journal Dashboard Card removed from main screen
        ],
      ),
    );
  }

  List<Widget> _buildQuickActionCards(AuthProvider authProvider) {
    final List<Widget> cards = [];

    // Add item card (for admins and super admins)
    if (authProvider.canManageInventory) {
      cards.add(_buildQuickActionCard(
        title: 'إضافة صنف جديد',
        icon: Icons.add_box_outlined,
        color: AppColors.success,
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const AddItemScreen()),
          );
        },
      ));
    }

    // Create invoice card (for all authenticated users)
    if (authProvider.canCreateInvoices) {
      cards.add(_buildQuickActionCard(
        title: 'إنشاء فاتورة بيع',
        icon: Icons.receipt_long_outlined,
        color: AppColors.sales,
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const CreateInvoiceScreen()),
          );
        },
      ));
    }

    // View inventory card
    cards.add(_buildQuickActionCard(
      title: 'عرض المخزون',
      icon: Icons.inventory_2_outlined,
      color: AppColors.inventory,
      onTap: () {
        setState(() {
          _selectedIndex = 1;
        });
      },
    ));

    // Document dashboard card (for managers and super admins)
    if (authProvider.canViewReports) {
      cards.add(_buildQuickActionCard(
        title: 'لوحة معلومات الجوابات',
        icon: Icons.dashboard_outlined,
        color: AppColors.reports,
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const DocumentDashboardScreen()),
          );
        },
      ));
    }

    // Accounting Journal card removed from quick actions

    // Document tracking card
    cards.add(_buildQuickActionCard(
      title: 'تتبع الجوابات',
      icon: Icons.track_changes_outlined,
      color: AppColors.accounting,
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const DocumentTrackingScreen()),
        );
      },
    ));

    // Reports card (for admins and super admins)
    if (authProvider.canViewReports) {
      cards.add(_buildQuickActionCard(
        title: 'التقارير',
        icon: Icons.analytics_outlined,
        color: AppColors.reports,
        onTap: () {
          setState(() {
            _selectedIndex = 3;
          });
        },
      ));
    }

    // Agent account card (for agents)
    if (authProvider.isAgent) {
      cards.add(_buildQuickActionCard(
        title: 'حسابي',
        icon: Icons.account_balance_wallet_outlined,
        color: AppColors.agents,
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const AgentAccountScreen()),
          );
        },
      ));
    }

    // Agent management card (for admins and super admins)
    if (authProvider.canManageUsers) {
      cards.add(_buildQuickActionCard(
        title: 'إدارة الوكلاء',
        icon: Icons.people_outline,
        color: AppColors.agents,
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const AgentManagementScreen()),
          );
        },
      ));
    }

    // Warehouse management card (for admins and super admins)
    if (authProvider.canManageInventory) {
      cards.add(_buildQuickActionCard(
        title: 'إدارة المخازن',
        icon: Icons.warehouse_outlined,
        color: Colors.brown,
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const WarehouseManagementScreen()),
          );
        },
      ));
    }

    // Warehouse movement reports card (for admins and super admins)
    if (authProvider.canViewReports) {
      cards.add(_buildQuickActionCard(
        title: 'تقارير حركة المخازن المتطورة',
        icon: Icons.swap_horiz_outlined,
        color: Colors.cyan,
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const AdvancedWarehouseReportsScreen(),
            ),
          );
        },
      ));
    }

    // Comprehensive customer inquiry card (for all authenticated users)
    cards.add(_buildQuickActionCard(
      title: 'استعلام العملاء الشامل',
      icon: Icons.manage_search_outlined,
      color: AppColors.info,
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const reports.ComprehensiveCustomerInquiryScreen()),
        );
      },
    ));

    // Android Design Test Card (for testing)
    cards.add(_buildQuickActionCard(
      title: 'اختبار التصميم الجديد',
      icon: Icons.palette_outlined,
      color: AppColors.secondary,
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const AndroidDesignTestScreen()),
        );
      },
    ));

    // Android Feature Test Card (for testing)
    cards.add(_buildQuickActionCard(
      title: 'اختبار ميزات Android',
      icon: Icons.android_outlined,
      color: AppColors.success,
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const AndroidFeatureTestScreen()),
        );
      },
    ));

    return cards;
  }

  Widget _buildQuickActionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
            spreadRadius: 2,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: color.withOpacity(0.1),
          width: 1.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          splashColor: color.withOpacity(0.1),
          highlightColor: color.withOpacity(0.05),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(14),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        color.withOpacity(0.15),
                        color.withOpacity(0.08),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: color.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    icon,
                    size: 28,
                    color: color,
                  ),
                ),
                const SizedBox(height: 12),
                Flexible(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                      fontSize: 13,
                      height: 1.2,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInventoryView(AuthProvider authProvider) {
    return const InventoryScreen();
  }

  Widget _buildSalesView(AuthProvider authProvider) {
    return const SalesScreen();
  }

  Widget _buildReportsView(AuthProvider authProvider) {
    return const ReportsScreen();
  }



  Widget? _buildBottomNavigationBar(AuthProvider authProvider) {
    final List<BottomNavigationBarItem> items = [
      const BottomNavigationBarItem(
        icon: Icon(Icons.dashboard_outlined),
        activeIcon: Icon(Icons.dashboard),
        label: 'الرئيسية',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.inventory_2_outlined),
        activeIcon: Icon(Icons.inventory_2),
        label: 'المخزون',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.point_of_sale_outlined),
        activeIcon: Icon(Icons.point_of_sale),
        label: 'المبيعات',
      ),
    ];

    // Add reports tab for users with permission
    if (authProvider.canViewReports) {
      items.add(
        const BottomNavigationBarItem(
          icon: Icon(Icons.analytics_outlined),
          activeIcon: Icon(Icons.analytics),
          label: 'التقارير',
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2E7D32).withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, -5),
            spreadRadius: 1,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.white,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: Colors.grey[600],
        selectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 11,
        ),
        elevation: 0,
        items: items,
      ),
    );
  }

  Widget? _buildDrawer(AuthProvider authProvider) {
    if (authProvider.currentUser == null) return null;

    final menuItems = PermissionsService.getMenuItems(authProvider.currentUser!);

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Company Logo
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.asset(
                          'assets/images/logo.png',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.motorcycle,
                              size: 30,
                              color: Theme.of(context).colorScheme.primary,
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // User Avatar
                    CircleAvatar(
                      radius: 25,
                      backgroundColor: Theme.of(context).colorScheme.onPrimary,
                      child: Icon(
                        Icons.person,
                        size: 25,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Text(
                  authProvider.currentUser?.fullName ?? '',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  authProvider.userRoleDisplayName,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          // Dynamic menu items based on permissions
          ...menuItems.map((item) => _buildMenuItem(item)),

          const Divider(),

          // About
          ListTile(
            leading: const Icon(Icons.info_outline),
            title: const Text('حول التطبيق'),
            onTap: () {
              Navigator.pop(context);
              // AboutScreen removed - not compatible with Windows
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('شاشة حول التطبيق غير متاحة في نسخة Windows')),
              );
            },
          ),

          // Logout
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
            onTap: () => _handleLogout(authProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(MenuItem item) {
    IconData iconData = _getIconData(item.icon);

    if (item.children != null && item.children!.isNotEmpty) {
      // Expandable menu item with children
      return ExpansionTile(
        leading: Icon(iconData),
        title: Text(item.title),
        children: item.children!
            .where((child) => child.isVisible)
            .map((child) => Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: ListTile(
                    leading: Icon(_getIconData(child.icon)),
                    title: Text(child.title),
                    onTap: () => _handleMenuNavigation(child.route),
                  ),
                ))
            .toList(),
      );
    } else {
      // Simple menu item
      return ListTile(
        leading: Icon(iconData),
        title: Text(item.title),
        onTap: () => _handleMenuNavigation(item.route),
      );
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'home':
        return Icons.home_outlined;
      case 'sales':
        return Icons.point_of_sale_outlined;
      case 'invoice':
        return Icons.receipt_outlined;
      case 'list':
        return Icons.list_outlined;
      case 'inventory':
        return Icons.inventory_2_outlined;
      case 'warehouse':
        return Icons.warehouse_outlined;
      case 'showroom':
        return Icons.store_outlined;
      case 'agents':
        return Icons.people_outline;
      case 'transfer':
        return Icons.swap_horiz_outlined;
      case 'my_inventory':
        return Icons.inventory_outlined;
      case 'showroom_inventory':
        return Icons.store_mall_directory_outlined;
      case 'agents_management':
        return Icons.manage_accounts_outlined;
      case 'accounts':
        return Icons.account_balance_outlined;
      case 'payments':
        return Icons.payment_outlined;
      case 'statements':
        return Icons.description_outlined;
      case 'my_account':
        return Icons.account_circle_outlined;
      case 'statement':
        return Icons.receipt_long_outlined;
      case 'my_payments':
        return Icons.payments_outlined;
      case 'tracking':
        return Icons.track_changes_outlined;
      case 'reports':
        return Icons.analytics_outlined;
      case 'sales_reports':
        return Icons.trending_up_outlined;
      case 'profit_reports':
        return Icons.monetization_on_outlined;
      case 'inventory_reports':
        return Icons.assessment_outlined;
      case 'users':
        return Icons.people_outline;
      case 'settings':
        return Icons.settings_outlined;
      case 'notifications':
        return Icons.notifications_outlined;
      case 'testing':
        return Icons.bug_report_outlined;
      case 'customer_inquiry':
        return Icons.manage_search_outlined;
      case 'company_poster':
        return Icons.image_outlined;
      default:
        return Icons.circle_outlined;
    }
  }

  void _handleMenuNavigation(String route) {
    Navigator.pop(context); // Close drawer

    switch (route) {
      case '/home':
        setState(() {
          _selectedIndex = 0;
        });
        break;
      case '/sales':
      case '/sales/create-invoice':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const CreateInvoiceScreen()),
        );
        break;
      case '/sales/invoices':
        setState(() {
          _selectedIndex = 2;
        });
        break;
      case '/inventory':
      case '/inventory/main':
      case '/inventory/showroom':
      case '/inventory/agents':
        setState(() {
          _selectedIndex = 1;
        });
        break;
      case '/inventory/transfer':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const TransferGoodsScreen()),
        );
        break;
      case '/agent/inventory':
        setState(() {
          _selectedIndex = 1;
        });
        break;
      case '/showroom/inventory':
        setState(() {
          _selectedIndex = 1;
        });
        break;
      case '/agents':
      case '/agents/accounts':
      case '/agents/payments':
      case '/agents/statements':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ComprehensiveAgentManagementScreen()),
        );
        break;
      case '/agent/account':
      case '/agent/statement':
      case '/agent/payments':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const AgentMyAccountScreen()),
        );
        break;
      case '/documents/tracking':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const DocumentTrackingScreen()),
        );
        break;
      case '/reports':
      case '/reports/sales':
      case '/reports/profits':
      case '/reports/inventory':
        setState(() {
          _selectedIndex = 3;
        });
        break;
      case '/admin/agents':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ComprehensiveAgentManagementScreen()),
        );
        break;
      case '/users':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const UserManagementScreen()),
        );
        break;
      case '/admin/warehouses':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const WarehouseManagementScreen()),
        );
        break;
      case '/settings':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SettingsScreen()),
        );
        break;
      case '/notifications':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const EnhancedNotificationsScreen()),
        );
        break;

      case '/reports/customer-inquiry':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const reports.ComprehensiveCustomerInquiryScreen()),
        );
        break;
      case '/admin/company-poster':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const CompanyPosterScreen()),
        );
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('الشاشة $route قيد التطوير')),
        );
    }
  }

  void _handleProfileMenuAction(String action, AuthProvider authProvider) {
    switch (action) {
      case 'profile':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SettingsScreen()),
        );
        break;
      case 'settings':
        if (authProvider.currentUser?.role == AppConstants.superAdminRole) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AdminSettingsScreen()),
          );
        } else {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SettingsScreen()),
          );
        }
        break;
      case 'logout':
        _handleLogout(authProvider);
        break;
    }
  }

  Future<void> _handleLogout(AuthProvider authProvider) async {
    try {
      await authProvider.signOut();
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تسجيل الخروج: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }





  /// Sync data from Firebase
  Future<void> _syncFromFirebase() async {
    if (_isSyncing) return;

    setState(() {
      _isSyncing = true;
    });

    try {
      await DataService.instance.syncFromFirebase();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تزامن البيانات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تزامن البيانات: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
      }
    }
  }

  String _getPageTitle(int index) {
    switch (index) {
      case 0:
        return 'لوحة التحكم';
      case 1:
        return 'إدارة المخزون';
      case 2:
        return 'إدارة المبيعات';
      case 3:
        return 'التقارير والإحصائيات';
      default:
        return 'نظام إدارة النقل';
    }
  }
}
