import 'package:flutter/material.dart';

class SlidePageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  final SlideDirection direction;
  final Duration duration;
  final Curve curve;

  SlidePageRoute({
    required this.page,
    this.direction = SlideDirection.rightToLeft,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: _getSlideAnimation(animation, direction, curve),
              child: child,
            );
          },
        );

  static Animation<Offset> _getSlideAnimation(
    Animation<double> animation,
    SlideDirection direction,
    Curve curve,
  ) {
    Offset begin;
    switch (direction) {
      case SlideDirection.rightToLeft:
        begin = const Offset(1.0, 0.0);
        break;
      case SlideDirection.leftToRight:
        begin = const Offset(-1.0, 0.0);
        break;
      case SlideDirection.topToBottom:
        begin = const Offset(0.0, -1.0);
        break;
      case SlideDirection.bottomToTop:
        begin = const Offset(0.0, 1.0);
        break;
    }

    return Tween<Offset>(
      begin: begin,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: curve,
    ));
  }
}

enum SlideDirection {
  rightToLeft,
  leftToRight,
  topToBottom,
  bottomToTop,
}

class FadePageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  final Duration duration;
  final Curve curve;

  FadePageRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: CurvedAnimation(
                parent: animation,
                curve: curve,
              ),
              child: child,
            );
          },
        );
}

class ScalePageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  final Duration duration;
  final Curve curve;
  final double initialScale;

  ScalePageRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.initialScale = 0.8,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return ScaleTransition(
              scale: Tween<double>(
                begin: initialScale,
                end: 1.0,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: curve,
              )),
              child: FadeTransition(
                opacity: animation,
                child: child,
              ),
            );
          },
        );
}

class RotationPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  final Duration duration;
  final Curve curve;

  RotationPageRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 400),
    this.curve = Curves.easeInOut,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return RotationTransition(
              turns: Tween<double>(
                begin: 0.0,
                end: 1.0,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: curve,
              )),
              child: ScaleTransition(
                scale: Tween<double>(
                  begin: 0.8,
                  end: 1.0,
                ).animate(CurvedAnimation(
                  parent: animation,
                  curve: curve,
                )),
                child: child,
              ),
            );
          },
        );
}

class SizePageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  final Duration duration;
  final Curve curve;
  final Alignment alignment;

  SizePageRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.alignment = Alignment.center,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return Align(
              alignment: alignment,
              child: SizeTransition(
                sizeFactor: CurvedAnimation(
                  parent: animation,
                  curve: curve,
                ),
                child: child,
              ),
            );
          },
        );
}

class CustomPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  final Duration duration;
  final Widget Function(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) _transitionsBuilder;

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return _transitionsBuilder(context, animation, secondaryAnimation, child);
  }

  CustomPageRoute({
    required this.page,
    required Widget Function(
      BuildContext context,
      Animation<double> animation,
      Animation<double> secondaryAnimation,
      Widget child,
    ) transitionsBuilder,
    this.duration = const Duration(milliseconds: 300),
  }) : _transitionsBuilder = transitionsBuilder,
       super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
        );
}

// Helper class for easy navigation with animations
class AnimatedNavigator {
  static Future<T?> push<T extends Object?>(
    BuildContext context,
    Widget page, {
    PageTransitionType type = PageTransitionType.slide,
    SlideDirection direction = SlideDirection.rightToLeft,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    PageRoute<T> route;

    switch (type) {
      case PageTransitionType.slide:
        route = SlidePageRoute<T>(
          page: page,
          direction: direction,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.fade:
        route = FadePageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.scale:
        route = ScalePageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.rotation:
        route = RotationPageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.size:
        route = SizePageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
    }

    return Navigator.push(context, route);
  }

  static Future<T?> pushReplacement<T extends Object?, TO extends Object?>(
    BuildContext context,
    Widget page, {
    PageTransitionType type = PageTransitionType.slide,
    SlideDirection direction = SlideDirection.rightToLeft,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
    TO? result,
  }) {
    PageRoute<T> route;

    switch (type) {
      case PageTransitionType.slide:
        route = SlidePageRoute<T>(
          page: page,
          direction: direction,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.fade:
        route = FadePageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.scale:
        route = ScalePageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.rotation:
        route = RotationPageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.size:
        route = SizePageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
    }

    return Navigator.pushReplacement(context, route, result: result);
  }

  static Future<T?> pushAndRemoveUntil<T extends Object?>(
    BuildContext context,
    Widget page,
    RoutePredicate predicate, {
    PageTransitionType type = PageTransitionType.slide,
    SlideDirection direction = SlideDirection.rightToLeft,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    PageRoute<T> route;

    switch (type) {
      case PageTransitionType.slide:
        route = SlidePageRoute<T>(
          page: page,
          direction: direction,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.fade:
        route = FadePageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.scale:
        route = ScalePageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.rotation:
        route = RotationPageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
      case PageTransitionType.size:
        route = SizePageRoute<T>(
          page: page,
          duration: duration,
          curve: curve,
        );
        break;
    }

    return Navigator.pushAndRemoveUntil(context, route, predicate);
  }
}

enum PageTransitionType {
  slide,
  fade,
  scale,
  rotation,
  size,
}
