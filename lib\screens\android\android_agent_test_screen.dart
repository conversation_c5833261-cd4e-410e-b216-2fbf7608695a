import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../services/android/android_agent_service.dart';
import '../../services/data_service.dart';
import '../../core/utils/app_utils.dart';

/// Android-specific screen for testing and validating agent account calculations
class AndroidAgentTestScreen extends StatefulWidget {
  const AndroidAgentTestScreen({super.key});

  @override
  State<AndroidAgentTestScreen> createState() => _AndroidAgentTestScreenState();
}

class _AndroidAgentTestScreenState extends State<AndroidAgentTestScreen> {
  final AndroidAgentService _agentService = AndroidAgentService.instance;
  final DataService _dataService = DataService.instance;
  
  bool _isLoading = false;
  List<Map<String, dynamic>> _agents = [];
  Map<String, Map<String, dynamic>> _calculationResults = {};
  Map<String, Map<String, dynamic>> _validationResults = {};

  @override
  void initState() {
    super.initState();
    _loadAgents();
  }

  Future<void> _loadAgents() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final agents = await _dataService.getUsersByRole('agent');
      setState(() {
        _agents = agents.map((agent) => {
          'id': agent.id,
          'name': agent.fullName,
          'phone': agent.phone,
          'username': agent.username,
        }).toList();
      });

      if (kDebugMode) {
        print('📊 Loaded ${_agents.length} agents for testing');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading agents: $e');
      }
      
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'خطأ في تحميل الوكلاء: $e',
          isError: true,
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testAgentCalculations(String agentId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('🧪 Testing calculations for agent: $agentId');
      }

      // Calculate using Android service
      final calculationResult = await _agentService.calculateAgentAccountData(agentId);
      
      // Validate against existing account
      final validationResult = await _agentService.validateAgentAccount(agentId);

      setState(() {
        _calculationResults[agentId] = calculationResult;
        _validationResults[agentId] = validationResult;
      });

      if (kDebugMode) {
        print('✅ Test completed for agent: $agentId');
        print('   Calculation result: ${calculationResult['currentBalance']} ج.م');
        print('   Validation: ${validationResult['isValid']}');
      }

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'تم اختبار حسابات الوكيل بنجاح',
          isError: false,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error testing agent calculations: $e');
      }
      
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'خطأ في اختبار الحسابات: $e',
          isError: true,
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _recalculateAgent(String agentId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('🔄 Recalculating agent account: $agentId');
      }

      await _agentService.createOrUpdateAgentAccount(agentId);
      
      // Re-test after recalculation
      await _testAgentCalculations(agentId);

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'تم إعادة حساب حساب الوكيل بنجاح',
          isError: false,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error recalculating agent: $e');
      }
      
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'خطأ في إعادة الحساب: $e',
          isError: true,
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _recalculateAllAgents() async {
    setState(() {
      _isLoading = true;
    });

    try {
      if (kDebugMode) {
        print('🔄 Recalculating all agent accounts...');
      }

      await _agentService.recalculateAllAgentAccounts();
      
      // Re-test all agents
      for (final agent in _agents) {
        await _testAgentCalculations(agent['id']);
      }

      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'تم إعادة حساب جميع حسابات الوكلاء بنجاح',
          isError: false,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error recalculating all agents: $e');
      }
      
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'خطأ في إعادة حساب جميع الحسابات: $e',
          isError: true,
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار حسابات الوكلاء - Android'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _loadAgents,
            tooltip: 'إعادة تحميل',
          ),
          IconButton(
            icon: const Icon(Icons.calculate),
            onPressed: _isLoading ? null : _recalculateAllAgents,
            tooltip: 'إعادة حساب الكل',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري المعالجة...'),
                ],
              ),
            )
          : _agents.isEmpty
              ? const Center(
                  child: Text(
                    'لا توجد وكلاء',
                    style: TextStyle(fontSize: 18),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _agents.length,
                  itemBuilder: (context, index) {
                    final agent = _agents[index];
                    final agentId = agent['id'];
                    final calculationResult = _calculationResults[agentId];
                    final validationResult = _validationResults[agentId];

                    return Card(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Agent Info
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        agent['name'],
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Text(
                                        'المعرف: ${agent['username']}',
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                      if (agent['phone'] != null)
                                        Text(
                                          'الهاتف: ${agent['phone']}',
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                // Action Buttons
                                Column(
                                  children: [
                                    ElevatedButton.icon(
                                      onPressed: () => _testAgentCalculations(agentId),
                                      icon: const Icon(Icons.calculate, size: 16),
                                      label: const Text('اختبار'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    ElevatedButton.icon(
                                      onPressed: () => _recalculateAgent(agentId),
                                      icon: const Icon(Icons.refresh, size: 16),
                                      label: const Text('إعادة حساب'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),

                            // Calculation Results
                            if (calculationResult != null) ...[
                              const SizedBox(height: 16),
                              const Divider(),
                              const Text(
                                'نتائج الحساب:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              _buildCalculationResultsWidget(calculationResult),
                            ],

                            // Validation Results
                            if (validationResult != null) ...[
                              const SizedBox(height: 16),
                              const Divider(),
                              const Text(
                                'نتائج التحقق:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              _buildValidationResultsWidget(validationResult),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
    );
  }

  Widget _buildCalculationResultsWidget(Map<String, dynamic> result) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildResultRow('إجمالي الدين:', '${result['totalDebt']} ج.م'),
          _buildResultRow('إجمالي الائتمان:', '${result['totalCredits']} ج.م'),
          _buildResultRow('إجمالي المدفوعات:', '${result['totalPaid']} ج.م'),
          _buildResultRow('الرصيد الحالي:', '${result['currentBalance']} ج.م', 
              isBalance: true),
          _buildResultRow('إجمالي المبيعات:', '${result['totalSales']} ج.م'),
          _buildResultRow('أرباح الوكيل:', '${result['totalAgentProfits']} ج.م'),
          _buildResultRow('المعاملات:', '${result['transactions']?.length ?? 0}'),
        ],
      ),
    );
  }

  Widget _buildValidationResultsWidget(Map<String, dynamic> result) {
    final isValid = result['isValid'] ?? false;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isValid ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isValid ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                isValid ? Icons.check_circle : Icons.error,
                color: isValid ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                isValid ? 'الحساب صحيح' : 'الحساب يحتاج مراجعة',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isValid ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          if (result['storedBalance'] != null)
            _buildResultRow('الرصيد المحفوظ:', '${result['storedBalance']} ج.م'),
          if (result['calculatedBalance'] != null)
            _buildResultRow('الرصيد المحسوب:', '${result['calculatedBalance']} ج.م'),
          if (result['difference'] != null)
            _buildResultRow('الفرق:', '${result['difference']} ج.م'),
          if (result['recommendation'] != null)
            Text(
              'التوصية: ${result['recommendation']}',
              style: const TextStyle(fontStyle: FontStyle.italic),
            ),
        ],
      ),
    );
  }

  Widget _buildResultRow(String label, String value, {bool isBalance = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: TextStyle(
              fontWeight: isBalance ? FontWeight.bold : FontWeight.normal,
              color: isBalance ? Theme.of(context).primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }
}
