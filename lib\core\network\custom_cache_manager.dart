import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

/// Custom cache manager that bypasses SSL verification for Cloudinary images
class CloudinaryCacheManager extends CacheManager with ImageCacheManager {
  static const key = 'cloudinaryCache';
  
  static final CloudinaryCacheManager _instance = CloudinaryCacheManager._();
  factory CloudinaryCacheManager() => _instance;
  
  CloudinaryCacheManager._() : super(
    Config(
      key,
      stalePeriod: const Duration(days: 7),
      maxNrOfCacheObjects: 200,
      repo: JsonCacheInfoRepository(databaseName: key),
      fileService: CloudinaryHttpFileService(),
    ),
  );
}

/// Custom HTTP file service that bypasses SSL verification
class CloudinaryHttpFileService extends FileService {
  late final http.Client _httpClient;

  CloudinaryHttpFileService() {
    _initializeHttpClient();
  }

  void _initializeHttpClient() {
    if (kDebugMode) {
      print('🔧 Initializing Cloudinary HTTP file service...');
    }

    // Create HttpClient with SSL bypass
    final httpClient = HttpClient();
    
    // Bypass SSL certificate verification completely
    httpClient.badCertificateCallback = (cert, host, port) {
      if (kDebugMode) {
        print('🔓 Cloudinary Cache: Bypassing SSL for $host:$port');
      }
      return true; // Allow all certificates
    };

    // Set timeouts
    httpClient.connectionTimeout = const Duration(seconds: 30);
    httpClient.idleTimeout = const Duration(seconds: 30);

    // Create IOClient wrapper
    _httpClient = IOClient(httpClient);

    if (kDebugMode) {
      print('✅ Cloudinary HTTP file service initialized');
    }
  }

  @override
  Future<FileServiceResponse> get(String url, {Map<String, String>? headers}) async {
    try {
      // Convert HTTPS Cloudinary URLs to HTTP to bypass SSL
      String finalUrl = url;
      if (url.contains('cloudinary.com') && url.startsWith('https://')) {
        finalUrl = url.replaceFirst('https://', 'http://');
        if (kDebugMode) {
          print('🔧 Cloudinary Cache: Converting HTTPS to HTTP');
          print('   Original: $url');
          print('   HTTP: $finalUrl');
        }
      }

      if (kDebugMode) {
        print('📥 Cloudinary Cache: Downloading $finalUrl');
      }

      final response = await _httpClient.get(
        Uri.parse(finalUrl),
        headers: {
          'User-Agent': 'El-Farhan-Desktop/1.0',
          'Accept': 'image/*',
          'Cache-Control': 'no-cache',
          ...?headers,
        },
      ).timeout(const Duration(seconds: 30));

      if (kDebugMode) {
        print('📥 Cloudinary Cache: Response ${response.statusCode} for $url');
      }

      return HttpGetResponse(response);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Cloudinary Cache: Error downloading $url - $e');
      }
      rethrow;
    }
  }

  void dispose() {
    _httpClient.close();
  }
}

/// HTTP response wrapper for cache manager
class HttpGetResponse implements FileServiceResponse {
  final http.Response _response;

  HttpGetResponse(this._response);

  @override
  Stream<List<int>> get content => Stream.value(_response.bodyBytes);

  @override
  String? get eTag => _response.headers['etag'];

  @override
  String get fileExtension {
    final contentType = _response.headers['content-type'];
    if (contentType != null) {
      if (contentType.contains('jpeg') || contentType.contains('jpg')) {
        return '.jpg';
      } else if (contentType.contains('png')) {
        return '.png';
      } else if (contentType.contains('gif')) {
        return '.gif';
      } else if (contentType.contains('webp')) {
        return '.webp';
      }
    }
    return '.jpg'; // Default
  }

  @override
  int? get contentLength => _response.contentLength;

  @override
  DateTime get validTill {
    final cacheControl = _response.headers['cache-control'];
    if (cacheControl != null) {
      final maxAge = RegExp(r'max-age=(\d+)').firstMatch(cacheControl);
      if (maxAge != null) {
        final seconds = int.tryParse(maxAge.group(1) ?? '');
        if (seconds != null) {
          return DateTime.now().add(Duration(seconds: seconds));
        }
      }
    }
    // Default to 7 days
    return DateTime.now().add(const Duration(days: 7));
  }

  @override
  int get statusCode => _response.statusCode;
}

/// Initialize the custom cache manager
void initializeCloudinaryCacheManager() {
  if (kDebugMode) {
    print('🔧 Initializing Cloudinary cache manager...');
  }
  
  // Initialize the cache manager
  CloudinaryCacheManager();
  
  if (kDebugMode) {
    print('✅ Cloudinary cache manager initialized');
  }
}
