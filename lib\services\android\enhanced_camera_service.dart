import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:permission_handler/permission_handler.dart';
import '../../core/utils/app_utils.dart';
import '../motor_data_extraction_service.dart';

/// خدمة كاميرا محسنة للأندرويد
/// تدعم التقاط الصور من الكاميرا أو المعرض مع تحسين الجودة واستخراج البيانات
class AndroidEnhancedCameraService {
  static final AndroidEnhancedCameraService _instance = AndroidEnhancedCameraService._internal();
  factory AndroidEnhancedCameraService() => _instance;
  AndroidEnhancedCameraService._internal();

  final ImagePicker _picker = ImagePicker();
  final MotorDataExtractionService _dataExtractionService = MotorDataExtractionService();

  /// التقاط صورة من الكاميرا
  Future<File?> captureImageFromCamera({
    bool enhanceQuality = true,
    int maxWidth = 1920,
    int maxHeight = 1080,
    int imageQuality = 85,
  }) async {
    try {
      if (kDebugMode) {
        print('📸 Capturing image from camera...');
      }

      // طلب إذن الكاميرا
      final cameraPermission = await Permission.camera.request();
      if (!cameraPermission.isGranted) {
        throw 'يجب السماح بالوصول للكاميرا';
      }

      // التقاط الصورة
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: maxWidth.toDouble(),
        maxHeight: maxHeight.toDouble(),
        imageQuality: imageQuality,
        preferredCameraDevice: CameraDevice.rear,
      );

      if (image == null) {
        if (kDebugMode) {
          print('❌ No image captured');
        }
        return null;
      }

      File imageFile = File(image.path);

      // تحسين جودة الصورة إذا كان مطلوباً
      if (enhanceQuality) {
        imageFile = await _enhanceImageQuality(imageFile);
      }

      if (kDebugMode) {
        print('✅ Image captured successfully: ${imageFile.path}');
      }

      return imageFile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error capturing image from camera: $e');
      }
      rethrow;
    }
  }

  /// اختيار صورة من المعرض
  Future<File?> pickImageFromGallery({
    bool enhanceQuality = true,
    int maxWidth = 1920,
    int maxHeight = 1080,
    int imageQuality = 85,
  }) async {
    try {
      if (kDebugMode) {
        print('🖼️ Picking image from gallery...');
      }

      // طلب إذن الوصول للتخزين
      final storagePermission = await Permission.storage.request();
      if (!storagePermission.isGranted) {
        // في Android 13+ نحتاج إذن photos
        final photosPermission = await Permission.photos.request();
        if (!photosPermission.isGranted) {
          throw 'يجب السماح بالوصول للصور';
        }
      }

      // اختيار الصورة
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: maxWidth.toDouble(),
        maxHeight: maxHeight.toDouble(),
        imageQuality: imageQuality,
      );

      if (image == null) {
        if (kDebugMode) {
          print('❌ No image selected');
        }
        return null;
      }

      File imageFile = File(image.path);

      // تحسين جودة الصورة إذا كان مطلوباً
      if (enhanceQuality) {
        imageFile = await _enhanceImageQuality(imageFile);
      }

      if (kDebugMode) {
        print('✅ Image selected successfully: ${imageFile.path}');
      }

      return imageFile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error picking image from gallery: $e');
      }
      rethrow;
    }
  }

  /// عرض خيارات اختيار الصورة (كاميرا أو معرض)
  Future<File?> showImageSourceOptions({
    required BuildContext context,
    bool enhanceQuality = true,
    int maxWidth = 1920,
    int maxHeight = 1080,
    int imageQuality = 85,
  }) async {
    try {
      final result = await showModalBottomSheet<String>(
        context: context,
        builder: (BuildContext context) {
          return SafeArea(
            child: Wrap(
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('التقاط صورة'),
                  onTap: () => Navigator.pop(context, 'camera'),
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('اختيار من المعرض'),
                  onTap: () => Navigator.pop(context, 'gallery'),
                ),
                ListTile(
                  leading: const Icon(Icons.cancel),
                  title: const Text('إلغاء'),
                  onTap: () => Navigator.pop(context, 'cancel'),
                ),
              ],
            ),
          );
        },
      );

      if (result == null || result == 'cancel') {
        return null;
      }

      if (result == 'camera') {
        return await captureImageFromCamera(
          enhanceQuality: enhanceQuality,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
          imageQuality: imageQuality,
        );
      } else if (result == 'gallery') {
        return await pickImageFromGallery(
          enhanceQuality: enhanceQuality,
          maxWidth: maxWidth,
          maxHeight: maxHeight,
          imageQuality: imageQuality,
        );
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error showing image source options: $e');
      }
      rethrow;
    }
  }

  /// التقاط صورة بصمة الموتور مع استخراج البيانات
  Future<Map<String, dynamic>?> captureMotorFingerprintWithData({
    required BuildContext context,
    bool enhanceQuality = true,
  }) async {
    try {
      if (kDebugMode) {
        print('🔍 Capturing motor fingerprint with data extraction...');
      }

      // التقاط الصورة
      final imageFile = await showImageSourceOptions(
        context: context,
        enhanceQuality: enhanceQuality,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 90,
      );

      if (imageFile == null) {
        return null;
      }

      // استخراج بيانات بصمة الموتور
      final extractedData = await _dataExtractionService.extractMotorFingerprintData(imageFile);

      return {
        'imageFile': imageFile,
        'extractedData': extractedData,
        'imagePath': imageFile.path,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error capturing motor fingerprint with data: $e');
      }
      rethrow;
    }
  }

  /// التقاط صورة رقم الشاسيه مع استخراج البيانات
  Future<Map<String, dynamic>?> captureChassisNumberWithData({
    required BuildContext context,
    bool enhanceQuality = true,
  }) async {
    try {
      if (kDebugMode) {
        print('🔍 Capturing chassis number with data extraction...');
      }

      // التقاط الصورة
      final imageFile = await showImageSourceOptions(
        context: context,
        enhanceQuality: enhanceQuality,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 90,
      );

      if (imageFile == null) {
        return null;
      }

      // استخراج رقم الشاسيه
      final extractedData = await _dataExtractionService.extractChassisNumber(imageFile);

      return {
        'imageFile': imageFile,
        'extractedData': extractedData,
        'imagePath': imageFile.path,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error capturing chassis number with data: $e');
      }
      rethrow;
    }
  }

  /// تحسين جودة الصورة
  Future<File> _enhanceImageQuality(File imageFile) async {
    try {
      if (kDebugMode) {
        print('🔧 Enhancing image quality...');
      }

      // قراءة الصورة
      final bytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(bytes);

      if (image == null) {
        if (kDebugMode) {
          print('❌ Could not decode image');
        }
        return imageFile;
      }

      // تطبيق فلاتر تحسين الصورة
      img.Image enhanced = image;

      // تحسين التباين
      enhanced = img.contrast(enhanced, contrast: 110);

      // تطبيق فلتر شحذ خفيف
      enhanced = img.convolution(enhanced, filter: [
        0, -1, 0,
        -1, 5, -1,
        0, -1, 0
      ]);

      // تحسين الوضوح
      enhanced = img.adjustColor(enhanced, saturation: 1.1);

      // ضغط الصورة المحسنة
      final enhancedBytes = img.encodeJpg(enhanced, quality: 90);

      // حفظ الصورة المحسنة
      final enhancedFile = File('${imageFile.path}_enhanced.jpg');
      await enhancedFile.writeAsBytes(enhancedBytes);

      if (kDebugMode) {
        print('✅ Image quality enhanced successfully');
      }

      return enhancedFile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error enhancing image quality: $e');
      }
      return imageFile; // إرجاع الصورة الأصلية في حالة الخطأ
    }
  }

  /// ضغط الصورة
  Future<File> compressImage(File imageFile, {int quality = 85}) async {
    try {
      if (kDebugMode) {
        print('📦 Compressing image...');
      }

      final bytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(bytes);

      if (image == null) {
        return imageFile;
      }

      // ضغط الصورة
      final compressedBytes = img.encodeJpg(image, quality: quality);

      // حفظ الصورة المضغوطة
      final compressedFile = File('${imageFile.path}_compressed.jpg');
      await compressedFile.writeAsBytes(compressedBytes);

      if (kDebugMode) {
        print('✅ Image compressed successfully');
        print('   Original size: ${bytes.length} bytes');
        print('   Compressed size: ${compressedBytes.length} bytes');
      }

      return compressedFile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error compressing image: $e');
      }
      return imageFile;
    }
  }

  /// تغيير حجم الصورة
  Future<File> resizeImage(
    File imageFile, {
    int? width,
    int? height,
    bool maintainAspectRatio = true,
  }) async {
    try {
      if (kDebugMode) {
        print('📏 Resizing image...');
      }

      final bytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(bytes);

      if (image == null) {
        return imageFile;
      }

      img.Image resized;
      if (maintainAspectRatio) {
        if (width != null) {
          resized = img.copyResize(image, width: width);
        } else if (height != null) {
          resized = img.copyResize(image, height: height);
        } else {
          return imageFile;
        }
      } else {
        resized = img.copyResize(image, width: width, height: height);
      }

      // حفظ الصورة المعدلة الحجم
      final resizedBytes = img.encodeJpg(resized, quality: 90);
      final resizedFile = File('${imageFile.path}_resized.jpg');
      await resizedFile.writeAsBytes(resizedBytes);

      if (kDebugMode) {
        print('✅ Image resized successfully');
        print('   Original: ${image.width}x${image.height}');
        print('   Resized: ${resized.width}x${resized.height}');
      }

      return resizedFile;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error resizing image: $e');
      }
      return imageFile;
    }
  }

  /// التحقق من صحة الصورة
  Future<bool> validateImage(File imageFile) async {
    try {
      // التحقق من وجود الملف
      if (!await imageFile.exists()) {
        return false;
      }

      // التحقق من حجم الملف
      final fileSize = await imageFile.length();
      if (fileSize == 0 || fileSize > 10 * 1024 * 1024) { // أكبر من 10 ميجا
        return false;
      }

      // التحقق من إمكانية فك تشفير الصورة
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);
      
      return image != null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error validating image: $e');
      }
      return false;
    }
  }

  /// الحصول على معلومات الصورة
  Future<Map<String, dynamic>?> getImageInfo(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        return null;
      }

      final fileSize = await imageFile.length();

      return {
        'width': image.width,
        'height': image.height,
        'fileSize': fileSize,
        'fileSizeFormatted': AppUtils.formatFileSize(fileSize),
        'format': imageFile.path.split('.').last.toUpperCase(),
        'aspectRatio': image.width / image.height,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting image info: $e');
      }
      return null;
    }
  }
}
