import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:io';
import '../../core/theme/desktop_theme.dart';
import '../../core/network/custom_cache_manager.dart';
import '../../core/network/ssl_bypass_image_widget.dart';

/// Professional desktop image widget with advanced loading and error handling
class DesktopImageWidget extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;
  final bool showLoadingProgress;
  final VoidCallback? onTap;
  final String? tooltip;

  const DesktopImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
    this.showLoadingProgress = true,
    this.onTap,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('🖼️ Loading image: $imageUrl');
    }

    // Configure HTTP client to handle SSL certificates
    _configureHttpOverrides();

    Widget imageWidget = _buildImageContent(context);

    // Add border radius if specified
    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    // Add tap functionality if specified
    if (onTap != null) {
      imageWidget = InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        child: imageWidget,
      );
    }

    // Add tooltip if specified
    if (tooltip != null) {
      imageWidget = Tooltip(
        message: tooltip!,
        child: imageWidget,
      );
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: DesktopTheme.backgroundTertiary,
        borderRadius: borderRadius,
        border: Border.all(
          color: DesktopTheme.borderLight,
          width: 1,
        ),
      ),
      child: imageWidget,
    );
  }

  Widget _buildImageContent(BuildContext context) {
    // Handle empty or invalid URLs
    if (imageUrl.isEmpty || !_isValidUrl(imageUrl)) {
      if (kDebugMode) {
        print('❌ Image URL is empty or invalid: $imageUrl');
      }
      return _buildErrorWidget(context, 'رابط الصورة غير صحيح');
    }

    // Use SSL Bypass Image Widget for complete SSL bypass
    return SSLBypassImageWidget(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: _buildPlaceholder(context),
      errorWidget: _buildErrorWidget(context, 'فشل في تحميل الصورة'),
      borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall),
      onTap: onTap,
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    if (placeholder != null) {
      return placeholder!;
    }

    return Container(
      width: width,
      height: height,
      color: DesktopTheme.backgroundTertiary,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (showLoadingProgress)
            SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  DesktopTheme.primaryBlue,
                ),
              ),
            )
          else
            Icon(
              Icons.image_outlined,
              size: 32,
              color: DesktopTheme.textTertiary,
            ),
          const SizedBox(height: 8),
          Text(
            'جاري التحميل...',
            style: DesktopTheme.bodySmall.copyWith(
              color: DesktopTheme.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    if (errorWidget != null) {
      return errorWidget!;
    }

    return Container(
      width: width,
      height: height,
      color: DesktopTheme.surfaceMedium,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.broken_image_outlined,
            size: 32,
            color: DesktopTheme.statusError,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: DesktopTheme.bodySmall.copyWith(
              color: DesktopTheme.statusError,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          TextButton.icon(
            onPressed: () {
              // Force rebuild to retry loading
              (context as Element).markNeedsBuild();
            },
            icon: Icon(
              Icons.refresh,
              size: 16,
              color: DesktopTheme.primaryBlue,
            ),
            label: Text(
              'إعادة المحاولة',
              style: DesktopTheme.bodySmall.copyWith(
                color: DesktopTheme.primaryBlue,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  void _configureHttpOverrides() {
    // SSL configuration is handled globally in main.dart
    // No need to configure here
  }
}

/// Specialized image widget for item thumbnails
class DesktopItemImageWidget extends StatelessWidget {
  final String imageUrl;
  final double size;
  final VoidCallback? onTap;

  const DesktopItemImageWidget({
    super.key,
    required this.imageUrl,
    this.size = 80,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SSLBypassItemImageWidget(
      imageUrl: imageUrl,
      size: size,
      onTap: onTap,
    );
  }
}

/// Image gallery widget for viewing multiple images
class DesktopImageGallery extends StatefulWidget {
  final List<String> imageUrls;
  final List<String>? imageTitles;
  final double? height;

  const DesktopImageGallery({
    super.key,
    required this.imageUrls,
    this.imageTitles,
    this.height,
  });

  @override
  State<DesktopImageGallery> createState() => _DesktopImageGalleryState();
}

class _DesktopImageGalleryState extends State<DesktopImageGallery> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    if (widget.imageUrls.isEmpty) {
      return Container(
        height: widget.height ?? 300,
        decoration: DesktopTheme.cardDecoration,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.photo_library_outlined,
                size: 64,
                color: DesktopTheme.textTertiary,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد صور',
                style: DesktopTheme.titleMedium.copyWith(
                  color: DesktopTheme.textTertiary,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      height: widget.height ?? 400,
      decoration: DesktopTheme.cardDecoration,
      child: Column(
        children: [
          // Main image display
          Expanded(
            child: DesktopImageWidget(
              imageUrl: widget.imageUrls[_selectedIndex],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(DesktopTheme.radiusLarge),
                topRight: Radius.circular(DesktopTheme.radiusLarge),
              ),
              onTap: () => _showFullScreenImage(context),
              tooltip: 'انقر لعرض الصورة بملء الشاشة',
            ),
          ),
          
          // Thumbnail navigation
          if (widget.imageUrls.length > 1)
            Container(
              height: 80,
              padding: const EdgeInsets.all(DesktopTheme.spacingMedium),
              decoration: BoxDecoration(
                color: DesktopTheme.backgroundTertiary,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(DesktopTheme.radiusLarge),
                  bottomRight: Radius.circular(DesktopTheme.radiusLarge),
                ),
              ),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: widget.imageUrls.length,
                itemBuilder: (context, index) {
                  final isSelected = index == _selectedIndex;
                  return Padding(
                    padding: const EdgeInsets.only(right: DesktopTheme.spacingSmall),
                    child: InkWell(
                      onTap: () => setState(() => _selectedIndex = index),
                      borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall),
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: isSelected
                                ? DesktopTheme.primaryBlue
                                : DesktopTheme.borderLight,
                            width: isSelected ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall),
                        ),
                        child: DesktopImageWidget(
                          imageUrl: widget.imageUrls[index],
                          borderRadius: BorderRadius.circular(DesktopTheme.radiusSmall - 1),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  void _showFullScreenImage(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black,
        child: Stack(
          children: [
            Center(
              child: DesktopImageWidget(
                imageUrl: widget.imageUrls[_selectedIndex],
                fit: BoxFit.contain,
              ),
            ),
            Positioned(
              top: 16,
              right: 16,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close, color: Colors.white),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.black54,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
