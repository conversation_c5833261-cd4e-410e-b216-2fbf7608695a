import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../core/network/custom_cache_manager.dart';

/// Android-style image widget that mimics the exact behavior of the Android version
class AndroidStyleImageWidget extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;
  final Widget? placeholder;
  final Widget? errorWidget;

  const AndroidStyleImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.onTap,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    Widget child;

    // Handle empty URLs exactly like Android
    if (imageUrl.isEmpty || imageUrl.trim().isEmpty) {
      if (kDebugMode) {
        print('❌ Android Style Image: Empty URL');
      }
      child = _buildDefaultErrorWidget(context);
    } else {
      // Use CachedNetworkImage exactly like Android version
      child = CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
        cacheManager: CloudinaryCacheManager(),
        placeholder: (context, url) => placeholder ?? _buildDefaultPlaceholder(context),
        errorWidget: (context, url, error) {
          if (kDebugMode) {
            print('❌ Android Style Image: Error loading $imageUrl - $error');
          }
          return errorWidget ?? _buildDefaultErrorWidget(context);
        },
        fadeInDuration: const Duration(milliseconds: 300),
        fadeOutDuration: const Duration(milliseconds: 100),
        httpHeaders: const {
          'User-Agent': 'El-Farhan-Android/1.0',
          'Accept': 'image/*',
          'Cache-Control': 'no-cache',
        },
      );
    }

    // Apply border radius if provided
    if (borderRadius != null) {
      child = ClipRRect(
        borderRadius: borderRadius!,
        child: child,
      );
    }

    // Apply tap handler if provided
    if (onTap != null) {
      child = InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        child: child,
      );
    }

    return child;
  }

  Widget _buildDefaultPlaceholder(BuildContext context) {
    return Container(
      width: width,
      height: height,
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: const Icon(Icons.motorcycle),
    );
  }

  Widget _buildDefaultErrorWidget(BuildContext context) {
    return Container(
      width: width,
      height: height,
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: const Icon(Icons.broken_image),
    );
  }
}

/// Android-style item image widget for thumbnails
class AndroidStyleItemImageWidget extends StatelessWidget {
  final String imageUrl;
  final double size;
  final VoidCallback? onTap;

  const AndroidStyleItemImageWidget({
    super.key,
    required this.imageUrl,
    this.size = 80,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AndroidStyleImageWidget(
      imageUrl: imageUrl,
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(8.0), // Same as Android
      onTap: onTap,
      placeholder: Container(
        width: size,
        height: size,
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        child: Icon(
          Icons.motorcycle,
          size: size * 0.4,
        ),
      ),
      errorWidget: Container(
        width: size,
        height: size,
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        child: Icon(
          Icons.broken_image,
          size: size * 0.4,
        ),
      ),
    );
  }
}

/// Android-style image gallery widget
class AndroidStyleImageGallery extends StatelessWidget {
  final List<String> imageUrls;
  final double itemSize;
  final int maxItems;

  const AndroidStyleImageGallery({
    super.key,
    required this.imageUrls,
    this.itemSize = 100,
    this.maxItems = 4,
  });

  @override
  Widget build(BuildContext context) {
    final displayUrls = imageUrls.take(maxItems).toList();
    
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: displayUrls.map((url) => AndroidStyleItemImageWidget(
        imageUrl: url,
        size: itemSize,
        onTap: () => _showFullImage(context, url),
      )).toList(),
    );
  }

  void _showFullImage(BuildContext context, String imageUrl) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800, maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppBar(
                title: const Text('عرض الصورة'),
                automaticallyImplyLeading: false,
                actions: [
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              Expanded(
                child: AndroidStyleImageWidget(
                  imageUrl: imageUrl,
                  fit: BoxFit.contain,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Android-style image card widget for detailed views
class AndroidStyleImageCard extends StatelessWidget {
  final String imageUrl;
  final String? title;
  final String? subtitle;
  final VoidCallback? onTap;

  const AndroidStyleImageCard({
    super.key,
    required this.imageUrl,
    this.title,
    this.subtitle,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AspectRatio(
              aspectRatio: 16 / 9,
              child: AndroidStyleImageWidget(
                imageUrl: imageUrl,
                fit: BoxFit.cover,
              ),
            ),
            if (title != null || subtitle != null)
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (title != null)
                      Text(
                        title!,
                        style: Theme.of(context).textTheme.titleMedium,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    if (subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        subtitle!,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
