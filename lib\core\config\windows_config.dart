import 'package:flutter/foundation.dart';

/// تكوين خاص بنسخة Windows
class WindowsConfig {
  // إعدادات النافذة
  static const double defaultWindowWidth = 1200.0;
  static const double defaultWindowHeight = 800.0;
  static const double minWindowWidth = 800.0;
  static const double minWindowHeight = 600.0;
  
  // إعدادات القائمة الجانبية
  static const double sidebarExpandedWidth = 280.0;
  static const double sidebarCollapsedWidth = 64.0;
  static const Duration sidebarAnimationDuration = Duration(milliseconds: 300);
  
  // إعدادات التبويبات
  static const int maxTabsCount = 10;
  static const double tabHeight = 48.0;
  
  // إعدادات الشريط العلوي
  static const double topBarHeight = 64.0;
  static const double searchBarWidth = 300.0;
  
  // إعدادات الرسوم المتحركة
  static const Duration defaultAnimationDuration = Duration(milliseconds: 250);
  static const Duration fastAnimationDuration = Duration(milliseconds: 150);
  static const Duration slowAnimationDuration = Duration(milliseconds: 400);
  
  // إعدادات التخطيط
  static const EdgeInsets defaultPadding = EdgeInsets.all(16.0);
  static const EdgeInsets cardPadding = EdgeInsets.all(20.0);
  static const EdgeInsets sectionPadding = EdgeInsets.all(24.0);
  
  // إعدادات الألوان
  static const double cardElevation = 2.0;
  static const double modalElevation = 8.0;
  static const double borderRadius = 12.0;
  
  // إعدادات الخطوط
  static const double titleFontSize = 18.0;
  static const double bodyFontSize = 14.0;
  static const double captionFontSize = 12.0;
  
  // إعدادات الأيقونات
  static const double defaultIconSize = 24.0;
  static const double smallIconSize = 16.0;
  static const double largeIconSize = 32.0;
  
  // إعدادات الشبكة
  static const int dashboardColumns = 3;
  static const double cardAspectRatio = 1.2;
  
  // إعدادات الأداء
  static const int maxCacheSize = 100;
  static const Duration cacheTimeout = Duration(minutes: 30);
  
  // اختصارات لوحة المفاتيح
  static const Map<String, String> keyboardShortcuts = {
    'new_item': 'Ctrl+N',
    'save': 'Ctrl+S',
    'search': 'Ctrl+F',
    'refresh': 'F5',
    'close_tab': 'Ctrl+W',
    'next_tab': 'Ctrl+Tab',
    'prev_tab': 'Ctrl+Shift+Tab',
    'zoom_in': 'Ctrl++',
    'zoom_out': 'Ctrl+-',
    'zoom_reset': 'Ctrl+0',
  };
  
  // إعدادات التصدير
  static const List<String> supportedExportFormats = [
    'PDF',
    'Excel',
    'CSV',
    'JSON',
  ];
  
  // إعدادات الطباعة
  static const Map<String, dynamic> printSettings = {
    'defaultPageSize': 'A4',
    'defaultOrientation': 'portrait',
    'defaultMargins': {
      'top': 20.0,
      'bottom': 20.0,
      'left': 20.0,
      'right': 20.0,
    },
  };
  
  // إعدادات النسخ الاحتياطي
  static const Duration autoBackupInterval = Duration(hours: 6);
  static const int maxBackupFiles = 10;
  
  // إعدادات الإشعارات
  static const Duration notificationDuration = Duration(seconds: 4);
  static const int maxNotifications = 5;
  
  // إعدادات البحث
  static const int searchResultsLimit = 50;
  static const Duration searchDebounceDelay = Duration(milliseconds: 300);
  
  /// التحقق من إمكانيات النظام
  static bool get isDesktop => !kIsWeb && (defaultTargetPlatform == TargetPlatform.windows ||
      defaultTargetPlatform == TargetPlatform.macOS ||
      defaultTargetPlatform == TargetPlatform.linux);
  
  /// التحقق من نظام Windows
  static bool get isWindows => defaultTargetPlatform == TargetPlatform.windows;
  
  /// الحصول على إعدادات النافذة الافتراضية
  static Map<String, double> get defaultWindowSettings => {
    'width': defaultWindowWidth,
    'height': defaultWindowHeight,
    'minWidth': minWindowWidth,
    'minHeight': minWindowHeight,
  };
  
  /// الحصول على إعدادات التخطيط حسب حجم الشاشة
  static Map<String, dynamic> getLayoutSettings(double screenWidth) {
    if (screenWidth < 800) {
      // شاشة صغيرة
      return {
        'columns': 1,
        'sidebarWidth': sidebarCollapsedWidth,
        'showSidebar': false,
        'cardPadding': const EdgeInsets.all(12.0),
      };
    } else if (screenWidth < 1200) {
      // شاشة متوسطة
      return {
        'columns': 2,
        'sidebarWidth': sidebarExpandedWidth,
        'showSidebar': true,
        'cardPadding': const EdgeInsets.all(16.0),
      };
    } else {
      // شاشة كبيرة
      return {
        'columns': 3,
        'sidebarWidth': sidebarExpandedWidth,
        'showSidebar': true,
        'cardPadding': cardPadding,
      };
    }
  }
  
  /// الحصول على إعدادات الخط حسب حجم الشاشة
  static Map<String, double> getFontSettings(double screenWidth) {
    final scale = screenWidth / defaultWindowWidth;
    return {
      'title': titleFontSize * scale.clamp(0.8, 1.2),
      'body': bodyFontSize * scale.clamp(0.8, 1.2),
      'caption': captionFontSize * scale.clamp(0.8, 1.2),
    };
  }
  
  /// إعدادات الرسوم البيانية
  static const Map<String, dynamic> chartSettings = {
    'defaultHeight': 300.0,
    'animationDuration': 1000,
    'colors': [
      0xFF2E7D32,
      0xFF4CAF50,
      0xFF8BC34A,
      0xFF66BB6A,
      0xFF81C784,
    ],
  };
  
  /// إعدادات الجداول
  static const Map<String, dynamic> tableSettings = {
    'rowHeight': 48.0,
    'headerHeight': 56.0,
    'defaultPageSize': 25,
    'pageSizeOptions': [10, 25, 50, 100],
  };
  
  /// إعدادات النماذج
  static const Map<String, dynamic> formSettings = {
    'fieldSpacing': 16.0,
    'sectionSpacing': 24.0,
    'buttonHeight': 48.0,
    'inputHeight': 48.0,
  };
}
