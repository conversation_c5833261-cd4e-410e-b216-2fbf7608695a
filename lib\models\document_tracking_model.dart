import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';

class DocumentTrackingModel {
  final String id;
  final String itemId; // معرف الصنف (بصمة الموتور)
  final String invoiceId; // معرف فاتورة البيع للعميل النهائي
  final String currentStatus; // الحالة الحالية للجواب
  final List<DocumentStatusHistory> statusHistory; // تاريخ تغيير الحالات
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy; // User ID who created this tracking
  final String? compositeImagePath; // Path to the composite image (motor fingerprint + chassis + customer ID)
  final Map<String, dynamic>? additionalData;

  DocumentTrackingModel({
    required this.id,
    required this.itemId,
    required this.invoiceId,
    required this.currentStatus,
    required this.statusHistory,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.compositeImagePath,
    this.additionalData,
  });

  // Convert from Firestore document
  factory DocumentTrackingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return DocumentTrackingModel(
      id: doc.id,
      itemId: data['itemId'] ?? '',
      invoiceId: data['invoiceId'] ?? '',
      currentStatus: data['currentStatus'] ?? '',
      statusHistory: (data['statusHistory'] as List<dynamic>?)
          ?.map((item) => DocumentStatusHistory.fromMap(item))
          .toList() ?? [],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] ?? '',
      compositeImagePath: data['compositeImagePath'],
      additionalData: data['additionalData'],
    );
  }

  // Convert from Map (for local database)
  factory DocumentTrackingModel.fromMap(Map<String, dynamic> map) {
    return DocumentTrackingModel(
      id: map['id'] ?? '',
      itemId: map['itemId'] ?? '',
      invoiceId: map['invoiceId'] ?? '',
      currentStatus: map['currentStatus'] ?? '',
      statusHistory: _parseStatusHistory(map['statusHistory']),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      createdBy: map['createdBy'] ?? '',
      compositeImagePath: map['compositeImagePath'],
      additionalData: _parseAdditionalData(map['additionalData']),
    );
  }

  // Convert to Map (for Firestore and local database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'itemId': itemId,
      'invoiceId': invoiceId,
      'currentStatus': currentStatus,
      'statusHistory': jsonEncode(statusHistory.map((item) => item.toMap()).toList()),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
      'compositeImagePath': compositeImagePath,
      'additionalData': additionalData,
    };
  }

  // Convert to Firestore format
  Map<String, dynamic> toFirestore() {
    return {
      'itemId': itemId,
      'invoiceId': invoiceId,
      'currentStatus': currentStatus,
      'statusHistory': statusHistory.map((item) => item.toFirestore()).toList(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'compositeImagePath': compositeImagePath,
      'additionalData': additionalData,
    };
  }

  // Copy with method for updates
  DocumentTrackingModel copyWith({
    String? id,
    String? itemId,
    String? invoiceId,
    String? currentStatus,
    List<DocumentStatusHistory>? statusHistory,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? compositeImagePath,
    Map<String, dynamic>? additionalData,
  }) {
    return DocumentTrackingModel(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      invoiceId: invoiceId ?? this.invoiceId,
      currentStatus: currentStatus ?? this.currentStatus,
      statusHistory: statusHistory ?? this.statusHistory,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      compositeImagePath: compositeImagePath ?? this.compositeImagePath,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  // Helper methods
  bool get isSentToManufacturer => currentStatus == 'sent_to_manufacturer';
  bool get isReceivedFromManufacturer => currentStatus == 'received_from_manufacturer';
  bool get isSentToSalePoint => currentStatus == 'sent_to_sale_point';
  bool get isReadyForPickup => currentStatus == 'ready_for_pickup';

  DocumentStatusHistory? get latestStatusUpdate => 
      statusHistory.isNotEmpty ? statusHistory.last : null;

  @override
  String toString() {
    return 'DocumentTrackingModel(id: $id, itemId: $itemId, currentStatus: $currentStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DocumentTrackingModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper method to parse status history from different formats
  static List<DocumentStatusHistory> _parseStatusHistory(dynamic statusHistoryData) {
    if (statusHistoryData == null) return [];

    try {
      List<dynamic> historyList;

      if (statusHistoryData is String) {
        // Data from local database (JSON string)
        historyList = jsonDecode(statusHistoryData) as List<dynamic>;
      } else if (statusHistoryData is List) {
        // Data already as List
        historyList = statusHistoryData;
      } else {
        return [];
      }

      return historyList
          .map((item) => DocumentStatusHistory.fromMap(item as Map<String, dynamic>))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Helper method to parse additional data from different formats
  static Map<String, dynamic>? _parseAdditionalData(dynamic additionalDataValue) {
    if (additionalDataValue == null) return null;

    try {
      if (additionalDataValue is String) {
        // Data from local database (JSON string)
        return Map<String, dynamic>.from(jsonDecode(additionalDataValue));
      } else if (additionalDataValue is Map) {
        // Data already as Map
        return Map<String, dynamic>.from(additionalDataValue);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }
}

class DocumentStatusHistory {
  final String status;
  final DateTime timestamp;
  final String updatedBy; // User ID who updated the status
  final String? notes;

  DocumentStatusHistory({
    required this.status,
    required this.timestamp,
    required this.updatedBy,
    this.notes,
  });

  factory DocumentStatusHistory.fromMap(Map<String, dynamic> map) {
    return DocumentStatusHistory(
      status: map['status'] ?? '',
      timestamp: map['timestamp'] is Timestamp 
          ? (map['timestamp'] as Timestamp).toDate()
          : DateTime.parse(map['timestamp']),
      updatedBy: map['updatedBy'] ?? '',
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'status': status,
      'timestamp': timestamp.toIso8601String(),
      'updatedBy': updatedBy,
      'notes': notes,
    };
  }

  Map<String, dynamic> toFirestore() {
    return {
      'status': status,
      'timestamp': Timestamp.fromDate(timestamp),
      'updatedBy': updatedBy,
      'notes': notes,
    };
  }

  @override
  String toString() {
    return 'DocumentStatusHistory(status: $status, timestamp: $timestamp)';
  }
}
