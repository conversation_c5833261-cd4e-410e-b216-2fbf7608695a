import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/user_model.dart';
import '../../models/warehouse_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/data_service.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';

class AddEditUserScreen extends StatefulWidget {
  final UserModel? user;

  const AddEditUserScreen({super.key, this.user});

  @override
  State<AddEditUserScreen> createState() => _AddEditUserScreenState();
}

class _AddEditUserScreenState extends State<AddEditUserScreen> {
  final _formKey = GlobalKey<FormState>();
  final DataService _dataService = DataService.instance;
  
  // Controllers
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _profitShareController = TextEditingController();
  
  // State variables
  String _selectedRole = AppConstants.agentRole;
  String? _selectedWarehouseId;
  List<WarehouseModel> _warehouses = [];
  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    _loadWarehouses();
    if (widget.user != null) {
      _populateFields();
    } else {
      // Set default profit share for new agents
      _profitShareController.text = '50';
    }
  }

  void _populateFields() {
    final user = widget.user!;
    _usernameController.text = user.username;
    _emailController.text = user.email;
    _fullNameController.text = user.fullName;
    _phoneController.text = user.phone;
    _profitShareController.text = (user.profitSharePercentage * 100).toStringAsFixed(0);
    _selectedRole = user.role;
    _selectedWarehouseId = user.warehouseId;
  }

  Future<void> _loadWarehouses() async {
    try {
      final warehouses = await _dataService.getWarehouses(isActive: true);
      setState(() {
        _warehouses = warehouses;
      });
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(context, 'خطأ في تحميل المخازن: $e', isError: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    // Check permissions
    if (!authProvider.canManageUsers) {
      return Scaffold(
        appBar: AppBar(title: Text(widget.user == null ? 'إضافة مستخدم' : 'تعديل مستخدم')),
        body: const Center(
          child: Text(
            'ليس لديك صلاحية لإدارة المستخدمين',
            style: TextStyle(fontSize: 18),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.user == null ? 'إضافة مستخدم جديد' : 'تعديل المستخدم'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Basic Information Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المعلومات الأساسية',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // Full Name
                      TextFormField(
                        controller: _fullNameController,
                        decoration: const InputDecoration(
                          labelText: 'الاسم الكامل *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.person),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال الاسم الكامل';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // Username
                      TextFormField(
                        controller: _usernameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم المستخدم *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.account_circle),
                        ),
                        enabled: widget.user == null, // Can't change username for existing users
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال اسم المستخدم';
                          }
                          if (value.trim().length < 3) {
                            return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // Email
                      TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'البريد الإلكتروني *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.email),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال البريد الإلكتروني';
                          }
                          if (!AppUtils.isValidEmail(value.trim())) {
                            return 'يرجى إدخال بريد إلكتروني صحيح';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // Phone
                      TextFormField(
                        controller: _phoneController,
                        decoration: const InputDecoration(
                          labelText: 'رقم الهاتف *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.phone),
                        ),
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال رقم الهاتف';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Role and Warehouse Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الدور والصلاحيات',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // Role Selection
                      DropdownButtonFormField<String>(
                        value: _selectedRole,
                        decoration: const InputDecoration(
                          labelText: 'الدور *',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.security),
                        ),
                        items: const [
                          DropdownMenuItem(value: AppConstants.adminRole, child: Text('مدير إداري')),
                          DropdownMenuItem(value: AppConstants.agentRole, child: Text('وكيل')),
                          DropdownMenuItem(value: AppConstants.showroomRole, child: Text('مستخدم معرض')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedRole = value!;
                            _selectedWarehouseId = null; // Reset warehouse selection
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى اختيار الدور';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: AppConstants.defaultPadding),
                      
                      // Warehouse Selection (for agents and showroom users)
                      if (_selectedRole == AppConstants.agentRole || _selectedRole == AppConstants.showroomRole)
                        DropdownButtonFormField<String>(
                          value: _selectedWarehouseId,
                          decoration: InputDecoration(
                            labelText: _selectedRole == AppConstants.agentRole ? 'مخزن الوكيل *' : 'مخزن المعرض *',
                            border: const OutlineInputBorder(),
                            prefixIcon: const Icon(Icons.warehouse),
                          ),
                          items: _warehouses
                              .where((warehouse) => 
                                  (_selectedRole == AppConstants.agentRole && warehouse.type == AppConstants.agentWarehouse) ||
                                  (_selectedRole == AppConstants.showroomRole && warehouse.type == AppConstants.showroomWarehouse))
                              .map((warehouse) => DropdownMenuItem(
                                    value: warehouse.id,
                                    child: Text(warehouse.name),
                                  ))
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedWarehouseId = value;
                            });
                          },
                          validator: (value) {
                            if ((_selectedRole == AppConstants.agentRole || _selectedRole == AppConstants.showroomRole) && 
                                (value == null || value.isEmpty)) {
                              return 'يرجى اختيار المخزن';
                            }
                            return null;
                          },
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: AppConstants.defaultPadding),

              // Profit Share Percentage (only for agents)
              if (_selectedRole == AppConstants.agentRole)
                Column(
                  children: [
                    TextFormField(
                      controller: _profitShareController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'نسبة ربح الوكيل (%)',
                        hintText: 'أدخل النسبة من 0 إلى 100',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.percent),
                        suffixText: '%',
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال نسبة الربح';
                        }
                        final percentage = double.tryParse(value.trim());
                        if (percentage == null || percentage < 0 || percentage > 100) {
                          return 'يرجى إدخال نسبة صحيحة من 0 إلى 100';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'ملاحظة: النسبة المتبقية ستكون للمؤسسة',
                      style: TextStyle(fontSize: 12, color: Colors.orange),
                    ),
                    const SizedBox(height: AppConstants.defaultPadding),
                  ],
                ),
              
              // Password Section (only for new users)
              if (widget.user == null)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'كلمة المرور',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        const SizedBox(height: AppConstants.defaultPadding),
                        
                        // Password
                        TextFormField(
                          controller: _passwordController,
                          decoration: InputDecoration(
                            labelText: 'كلمة المرور *',
                            border: const OutlineInputBorder(),
                            prefixIcon: const Icon(Icons.lock),
                            suffixIcon: IconButton(
                              icon: Icon(_isPasswordVisible ? Icons.visibility_off : Icons.visibility),
                              onPressed: () {
                                setState(() {
                                  _isPasswordVisible = !_isPasswordVisible;
                                });
                              },
                            ),
                          ),
                          obscureText: !_isPasswordVisible,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال كلمة المرور';
                            }
                            if (value.trim().length < 6) {
                              return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                            }
                            return null;
                          },
                        ),
                        
                        const SizedBox(height: AppConstants.defaultPadding),
                        
                        // Confirm Password
                        TextFormField(
                          controller: _confirmPasswordController,
                          decoration: InputDecoration(
                            labelText: 'تأكيد كلمة المرور *',
                            border: const OutlineInputBorder(),
                            prefixIcon: const Icon(Icons.lock_outline),
                            suffixIcon: IconButton(
                              icon: Icon(_isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility),
                              onPressed: () {
                                setState(() {
                                  _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                                });
                              },
                            ),
                          ),
                          obscureText: !_isConfirmPasswordVisible,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى تأكيد كلمة المرور';
                            }
                            if (value.trim() != _passwordController.text.trim()) {
                              return 'كلمة المرور غير متطابقة';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              
              const SizedBox(height: AppConstants.defaultPadding * 2),
              
              // Save Button
              ElevatedButton(
                onPressed: _isLoading ? null : _saveUser,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : Text(
                        widget.user == null ? 'إضافة المستخدم' : 'حفظ التغييرات',
                        style: const TextStyle(fontSize: 16),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser!;
      final now = DateTime.now();

      if (widget.user == null) {
        // Create new user
        final profitShare = _selectedRole == AppConstants.agentRole && _profitShareController.text.isNotEmpty
            ? (double.tryParse(_profitShareController.text.trim()) ?? 50.0) / 100
            : 0.5; // Default 50%

        final newUser = UserModel(
          id: '', // Will be set by the service
          username: _usernameController.text.trim(),
          email: _emailController.text.trim(),
          fullName: _fullNameController.text.trim(),
          phone: _phoneController.text.trim(),
          role: _selectedRole,
          warehouseId: _selectedWarehouseId,
          profitSharePercentage: profitShare,
          createdAt: now,
          updatedAt: now,
        );

        await _dataService.createUserWithPassword(
          newUser,
          _passwordController.text.trim(),
        );

        if (mounted) {
          AppUtils.showSnackBar(context, 'تم إنشاء المستخدم بنجاح');
          Navigator.pop(context);
        }
      } else {
        // Update existing user
        final profitShare = _selectedRole == AppConstants.agentRole && _profitShareController.text.isNotEmpty
            ? (double.tryParse(_profitShareController.text.trim()) ?? 50.0) / 100
            : widget.user!.profitSharePercentage; // Keep existing value if not agent

        final updatedUser = widget.user!.copyWith(
          email: _emailController.text.trim(),
          fullName: _fullNameController.text.trim(),
          phone: _phoneController.text.trim(),
          role: _selectedRole,
          warehouseId: _selectedWarehouseId,
          profitSharePercentage: profitShare,
          updatedAt: now,
        );

        await _dataService.updateUser(updatedUser);

        if (mounted) {
          AppUtils.showSnackBar(context, 'تم تحديث بيانات المستخدم بنجاح');
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showSnackBar(
          context, 
          'خطأ في ${widget.user == null ? 'إنشاء' : 'تحديث'} المستخدم: $e', 
          isError: true
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _fullNameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }
}
