// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_windows.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBqJVJKvLvpqQeQzKzKzKzKzKzKzKzKzKz',
    appId: '1:123456789:web:abcdefghijklmnop',
    messagingSenderId: '123456789',
    projectId: 'al-farhan-c3a30',
    authDomain: 'al-farhan-c3a30.firebaseapp.com',
    storageBucket: 'al-farhan-c3a30.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBqJVJKvLvpqQeQzKzKzKzKzKzKzKzKzKz',
    appId: '1:123456789:android:abcdefghijklmnop',
    messagingSenderId: '123456789',
    projectId: 'al-farhan-c3a30',
    storageBucket: 'al-farhan-c3a30.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBqJVJKvLvpqQeQzKzKzKzKzKzKzKzKzKz',
    appId: '1:123456789:ios:abcdefghijklmnop',
    messagingSenderId: '123456789',
    projectId: 'al-farhan-c3a30',
    storageBucket: 'al-farhan-c3a30.appspot.com',
    iosBundleId: 'com.example.elFarhanTransport',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBqJVJKvLvpqQeQzKzKzKzKzKzKzKzKzKz',
    appId: '1:123456789:macos:abcdefghijklmnop',
    messagingSenderId: '123456789',
    projectId: 'al-farhan-c3a30',
    storageBucket: 'al-farhan-c3a30.appspot.com',
    iosBundleId: 'com.example.elFarhanTransport',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBqJVJKvLvpqQeQzKzKzKzKzKzKzKzKzKz',
    appId: '1:123456789:web:abcdefghijklmnop',
    messagingSenderId: '123456789',
    projectId: 'al-farhan-c3a30',
    authDomain: 'al-farhan-c3a30.firebaseapp.com',
    storageBucket: 'al-farhan-c3a30.appspot.com',
  );
}
