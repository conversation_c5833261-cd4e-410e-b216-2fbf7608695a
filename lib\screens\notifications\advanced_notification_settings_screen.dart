import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

import '../../core/constants/app_constants.dart';
import '../../core/utils/app_utils.dart';
import '../../models/notification_settings_model.dart';
import '../../services/advanced_notification_system.dart';

/// شاشة إعدادات الإشعارات المتطورة
class AdvancedNotificationSettingsScreen extends StatefulWidget {
  const AdvancedNotificationSettingsScreen({Key? key}) : super(key: key);

  @override
  State<AdvancedNotificationSettingsScreen> createState() => _AdvancedNotificationSettingsScreenState();
}

class _AdvancedNotificationSettingsScreenState extends State<AdvancedNotificationSettingsScreen> {
  final AdvancedNotificationSystem _notificationSystem = AdvancedNotificationSystem.instance;
  late NotificationSettings _settings;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _settings = _notificationSystem.settings;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildSettingsBody(),
    );
  }

  Widget _buildSettingsBody() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildGeneralSettings(),
        const SizedBox(height: 24),
        _buildNotificationTypes(),
        const SizedBox(height: 24),
        _buildQuietHours(),
        const SizedBox(height: 24),
        _buildAdvancedSettings(),
        const SizedBox(height: 24),
        _buildAnalyticsSettings(),
        const SizedBox(height: 32),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildGeneralSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإعدادات العامة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تفعيل الإشعارات'),
              subtitle: const Text('تشغيل/إيقاف جميع الإشعارات'),
              value: _settings.enabled,
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(enabled: value);
                });
              },
            ),
            SwitchListTile(
              title: const Text('الصوت'),
              subtitle: const Text('تشغيل الصوت مع الإشعارات'),
              value: _settings.soundEnabled,
              onChanged: _settings.enabled ? (value) {
                setState(() {
                  _settings = _settings.copyWith(soundEnabled: value);
                });
              } : null,
            ),
            SwitchListTile(
              title: const Text('الاهتزاز'),
              subtitle: const Text('تشغيل الاهتزاز مع الإشعارات'),
              value: _settings.vibrationEnabled,
              onChanged: _settings.enabled ? (value) {
                setState(() {
                  _settings = _settings.copyWith(vibrationEnabled: value);
                });
              } : null,
            ),
            SwitchListTile(
              title: const Text('عرض في التطبيق'),
              subtitle: const Text('عرض الإشعارات داخل التطبيق'),
              value: _settings.showInApp,
              onChanged: _settings.enabled ? (value) {
                setState(() {
                  _settings = _settings.copyWith(showInApp: value);
                });
              } : null,
            ),
            SwitchListTile(
              title: const Text('عرض على شاشة القفل'),
              subtitle: const Text('عرض الإشعارات على شاشة القفل'),
              value: _settings.showOnLockScreen,
              onChanged: _settings.enabled ? (value) {
                setState(() {
                  _settings = _settings.copyWith(showOnLockScreen: value);
                });
              } : null,
            ),
            SwitchListTile(
              title: const Text('تجميع الإشعارات المتشابهة'),
              subtitle: const Text('تجميع الإشعارات من نفس النوع'),
              value: _settings.groupSimilar,
              onChanged: _settings.enabled ? (value) {
                setState(() {
                  _settings = _settings.copyWith(groupSimilar: value);
                });
              } : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationTypes() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أنواع الإشعارات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildNotificationTypeItem(
              'invoice_created',
              'إشعارات الفواتير',
              'إشعارات إنشاء وتحديث الفواتير',
              Icons.receipt,
            ),
            _buildNotificationTypeItem(
              'payment_received',
              'إشعارات الدفعات',
              'إشعارات استلام الدفعات',
              Icons.payment,
            ),
            _buildNotificationTypeItem(
              'payment_reminder',
              'تذكير بالدفع',
              'تذكيرات الدفعات المستحقة',
              Icons.alarm,
            ),
            _buildNotificationTypeItem(
              'inventory_added',
              'إشعارات المخزون',
              'إشعارات إضافة وتحديث المخزون',
              Icons.inventory,
            ),
            _buildNotificationTypeItem(
              'document_updated',
              'إشعارات الوثائق',
              'إشعارات تحديث الوثائق',
              Icons.description,
            ),
            _buildNotificationTypeItem(
              'general',
              'إشعارات عامة',
              'إشعارات عامة ومعلومات النظام',
              Icons.info,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationTypeItem(String type, String title, String subtitle, IconData icon) {
    final isEnabled = _settings.enabledTypes.contains(type);
    
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: isEnabled,
        onChanged: _settings.enabled ? (value) {
          setState(() {
            final newTypes = Set<String>.from(_settings.enabledTypes);
            if (value) {
              newTypes.add(type);
            } else {
              newTypes.remove(type);
            }
            _settings = _settings.copyWith(enabledTypes: newTypes);
          });
        } : null,
      ),
      onTap: () => _showTypeSettings(type, title),
    );
  }

  Widget _buildQuietHours() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ساعات الهدوء',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('تفعيل ساعات الهدوء'),
              subtitle: const Text('إيقاف الإشعارات في أوقات محددة'),
              value: _settings.quietHours != null,
              onChanged: _settings.enabled ? (value) {
                setState(() {
                  if (value) {
                    _settings = _settings.copyWith(
                      quietHours: const QuietHours(
                        startTime: TimeOfDay(hour: 22, minute: 0),
                        endTime: TimeOfDay(hour: 7, minute: 0),
                      ),
                    );
                  } else {
                    _settings = _settings.copyWith(quietHours: null);
                  }
                });
              } : null,
            ),
            if (_settings.quietHours != null) ...[
              ListTile(
                title: const Text('وقت البداية'),
                subtitle: Text(_formatTime(_settings.quietHours!.startTime)),
                trailing: const Icon(Icons.access_time),
                onTap: () => _selectTime(true),
              ),
              ListTile(
                title: const Text('وقت النهاية'),
                subtitle: Text(_formatTime(_settings.quietHours!.endTime)),
                trailing: const Icon(Icons.access_time),
                onTap: () => _selectTime(false),
              ),
              SwitchListTile(
                title: const Text('السماح بالإشعارات الحرجة'),
                subtitle: const Text('عرض الإشعارات الحرجة حتى في ساعات الهدوء'),
                value: _settings.quietHours!.allowCritical,
                onChanged: (value) {
                  setState(() {
                    _settings = _settings.copyWith(
                      quietHours: _settings.quietHours!.copyWith(allowCritical: value),
                    );
                  });
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإعدادات المتقدمة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('الحد الأقصى للإشعارات في الساعة'),
              subtitle: Text('${_settings.maxNotificationsPerHour} إشعار'),
              trailing: const Icon(Icons.tune),
              onTap: _showRateLimitDialog,
            ),
            SwitchListTile(
              title: const Text('التصفية الذكية'),
              subtitle: const Text('تصفية الإشعارات المكررة والغير مهمة'),
              value: _settings.smartFiltering,
              onChanged: _settings.enabled ? (value) {
                setState(() {
                  _settings = _settings.copyWith(smartFiltering: value);
                });
              } : null,
            ),
            SwitchListTile(
              title: const Text('الاقتراحات الذكية'),
              subtitle: const Text('عرض اقتراحات ذكية بناءً على سلوكك'),
              value: _settings.aiSuggestions,
              onChanged: _settings.enabled ? (value) {
                setState(() {
                  _settings = _settings.copyWith(aiSuggestions: value);
                });
              } : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإحصائيات والتحليلات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('عرض إحصائيات الإشعارات'),
              subtitle: const Text('مشاهدة تقارير مفصلة عن الإشعارات'),
              trailing: const Icon(Icons.analytics),
              onTap: _showAnalytics,
            ),
            ListTile(
              title: const Text('تصدير البيانات'),
              subtitle: const Text('تصدير بيانات الإشعارات'),
              trailing: const Icon(Icons.download),
              onTap: _exportData,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _testNotification,
            icon: const Icon(Icons.notifications_active),
            label: const Text('اختبار الإشعارات'),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _resetToDefaults,
            icon: const Icon(Icons.restore),
            label: const Text('استعادة الإعدادات الافتراضية'),
          ),
        ),
      ],
    );
  }

  // ==================== HELPER METHODS ====================

  String _formatTime(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  Future<void> _selectTime(bool isStartTime) async {
    final currentTime = isStartTime
        ? _settings.quietHours!.startTime
        : _settings.quietHours!.endTime;

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: currentTime,
    );

    if (selectedTime != null) {
      setState(() {
        if (isStartTime) {
          _settings = _settings.copyWith(
            quietHours: _settings.quietHours!.copyWith(startTime: selectedTime),
          );
        } else {
          _settings = _settings.copyWith(
            quietHours: _settings.quietHours!.copyWith(endTime: selectedTime),
          );
        }
      });
    }
  }

  Future<void> _showTypeSettings(String type, String title) async {
    // Show detailed settings for specific notification type
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إعدادات $title'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('قريباً: إعدادات مفصلة لكل نوع إشعار'),
            const SizedBox(height: 16),
            const Text('• أولوية الإشعار'),
            const Text('• صوت مخصص'),
            const Text('• لون الإشعار'),
            const Text('• تأخير الإرسال'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  Future<void> _showRateLimitDialog() async {
    int currentLimit = _settings.maxNotificationsPerHour;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('الحد الأقصى للإشعارات'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('اختر الحد الأقصى لعدد الإشعارات في الساعة الواحدة:'),
              const SizedBox(height: 16),
              Slider(
                value: currentLimit.toDouble(),
                min: 1,
                max: 50,
                divisions: 49,
                label: '$currentLimit إشعار',
                onChanged: (value) {
                  setDialogState(() {
                    currentLimit = value.round();
                  });
                },
              ),
              Text('$currentLimit إشعار في الساعة'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _settings = _settings.copyWith(maxNotificationsPerHour: currentLimit);
                });
                Navigator.pop(context);
              },
              child: const Text('حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showAnalytics() async {
    final stats = _notificationSystem.notificationStats;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات الإشعارات'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (stats.isEmpty)
                const Text('لا توجد إحصائيات متاحة حالياً')
              else
                ...stats.entries.map((entry) => ListTile(
                  title: Text(entry.key),
                  trailing: Text('${entry.value}'),
                )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportData() async {
    // Show export options
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البيانات'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('قريباً: تصدير بيانات الإشعارات'),
            SizedBox(height: 16),
            Text('• تصدير كـ CSV'),
            Text('• تصدير كـ JSON'),
            Text('• تصدير كـ PDF'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  Future<void> _testNotification() async {
    try {
      await _notificationSystem.sendGeneralNotification(
        title: 'اختبار الإشعارات',
        message: 'هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح',
        targetUserId: null, // Send to current user
        priority: 'medium',
      );

      if (mounted) {
        AppUtils.showSuccessSnackBar(context, 'تم إرسال إشعار تجريبي بنجاح');
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'فشل في إرسال الإشعار التجريبي');
      }
    }
  }

  Future<void> _resetToDefaults() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعادة الإعدادات الافتراضية'),
        content: const Text('هل أنت متأكد من استعادة جميع الإعدادات إلى القيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('استعادة'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _settings = const NotificationSettings();
      });

      if (mounted) {
        AppUtils.showSuccessSnackBar(context, 'تم استعادة الإعدادات الافتراضية');
      }
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _notificationSystem.updateSettings(_settings);

      if (mounted) {
        AppUtils.showSuccessSnackBar(context, 'تم حفظ الإعدادات بنجاح');
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        AppUtils.showErrorSnackBar(context, 'فشل في حفظ الإعدادات');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
