import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/windows/windows_export_service.dart';
import 'advanced_warehouse_reports_screen.dart';

/// شاشة التقارير المحسنة للويندوز - مستقرة وكاملة
class WindowsReportsScreen extends StatefulWidget {
  const WindowsReportsScreen({Key? key}) : super(key: key);

  @override
  State<WindowsReportsScreen> createState() => _WindowsReportsScreenState();
}

class _WindowsReportsScreenState extends State<WindowsReportsScreen> {
  bool _isLoading = false;
  String _selectedReportType = 'sales';
  DateTimeRange? _selectedDateRange;

  final List<Map<String, dynamic>> _reportTypes = [
    {
      'id': 'sales',
      'title': 'تقرير المبيعات',
      'icon': Icons.shopping_cart,
      'color': Colors.green,
      'description': 'تقرير شامل بجميع المبيعات والفواتير',
    },
    {
      'id': 'inventory',
      'title': 'تقرير المخزون',
      'icon': Icons.inventory,
      'color': Colors.blue,
      'description': 'تقرير حالة المخزون والأصناف',
    },
    {
      'id': 'agents',
      'title': 'تقرير الوكلاء',
      'icon': Icons.people,
      'color': Colors.orange,
      'description': 'تقرير حسابات الوكلاء والمعاملات',
    },
    {
      'id': 'financial',
      'title': 'التقرير المالي',
      'icon': Icons.account_balance,
      'color': Colors.purple,
      'description': 'تقرير الوضع المالي والأرباح',
    },
    {
      'id': 'warehouse_movements',
      'title': 'تقارير حركة المخازن المتطورة',
      'icon': Icons.swap_horiz,
      'color': const Color(0xFF1565C0),
      'description': 'تقرير شامل لحركات المخازن وحركة المنتجات وفواتير التحويل',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.analytics,
                    size: 32,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'التقارير والإحصائيات',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'إنشاء وتصدير التقارير المختلفة - محسن للويندوز',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.green.withOpacity(0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.verified, color: Colors.green, size: 16),
                      const SizedBox(width: 6),
                      Text(
                        'مستقر وكامل',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Date Range Selector
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'فترة التقرير',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: _selectDateRange,
                            icon: const Icon(Icons.date_range),
                            label: Text(
                              _selectedDateRange != null
                                  ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                                  : 'اختيار الفترة الزمنية',
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                              _selectedDateRange = DateTimeRange(
                                start: DateTime.now().subtract(const Duration(days: 30)),
                                end: DateTime.now(),
                              );
                            });
                          },
                          icon: const Icon(Icons.today),
                          label: const Text('آخر 30 يوم'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Report Types
            const Text(
              'أنواع التقارير',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.8,
                ),
                itemCount: _reportTypes.length,
                itemBuilder: (context, index) {
                  final reportType = _reportTypes[index];
                  final isSelected = _selectedReportType == reportType['id'];
                  
                  return Card(
                    elevation: isSelected ? 8 : 2,
                    child: InkWell(
                      onTap: () {
                        if (reportType['id'] == 'warehouse_movements') {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AdvancedWarehouseReportsScreen(),
                            ),
                          );
                        } else {
                          setState(() {
                            _selectedReportType = reportType['id'];
                          });
                        }
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: isSelected
                              ? Border.all(color: reportType['color'], width: 2)
                              : null,
                          gradient: isSelected
                              ? LinearGradient(
                                  colors: [
                                    reportType['color'].withOpacity(0.1),
                                    reportType['color'].withOpacity(0.05),
                                  ],
                                )
                              : null,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: reportType['color'].withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    reportType['icon'],
                                    color: reportType['color'],
                                    size: 24,
                                  ),
                                ),
                                const Spacer(),
                                if (isSelected)
                                  Icon(
                                    Icons.check_circle,
                                    color: reportType['color'],
                                    size: 20,
                                  ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              reportType['title'],
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? reportType['color'] : null,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              reportType['description'],
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Export Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _generateReport('excel'),
                    icon: _isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.table_chart),
                    label: const Text('تصدير Excel'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _generateReport('csv'),
                    icon: const Icon(Icons.file_download),
                    label: const Text('تصدير CSV'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : () => _generateReport('html'),
                    icon: const Icon(Icons.web),
                    label: const Text('تصدير HTML'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _generateReport(String format) async {
    if (_selectedDateRange == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار الفترة الزمنية أولاً')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final exportService = Provider.of<WindowsExportService>(context, listen: false);
      final data = await _getReportData();

      String? filePath;
      switch (format) {
        case 'excel':
          filePath = await exportService.exportToExcel(
            data: data,
            fileName: '${_getReportTitle()}_${DateTime.now().millisecondsSinceEpoch}',
            title: _getReportTitle(),
            headers: _getReportHeaders(),
          );
          break;
        case 'csv':
          filePath = await exportService.exportToCSV(
            data: data,
            fileName: '${_getReportTitle()}_${DateTime.now().millisecondsSinceEpoch}',
            headers: _getReportHeaders(),
          );
          break;
        case 'html':
          filePath = await exportService.exportToHTML(
            data: data,
            fileName: '${_getReportTitle()}_${DateTime.now().millisecondsSinceEpoch}',
            title: _getReportTitle(),
            headers: _getReportHeaders(),
          );
          break;
      }

      if (filePath != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تصدير التقرير بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تصدير التقرير: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getReportTitle() {
    final reportType = _reportTypes.firstWhere((r) => r['id'] == _selectedReportType);
    return reportType['title'];
  }

  List<String> _getReportHeaders() {
    switch (_selectedReportType) {
      case 'sales':
        return ['رقم الفاتورة', 'التاريخ', 'العميل', 'المبلغ', 'الحالة'];
      case 'inventory':
        return ['كود الصنف', 'اسم الصنف', 'الكمية', 'السعر', 'المخزن'];
      case 'agents':
        return ['اسم الوكيل', 'الرصيد', 'آخر معاملة', 'الحالة'];
      case 'financial':
        return ['التاريخ', 'النوع', 'المبلغ', 'الوصف'];
      default:
        return ['البيانات'];
    }
  }

  Future<List<Map<String, dynamic>>> _getReportData() async {
    // محاكاة بيانات التقرير
    await Future.delayed(const Duration(seconds: 1));

    switch (_selectedReportType) {
      case 'sales':
        return [
          {'رقم الفاتورة': '001', 'التاريخ': '2024/01/15', 'العميل': 'أحمد محمد', 'المبلغ': '1500', 'الحالة': 'مدفوعة'},
          {'رقم الفاتورة': '002', 'التاريخ': '2024/01/16', 'العميل': 'محمد علي', 'المبلغ': '2300', 'الحالة': 'معلقة'},
        ];
      case 'inventory':
        return [
          {'كود الصنف': 'ITM001', 'اسم الصنف': 'قطعة غيار A', 'الكمية': '50', 'السعر': '100', 'المخزن': 'المخزن الرئيسي'},
          {'كود الصنف': 'ITM002', 'اسم الصنف': 'قطعة غيار B', 'الكمية': '30', 'السعر': '150', 'المخزن': 'المخزن الرئيسي'},
        ];
      case 'agents':
        return [
          {'اسم الوكيل': 'أحمد محمد', 'الرصيد': '5000', 'آخر معاملة': '2024/01/15', 'الحالة': 'نشط'},
          {'اسم الوكيل': 'محمد علي', 'الرصيد': '3200', 'آخر معاملة': '2024/01/14', 'الحالة': 'نشط'},
        ];
      case 'financial':
        return [
          {'التاريخ': '2024/01/15', 'النوع': 'إيراد', 'المبلغ': '5000', 'الوصف': 'مبيعات يومية'},
          {'التاريخ': '2024/01/15', 'النوع': 'مصروف', 'المبلغ': '1200', 'الوصف': 'مصاريف تشغيلية'},
        ];
      default:
        return [
          {'البيانات': 'لا توجد بيانات متاحة'},
        ];
    }
  }
}
