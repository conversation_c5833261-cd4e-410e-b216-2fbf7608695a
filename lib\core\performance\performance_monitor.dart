import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class PerformanceMonitor {
  static PerformanceMonitor? _instance;
  static PerformanceMonitor get instance => _instance ??= PerformanceMonitor._();
  PerformanceMonitor._();

  final Map<String, Stopwatch> _timers = {};
  final Map<String, List<int>> _metrics = {};
  bool _isEnabled = kDebugMode;

  // Enable/disable performance monitoring
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  // Start timing an operation
  void startTimer(String operation) {
    if (!_isEnabled) return;
    
    _timers[operation] = Stopwatch()..start();
    if (kDebugMode) {
      print('⏱️ Started timer for: $operation');
    }
  }

  // Stop timing an operation and record the result
  int stopTimer(String operation) {
    if (!_isEnabled) return 0;
    
    final timer = _timers[operation];
    if (timer == null) {
      if (kDebugMode) {
        print('⚠️ Timer not found for: $operation');
      }
      return 0;
    }

    timer.stop();
    final elapsed = timer.elapsedMilliseconds;
    
    // Record metric
    _metrics[operation] ??= [];
    _metrics[operation]!.add(elapsed);
    
    // Keep only last 100 measurements
    if (_metrics[operation]!.length > 100) {
      _metrics[operation]!.removeAt(0);
    }
    
    _timers.remove(operation);
    
    if (kDebugMode) {
      print('⏱️ $operation completed in ${elapsed}ms');
    }
    
    return elapsed;
  }

  // Measure a future operation
  Future<T> measureAsync<T>(String operation, Future<T> Function() function) async {
    if (!_isEnabled) return await function();
    
    startTimer(operation);
    try {
      final result = await function();
      stopTimer(operation);
      return result;
    } catch (e) {
      stopTimer(operation);
      rethrow;
    }
  }

  // Measure a synchronous operation
  T measureSync<T>(String operation, T Function() function) {
    if (!_isEnabled) return function();
    
    startTimer(operation);
    try {
      final result = function();
      stopTimer(operation);
      return result;
    } catch (e) {
      stopTimer(operation);
      rethrow;
    }
  }

  // Get performance statistics
  Map<String, PerformanceStats> getStats() {
    final stats = <String, PerformanceStats>{};
    
    for (final entry in _metrics.entries) {
      final operation = entry.key;
      final measurements = entry.value;
      
      if (measurements.isEmpty) continue;
      
      final total = measurements.reduce((a, b) => a + b);
      final average = total / measurements.length;
      final min = measurements.reduce((a, b) => a < b ? a : b);
      final max = measurements.reduce((a, b) => a > b ? a : b);
      
      stats[operation] = PerformanceStats(
        operation: operation,
        count: measurements.length,
        totalTime: total,
        averageTime: average,
        minTime: min,
        maxTime: max,
        measurements: List.from(measurements),
      );
    }
    
    return stats;
  }

  // Print performance report
  void printReport() {
    if (!_isEnabled) return;
    
    final stats = getStats();
    if (stats.isEmpty) {
      debugPrint('📊 No performance data available');
      return;
    }
    
    debugPrint('\n📊 Performance Report');
    debugPrint('=' * 50);
    
    final sortedStats = stats.values.toList()
      ..sort((a, b) => b.averageTime.compareTo(a.averageTime));
    
    for (final stat in sortedStats) {
      debugPrint('🔍 ${stat.operation}:');
      debugPrint('   Count: ${stat.count}');
      debugPrint('   Average: ${stat.averageTime.toStringAsFixed(2)}ms');
      debugPrint('   Min: ${stat.minTime}ms');
      debugPrint('   Max: ${stat.maxTime}ms');
      debugPrint('   Total: ${stat.totalTime}ms');
      
      if (stat.averageTime > 1000) {
        debugPrint('   ⚠️ SLOW OPERATION (>1s average)');
      } else if (stat.averageTime > 500) {
        debugPrint('   ⚡ Consider optimization (>500ms average)');
      }
      debugPrint('');
    }
    
    debugPrint('=' * 50);
  }

  // Clear all metrics
  void clearMetrics() {
    _metrics.clear();
    _timers.clear();
    if (kDebugMode) {
      print('🧹 Performance metrics cleared');
    }
  }

  // Monitor memory usage
  Future<MemoryInfo> getMemoryInfo() async {
    try {
      final info = ProcessInfo.currentRss;
      return MemoryInfo(
        rss: info,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting memory info: $e');
      }
      return MemoryInfo(
        rss: 0,
        timestamp: DateTime.now(),
      );
    }
  }

  // Monitor app lifecycle performance
  void monitorAppLifecycle() {
    if (!_isEnabled) return;
    
    SystemChannels.lifecycle.setMessageHandler((message) async {
      final timestamp = DateTime.now();
      
      switch (message) {
        case 'AppLifecycleState.resumed':
          if (kDebugMode) {
            print('📱 App resumed at $timestamp');
          }
          startTimer('app_session');
          break;
        case 'AppLifecycleState.paused':
          if (kDebugMode) {
            print('📱 App paused at $timestamp');
          }
          stopTimer('app_session');
          break;
        case 'AppLifecycleState.detached':
          if (kDebugMode) {
            print('📱 App detached at $timestamp');
          }
          printReport();
          break;
      }
      
      return null;
    });
  }

  // Check for performance issues
  List<PerformanceIssue> analyzePerformance() {
    final issues = <PerformanceIssue>[];
    final stats = getStats();
    
    for (final stat in stats.values) {
      // Check for slow operations
      if (stat.averageTime > 2000) {
        issues.add(PerformanceIssue(
          type: PerformanceIssueType.slowOperation,
          operation: stat.operation,
          severity: IssueSeverity.high,
          description: 'Operation takes ${stat.averageTime.toStringAsFixed(2)}ms on average',
          suggestion: 'Consider optimizing this operation or adding loading indicators',
        ));
      } else if (stat.averageTime > 1000) {
        issues.add(PerformanceIssue(
          type: PerformanceIssueType.slowOperation,
          operation: stat.operation,
          severity: IssueSeverity.medium,
          description: 'Operation takes ${stat.averageTime.toStringAsFixed(2)}ms on average',
          suggestion: 'Consider optimization or async processing',
        ));
      }
      
      // Check for inconsistent performance
      final variance = stat.maxTime - stat.minTime;
      if (variance > stat.averageTime * 2) {
        issues.add(PerformanceIssue(
          type: PerformanceIssueType.inconsistentPerformance,
          operation: stat.operation,
          severity: IssueSeverity.medium,
          description: 'High variance in execution time (${variance}ms)',
          suggestion: 'Investigate causes of performance inconsistency',
        ));
      }
      
      // Check for frequent operations
      if (stat.count > 100) {
        issues.add(PerformanceIssue(
          type: PerformanceIssueType.frequentOperation,
          operation: stat.operation,
          severity: IssueSeverity.low,
          description: 'Operation called ${stat.count} times',
          suggestion: 'Consider caching or reducing call frequency',
        ));
      }
    }
    
    return issues;
  }

  // Generate performance report
  String generateReport() {
    final buffer = StringBuffer();
    final stats = getStats();
    final issues = analyzePerformance();
    
    buffer.writeln('Al Farhan Transport App - Performance Report');
    buffer.writeln('Generated: ${DateTime.now()}');
    buffer.writeln('=' * 60);
    buffer.writeln();
    
    // Summary
    buffer.writeln('📊 Summary:');
    buffer.writeln('Total operations monitored: ${stats.length}');
    buffer.writeln('Total measurements: ${stats.values.fold(0, (sum, stat) => sum + stat.count)}');
    buffer.writeln('Performance issues found: ${issues.length}');
    buffer.writeln();
    
    // Top slowest operations
    final slowest = stats.values.toList()
      ..sort((a, b) => b.averageTime.compareTo(a.averageTime));
    
    buffer.writeln('🐌 Slowest Operations:');
    for (int i = 0; i < slowest.length && i < 5; i++) {
      final stat = slowest[i];
      buffer.writeln('${i + 1}. ${stat.operation}: ${stat.averageTime.toStringAsFixed(2)}ms avg');
    }
    buffer.writeln();
    
    // Issues
    if (issues.isNotEmpty) {
      buffer.writeln('⚠️ Performance Issues:');
      for (final issue in issues) {
        buffer.writeln('${issue.severity.emoji} ${issue.operation}:');
        buffer.writeln('   ${issue.description}');
        buffer.writeln('   💡 ${issue.suggestion}');
        buffer.writeln();
      }
    }
    
    // Detailed stats
    buffer.writeln('📈 Detailed Statistics:');
    for (final stat in stats.values) {
      buffer.writeln('${stat.operation}:');
      buffer.writeln('  Count: ${stat.count}');
      buffer.writeln('  Average: ${stat.averageTime.toStringAsFixed(2)}ms');
      buffer.writeln('  Min: ${stat.minTime}ms');
      buffer.writeln('  Max: ${stat.maxTime}ms');
      buffer.writeln('  Total: ${stat.totalTime}ms');
      buffer.writeln();
    }
    
    return buffer.toString();
  }
}

class PerformanceStats {
  final String operation;
  final int count;
  final int totalTime;
  final double averageTime;
  final int minTime;
  final int maxTime;
  final List<int> measurements;

  PerformanceStats({
    required this.operation,
    required this.count,
    required this.totalTime,
    required this.averageTime,
    required this.minTime,
    required this.maxTime,
    required this.measurements,
  });
}

class MemoryInfo {
  final int rss;
  final DateTime timestamp;

  MemoryInfo({
    required this.rss,
    required this.timestamp,
  });
}

class PerformanceIssue {
  final PerformanceIssueType type;
  final String operation;
  final IssueSeverity severity;
  final String description;
  final String suggestion;

  PerformanceIssue({
    required this.type,
    required this.operation,
    required this.severity,
    required this.description,
    required this.suggestion,
  });
}

enum PerformanceIssueType {
  slowOperation,
  inconsistentPerformance,
  frequentOperation,
  memoryLeak,
}

enum IssueSeverity {
  low,
  medium,
  high,
}

extension IssueSeverityExtension on IssueSeverity {
  String get emoji {
    switch (this) {
      case IssueSeverity.low:
        return '🟡';
      case IssueSeverity.medium:
        return '🟠';
      case IssueSeverity.high:
        return '🔴';
    }
  }
}
