import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';

class AdvancedCacheService {
  static final AdvancedCacheService _instance = AdvancedCacheService._internal();
  factory AdvancedCacheService() => _instance;
  AdvancedCacheService._internal();

  static AdvancedCacheService get instance => _instance;

  // Memory cache
  final Map<String, CacheEntry> _memoryCache = {};
  
  // Cache configuration
  static const int _maxMemoryCacheSize = 200;
  static const Duration _defaultExpiry = Duration(hours: 1);
  static const Duration _longTermExpiry = Duration(days: 7);
  static const Duration _shortTermExpiry = Duration(minutes: 15);

  // Cache statistics
  int _cacheHits = 0;
  int _cacheMisses = 0;
  int _cacheEvictions = 0;

  /// Initialize cache service
  Future<void> initialize() async {
    await _loadPersistentCache();
    _startCacheCleanup();
    
    if (kDebugMode) {
      print('💾 Advanced cache service initialized');
    }
  }

  /// Start periodic cache cleanup
  void _startCacheCleanup() {
    Timer.periodic(const Duration(minutes: 5), (timer) {
      _cleanExpiredEntries();
    });
  }

  /// Cache data with different strategies
  Future<void> cacheData(
    String key,
    dynamic data, {
    Duration? expiry,
    CacheStrategy strategy = CacheStrategy.memoryAndDisk,
    CachePriority priority = CachePriority.normal,
  }) async {
    final effectiveExpiry = expiry ?? _getDefaultExpiry(priority);
    final entry = CacheEntry(
      key: key,
      data: data,
      timestamp: DateTime.now(),
      expiry: effectiveExpiry,
      strategy: strategy,
      priority: priority,
      accessCount: 0,
      lastAccessed: DateTime.now(),
    );

    // Store in memory cache
    if (strategy == CacheStrategy.memoryOnly || strategy == CacheStrategy.memoryAndDisk) {
      await _storeInMemoryCache(entry);
    }

    // Store in persistent cache
    if (strategy == CacheStrategy.diskOnly || strategy == CacheStrategy.memoryAndDisk) {
      await _storePersistentCache(entry);
    }

    if (kDebugMode) {
      print('💾 Cached: $key (${strategy.name}, ${priority.name})');
    }
  }

  /// Get cached data
  Future<T?> getCachedData<T>(String key) async {
    // Try memory cache first
    final memoryEntry = _memoryCache[key];
    if (memoryEntry != null && !memoryEntry.isExpired) {
      memoryEntry.accessCount++;
      memoryEntry.lastAccessed = DateTime.now();
      _cacheHits++;
      
      if (kDebugMode) {
        print('🎯 Memory cache hit: $key');
      }
      
      return memoryEntry.data as T?;
    }

    // Try persistent cache
    final persistentEntry = await _getPersistentCache(key);
    if (persistentEntry != null && !persistentEntry.isExpired) {
      // Promote to memory cache if it's a frequent access
      if (persistentEntry.accessCount > 2) {
        _memoryCache[key] = persistentEntry;
      }
      
      persistentEntry.accessCount++;
      persistentEntry.lastAccessed = DateTime.now();
      await _storePersistentCache(persistentEntry);
      
      _cacheHits++;
      
      if (kDebugMode) {
        print('🎯 Disk cache hit: $key');
      }
      
      return persistentEntry.data as T?;
    }

    _cacheMisses++;
    
    if (kDebugMode) {
      print('❌ Cache miss: $key');
    }
    
    return null;
  }

  /// Store in memory cache with LRU eviction
  Future<void> _storeInMemoryCache(CacheEntry entry) async {
    // Remove expired entries first
    _cleanExpiredEntries();

    // If cache is full, evict least recently used entry
    if (_memoryCache.length >= _maxMemoryCacheSize) {
      await _evictLRUEntry();
    }

    _memoryCache[entry.key] = entry;
  }

  /// Evict least recently used entry
  Future<void> _evictLRUEntry() async {
    if (_memoryCache.isEmpty) return;

    // Find LRU entry (considering priority)
    CacheEntry? lruEntry;
    String? lruKey;

    for (final entry in _memoryCache.entries) {
      if (lruEntry == null || _shouldEvict(entry.value, lruEntry)) {
        lruEntry = entry.value;
        lruKey = entry.key;
      }
    }

    if (lruKey != null) {
      _memoryCache.remove(lruKey);
      _cacheEvictions++;
      
      if (kDebugMode) {
        print('🗑️ Evicted from memory cache: $lruKey');
      }
    }
  }

  /// Determine if entry should be evicted over another
  bool _shouldEvict(CacheEntry candidate, CacheEntry current) {
    // Higher priority entries are less likely to be evicted
    if (candidate.priority.index > current.priority.index) {
      return true;
    }
    if (candidate.priority.index < current.priority.index) {
      return false;
    }

    // Among same priority, evict least recently used
    return candidate.lastAccessed.isBefore(current.lastAccessed);
  }

  /// Store in persistent cache
  Future<void> _storePersistentCache(CacheEntry entry) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final serialized = entry.toJson();
      await prefs.setString('cache_${entry.key}', jsonEncode(serialized));
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error storing persistent cache: $e');
      }
    }
  }

  /// Get from persistent cache
  Future<CacheEntry?> _getPersistentCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final serialized = prefs.getString('cache_$key');
      
      if (serialized != null) {
        final json = jsonDecode(serialized) as Map<String, dynamic>;
        return CacheEntry.fromJson(json);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting persistent cache: $e');
      }
    }
    
    return null;
  }

  /// Load persistent cache into memory
  Future<void> _loadPersistentCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_'));
      
      for (final key in keys) {
        final cacheKey = key.substring(6); // Remove 'cache_' prefix
        final entry = await _getPersistentCache(cacheKey);
        
        if (entry != null && !entry.isExpired) {
          if (entry.strategy == CacheStrategy.memoryAndDisk && 
              _memoryCache.length < _maxMemoryCacheSize) {
            _memoryCache[cacheKey] = entry;
          }
        }
      }
      
      if (kDebugMode) {
        print('📂 Loaded ${_memoryCache.length} cache entries from disk');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading persistent cache: $e');
      }
    }
  }

  /// Clean expired entries
  void _cleanExpiredEntries() {
    final expiredKeys = <String>[];
    
    for (final entry in _memoryCache.entries) {
      if (entry.value.isExpired) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _memoryCache.remove(key);
    }
    
    if (expiredKeys.isNotEmpty && kDebugMode) {
      print('🧹 Cleaned ${expiredKeys.length} expired memory cache entries');
    }
  }

  /// Clean persistent cache
  Future<void> cleanPersistentCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_'));
      final expiredKeys = <String>[];
      
      for (final key in keys) {
        final cacheKey = key.substring(6);
        final entry = await _getPersistentCache(cacheKey);
        
        if (entry == null || entry.isExpired) {
          expiredKeys.add(key);
        }
      }
      
      for (final key in expiredKeys) {
        await prefs.remove(key);
      }
      
      if (kDebugMode) {
        print('🧹 Cleaned ${expiredKeys.length} expired persistent cache entries');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error cleaning persistent cache: $e');
      }
    }
  }

  /// Get default expiry based on priority
  Duration _getDefaultExpiry(CachePriority priority) {
    switch (priority) {
      case CachePriority.low:
        return _shortTermExpiry;
      case CachePriority.normal:
        return _defaultExpiry;
      case CachePriority.high:
        return _longTermExpiry;
    }
  }

  /// Cache specific data types with optimized strategies
  Future<void> cacheUserData(String userId, Map<String, dynamic> userData) async {
    await cacheData(
      'user_$userId',
      userData,
      expiry: _longTermExpiry,
      strategy: CacheStrategy.memoryAndDisk,
      priority: CachePriority.high,
    );
  }

  Future<void> cacheInvoiceData(String invoiceId, Map<String, dynamic> invoiceData) async {
    await cacheData(
      'invoice_$invoiceId',
      invoiceData,
      expiry: _defaultExpiry,
      strategy: CacheStrategy.memoryAndDisk,
      priority: CachePriority.normal,
    );
  }

  Future<void> cacheInventoryData(List<Map<String, dynamic>> inventoryData) async {
    await cacheData(
      'inventory_all',
      inventoryData,
      expiry: _shortTermExpiry,
      strategy: CacheStrategy.memoryOnly,
      priority: CachePriority.normal,
    );
  }

  Future<void> cacheReportData(String reportType, Map<String, dynamic> reportData) async {
    await cacheData(
      'report_$reportType',
      reportData,
      expiry: _shortTermExpiry,
      strategy: CacheStrategy.memoryOnly,
      priority: CachePriority.low,
    );
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStatistics() {
    final totalRequests = _cacheHits + _cacheMisses;
    final hitRate = totalRequests > 0 ? (_cacheHits / totalRequests) * 100 : 0.0;
    
    return {
      'memoryEntries': _memoryCache.length,
      'cacheHits': _cacheHits,
      'cacheMisses': _cacheMisses,
      'cacheEvictions': _cacheEvictions,
      'hitRate': hitRate.toStringAsFixed(2),
      'totalRequests': totalRequests,
    };
  }

  /// Clear all cache
  Future<void> clearAllCache() async {
    _memoryCache.clear();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_'));
      
      for (final key in keys) {
        await prefs.remove(key);
      }
      
      if (kDebugMode) {
        print('🗑️ All cache cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing cache: $e');
      }
    }
  }

  /// Dispose cache service
  void dispose() {
    _memoryCache.clear();
    
    if (kDebugMode) {
      print('🧹 Cache service disposed');
    }
  }
}

/// Cache entry model
class CacheEntry {
  final String key;
  final dynamic data;
  final DateTime timestamp;
  final Duration expiry;
  final CacheStrategy strategy;
  final CachePriority priority;
  int accessCount;
  DateTime lastAccessed;

  CacheEntry({
    required this.key,
    required this.data,
    required this.timestamp,
    required this.expiry,
    required this.strategy,
    required this.priority,
    required this.accessCount,
    required this.lastAccessed,
  });

  bool get isExpired => DateTime.now().difference(timestamp) > expiry;

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      'expiry': expiry.inMilliseconds,
      'strategy': strategy.index,
      'priority': priority.index,
      'accessCount': accessCount,
      'lastAccessed': lastAccessed.toIso8601String(),
    };
  }

  factory CacheEntry.fromJson(Map<String, dynamic> json) {
    return CacheEntry(
      key: json['key'],
      data: json['data'],
      timestamp: DateTime.parse(json['timestamp']),
      expiry: Duration(milliseconds: json['expiry']),
      strategy: CacheStrategy.values[json['strategy']],
      priority: CachePriority.values[json['priority']],
      accessCount: json['accessCount'],
      lastAccessed: DateTime.parse(json['lastAccessed']),
    );
  }
}

/// Cache strategies
enum CacheStrategy {
  memoryOnly,
  diskOnly,
  memoryAndDisk,
}

/// Cache priorities
enum CachePriority {
  low,
  normal,
  high,
}
